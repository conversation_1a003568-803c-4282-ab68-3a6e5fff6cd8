{"env": {"browser": true, "es6": true, "cypress/globals": true}, "plugins": ["cypress"], "extends": ["eslint:recommended"], "parser": "babel-es<PERSON>", "parserOptions": {"ecmaFeatures": {"jsx": true}, "sourceType": "module"}, "rules": {"no-this-before-super": "warn", "no-undef": "warn", "no-unreachable": "error", "no-unused-vars": "warn", "constructor-super": "warn", "valid-typeof": "warn", "no-console": "warn", "space-before-function-paren": "off", "semi": ["error", "always"], "no-unexpected-multiline": "error", "camelcase": "off", "eqeqeq": "off"}}