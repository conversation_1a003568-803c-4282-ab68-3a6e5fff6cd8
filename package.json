{"name": "bo-rocketbird", "version": "2.0.0", "private": true, "description": "rocketbird fe", "scripts": {"dev": "vue-cli-service serve", "build": "vue-cli-service build"}, "dependencies": {"@babel/polyfill": "^7.6.0", "ali-oss": "^6.1.1", "axios": "^0.15.3", "big.js": "^6.2.1", "echarts": "^3.6.2", "element-ui": ">2.15.9 || 2.15.8", "file-saver": "^1.3.3", "font-awesome": "^4.7.0", "js-cookie": "^2.2.1", "json2csv": "^4.5.4", "lockr": "^0.8.4", "lodash": "^4.17.15", "lodash-es": "^4.17.15", "moment": "^2.24.0", "nprogress": "^0.2.0", "qs": "^6.9.0", "query-string": "^4.2.3", "quill": "^1.3.7", "vee-validate": "^2.2.15", "vue": "^2.5.3", "vue-amap": "^0.2.9", "vue-cropperjs": "^2.2.0", "vue-html5-editor": "^1.1.1", "vue-json-pretty": "^1.8.3", "vue-resource": "^1.3.4", "vue-router": "^2.3.1", "vuex": "^2.0.0-rc.6", "xlsx": "^0.11.4"}, "devDependencies": {"@vue/cli-plugin-babel": "^3.12.1", "@vue/cli-plugin-eslint": "^3.12.1", "@vue/cli-plugin-unit-jest": "^3.12.1", "@vue/cli-service": "^3.12.1", "@vue/eslint-config-standard": "^4.0.0", "@vue/test-utils": "^1.0.0-beta.20", "babel-core": "7.0.0-bridge.0", "babel-eslint": "^10.0.3", "babel-jest": "^23.6.0", "chalk": "^2.3.2", "cypress": "^14.1.0", "eslint": "^5.8.0", "eslint-plugin-vue": "^5.2.3", "less": "^3.10.3", "less-loader": "^4.1.0", "opn": "^4.0.2", "ora": "^0.3.0", "script-loader": "^0.7.0", "semver": "^5.7.1", "shelljs": "^0.7.4", "vue-quill-editor": "^3.0.6", "vue-template-compiler": "^2.5.21"}, "browserslist": ["last 3 Chrome versions", "last 3 Firefox versions", "Safari >= 10", "Explorer >= 11", "Edge >= 12", "iOS >= 10", "Android >= 6"], "engines": {"node": ">= 8.9.0", "npm": ">= 5.2.0"}, "license": "ISC"}