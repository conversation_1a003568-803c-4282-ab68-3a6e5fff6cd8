# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

bo-rocketbird is a Vue.js 2 business management platform that integrates multiple service modules including payment processing, device management, and social media platform integrations.

## Development Commands

```bash
# Install dependencies
npm install

# Start development server (runs on port 8890)
npm run dev

# Build for production
npm run build
```

## Architecture

### Core Technology Stack
- **Vue.js 2.5.3** with Vue Router (history mode) and Vuex
- **Element UI** for component library
- **Axios** with custom service wrapper for HTTP requests
- **ECharts** for data visualization

### Global Configuration
- Global utilities exposed on `window` object: `axios`, `_` (lodash), `moment`, `Lockr`, `Cookies`, `_g`, `bus`
- Base URL determination via subdomain detection in `src/assets/js/global.js:getBaseUrl()`
- Environment-specific API endpoints (bo-test, bo, bo-fe-sim, op subdomains)

### Routing Architecture
- Routes organized by modules in separate files under `src/router/`
- Main route definitions in `src/routes.js` with module imports
- Route meta properties:
  - `hideLeft`: Controls left menu visibility
  - `module`: Module categorization for menu highlighting
  - `menu`: Menu grouping for navigation state

### Module Structure
The application is organized into distinct modules:

1. **Administrative** (`/home`) - User management, system config, organizational structures
2. **Web Business** (`/web`) - Main business operations, payment management, approval workflows
3. **Client** (`/c`) - Client-specific features like games, cards, member management
4. **External Platforms** - Separate router files for:
   - Alipay integration (`src/router/alipay.js`)
   - WeChat Platform (`src/router/wxPlatform.js`)
   - Douyin Platform (`src/router/dyPlatform.js`)
   - BirdNets (`src/router/birdnets.js`)
   - Cloud services (`src/router/cloud.js`)
   - IVEP educational platform (`src/router/ivep.js`)
   - Advertising (`src/router/ad.js`)

### HTTP Service Layer
- Centralized HTTP service in `src/service/index.js`
- Automatic authentication headers (`authKey`, `sessionId`)
- Platform-specific data injection (WeChat/Douyin comp_appid)
- Dynamic timeout adjustment for large page sizes
- Base URL configuration via environment detection

### State Management
- Vuex store in `src/vuex/` with standard pattern (actions, mutations, getters, state)
- Global loading state management
- Left menu visibility control

### Component Organization
- Common components in `src/components/Common/`
- Form utilities in `src/components/form/`
- Platform-specific components within respective view directories
- Reusable business components like file uploaders, selectors

### File Upload Architecture
Multiple specialized uploaders:
- `OssUploaderApk.vue`, `OssUploaderVideo.vue` - Cloud storage uploads
- `imgUploader.vue` - Image handling
- `OfficeUpload.vue` - Document processing

### Development Notes
- Lazy loading implemented for route components using `require()` syntax
- Prefetch disabled in webpack config to reduce bandwidth
- Development server configured with host check disabled
- CSS preprocessing with Less support and JavaScript enabled