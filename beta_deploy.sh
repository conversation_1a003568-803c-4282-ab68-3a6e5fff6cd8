#!/bin/bash
ls ../../../business_operation_fe | egrep -v "dist|node_modules" |awk '{print "../../../business_operation_fe/"$0}' | xargs rm -rf;
cp -rf * ../../../business_operation_fe;
cd ../../../business_operation_fe;
npm run build
eval $(awk -v version1=$1 'BEGIN{split(version1, arr,"/");print "version="arr[8]}')
mkdir -p "../business_operation_fe-production/"$version;
cp -rf dist/* "../business_operation_fe-production/"$version;
cd ../business_operation_fe-production;
rm -rf online;
ln -sf $version online;
