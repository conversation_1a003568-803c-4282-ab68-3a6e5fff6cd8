import axios from 'axios';
import _g from '../assets/js/global'
import Lockr from 'lockr'
function setWxCompAppid(data) {
  if (router.currentRoute.meta.module === 'wxPlatform') {
    const wxCompAppid = localStorage.getItem('wxCompAppid') || ''
    if (typeof data === 'undefined') {
      data = { comp_appid: wxCompAppid }
    } else if (typeof data === 'string' || typeof data === 'object') {
      data = JSON.parse(JSON.stringify(data))
      data.comp_appid = wxCompAppid
    }
    return JSON.stringify(data)
  } else if (router.currentRoute.meta.module === 'dyPlatform') {
    const dyCompAppid = localStorage.getItem('dyCompAppid') || ''
    if (typeof data === 'undefined') {
      data = { comp_appid: dyCompAppid }
    } else if (typeof data === 'string' || typeof data === 'object') {
      data = JSON.parse(JSON.stringify(data))
      data.comp_appid = dyCompAppid
    }
    return JSON.stringify(data)
  } else {
    return data
  }
}
const service = axios.create({
  baseURL: _g.getBaseUrl(), // api的base_url
  headers: {
    authKey: Lockr.get('authKey'),
    sessionId: Lockr.get('sessionId'),
    'Content-Type': 'application/json'
  },
  timeout: 30 * 1000 // 请求超时时间
})

// 请求拦截器
service.interceptors.request.use(config => {
  let configData = config.method == 'get' ? config.params : config.data
  if (configData && (+configData.page_size > 50)) {
    config.timeout = 5 * 60 * 1000;
  }
  if (config.method == 'get') {
    try {
      config.params = JSON.parse(setWxCompAppid(config.params) || '{}')
    } catch (e) {
      console.log(e);
    }
  } else {
    config.data = setWxCompAppid(config.data)
  }
  return config;
}, error => {
  Promise.reject(error);
})

export default service;
