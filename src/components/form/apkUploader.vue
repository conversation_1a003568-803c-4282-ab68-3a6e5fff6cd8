<template>
  <el-upload
    class="upload-demo"
    ref="upload"
    :action="uploadUrl"
    :before-upload="beforeUpload"
    :show-file-list="false"
    :on-progress="uploadProgress"
    :limit="1"
    :headers= "{
      authKey: authKey,
      sessionId: sessionId
    }"
    :on-success="handleUploadSuccess"
    :file-list="fileList"
    :multiple="false"
  >
    <el-button slot="trigger" size="small" type="primary" :loading="isLoading">{{loadingTxt}}</el-button>
  </el-upload>
</template>

<script>
export default {
  name: "apkUploader",
  props: {
    value: {}
  },
  data() {
    return {
      uploadUrl: _g.getBaseUrl() + "/device/versionManagement/upload",
      fileList: [],
      authKey: Lockr.get('authKey'),
      sessionId: Lockr.get('sessionId'),
      loadingTxt: "点击上传",
      isLoading: false
    };
  },
  methods: {
    beforeUpload(file) {
      this.isLoading = true;
      this.loadingTxt = "上传中";
      const fileType = ["application/vnd.android.package-archive"];
      const isApk = fileType.includes(file.type);
      if (!isApk) {
        this.isLoading = false;
        this.loadingTxt = "点击上传";
        this.$message.error("上传文件只能是 apk 格式!");
      }
      return isApk;
    },
    uploadProgress(event, file, fileList) {
      if (event && event.percent) {
        this.loadingTxt = parseInt(event.percent) + "%";
      }
    },
    handleUploadSuccess(res, file) {
      this.isLoading = false;
      this.loadingTxt = "点击上传";
      if (res.errorcode === 0) {
        this.$emit("input", res.data);
      } else {
        this.$message.error("上传失败");
      }
    }
  }
};
</script>

<style scoped>
.upload-demo {
  display: inline-block;
  vertical-align: top;
}
</style>
