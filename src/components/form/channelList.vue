<template>
  <el-select v-model="channel_id" v-if="(channelList.length > 1 || showAll) && adminAuth" placeholder="全部区域" clearable>
    <el-option v-for="item in channelList" :value="item.id" :label="item.name" :key="item.id"></el-option>
  </el-select>
</template>

<script>
import { mapState } from 'vuex';
export default {
  name: 'channelList',
  props: {
    value: {},
    showAll: {
      default: false,
      type: Boolean
    }
  },
  data() {
    return {
      adminAuth: true
    };
  },
  created() {
    const channel_id = JSON.parse(localStorage.userInfo).data.channel_id;
    if (channel_id != 0) {
      this.adminAuth = false;
    }
  },
  computed: {
    ...mapState(['channelList']),
    channel_id: {
      get() {
        return this.value || JSON.parse(localStorage.userInfo).data.channel_id;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  }
};
</script>

<style scoped>
</style>
