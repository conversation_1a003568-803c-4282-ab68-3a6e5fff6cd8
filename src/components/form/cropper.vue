<style lang="less" scoped>
  @btn-color: #19be6b;
  .cropper {
    #preview {
      overflow: hidden;
      position: absolute;
      border: 1px solid #dcdcdc;
      top: 0;
      background-color: #ccc;
    }
    .button {
      display: flex;
      align-items: center;
      .upload-btn {
        border: 1px solid @btn-color;
        border-radius: 4px;
        height: 32px;
        line-height: 32px;
        padding: 0 10px;
        margin-right: 30px;
        font-size: 14px;
        display: inline-block;
        cursor: pointer;
        color: @btn-color;
        &:hover {
          color: #fff;
          background-color: @btn-color;
        }
      }
    }
    .image-wrapper {
      display: flex;
      justify-content: center;
      width: 500px;
      height: 300px;
      border: 2px solid #dcdcdc;
      margin-bottom: 30px;
      position: relative;
      .upload-image {
        width: 100%;
        height: 100%;
      }
    }
  }
</style>

<template>
  <div class="cropper">
    <div class="image-wrapper" v-if="(imgSrc || cropImg) && !multiLoaded" :style="{ width: width + 'px', height: height==='auto'?'auto':height+'px' }">
      <img :src="cropImg" v-if="cropImg" class="upload-image" :alt="alt">
      <div id="preview" v-if="!cropImg" :style="{ width: bool ? '200px' : '300px', height: bool ? '200px' : '300px', left: (width + 10) + 'px'}"></div>
      <VueCropper :ref="refName" v-show="imgSrc && !cropImg" preview="#preview" :view-mode="viewMode" :aspectRatio="aspectRatio" :auto-crop-area="1" :src="imgSrc" alt="原图片" :container-style="{ width: width + 'px', height: height==='auto'?'auto':height+'px'  }" :img-style="{ width: width + 'px', height: height==='auto' ? 'auto' : height+'px'  }">
      </VueCropper>
    </div>
    <div class="button">
      <label class="upload-btn">{{btnText}}
        <input type="file" :id="refName" name="image" style="font-size: 1.2em; padding: 10px 0; display: none" @change="setImage" />
      </label>
      <el-button type="success" size="small" :loading="uploadLoading" @click="doUpload" v-if="imgSrc && !cropImg">保存图片</el-button>
    </div>
  </div>
</template>

<script>
  import ImgUploader from './imgUploader';
  import VueCropper from 'vue-cropperjs';
  export default {
    name: 'cropper',
    data() {
      return {
        imgSrc: '',
        cropImg: '',
        uploadLoading: false,
        multiLoaded: false,
      };
    },
    props: {
      url: {
        type: String,
        default: '/admin/UploadImages/uploadImages'
      },
      btnText: {
        type: String,
        default: '选择图片'
      },
      alt: {
        type: String,
        default: '图片'
      },
      value: {},
      multiple: {
        type: Boolean,
        default: false
      },
      width: {
        type: Number,
        default: 500
      },
      height: {
        type: [Number, String],
        default: 300
      },
      options: {
      },
      refName: {
        default: 'cropper'
      },
      bool: {
        type: Boolean,
        default: false
      },
      toDataURLType: {
        type: String,
        default: 'image/png'
      },
      toDataURLEncoderOptions: { // 需要为image/jpeg
        type: Number,
        default: 0.92
      },
      viewMode: {
        type: Number,
        default: 2
      },
      outputWidth: {
        type: String,
        default: ''
      },
      outputHeight: {
        type: String,
        default: ''
      },
      maxSize: {
        type: Number,
        default: 5
      },
    },
    components: {
      VueCropper,
      ImgUploader
    },
    computed: {
      aspectRatio() {
        return (this.options &&  typeof(this.options.aspectRatio) !== 'undefined')? this.options.aspectRatio : 1;
      }
    },
    watch: {
      value: {
        handler(val) {
          if (val) {
            this.cropImg = val;
          }
        },
        immediate: true
      }
    },
    methods: {
      setImage(e) {
        const file = e.target.files[0];
         if (this.maxSize && file.size > this.maxSize * 1024 * 1024) {
          this.$message.error(`图片过大，超过${this.maxSize}M`);
          return;
        }
        if (!file.type.includes('image/')) {
          this.$message.error('请选择图片文件');
          return;
        }
        if (typeof FileReader === 'function') {
          const reader = new FileReader();
          reader.onload = event => {
            this.multiLoaded = false;
            this.cropImg = this.uploadedImg = '';
            this.imgSrc = event.target.result;
            this.$nextTick(() => {
              this.$refs[this.refName].replace(event.target.result);
            });
          };
          if(this.bool) {
            e.target.value=''
          }
          reader.readAsDataURL(file);
        } else {
          this.$message.error('您的浏览器版本过低，无法上传图片');
        }
      },
      cropImage() {
        let obj = {}
        if(this.outputWidth && this.outputHeight) {
          obj = {
            maxWidth: this.outputWidth,
            maxHeight: this.outputHeight,
          }
        }
        return this.$refs[this.refName].getCroppedCanvas(obj).toDataURL(this.toDataURLType, this.toDataURLEncoderOptions);
      },
      send(url, postData, cropImg){
        const msg = this.$message.warning({
          message: '图片上传中，请稍候...',
          duration: 35000
        })
        this.uploadLoading = true
        this.$service
          .post(url, postData)
          .then(res => {
            console.log(res)
            setTimeout(() => {
              msg && msg.close()
              this.uploadLoading = false

              if (res.data.status === 1 || res.data.errorcode == 0) {
                this.cropImg = cropImg;
                if (this.multiple) {
                  this.multiLoaded = true;
                }
                if (this.bool) {
                  this.$emit('input', res.data.path || res.data.pictureUrl)
                } else {
                  this.$emit('input', res.data.path + '@70q_1pr')
                }
                this.$message.success(res.data.info || res.data.errormsg)
              } else {
                this.$message.error(res.data.info || res.data.errormsg);
              }
            }, 1000);
          })
          .catch(err => {
            console.log(err);

            msg && msg.close()
            this.uploadLoading = false
            this.$message.error('上传失败');
          });
      },
      doUpload() {
        const cropImg = this.cropImage();
        const url = _g.getBaseUrl() + this.url;
        const postData = {
          image_data: cropImg,
          _type: 'platform'
        };
        if (this.bool) {
          postData.file = cropImg
          this.send(url, postData, cropImg)
        } else {
          this.send(url, postData, cropImg)
        }
      }
    }
  };
</script>
