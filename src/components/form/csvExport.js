import { parse as json2csv, Parser } from 'json2csv';

function GetRow(row, columns) {
  const obj = {};

  columns.forEach(col => {
    let val = row[col.prop];
    if (col.formatter) {
      val = col.formatter(row, col, val);
    }
    obj[col.prop] = val;
  });

  return obj;
}

export default function ExportCsv(data, columns, fileName = '列表') {
  const rows = data.map(t => GetRow(t, columns));
  const fields = columns.map(t => {
    return {
      value: t.prop,
      label: t.label
    };
  });

  try {
    const json2csvParser = new Parser({ fields });
    const result = json2csvParser.parse(rows, { fields });
    const csvContent = 'data:text/csv;charset=GBK,\uFEFF' + result;
    const link = document.createElement('a');
    link.href = encodeURI(csvContent);
    link.download = `${fileName}.csv`;
    document.body.appendChild(link); // Required for FF
    link.click(); // This will download the data file named 'my_data.csv'.
    document.body.removeChild(link); // Required for FF
  } catch (err) {
    // Errors are thrown for bad options, or if the data is empty and no fields are provided.
    // Be sure to provide fields if it is possible that your data array will be empty.
    console.error(err);
  }
}
