<template>
<div class="w-320">
  <!-- <el-input class="w-320" v-model="apkPath"></el-input> -->
  <el-progress :percentage="progress" :stroke-width="10" v-show="isLoading"></el-progress>
  <el-upload
  ref="upload"
  class="upload-apk"
    drag
    action=""
    :http-request="doUpload"
    :before-upload="beforeUpload"
    :show-file-list="true"
    :on-progress="uploadProgress"
    :file-list="fileList"
    :on-change="changeFile">
  <i class="el-icon-upload"></i>
  <div class="el-upload__text">将文件拖到此处，或<em>{{loadingTxt}}</em></div>
  <div class="el-upload__tip" slot="tip">只能上传Apk文件，且不超过200M</div>
</el-upload>
</div>
</template>

<script>
import { md5 } from 'utility';

const OSS = require('ali-oss');
const ossConfig = {
  region: 'oss-cn-shenzhen',
  bucket: 'rb-platform',
  accessKeyId: '',
  accessKeySecret: '',
  stsToken: ''
}
let client = null;
export default {
  name: 'OssUploaderApk',
  props: {
    value: {}
  },
  data () {
    return {
      isLoading: false,
      apkPath: '',
      loadingTxt: '点击上传',
      fileList: [],
      progress:0,
      fileList: [],
    };
  },
  created () {
    this.$service.get('/Web/Tools/get_oss_token').then((res) => {
      if (res.data.errorcode === 0) {
        ossConfig.accessKeyId = res.data.data.AccessKeyId;
        ossConfig.accessKeySecret = res.data.data.AccessKeySecret;
        ossConfig.stsToken = res.data.data.SecurityToken;
        client = new OSS(ossConfig);
      } else {
        this.$message.error('获取上传凭证失败');
      }
    });
  },
  watch: {
    value(val) {
      if (val) {
        this.apkPath = val;
      }
    }
  },
  methods: {
    beforeUpload(file) {
      // this.$children[0].$children[0].$refs.upload.clearFiles();
      this.isLoading = true;
      this.loadingTxt = "上传中";
      const fileType = ["application/vnd.android.package-archive"];
      const isApk = fileType.includes(file.type);
      if (!isApk) {
        this.isLoading = false;
        this.loadingTxt = "点击上传";
        this.$message.error("上传文件只能是 apk 格式!");
      }
      if(file.size > 200*1024*1024){
        this.isLoading = false;
        this.loadingTxt = "点击上传";
        this.$message.error("上传文件不能超过200M");
      }
      return isApk;
    },
    getDir(){
      // let host = window.location.host;
      // let subDomain = host.split('.')[0];
      // if (subDomain === 'bo-test') {
      //   return "test/apk/"; 
      // }
      return "online/apk/"
    },
    doUpload ({file}) {
      if (!file) {
        this.$message.warning('请先选择文件')
        return false
      }
      let time = new Date().getTime();
      console.log("--time:" + time);
      let fileName = md5(""+ time);
      console.log("--fileName:" + fileName);
      let that = this;
      return client.multipartUpload(this.getDir() + fileName +".apk", file, {
        progress: function(p) {
              //p进度条的值
              // console.log(p);
              that.isLoading = true;
              that.progress = Math.floor(p * 100);
            }
      }).then((res) => {
        if(res){
          this.handleUploadSuccess(res,file.size)
        }else{
          console.log("res:" + res);
        }
      }).catch(err => {
        console.log("err:"+ err);
        this.$message.error('上传失败！')
      })
    },
    changeFile(file){
      this.$refs.upload.clearFiles() //清除文件对象
      this.logo = file.raw // 取出上传文件的对象，在其它地方也可以使用
      this.fileList = [{name: file.name, url: file.url}] // 重新手动赋值filstList， 免得自定义上传成功了, 而fileList并没有动态改变， 这样每次都是上传一个对象
    },
    uploadProgress(event, file, fileList) {
      this.isLoading = true
      if (event && event.percent) {
        console.log("process:" + event.percent);
        this.loadingTxt = parseInt(event.percent) + "%";
      }
    },
    handleUploadSuccess (res,fsize) {
      this.isLoading = false;
      this.loadingTxt = '点击上传';
      console.log(res);
      console.log(fsize);
      if (res && res.name) {
        console.log("res.name:"+ res.name);
        const path = `https://imagecdn.rocketbird.cn/${res.name}`
        console.log("path:"+ path);
        // this.setPath(path);
        this.apkPath = path
        let apkinfo = {
          filepath:path,
          filesize:fsize
        }
        // this.$emit('input', path);
        this.$emit("input", apkinfo);
        this.$message.success('上传成功');
      } else {
        this.$message.error('上传失败');
      }
    }
  }
};
</script>

<style lang="less" scoped>
.flex-video {
  video {
    display: block;
  }
}

/deep/ .el-upload{
  width: 100%;
}
/deep/ .el-upload .el-upload-dragger{
  width: 100%;
}
</style>
