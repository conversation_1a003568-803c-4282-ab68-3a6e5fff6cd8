<template>
  <el-select v-model="device_type_id" placeholder="设备类型">
    <slot></slot>
    <el-option v-for="item in deviceTypeList" :value="item.id" :label="item.device_type_name" :key="item.id"></el-option>
  </el-select>
</template>

<script>
  import { mapState, mapActions } from 'vuex';
  export default {
    name: 'deviceType',
    props: {
      value: {}
    },
    data() {
      return {
        // typeList: ['签到一体机', '闸机', '立式门禁', '壁挂门禁', '中控柜32寸', '中控柜13寸']
      };
    },
    created() {
      if (this.deviceTypeList && this.deviceTypeList.length == 0) {
        this.getDeviceTypeList();
      }
    },
    computed: {
      ...mapState(['deviceTypeList']),
      device_type_id: {
        get() {
          return this.value;
        },
        set(val) {
          this.$emit('input', val);
        }
      }
    },
    methods: {
      ...mapActions(['getDeviceTypeList'])
    }
  };
</script>

<style scoped>
</style>
