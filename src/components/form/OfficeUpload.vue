<template>
  <el-upload class="upload-demo" ref="upload" accept=".pdf,.doc,.docx,.wps,.csv,.xls,.xlsx" :action="uploadUrl" :before-upload="beforeUpload" :headers="headers" :show-file-list="false" :on-progress="uploadProgress" :limit="1" :on-success="handleUploadSuccess" :file-list="fileList" :multiple="false" name="files">
    <el-button slot="trigger" size="small" type="primary" :loading="isLoading">{{loadingTxt}}</el-button>
  </el-upload>
</template>

<script>
  export default {
    name: 'officeUpload',
    props: {
      value: {}
    },
    data() {
      return {
        uploadUrl: _g.getBaseUrl() + '/admin/UploadImages/uploadFile',
        fileList: [],
        headers: {
          authKey: Lockr.get('authKey'),
          sessionId: Lockr.get('sessionId')
        },
        loadingTxt: '点击上传',
        isLoading: false
      };
    },
    methods: {
      beforeUpload(file) {
        this.isLoading = true;
        this.loadingTxt = '上传中';
        const isLt2M = file.size / 1024 / 1024 < 2;
        if (!isLt2M) {
          this.$message.error('上传文件大小不能超过 2MB!');
        }
        return isLt2M;
      },
      uploadProgress(event, file, fileList) {
        if (event && event.percent) {
          this.loadingTxt = parseInt(event.percent) + '%';
        }
      },
      handleUploadSuccess(res, file) {
        this.isLoading = false;
        this.loadingTxt = '点击上传';
        this.$emit('on-compelate', res);
        if (res.status === 1) {
          this.$message.success(res.info);
          this.$emit('input', res.path);
        } else {
          this.$message.error(res.info);
        }
      }
    }
  };
</script>

<style scoped>
  .upload-demo {
    display: inline-block;
    vertical-align: top;
  }
</style>
