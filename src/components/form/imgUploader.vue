<template>
  <el-upload 
    class="upload-demo" 
    ref="upload" 
    :action="uploadUrl" 
    :before-upload="beforeUpload" 
    :on-success="handleUploadSuccess" 
    :show-file-list="false" 
    :data="{savePath: './Uploads/'}" 
    :file-list="fileList" 
    :multiple="false">
    <el-button slot="trigger" size="small" ref="uploadBtn" type="primary">选取文件</el-button>
    <el-button v-if="value" class='m-l-20' size="small" ref="delBtn" type="danger" @click="delPic">删除文件</el-button>
    <a href="javascript:" style="display: block">
      <img :src="value" alt="" style="max-width: 100%; max-height: 300px" />
    </a>
  </el-upload>
</template>

<script>
  export default {
    name: 'imgUploader',
    props: {
      value: {}
    },
    data() {
      return {
        uploadUrl: _g.getRbBaseUrl() + '/Admin/Public/upload',
        fileList: []
      };
    },
    methods: {
      delPic() {
        this.$emit('input','')
      },
      beforeUpload(file) {
        const fileType = ['image/jpg', 'image/png', 'image/jpeg'];
        const isJPG = fileType.includes(file.type);
        const isLt2M = file.size / 1024 / 1024 < 4;

        if (!isJPG) {
          this.$message.error('上传图片只能是 JPG/JPEG/PNG 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 4MB!');
        }
        return isJPG && isLt2M;
      },
      handleUploadSuccess(res, file) {
        if (res.status === 1) {
          this.$emit('input', res.info);
        } else {
          this.$message.error('上传失败');
        }
      }
    }
  };
</script>

<style scoped>
</style>
