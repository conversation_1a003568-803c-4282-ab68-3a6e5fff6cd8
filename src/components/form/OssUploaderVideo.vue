<template>
<div>
  <div v-if="videoPath" class="flex-video">
     <video width="320" controls name="media" :src="videoPath"></video>
     <el-button size="small"
               @click="setPath('')"
               type="primary">重新上传</el-button>
  </div>
  <div v-else>
    <input type="file"
           ref="input"
           id="file"
           accept=".MP4,.AVI,.mov,.rmvb,.rm,.FLV,.3GP" />
    <el-button size="small"
               @click="doUpload"
               :loading="isLoading"
               type="primary">{{ loadingTxt }}</el-button>

  </div>
</div>
</template>

<script>
const OSS = require('ali-oss');
const ossConfig = {
  region: 'oss-cn-shenzhen',
  bucket: 'rb-platform',
  accessKeyId: '',
  accessKeySecret: '',
  stsToken: ''
}
let client = null;
export default {
  name: 'OssUploaderVideo',
  props: {
    value: String
  },
  data () {
    return {
      isLoading: false,
      videoPath: '',
      loadingTxt: '点击上传',
    };
  },
  created () {
    this.$service.get('/Web/Tools/get_oss_token').then((res) => {
      if (res.data.errorcode === 0) {
        ossConfig.accessKeyId = res.data.data.AccessKeyId;
        ossConfig.accessKeySecret = res.data.data.AccessKeySecret;
        ossConfig.stsToken = res.data.data.SecurityToken;
        client = new OSS(ossConfig);
      } else {
        this.$message.error('获取上传凭证失败');
      }
    });
  },
  watch: {
    value(val) {
      if (val) {
        this.videoPath = val;
      }
    }
  },
  methods: {
    doUpload () {
      let file = this.$refs.input.files[0];
      if (!file) {
        this.$message.warning('请先选择文件')
        return false
      }
      return client.multipartUpload('IvepVideo/' + file.name, file, {
        progress: this.uploadProgress
      }).then((res) => {
        this.handleUploadSuccess(res)
      }).catch(err => {
        console.log(err);
        this.$message.error('上传失败！')
      })
    },
    setPath(path) {
      this.videoPath = path
      this.$emit('input', path);
    },
    uploadProgress (p) {
      this.isLoading = true
      this.loadingTxt = Math.floor(p * 100) + '%';
    },

    handleUploadSuccess (res, file) {
      this.isLoading = false;
      this.loadingTxt = '点击上传';
      if (res.name) {
        const path = `https://videocdn.rocketbird.cn/${res.name}`
        this.setPath(path)
      } else {
        this.$message.error('上传失败');
      }
    }
  }
};
</script>

<style lang="less" scoped>
.flex-video {
  video {
    display: block;
  }
}
</style>
