<template>
  <div>
    <ImgUploader ref="imgUpload" v-model="photo" style="display:none"></ImgUploader>
    <quill-editor class="client-editor" :disabled="disabled" ref="myTextEditor" v-model="content"></quill-editor>
  </div>
</template>

<script>
  import 'quill/dist/quill.snow.css';
  import { quillEditor } from 'vue-quill-editor';
  import Quill from 'quill';
  import ImgUploader from './imgUploader';
  const AlignStyle = Quill.import('attributors/style/align');
  const BackgroundStyle = Quill.import('attributors/style/background');
  const ColorStyle = Quill.import('attributors/style/color');
  const DirectionStyle = Quill.import('attributors/style/direction');
  const FontStyle = Quill.import('attributors/style/font');
  const SizeStyle = Quill.import('attributors/style/size');
  Quill.register(AlignStyle, true);
  Quill.register(BackgroundStyle, true);
  Quill.register(ColorStyle, true);
  Quill.register(DirectionStyle, true);
  Quill.register(FontStyle, true);
  Quill.register(SizeStyle, true);
  export default {
    name: 'Editor',
    data() {
      return {
        photo: '',
        addImgRange: []
      };
    },
    props: {
      compress: {
        type: Boolean,
        default: true
      },
      value: {},
      disabled: {
        type: Boolean,
        default: false
      }
    },
    components: {
      quillEditor,
      ImgUploader
    },
    computed: {
      content: {
        get() {
          return this.value;
        },
        set(value) {
          this.$emit('input', value);
        }
      }
    },
    watch: {
      photo(newVal) {
        const url = this.compress ? `${newVal}@70q_1pr` : newVal;
        if (newVal) {
          this.addImgRange = this.$refs.myTextEditor.quill.getSelection();
          this.$refs.myTextEditor.quill.insertEmbed(
            this.addImgRange != null ? this.addImgRange.index : 0,
            'image',
            url,
            Quill.sources.USER
          );
        }
      }
    },
    mounted() {
      this.initialize();
    },
    methods: {
      initialize() {
        this.$refs.myTextEditor.quill.getModule('toolbar').addHandler('image', this.imageHandler);
      },
      imageHandler(state) {
        if (state) {
          this.$refs.imgUpload.$refs.uploadBtn.$el.click();
        }
      }
    }
  };
</script>

<style>
  .client-editor {
    line-height: normal;
  }
</style>
