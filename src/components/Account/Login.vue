<template>
  <div class="wrap">
    <div class="container">
      <div class="tip">
        <div class="message">{{ showCode ? '选择密码登录' : '打开钉钉扫码登录' }}</div>
        <img v-show="showCode" @click="showCode = false" style="width: 51px; height: 44px; cursor: pointer" src="~assets/images/desktop.png" alt="">
        <img v-show="!showCode" @click="showCode = true" style="width: 64px; height: 65px; cursor: pointer" src="~assets/images/code.png" alt="">
      </div>
      <h3 class="title">勤鸟运动运营平台</h3>
      <div class="code" v-show="showCode">
        <img src="~assets/images/example.jpg" style="width: 264px;" alt="">
        <div id="login_container"></div>
      </div>
      <el-form v-show="!showCode" :model="form" :rules="rules2" ref="form" label-position="left" label-width="0px"
               class="demo-ruleForm card-box loginform">
        <el-form-item prop="username">
          <el-input type="text" v-model="form.username" auto-complete="off" placeholder="账号"></el-input>
        </el-form-item>
        <el-form-item prop="password">
          <el-input type="password" v-model="form.password" auto-complete="off" placeholder="密码"></el-input>
        </el-form-item>
        <!--<el-form-item v-if="requireVerify" prop="verifyCode">-->
          <!--<el-input type="text" v-model="form.verifyCode" auto-complete="off" placeholder="验证码" class="w-150"></el-input>-->
          <!--<img :src="verifyUrl" @click="refreshVerify()" class="verify-pos"/>-->
        <!--</el-form-item>-->
        <el-checkbox v-model="checked" style="margin-bottom: 22px;;">记住密码</el-checkbox>
        <el-form-item style="width:100%;">
          <el-button type="primary" style="width:100%;" v-loading="loading" @click.native.prevent="handleSubmit2('form')">
            登录
          </el-button>
        </el-form-item>
      </el-form>
    </div>
  </div>
</template>

<script>
  import http from '../../assets/js/http';
  import * as Types from '../../vuex/mutationTypes';
  import Global from 'src/assets/js/global'

  export default {
    name: 'login',
    data() {
      return {
        showCode: true,
        title: '',
        systemName: '',
        loading: false,
        form: {
          username: '',
          password: '',
          // verifyCode: ''
        },
        requireVerify: false,
        isVerify: 0,
        loginTimes: 0,
        verifyUrl: '',
        verifyImg: window.HOST + '/admin/base/getVerify',
        rules2: {
          username: [{ required: true, message: '请输入账号', trigger: 'blur' }],
          password: [{ required: true, message: '请输入密码', trigger: 'blur' }],
          // verifyCode: [{ required: true, message: '请输入验证码', trigger: 'blur' }]
        },
        checked: false
      };
    },
    methods: {
      initScanCode() {
        /*
        * 解释一下goto参数，参考以下例子：
        * var url = encodeURIComponent('http://localhost.me/index.php?test=1&aa=2');
        * var goto = encodeURIComponent('https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=appid&response_type=code&scope=snsapi_login&state=STATE&redirect_uri='+url)
        */
        const url = window.location.origin;
        const appId = Global.getLoginAppId();
        const goto = encodeURIComponent(`https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=${appId}&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=${url}`)
        var obj = DDLogin({
          id: "login_container",//这里需要你在自己的页面定义一个HTML标签并设置id，例如<div id="login_container"></div>或<span id="login_container"></span>
          goto, //请参考注释里的方式
          style: "background-color: #fff; padding: 0; border: 0",
          width: "250",
          height: "300"
        });
        var hanndleMessage = function (event) {
          var origin = event.origin;
          console.log("origin", event.origin);
          if(origin == "https://login.dingtalk.com" ) { //判断是否来自ddLogin扫码事件。
            var loginTmpCode = event.data; //拿到loginTmpCode后就可以在这里构造跳转链接进行跳转了
            console.log("loginTmpCode", loginTmpCode);
            window.location.href = `https://oapi.dingtalk.com/connect/oauth2/sns_authorize?appid=dingoas3ykd8ps1kiuuhcq&response_type=code&scope=snsapi_login&state=STATE&redirect_uri=${url}&loginTmpCode=${loginTmpCode}`
          }
        };
        if (typeof window.addEventListener != 'undefined') {
          window.addEventListener('message', hanndleMessage, false);
        } else if (typeof window.attachEvent != 'undefined') {
          window.attachEvent('onmessage', hanndleMessage);
        }
      },
      refreshVerify() {
        this.verifyUrl = '';
        setTimeout(() => {
          this.verifyUrl = this.verifyImg + '?v=' + moment().unix();
        }, 300);
      },
      handleSubmit2(form) {
        if (this.loading) return;
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = !this.loading;
            let data = {};
            if (this.requireVerify) {
              data.username = this.form.username;
              data.password = this.form.password;
              data.verifyCode = this.form.verifyCode;
            } else {
              data.username = this.form.username;
              data.password = this.form.password;
            }
            if (this.checked) {
              data.isRemember = 1;
            } else {
              data.isRemember = 0;
            }
            this.apiPost('admin/base/login', data).then(res => {
              if (res.errorcode !== 0) {
                ++this.loginTimes;
                if (this.loginTimes >= 3 || res.errorcode == 40021) {
                  this.requireVerify = true;
                  this.refreshVerify();
                }
                this.loading = !this.loading;
                this.handleError(res);
              } else {
                this.refreshVerify();
                if (this.checked) {
                  Cookies.set('rememberPwd', true, { expires: 1 });
                }
                this.resetCommonData(res.data);
                this.$store.commit(Types.SET_USER_INFO, res.data.userInfo);
                _g.toastMsg('success', '登录成功', 1500);
              }
            });
          } else {
            return false;
          }
        });
      },
      checkIsRememberPwd() {
        if (Cookies.get('rememberPwd')) {
          let data = {
            rememberKey: Lockr.get('rememberKey')
          };
          this.apiPost('admin/base/relogin', data).then(res => {
            this.handelResponse(res, data => {
              this.resetCommonData(data);
            });
          });
        }
      },
      codeLogin() {
        const url = 'admin/base/login'
        const postData = {
          auth_code: this.$route.query.code,
          login_type: 2
        }
        this.$service.post(url, postData).then(res => {
          if (res.data.errorcode === 0) {
            this.resetCommonData(res.data.data);
            this.$store.commit(Types.SET_USER_INFO, res.data.data.userInfo);
            _g.toastMsg('success', '登录成功', 1500);
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      }
    },
    created() {
      if (this.$route.query.code) {
        return this.codeLogin()
      }
      this.checkIsRememberPwd();
      this.apiPost('admin/base/getConfigs').then(res => {
        this.handelResponse(res, data => {
          document.title = data.SYSTEM_NAME;
          this.systemName = data.SYSTEM_NAME;
          if (parseInt(data.IDENTIFYING_CODE)) {
            this.requireVerify = true;
            try {
              this.rules2.verifyCode[0].required = true;
            } catch (e) {
              console.log(e)
            }
          }
        });
      });
      this.verifyUrl = this.verifyImg;
    },
    mounted() {
      this.initScanCode()
      window.addEventListener('keyup', e => {
        if (e.keyCode === 13) {
          this.handleSubmit2('form');
        }
      });
    },
    mixins: [http]
  };
</script>

<style lang="less" scoped>
  .wrap {
    display: flex;
    justify-content: center;
    padding-top: 15vh;
  }
  .container {
    padding: 17px;
    background-color: #fff;
    min-width: 590px;
    box-sizing: border-box;

    .tip {
      display: flex;
      justify-content: flex-end;
    }

    .message {
      position: relative;
      background: #fff6cc;
      border: 1px solid #fed400;
      color: #474747;
      font-size: 16px;
      padding: 0 10px;
      height: 30px;
      line-height: 30px;
      margin-right: 10px;
    }
    .message:after, .message:before {
      left: 100%;
      top: 50%;
      border: solid transparent;
      content: " ";
      height: 0;
      width: 0;
      position: absolute;
      pointer-events: none;
    }

    .message:after {
      border-left-color: #fff6cc;
      border-width: 3px;
      margin-top: -3px;
    }
    .message:before {
      border-left-color: #fed400;
      border-width: 4px;
      margin-top: -4px;
    }
  }
  .code {
    display: flex;
    justify-content: center;
    padding: 30px 65px 30px;
  }
  .verify-pos {
    position: absolute;
    right: 100px;
    top: 0;
  }

  .card-box {
    width: 420px;
    margin: 0 auto;
    padding-bottom: 30px;
  }

  .title {
    margin: 0px auto 40px auto;
    text-align: center;
    color: #505458;
  }

  .loginform {

  }
</style>
