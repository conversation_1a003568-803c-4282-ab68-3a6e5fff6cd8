<template>
  <el-pagination layout="total, prev, pager, next, sizes" background @size-change="sizeChange" @current-change="pageChange" :page-size="postData.page_size" :current-page.sync="postData.page_no" :page-sizes="[10, 20, 30, 40]" :total="total"></el-pagination>
</template>

<script>
  export default {
    name: 'pager',
    props: {
      total: Number,
      postData: {
        default: () => ({ page_no: 1, page_size: 10 })
      }
    },
    data() {
      return {
      }
    },
    methods: {
      sizeChange(e) {
        this.$emit('on-change', { page_size: e, page_no: 1 })
      },
      pageChange(e) {
        this.$emit('on-change', { page_no: e, page_size: this.postData.page_size })
      }
    },
  }
</script>

<style scoped>

</style>
