<template>
  <div class="bus-select-container">
    <el-select
      ref="select"
      v-model="busId"
      class="type-select"
      :class="customClass"
      :placeholder="placeholder"
      :filterable="filterable"
      :filter-method="filterMethod"
      :clearable="clearable"
      :disabled="disabled || loading"
      @clear="handleClear"
      @blur="handleClear"
      @visible-change="handleVisibleChange"
      :default-first-option="true"
      :remote-method="remoteSearch"
      :remote="true"
      :reserve-keyword="true"
      v-loading="loading"
    >
      <slot />
      <template slot="empty">
        <div v-if="loading" class="loading-text">加载中...</div>
        <div v-else class="empty-text">暂无数据</div>
      </template>
      <el-option v-for="item in displayedBuses" :label="item.name" :value="item.id" :key="item.id"></el-option>
      <div v-if="hasMoreItems && !loading" class="load-more" @mousedown.prevent @click.stop="loadMoreItems">加载更多...</div>
    </el-select>
  </div>
</template>

<script>
import _ from 'lodash'

export default {
  name: 'BusSelect',
  props: {
    placeholder: {
      type: String,
      default: '请选择场馆',
    },
    value: {
      type: [String, Number],
      default: '',
    },
    filterable: {
      type: Boolean,
      default: true,
    },
    clearable: {
      type: Boolean,
      default: true,
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    pageSize: {
      type: Number,
      default: 50,
    },
    customClass: {
      type: String,
      default: 'type-select',
    },
  },
  data() {
    return {
      allBuses: [], // Store all buses
      displayedBuses: [], // Buses currently displayed
      filteredBuses: [], // Buses after filtering
      loading: false,
      currentPage: 1,
      searchQuery: '',
      dropdownVisible: false,
    }
  },
  computed: {
    busId: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
    hasMoreItems() {
      return this.filteredBuses.length > this.currentPage * this.pageSize
    },
  },
  watch: {
    // Watch for changes in the selected value to ensure it's always displayed
    value: {
      handler() {
        if (this.allBuses.length > 0) {
          this.updateDisplayedBuses()
        }
      },
      immediate: false,
    },
  },
  created() {
    this.getBusList()
  },
  methods: {
    getBusList() {
      this.loading = true
      this.$service
        .post('web/business/bus_list')
        .then((res) => {
          if (res.data.errorcode == 0) {
            this.allBuses = res.data.data.bus_list || []
            this.filteredBuses = [...this.allBuses]
            this.updateDisplayedBuses()
          } else {
            _g.toastMsg('warning', res.data.errormsg)
            this.allBuses = []
            this.filteredBuses = []
            this.displayedBuses = []
          }
        })
        .catch((error) => {
          console.error('Failed to fetch bus list:', error)
          this.allBuses = []
          this.filteredBuses = []
          this.displayedBuses = []
        })
        .finally(() => {
          this.loading = false
          // Emit event when data loading is complete
          this.$emit('data-loaded')
        })
    },

    // Update displayed buses based on current page
    updateDisplayedBuses() {
      let buses = this.filteredBuses.slice(0, this.currentPage * this.pageSize)

      // If there's a selectedValue and it's not in the displayed buses, add it to the beginning
      if (this.value && this.allBuses.length > 0) {
        const selectedBus = this.allBuses.find((item) => String(item.id) === String(this.value))

        if (selectedBus) {
          const isAlreadyDisplayed = buses.some((item) => String(item.id) === String(this.value))

          if (!isAlreadyDisplayed) {
            // Add the selected bus to the beginning of the list
            buses = [selectedBus, ...buses]
          }
        }
      }

      this.displayedBuses = buses
    },

    // Load more items when scrolling or clicking "load more"
    loadMoreItems() {
      this.currentPage++
      this.updateDisplayedBuses()
    },

    // Handle dropdown visibility change
    handleVisibleChange(visible) {
      this.dropdownVisible = visible
      if (visible) {
        // Reset pagination when opening dropdown
        this.currentPage = 1
        this.updateDisplayedBuses()
      }
    },

    // Debounced search function
    remoteSearch: _.debounce(function (query) {
      this.searchQuery = query
      this.filterBuses()
    }, 300),

    // Filter method for el-select
    filterMethod(query) {
      // This is needed to make the built-in filtering work with our custom implementation
      return true
    },

    // Filter buses based on search query
    filterBuses() {
      if (!this.searchQuery) {
        this.filteredBuses = [...this.allBuses]
      } else {
        const lowerQuery = this.searchQuery.toLowerCase()
        this.filteredBuses = this.allBuses.filter(
          (item) =>
            (item.name && item.name.toLowerCase().includes(lowerQuery)) ||
            (item.id && String(item.id).toLowerCase().includes(lowerQuery))
        )
      }

      // Reset pagination and update displayed buses
      this.currentPage = 1
      this.updateDisplayedBuses()
    },
    handleClear: _.debounce(function () {
      this.searchQuery = ''
      this.filterBuses()
    }, 300),
  },
}
</script>
<style scoped>
.bus-select-container {
  position: relative;
  display: inline-block;
}

.loading-text,
.empty-text {
  padding: 10px;
  text-align: center;
  color: #909399;
}

.loading-text {
  color: #409eff;
}

.load-more {
  text-align: center;
  padding: 8px;
  color: #409eff;
  cursor: pointer;
  border-top: 1px solid #ebeef5;
}

.load-more:hover {
  background-color: #f5f7fa;
}
</style>
