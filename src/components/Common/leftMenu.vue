<template>
  <!-- <div>
<el-menu mode="vertical" default-active="/table" class="el-menu-vertical-demo" @select="handleselect" theme="dark" router>
<el-menu-item-group v-for="menu in menuData" :title="menu.title">
<el-menu-item v-for="item in menu.items" :index="item.path">&nbsp;&nbsp;&nbsp;&nbsp;{{item.name}}</el-menu-item>
</el-menu-item-group>
</el-menu>
</div> -->
  <div class="left-menus">
    <AppTypeSelect  v-if="$route.meta.module === 'wxPlatform' || $route.meta.module === 'dyPlatform'" ref="appTypeSelect"/>
    <div v-for="secMenu in menuData">
      <div class="c-light-gray p-l-10 m-t-15" :class="{isLink: secMenu.url ? true : false,'c-blue': secMenu.menu == menu}" @click="routerChange(secMenu)">{{secMenu.title}}</div>
      <div class="h-50" v-for="item in secMenu.child">
        <template v-if="item.menu == menu">
          <div class="w-100p h-50 p-l-40 left-menu pointer c-blue" @click="routerChange(item)">{{item.title}}</div>
        </template>
        <template v-else>
          <div class="w-100p h-50 p-l-40 left-menu pointer c-gra" @click="routerChange(item)">
            {{item.title}}
          </div>
        </template>
      </div>
    </div>
  </div>
</template>
<script>
  import * as Types from '../../vuex/mutationTypes';
  import AppTypeSelect from './AppTypeSelect';
  export default {
    name: 'leftMenu',
    props: ['menuData', 'menu'],
    data() {
      return {};
    },
    created() {
      this.getChannel();
    },
    components: {
      AppTypeSelect
    },
    watch: {
      "$route.meta.module": {
        immediate: true,
        handler(newValue) {
          if (["dyPlatform", "wxPlatform"].includes(newValue) && this.$refs.appTypeSelect) {
            this.$refs.appTypeSelect.resetData();
          }
        },
      },
    },
    methods: {
      routerChange(item) {
        // 与当前页面路由相等则刷新页面
        // console.log(this.$route)
        if (item.url) {
          if (item.url != this.$route.path) {
            router.push(item.url);
          } else {
            _g.shallowRefresh(this.$route.name);
          }
        }
      },
      getChannel() {
        const url = '/admin/channel/index';
        return this.$service
          .post(url, { channel_id: JSON.parse(localStorage.userInfo).data.channel_id })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.channelList = data.list;
              this.$store.commit(Types.SET_CHANNEL_LIST, data.list);
            } else {
              // this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>
<style scoped>
  .isLink {
    cursor: pointer;
  }
  .left-menus >div:last-child {
    margin-bottom: 20px;
  }
</style>
