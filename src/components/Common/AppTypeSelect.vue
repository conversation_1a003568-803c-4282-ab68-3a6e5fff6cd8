<template>
  <el-select
    v-model="adString"
    class="type-select"
    :placeholder="placeholder"
    :filterable="filterable"
    :clearable="clearable"
    :disabled="disabled"
    @change="adChange"
  >
    <slot />
    <el-option
      v-for="item in adOptions"
      :key="item.comp_appid"
      :label="item.comp_appname"
      :value="item.comp_appid"
    />
  </el-select>
</template>

<script>
export default {
  name: 'AppTypeSelect',
  inject:['reload'],
  props: {
    placeholder: {
      type: String,
      default: '三方平台类型'
    },
    filterable: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: <PERSON><PERSON>an,
      default: false
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      adOptions: [],
      adString: ''
    }
  },
  created() {
    this.initData()
  },
  methods: {
    adChange(id) {
      let info = ''
      this.adOptions.forEach((item, index) => {
        if (item.comp_appname === this.adString) {
          info = item
        }
      })
      const key = this.$route.meta.module === 'wxPlatform' ? 'wxCompAppid' : 'dyCompAppid';
      localStorage.setItem(key, id);
      this.reload();
    },
    resetData() {
      this.adOptions = [];
      this.adString = '';
      this.initData();
    },
    initData() {
      const module = this.$route.meta.module;
      const data = module === 'dyPlatform' ? { is_dy: true } : {};
      const key = module === 'wxPlatform' ? 'wxCompAppid' : 'dyCompAppid';
      this.$service
        .get('/Web/Component/getList',{params: data})
        .then(res => {
          if (res.data.errorcode === 0) {
            this.adOptions = res.data.data.list
            const storedId = localStorage.getItem(key);
            this.adString = storedId || (res.data.data.list[0] && res.data.data.list[0].comp_appid);
            localStorage.setItem(key, this.adString);
            this.reload()
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
    }
  }
}
</script>
<style scoped>
.type-select {
  display: block;
  width: 150px;
  margin: 15px auto;
}
</style>
