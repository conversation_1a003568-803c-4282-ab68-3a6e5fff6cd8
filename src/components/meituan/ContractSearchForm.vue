<template>
  <div class="search-form">
    <el-form :model="formData" inline class="search-form-inline">
      <el-form-item label="合约编号">
        <el-input v-model="formData.contract_no" placeholder="请输入合约编号" class="search-input" clearable></el-input>
      </el-form-item>
      <el-form-item v-if="activeTab === 'all'" label="合约状态">
        <el-select v-model="formData.contract_status" placeholder="请选择合约状态" class="search-input" clearable>
          <el-option
            v-for="item in contractStatusOptions"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          ></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="美团券码">
        <el-input v-model="formData.code" placeholder="请输入美团券码" class="search-input" clearable></el-input>
      </el-form-item>
      <el-form-item label="签约会员">
        <el-input v-model="formData.username" placeholder="请输入会员名称" class="search-input" clearable></el-input>
      </el-form-item>
      <el-form-item label="手机号">
        <el-input v-model="formData.mobile" placeholder="请输入会员手机号" class="search-input" clearable></el-input>
      </el-form-item>
      <el-form-item label="商家">
        <!-- <MerchantSelect v-model="formData.m_id" placeholder="请输入商家名称" custom-class="search-input" clearable /> -->
         <el-input v-model="formData.mer_name" placeholder="请输入商家名称" class="search-input" clearable></el-input>
      </el-form-item>
      <el-form-item label="场馆">
        <!-- <BusSelect v-model="formData.bus_id" placeholder="请输入场馆名称" custom-class="search-input" clearable /> -->
         <el-input v-model="formData.bus_name" placeholder="请输入场馆名称" class="search-input" clearable></el-input>
      </el-form-item>
      <el-form-item label="核销时间">
        <el-date-picker
          v-model="formData.write_off_time_range"
          type="daterange"
          range-separator="至"
          start-placeholder="YYYY-MM-DD"
          end-placeholder="YYYY-MM-DD"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          class="date-range-picker"
        ></el-date-picker>
      </el-form-item>
      <el-form-item v-if="activeTab === 'terminated'" label="解约时间">
        <el-date-picker
          v-model="formData.termination_time_range"
          type="daterange"
          range-separator="至"
          start-placeholder="YYYY-MM-DD"
          end-placeholder="YYYY-MM-DD"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          class="date-range-picker"
        ></el-date-picker>
      </el-form-item>
      <el-form-item v-if="activeTab === 'fulfillment_complete'" label="完成时间">
        <el-date-picker
          v-model="formData.finish_time_range"
          type="daterange"
          range-separator="至"
          start-placeholder="YYYY-MM-DD"
          end-placeholder="YYYY-MM-DD"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          class="date-range-picker"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch" :loading="loading">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
// import MerchantSelect from '@/components/Payment/MerchantSelect'
// import BusSelect from '@/components/Common/BusSelect'

export default {
  name: 'ContractSearchForm',
  // components: {
  //   MerchantSelect,
  //   BusSelect,
  // },
  props: {
    activeTab: {
      type: String,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    initialValues: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {
        contract_no: '',
        contract_status: '',
        code: '',
        username: '',
        mobile: '',
        // m_id: '',
        // bus_id: '',
        mer_name: '',
        bus_name: '',
        write_off_time_range: [],
        termination_time_range: [],
        finish_time_range: [],
      },
      contractStatusOptions: [
        { value: 1, label: '履约中' },
        { value: 2, label: '已解约' },
        { value: 3, label: '履约完成' },
      ],
    }
  },
  watch: {
    initialValues: {
      handler(newVal) {
        if (newVal) {
          this.formData = { ...this.getDefaultFormData(), ...newVal }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    getDefaultFormData() {
      return {
        contract_no: '',
        contract_status: '',
        code: '',
        username: '',
        mobile: '',
        m_id: '',
        bus_id: '',
        write_off_time_range: [],
        termination_time_range: [],
        finish_time_range: [],
      }
    },
    
    handleSearch() {
      this.$emit('search', { ...this.formData })
    },
    
    handleReset() {
      this.formData = this.getDefaultFormData()
      this.$emit('reset')
    },
  },
}
</script>

<style scoped>
/* Search form styles */
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #ebeef5;
}

.search-form-inline {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.search-form-inline .el-form-item {
  margin-right: 20px;
  margin-bottom: 16px;
}

.search-input {
  width: 200px;
}

.date-range-picker {
  width: 240px;
}

/* Responsive design */
@media (max-width: 768px) {
  .search-form-inline {
    display: block;
  }

  .search-form-inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 16px;
  }

  .search-input,
  .date-range-picker {
    width: 100%;
  }
}
</style>