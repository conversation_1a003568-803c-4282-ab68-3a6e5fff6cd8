<template>
  <div class="search-form">
    <el-form :model="formData" inline class="search-form-inline">
      <el-form-item label="商家">
        <MerchantSelect
          v-model="formData.m_id"
          placeholder="请输入商家名称，支持模糊搜索"
          custom-class="search-input"
          clearable
        />
      </el-form-item>
      <el-form-item label="场馆">
        <BusSelect
          v-model="formData.bus_id"
          placeholder="请输入场馆名称，支持模糊搜索"
          custom-class="search-input"
          clearable
        />
      </el-form-item>
      <el-form-item label="业务开通时间">
        <el-date-picker
          v-model="formData.start_time_range"
          type="daterange"
          range-separator="至"
          start-placeholder="YYYY-MM-DD"
          end-placeholder="YYYY-MM-DD"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          class="date-range-picker"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="到期时间">
        <el-date-picker
          v-model="formData.expire_time_range"
          type="daterange"
          range-separator="至"
          start-placeholder="YYYY-MM-DD"
          end-placeholder="YYYY-MM-DD"
          format="yyyy-MM-dd"
          value-format="yyyy-MM-dd"
          class="date-range-picker"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleSearch" :loading="loading">查询</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import MerchantSelect from '@/components/Payment/MerchantSelect.vue'
import BusSelect from '@/components/Common/BusSelect.vue'

export default {
  name: 'RenewSearchForm',
  components: {
    MerchantSelect,
    BusSelect,
  },
  props: {
    activeTab: {
      type: String,
      required: true,
    },
    loading: {
      type: Boolean,
      default: false,
    },
    initialValues: {
      type: Object,
      default: () => ({}),
    },
  },
  data() {
    return {
      formData: {
        m_id: '',
        bus_id: '',
        start_time_range: [],
        expire_time_range: [],
      },
    }
  },
  watch: {
    initialValues: {
      handler(newVal) {
        if (newVal) {
          this.formData = { ...this.getDefaultFormData(), ...newVal }
        }
      },
      immediate: true,
      deep: true,
    },
  },
  methods: {
    getDefaultFormData() {
      return {
        m_id: '',
        bus_id: '',
        start_time_range: [],
        expire_time_range: [],
      }
    },

    handleSearch() {
      this.$emit('search', { ...this.formData })
    },

    handleReset() {
      this.formData = this.getDefaultFormData()
      this.$emit('reset')
    },
  },
}
</script>

<style scoped>
/* Search form styles */
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #ebeef5;
}

.search-form-inline {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.search-form-inline .el-form-item {
  margin-right: 20px;
  margin-bottom: 16px;
}

.search-input {
  width: 200px;
}

.date-range-picker {
  width: 300px;
}

/* Responsive design */
@media (max-width: 768px) {
  .search-form-inline {
    display: block;
  }

  .search-form-inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 16px;
  }

  .search-input,
  .date-range-picker {
    width: 100%;
  }
}
</style>