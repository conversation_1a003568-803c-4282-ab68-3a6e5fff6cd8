<template>
  <div class="merchant-select-container">
    <el-select
      ref="select"
      clearable
      v-model="selectedValue"
      :placeholder="placeholder"
      :class="customClass"
      filterable
      :filter-method="filterMethod"
      :disabled="disabled || loading"
      @change="handleChange"
      @clear="handleClear"
      @blur="handleClear"
      @visible-change="handleVisibleChange"
      v-loading="loading"
      :default-first-option="true"
      :remote-method="remoteSearch"
      :remote="true"
      :reserve-keyword="true"
    >
      <template slot="empty">
        <div v-if="loading" class="loading-text">加载中...</div>
        <div v-else class="empty-text">暂无数据</div>
      </template>
      <el-option
        v-for="item in displayedMerchants"
        :label="`${item.mer_name} (${item.mer_id})`"
        :value="item.mer_id"
        :key="item.mer_id"
      >
        <div class="option-content">
          <span class="mer-name">{{ item.mer_name }}</span>
          <el-tag type="info" size="mini" class="appid-tag">ID: {{ item.mer_id }}</el-tag>
        </div>
      </el-option>
      <div v-if="hasMoreItems && !loading" class="load-more" @mousedown.prevent @click.stop="loadMoreItems">
        加载更多...
      </div>
    </el-select>
  </div>
</template>

<script>
import _ from 'lodash';

export default {
  name: 'MerchantSelect',
  props: {
    value: {
      type: [String, Number],
      default: '',
    },
    placeholder: {
      type: String,
      default: '商家列表',
    },
    customClass: {
      type: String,
      default: 'w-200 m-r-10',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    pageSize: {
      type: Number,
      default: 50,
    },
  },
  data() {
    return {
      allMerchants: [], // Store all merchants
      displayedMerchants: [], // Merchants currently displayed
      filteredMerchants: [], // Merchants after filtering
      loading: false,
      currentPage: 1,
      searchQuery: '',
      dropdownVisible: false,
    }
  },
  computed: {
    selectedValue: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
    hasMoreItems() {
      return this.filteredMerchants.length > this.currentPage * this.pageSize;
    },
  },
  watch: {
    // Watch for changes in the selected value to ensure it's always displayed
    value: {
      handler() {
        if (this.allMerchants.length > 0) {
          this.updateDisplayedMerchants();
        }
      },
      immediate: false
    }
  },
  methods: {
    // 获取商家列表
    getMerList() {
      this.loading = true
      this.$service
        .get('/web/domain/mer_list?comp_appid=1')
        .then((res) => {
          if (res.data.errorcode === 0) {
            this.allMerchants = res.data.data.list || [];
            this.filteredMerchants = [...this.allMerchants];
            this.updateDisplayedMerchants();
          } else {
            this.$message.warning(res.data.errormsg || '获取商家列表失败')
            this.allMerchants = [];
            this.filteredMerchants = [];
            this.displayedMerchants = [];
          }
        })
        .catch((error) => {
          console.error('Failed to fetch merchant list:', error)
          this.$message.error('获取商家列表失败')
          this.allMerchants = [];
          this.filteredMerchants = [];
          this.displayedMerchants = [];
        })
        .finally(() => {
          this.loading = false
          // Emit event when data loading is complete
          this.$emit('data-loaded')
        })
    },

    // Update displayed merchants based on current page
    updateDisplayedMerchants() {
      let merchants = this.filteredMerchants.slice(0, this.currentPage * this.pageSize);

      // If there's a selectedValue and it's not in the displayed merchants, add it to the beginning
      if (this.selectedValue && this.allMerchants.length > 0) {
        const selectedMerchant = this.allMerchants.find(item =>
          String(item.mer_id) === String(this.selectedValue)
        );

        if (selectedMerchant) {
          const isAlreadyDisplayed = merchants.some(item =>
            String(item.mer_id) === String(this.selectedValue)
          );

          if (!isAlreadyDisplayed) {
            // Add the selected merchant to the beginning of the list
            merchants = [selectedMerchant, ...merchants];
          }
        }
      }

      this.displayedMerchants = merchants;
    },

    // Load more items when scrolling or clicking "load more"
    loadMoreItems() {
      this.currentPage++;
      this.updateDisplayedMerchants();
    },

    // Handle dropdown visibility change
    handleVisibleChange(visible) {
      this.dropdownVisible = visible;
      if (visible) {
        // Reset pagination when opening dropdown
        this.currentPage = 1;
        this.updateDisplayedMerchants();
      }
    },

    // Debounced search function
    remoteSearch: _.debounce(function(query) {
      this.searchQuery = query;
      this.filterMerchants();
    }, 300),

    // Filter method for el-select
    filterMethod(query) {
      // This is needed to make the built-in filtering work with our custom implementation
      return true;
    },

    // Filter merchants based on search query
    filterMerchants() {
      if (!this.searchQuery) {
        this.filteredMerchants = [...this.allMerchants];
      } else {
        const lowerQuery = this.searchQuery.toLowerCase();
        this.filteredMerchants = this.allMerchants.filter(item =>
          item.mer_name.toLowerCase().includes(lowerQuery) ||
          String(item.mer_id).toLowerCase().includes(lowerQuery)
        );
      }

      // Reset pagination and update displayed merchants
      this.currentPage = 1;
      this.updateDisplayedMerchants();
    },

    handleChange(val) {
      this.$emit('change', val)
    },
    handleClear: _.debounce(function() {
      this.searchQuery = '';
      this.filterMerchants();
    }, 300),
  },
  created() {
    this.getMerList()
  },
}
</script>

<style scoped>
.merchant-select-container {
  position: relative;
  display: inline-block;
}

.loading-text,
.empty-text {
  padding: 10px;
  text-align: center;
  color: #909399;
}

.loading-text {
  color: #409eff;
}

.option-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.load-more {
  text-align: center;
  padding: 8px;
  color: #409eff;
  cursor: pointer;
  border-top: 1px solid #ebeef5;
}

.load-more:hover {
  background-color: #f5f7fa;
}
</style>
