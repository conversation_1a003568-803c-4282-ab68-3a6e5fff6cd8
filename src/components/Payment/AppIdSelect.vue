<template>
  <div class="appid-select-container">
    <el-select
      ref="select"
      clearable
      v-model="selectedValue"
      value-key="appid"
      :placeholder="placeholder"
      :class="customClass"
      filterable
      :filter-method="filterMethod"
      :disabled="disabled || loading"
      @change="handleChange"
      @clear="handleClear"
      @blur="handleClear"
      @visible-change="handleVisibleChange"
      v-loading="loading"
      :default-first-option="true"
      :remote-method="remoteSearch"
      :remote="true"
      :reserve-keyword="true"
    >
      <template slot="empty">
        <div v-if="loading" class="loading-text">加载中...</div>
        <div v-else class="empty-text">暂无数据</div>
      </template>
      <el-option
        v-for="item in displayedAppids"
        :value="item.appid"
        :key="item.appid"
        :label="`${item.mer_name} (${item.appid})`"
      >
        <div class="option-content">
          <span class="mer-name">{{ item.mer_name }}</span>
          <el-tag type="info" size="mini" class="appid-tag">{{ item.appid }}</el-tag>
        </div>
      </el-option>
      <div v-if="hasMoreItems && !loading" class="load-more" @mousedown.prevent @click.stop="loadMoreItems">
        加载更多...
      </div>
    </el-select>
  </div>
</template>

<script>
import _ from 'lodash';

export default {
  name: 'AppIdSelect',
  props: {
    value: {
      type: String,
      default: '',
    },
    placeholder: {
      type: String,
      default: 'AppID列表',
    },
    customClass: {
      type: String,
      default: 'w-200 m-r-10',
    },
    disabled: {
      type: Boolean,
      default: false,
    },
    emitMerchantId: {
      type: Boolean,
      default: false,
    },
    pageSize: {
      type: Number,
      default: 50,
    },
  },
  data() {
    return {
      allAppids: [], // Store all appids
      displayedAppids: [], // Appids currently displayed
      filteredAppids: [], // Appids after filtering
      loading: false,
      currentPage: 1,
      searchQuery: '',
      dropdownVisible: false,
    }
  },
  computed: {
    selectedValue: {
      get() {
        // If value is empty, return empty string
        if (!this.value) return ''

        // If allAppids is not loaded yet, return the raw value
        if (this.allAppids.length === 0) return this.value

        // Find the selected item
        const selectedItem = this.allAppids.find((item) => item.appid === this.value)
        return selectedItem ? selectedItem.appid : this.value
      },
      set(val) {
        this.$emit('input', val)
      },
    },
    hasMoreItems() {
      return this.filteredAppids.length > this.currentPage * this.pageSize;
    },
  },
  watch: {
    // Watch for changes in the selected value to ensure it's always displayed
    value: {
      handler() {
        if (this.allAppids.length > 0) {
          this.updateDisplayedAppids();
        }
      },
      immediate: false
    }
  },
  methods: {
    // 获取AppID列表
    getAppidList() {
      this.loading = true
      this.$service
        .get('/web/payment/getAppidList')
        .then((res) => {
          if (res.data.errorcode === 0) {
            this.allAppids = res.data.data || [];
            this.filteredAppids = [...this.allAppids];
            this.updateDisplayedAppids();
          } else {
            this.$message.warning(res.data.errormsg || '获取AppID列表失败')
            this.allAppids = [];
            this.filteredAppids = [];
            this.displayedAppids = [];
          }
        })
        .catch((error) => {
          console.error('Failed to fetch AppID list:', error)
          this.$message.error('获取AppID列表失败')
          this.allAppids = [];
          this.filteredAppids = [];
          this.displayedAppids = [];
        })
        .finally(() => {
          this.loading = false
          // Emit event when data loading is complete
          this.$emit('data-loaded')
        })
    },

    // Update displayed appids based on current page
    updateDisplayedAppids() {
      let appids = this.filteredAppids.slice(0, this.currentPage * this.pageSize);

      // If there's a selectedValue and it's not in the displayed appids, add it to the beginning
      if (this.selectedValue && this.allAppids.length > 0) {
        const selectedAppid = this.allAppids.find(item =>
          item.appid === this.selectedValue
        );

        if (selectedAppid) {
          const isAlreadyDisplayed = appids.some(item =>
            item.appid === this.selectedValue
          );

          if (!isAlreadyDisplayed) {
            // Add the selected appid to the beginning of the list
            appids = [selectedAppid, ...appids];
          }
        }
      }

      this.displayedAppids = appids;
    },

    // Load more items when scrolling or clicking "load more"
    loadMoreItems() {
      this.currentPage++;
      this.updateDisplayedAppids();
    },

    // Handle dropdown visibility change
    handleVisibleChange(visible) {
      this.dropdownVisible = visible;
      if (visible) {
        // Reset pagination when opening dropdown
        this.currentPage = 1;
        this.updateDisplayedAppids();
      }
    },

    // Debounced search function
    remoteSearch: _.debounce(function(query) {
      this.searchQuery = query;
      this.filterAppids();
    }, 300),

    // Filter method for el-select
    filterMethod(query) {
      // This is needed to make the built-in filtering work with our custom implementation
      return true;
    },

    // Filter appids based on search query
    filterAppids() {
      if (!this.searchQuery) {
        this.filteredAppids = [...this.allAppids];
      } else {
        const lowerQuery = this.searchQuery.toLowerCase();
        this.filteredAppids = this.allAppids.filter(item =>
          (item.mer_name && item.mer_name.toLowerCase().includes(lowerQuery)) ||
          (item.appid && item.appid.toLowerCase().includes(lowerQuery))
        );
      }

      // Reset pagination and update displayed appids
      this.currentPage = 1;
      this.updateDisplayedAppids();
    },

    handleChange(val) {
      // Emit the merchant ID if enabled
      if (this.emitMerchantId && val) {
        const selectedItem = this.allAppids.find((item) => item.appid === val)
        if (selectedItem) {
          // Emit the merchant ID
          this.$emit('merchant-id-change', selectedItem.m_id)
        }
      }
      // Emit the appid value
      this.$emit('change', val)
    },
    handleClear: _.debounce(function() {
      this.searchQuery = '';
      this.filterAppids();
    }, 300),
  },
  created() {
    this.getAppidList()
  },
}
</script>

<style scoped>
.appid-select-container {
  position: relative;
  display: inline-block;
}

.loading-text,
.empty-text {
  padding: 10px;
  text-align: center;
  color: #909399;
}

.loading-text {
  color: #409eff;
}

.option-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.mer-name {
  font-size: 14px;
  margin-right: 8px;
}

.appid-tag {
  font-size: 12px;
}

.load-more {
  text-align: center;
  padding: 8px;
  color: #409eff;
  cursor: pointer;
  border-top: 1px solid #ebeef5;
}

.load-more:hover {
  background-color: #f5f7fa;
}
</style>
