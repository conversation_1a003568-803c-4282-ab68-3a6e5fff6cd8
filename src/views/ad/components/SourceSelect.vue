<template>
  <el-select
    v-model="selId"
    :placeholder="placeholder"
    :filterable="filterable"
    :clearable="clearable"
    :disabled="disabled"
  >
    <slot />
    <el-option v-for="item in dataList" :label="item.name" :value="item.id" :key="item.id"></el-option>
  </el-select>
</template>

<script>
export default {
  name: 'SourceSelect',
  props: {
    placeholder: {
      type: String,
      default: '素材名称'
    },
    value: {
      type: [String, Number],
      default: ''
    },
    filterable: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dataList: [],
    }
  },
  computed: {
    selId: {
      get() {
        return this.value;
      },
      set(val) {
        this.$emit('input', val);
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.$service.post('web/advert/source_list',{
        page_no:1,
        page_size:1000
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.dataList = res.data.data.list;
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    },
  }
}
</script>
<style scoped>
</style>
