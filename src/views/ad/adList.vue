<template>
  <div class="container business">
    <header>
      <el-input placeholder="广告ID" v-model="postData.ad_no" class="w-150 m-r-10"></el-input>
      <SourceSelect v-model="postData.source_id" class="w-150 m-r-10"></SourceSelect>
      <el-select v-model="postData.type" placeholder="广告类型"  class="w-150 m-r-10" clearable>
        <el-option v-for="(value, key) in typeObj" :value="key" :label="value" :key="key" />
      </el-select>
      <el-button type="success" icon="search" @click="search">搜索</el-button>
    </header> 
    <el-table :data="tableData" stripe>
      <el-table-column prop="ad_no" align="center" label="广告ID"></el-table-column>
      <el-table-column prop="name" align="center" label="广告名称" show-overflow-tooltip></el-table-column>
      <el-table-column prop="source_name" align="center" label="素材名称" show-overflow-tooltip></el-table-column>
      <el-table-column prop="typeName" align="center" label="广告类型"></el-table-column>
      <el-table-column prop="create_time" align="center" label="添加日期"></el-table-column>
      <el-table-column prop="exp_time" align="center" label="有效期"></el-table-column>
      <el-table-column prop="sort" align="center" label="展示顺序"></el-table-column>
      <el-table-column prop="statusName" align="center" label="状态">
        <template scope="{row}">
          <span :style="{color: row.isExpired ? '#B8741A' : row.status === 1 ? '#F59A23' : '#70B603'}">{{ row.statusName }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="remark" align="center" label="备注" show-overflow-tooltip></el-table-column>
      <el-table-column prop="id" align="center" label="操作" width="210">
        <template scope="{row}">
          <el-button v-if="!row.isExpired && row.status === 2" size="small" @click="changeStatus(row.id, 1)" type="primary">下架</el-button>
          <el-button v-if="!row.isExpired && row.status === 1" size="small" @click="changeStatus(row.id, 2)" type="primary">上架</el-button>
          <el-button size="small" @click="toDetail(row.id)" type="primary">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteNews(row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
        <el-button type="success" @click="toDetail('')" size="small" icon="el-icon-plus">添加</el-button>
      </div>
      <el-pagination
        layout="total, prev, pager, next, sizes"
        background
        @size-change="sizeChange"
        @current-change="pageChange"
        :page-size="postData.page_size"
        :current-page.sync="postData.page_no"
        :page-sizes="[10, 20, 30, 40]"
        :total="total"
      ></el-pagination>
    </footer>
  </div>
</template>
<script>
import SourceSelect from './components/SourceSelect.vue'
export default {
  name: 'BannerList',
  components: {
    SourceSelect,
  },
  data() {
    return {
      typeObj: {
        1: '开屏广告',
        2: '首页弹窗',
        3: '信息流',
      },
      statusObj: {
        1: '未上架',
        2: '已上架',
      },
      postData: {
        page_no: 1,
        page_size: 10,
        ad_no: '',
        source_id: '',
        type: '',
      },
      total: 0,
      tableData: [],
    }
  },
  created() {
    this.getList()
  },
  methods: {
    search() {
      this.postData.page_no = 1
      this.getList()
    },
    pageChange(page) {
      this.postData.page_no = page
      this.getList()
    },
    toDetail(id = '') {
      this.$router.push({ path: '/ad/adEdit', query: { id } })
    },
    changeStatus(id, status) {
      this.$service.post('/web/advert/ad_status', { id, status }).then((res) => {
        if (res.data.errorcode === 0) {
          this.$message.success(res.data.errormsg)
          this.getList()
        } else {
          this.$message.error(res.data.errormsg)
        }
      })
    },
    deleteNews(id) {
      this.$confirm('确定删除?', '提示', {
        type: 'warning',
      }).then(() => {
        this.$service.post('/web/advert/ad_del', { id }).then((res) => {
          if (res.data.errorcode === 0) {
            this.$message.success(res.data.errormsg)
            this.getList()
          } else {
            this.$message.error(res.data.errormsg)
          }
        })
      })
    },
    sizeChange(e) {
      this.postData.page_no = 1
      this.postData.page_size = e
      this.getList()
    },
    getList() {
      this.$service.post('/web/advert/ad_list', this.postData).then((res) => {
        if (res.data.errorcode === 0) {
          const data = res.data.data
          this.total = data.count
          this.tableData = data.list.map((item) => {
            // 是否过期判断
            const isExpired = new Date(item.exp_time+' 23:59:59') < new Date()
            return {
              ...item,
              typeName: this.typeObj[item.type],
              isExpired: isExpired,
              statusName: isExpired ? '过期' : this.statusObj[item.status],
            }
          })
        } else {
          this.$message.error(res.data.errormsg)
        }
      })
    },
  },
}
</script>
<style lang="less" scoped></style>
