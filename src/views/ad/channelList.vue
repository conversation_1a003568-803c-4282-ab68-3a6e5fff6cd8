<template>
  <div class="container business">
    <el-table :data="tableData" stripe>
      <el-table-column prop="id" align="center" label="渠道ID"></el-table-column>
      <el-table-column prop="name" align="center" label="渠道名称"></el-table-column>
      <el-table-column prop="id" align="center" label="操作">
        <template scope="scope">
          <el-button size="small" @click="toDetail(scope.row.id)" type="primary">播放设置</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
      </div>
      <el-pagination
        layout="total, prev, pager, next, sizes"
        background
        @size-change="sizeChange"
        @current-change="pageChange"
        :page-size="postData.page_size"
        :current-page.sync="postData.page_no"
        :page-sizes="[10, 20, 30, 40]"
        :total="total"
      ></el-pagination>
    </footer>
  </div>
</template>
<script>
export default {
  name: 'BannerList',
  data() {
    return {
      postData: {
        page_no: 1,
        page_size: 10,
      },
      total: 0,
      tableData: [],
    }
  },
  created() {
    this.getList()
  },
  methods: {
    search() {
      this.postData.page_no = 1
      this.getList()
    },
    pageChange(page) {
      this.postData.page_no = page
      this.getList()
    },
    toDetail(id = '') {
      this.$router.push({ path: '/ad/channelEdit', query: { id } })
    },
    sizeChange(e) {
      this.postData.page_no = 1
      this.postData.page_size = e
      this.getList()
    },
    getList() {
      this.$service.post('/web/advert/channel_list', this.postData).then((res) => {
        if (res.data.errorcode === 0) {
          const data = res.data.data
          this.total = data.count
          this.tableData = data.list
        } else {
          this.$message.error(res.data.errormsg)
        }
      })
    },
  },
}
</script>
<style lang="less" scoped></style>
