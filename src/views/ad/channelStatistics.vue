<template>
  <div class="container business">
    <header>
      <el-input placeholder="渠道名称" v-model="postData.channel_name" class="w-150 m-r-10"></el-input>
      <el-date-picker
        v-model="rangeArray"
        :clearable="true"
        class="m-r-10"
        type="daterange"
        @change="pickChange"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      ></el-date-picker>
      <el-button type="success" icon="search" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column prop="date_time" align="center" label="日期"></el-table-column>
      <el-table-column prop="channel_name" align="center" label="渠道名称"></el-table-column>
      <el-table-column prop="total_show_num" align="center" label="总展示数量">
        <template slot="header" slot-scope="scope">
          总展示数量
          <Info value="该渠道当日所有广告展示的总次数" />
        </template>
      </el-table-column>
      <el-table-column prop="total_click_num" align="center" label="总点击量">
        <template slot="header" slot-scope="scope">
          总点击量
          <Info value="该渠道当日所有广告被点击的总次数" />
        </template>
      </el-table-column>
      <el-table-column prop="user_show_num" align="center" label="展示用户数">
        <template slot="header" slot-scope="scope">
          展示用户数
          <Info value="渠道当日展示所有广告的用户数（用户去重）" />
        </template>
      </el-table-column>
      <el-table-column prop="user_click_num" align="center" label="点击用户数">
        <template slot="header" slot-scope="scope">
          点击用户数
          <Info value="渠道当日点击所有广告的用户数（用户去重）" />
        </template>
      </el-table-column>
      <el-table-column prop="screen_show_num" align="center" label="开屏展示量">
        <template slot="header" slot-scope="scope">
          开屏展示量
          <Info value="该渠道当日开屏广告展示的总次数" />
        </template>
      </el-table-column>
      <el-table-column prop="screen_click_num" align="center" label="开屏点击量">
        <template slot="header" slot-scope="scope">
          开屏点击量
          <Info value="该渠道当日开屏广告被点击的总次数" />
        </template>
      </el-table-column>
      <el-table-column prop="pop_show_num" align="center" label="弹窗展示量">
        <template slot="header" slot-scope="scope">
          弹窗展示量
          <Info value="该渠道当日弹窗广告展示的总次数" />
        </template>
      </el-table-column>
      <el-table-column prop="pop_click_num" align="center" label="弹窗点击量">
        <template slot="header" slot-scope="scope">
          弹窗点击量
          <Info value="该渠道当日弹窗广告被点击的总次数" />
        </template>
      </el-table-column>
      <el-table-column prop="corset_show_num" align="center" label="信息流展示量">
        <template slot="header" slot-scope="scope">
          信息流展示量
          <Info value="该渠道当日信息流广告展示的总次数" />
        </template>
      </el-table-column>
      <el-table-column prop="corset_click_num" align="center" label="信息流点击量">
        <template slot="header" slot-scope="scope">
          信息流点击量
          <Info value="该渠道当日信息流广告被点击的总次数" />
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
        <el-button type="success" @click="handleExportCsv" size="small">导出Excel</el-button>
      </div>
      <el-pagination
        layout="total, prev, pager, next, sizes"
        background
        @size-change="sizeChange"
        @current-change="pageChange"
        :page-size="postData.page_size"
        :current-page.sync="postData.page_no"
        :page-sizes="[10, 20, 30, 40]"
        :total="total"
      ></el-pagination>
    </footer>
  </div>
</template>
<script>
import Info from 'src/components/info'
import ExportCsv from 'src/components/form/csvExport';

export default {
  name: 'channelStatistics',
  components: {
    Info,
  },
  data() {
    return {
      typeObj: {
        1: '开屏广告',
        2: '首页弹窗',
        3: '信息流',
      },
      rangeArray: [],
      postData: {
        page_no: 1,
        page_size: 10,
        channel_name: '',
        begin_time: '',
        end_time: '',
      },
      total: 0,
      tableData: [],
    }
  },
  created() {
    this.getList()
  },
  methods: {
    pickChange(val) {
      this.postData.begin_time = val ? _g.formatDate(val[0], 'yyyy-MM-dd') : ''
      this.postData.end_time = val ? _g.formatDate(val[1], 'yyyy-MM-dd') : ''
    },
    search() {
      this.postData.page_no = 1
      this.getList()
    },
    pageChange(page) {
      this.postData.page_no = page
      this.getList()
    },
    sizeChange(e) {
      this.postData.page_no = 1
      this.postData.page_size = e
      this.getList()
    },
    getList() {
      this.$service.post('/web/advert/channelStatistics', this.postData).then((res) => {
        if (res.data.errorcode === 0) {
          const data = res.data.data
          this.total = data.count
          this.tableData = data.list.map((item) => {
            return {
              ...item,
              typeName: this.typeObj[item.ad_type],
            }
          })
        } else {
          this.$message.error(res.data.errormsg)
        }
      })
    },
     handleExportCsv() {
      const fakePost = {
        ...this.postData,
        page_no: 1,
        page_size: this.total,
        is_export: 1,
      }
      this.$service.post('/web/advert/channelStatistics', fakePost).then((res) => {
        if (res.data.errorcode == 0) {
          const list = res.data.data.list.map((item) => {
            return {
              ...item,
              typeName: this.typeObj[item.ad_type],
            }
          })
          ExportCsv(list, [
            { prop: 'date_time', label: '日期' },
            { prop: 'channel_name', label: '渠道名称' },
            { prop: 'total_show_num', label: '总展示数量' },
            { prop: 'total_click_num', label: '总点击量' },
            { prop: 'user_show_num', label: '展示用户数' },
            { prop: 'user_click_num', label: '点击用户数' },
            { prop: 'screen_show_num', label: '开屏展示量' },
            { prop: 'screen_click_num', label: '开屏点击量' },
            { prop: 'pop_show_num', label: '弹窗展示量' },
            { prop: 'pop_click_num', label: '弹窗点击量' },
            { prop: 'corset_show_num', label: '信息流展示量' },
            { prop: 'corset_click_num', label: '信息流点击量' }
          ], '渠道统计')
        }
      })
    },
  },
}
</script>
<style lang="less" scoped></style>
