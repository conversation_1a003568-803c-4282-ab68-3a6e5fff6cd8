<template>
  <div class="container">
    <el-form class="form-frame" ref="form" :model="postData" label-width="100px">
      <el-card class="set-card">
        <div slot="header" class="clearfix">
          <span>开屏广告播放设置</span>
        </div>
        <el-form-item label="展示逻辑：" prop="screen_num">
          单用户单日展示次数
          <el-input-number v-model="postData.screen_num" type="number" class="set-input" placeholder="次" :controls="false" :min="0" :precision="0" />
          次
        </el-form-item>
        <el-form-item label="弹出间隔：" prop="screen_interval">
          <el-input-number v-model="postData.screen_interval" type="number" class="set-input" :controls="false" :max="720" :min="0" :precision="0" />
          分钟
          <span style="color: #999">0为不限制，最大720</span>
        </el-form-item>
      </el-card>
      <el-card class="set-card">
        <div slot="header" class="clearfix">
          <span>弹窗广告播放设置</span>
        </div>
        <el-form-item label="展示逻辑：" prop="pop_num">
          单用户单日展示次数
          <el-input-number v-model="postData.pop_num" type="number" class="set-input" placeholder="次" :controls="false" :min="0" :precision="0" />
          次
        </el-form-item>
        <el-form-item label="弹出间隔：" prop="pop_interval">
          <el-input-number v-model="postData.pop_interval" type="number" class="set-input" :controls="false" :max="720" :min="0" :precision="0" />
          分钟
          <span style="color: #999">0为不限制，最大720</span>
        </el-form-item>
      </el-card>
      <el-card class="set-card">
        <div slot="header" class="clearfix">
          <span>信息流广告播放设置</span>
        </div>
        <el-form-item label="展示逻辑：" prop="corset_num">
          单用户单日展示次数
          <el-input-number v-model="postData.corset_num" type="number" class="set-input" placeholder="次" :controls="false" :min="0" :precision="0" />
          次
        </el-form-item>
        <el-form-item label="是否轮播：" prop="corset_carousel_status">
          <el-radio-group v-model="postData.corset_carousel_status">
            <el-radio :label="0">关闭</el-radio>
            <el-radio :label="1">开启</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="轮播间隔：" prop="corset_interval">
          <el-select v-model="postData.corset_interval" class="w-150 m-r-10" clearable>
            <el-option v-for="(value, key) in timeObj" :value="key">{{ value }}</el-option>
          </el-select>秒
        </el-form-item>
      </el-card>
      <el-form-item style="padding-top: 50px">
        <el-button type="success" @click="clickSave">保存</el-button>
        <el-button type="info" @click="$router.back()" style="margin-left: 100px">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import Cropper from 'components/form/cropper'

export default {
  name: 'ChannelEdit',
  components: {
    Cropper,
  },
  data() {
    return {
      timeObj: {
        5: 5,
        8: 8,
        10: 10,
        15: 15,
        30: 30,
        60: 60,
      },
      postData: {
        id: this.$route.query.id || '',
        name: '',
        screen_num: '',
        screen_interval: '',
        pop_num: '',
        pop_interval: '',
        corset_num: '',
        corset_carousel_status: '',
        corset_interval: '',
      },
    }
  },
  created() {
    if (this.postData.id) {
      this.getInfo()
    }
  },
  methods: {
    getInfo() {
      this.$service
        .post('/web/advert/channel_detail', { id: this.postData.id })
        .then((res) => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            Object.keys(this.postData).forEach((key) => {
              this.postData[key] = data[key]
            })
          } else {
            this.$message.error(res.data.errormsg)
          }
        })
        .catch((err) => {
          console.error(err)
        })
    },
    clickSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.saveEdit()
        } else {
          this.$message.error('请先正确填写信息')
        }
      })
    },
    saveEdit() {
      this.$service.post('/web/advert/channel_upsert', this.postData).then((res) => {
        if (res.data.errorcode === 0) {
          this.$router.back()
        } else {
          this.$message.error(res.data.errormsg)
        }
      })
    },
  },
}
</script>
<style lang="less">
.clearfix:before,
.clearfix:after {
  display: table;
  content: '';
}
.clearfix:after {
  clear: both;
}
.set-card {
  width: 600px;
  margin-left: 20px;
  margin-bottom: 20px;
}
.set-input {
  width: 60px;
  margin: 0 10px;
}
</style>
