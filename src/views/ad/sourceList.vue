<template>
  <div class="container business">
    <header>
      <el-input placeholder="素材名称" v-model="postData.name" class="w-150 m-r-10"></el-input>
      <el-button type="success" icon="search" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column prop="name" align="center" label="素材名称" show-overflow-tooltip></el-table-column>
      <el-table-column prop="create_time" align="center" label="展示图">
        <template scope="{row}">
          <a v-if="row.screen_banner" :href="row.screen_banner" style="color: #409eff;margin-right:5px;" target="_blank">开屏</a>
          <a v-if="row.pop_banner" :href="row.pop_banner" style="color: #409eff;margin-right:5px;" target="_blank">弹窗</a>
          <a v-if="row.corset_banner" :href="row.corset_banner" style="color: #409eff" target="_blank">信息流</a>
        </template>
      </el-table-column>
      <el-table-column prop="status" align="center" label="详情页">
        <template scope="{row}">
          <a v-if="row.detail_img" :href="row.detail_img" style="color: #409eff" target="_blank">预览</a>
        </template>
      </el-table-column>
      <el-table-column prop="create_time" align="center" label="添加日期"></el-table-column>
      <el-table-column prop="remark" align="center" label="备注" show-overflow-tooltip></el-table-column>
      <el-table-column prop="id" align="center" label="操作">
        <template scope="scope">
          <el-button size="small" @click="toDetail(scope.row.id)" type="primary">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteNews(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
        <el-button type="success" @click="toDetail('')" size="small" icon="el-icon-plus">添加</el-button>
      </div>
      <el-pagination
        layout="total, prev, pager, next, sizes"
        background
        @size-change="sizeChange"
        @current-change="pageChange"
        :page-size="postData.page_size"
        :current-page.sync="postData.page_no"
        :page-sizes="[10, 20, 30, 40]"
        :total="total"
      ></el-pagination>
    </footer>
  </div>
</template>
<script>
export default {
  name: 'BannerList',
  data() {
    return {
      postData: {
        page_no: 1,
        page_size: 10,
        name: '',
      },
      total: 0,
      tableData: [],
    }
  },
  created() {
    this.getList()
  },
  methods: {
    search() {
      this.postData.page_no = 1
      this.getList()
    },
    pageChange(page) {
      this.postData.page_no = page
      this.getList()
    },
    toDetail(id = '') {
      this.$router.push({ path: '/ad/sourceEdit', query: { id } })
    },
    deleteNews(id) {
      this.$confirm('确定删除?', '提示', {
        type: 'warning',
      }).then(() => {
        this.$service.post('/web/advert/source_del', { id }).then((res) => {
          if (res.data.errorcode === 0) {
            this.$message.success(res.data.errormsg)
            this.getList()
          } else {
            this.$message.error(res.data.errormsg)
          }
        })
      })
    },
    sizeChange(e) {
      this.postData.page_no = 1
      this.postData.page_size = e
      this.getList()
    },
    getList() {
      this.$service.post('/web/advert/source_list', this.postData).then((res) => {
        if (res.data.errorcode === 0) {
          const data = res.data.data
          this.total = data.count
          this.tableData = data.list
        } else {
          this.$message.error(res.data.errormsg)
        }
      })
    },
  },
}
</script>
<style lang="less" scoped></style>
