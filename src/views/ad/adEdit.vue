<template>
  <div class="container">
    <el-form class="form-frame" ref="form" :model="postData" label-width="150px">
      <el-form-item label="广告名称" prop="name" :rules="[{ required: true, message: '请填写' }]">
        <el-input v-model="postData.name" class="w-320" placeholder="广告名称" />
      </el-form-item>
      <el-form-item label="广告素材" prop="source_id" :rules="[{ required: true, message: '请选择' }]">
        <SourceSelect v-model="postData.source_id" class="w-320" ></SourceSelect>
      </el-form-item>
      <el-form-item label="广告类型" prop="type" :rules="[{ required: true, message: '请选择' }]">
        <el-radio-group v-model="postData.type" :disabled="!!postData.id">
          <el-radio v-for="(value, key) in typeObj" :label="+key" :key="key">{{value}}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="点击查看详情" prop="is_open_detail" :rules="[{ required: true, message: '请选择' }]">
        <el-radio-group v-model="postData.is_open_detail">
          <el-radio :label="0">关闭</el-radio>
          <el-radio :label="1">开启</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="展示顺序" prop="sort" :rules="[{ required: true, message: '请输入' }]">
        <el-input-number v-model="postData.sort" :min="1" :precision="0"></el-input-number>
      </el-form-item>
      <el-form-item label="有效期" prop="exp_time" :rules="[{ required: true, message: '请选择' }]">
        <el-date-picker v-model="postData.exp_time" value-format="yyyy-MM-dd" type="date" placeholder="选择日期" class="w-320" :picker-options="pickerOptions"></el-date-picker>
      </el-form-item>
      
      <el-form-item label="备注">
        <el-input type="textarea" :rows="2" placeholder="备注" v-model="postData.remark" class="w-320"></el-input>
      </el-form-item>
      <el-form-item style="padding-top: 50px">
        <el-button type="success" @click="clickSave">保存</el-button>
        <el-button type="info" @click="$router.back()" style="margin-left: 100px">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import Cropper from 'components/form/cropper.vue'
import SourceSelect from './components/SourceSelect.vue'

export default {
  name: 'AdEdit',
  components: {
    Cropper,
    SourceSelect
  },
  data() {
    return {
       pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 24 * 3600 * 1000;
        }
      },
      typeObj: {
        1: '开屏广告',
        2: '首页弹窗',
        3: '信息流',
      },
      postData: {
        id: this.$route.query.id || '',
        name: '',
        source_id: '',
        type: 1,
        is_open_detail: 1,
        sort: 1,
        exp_time: '',
        remark: '',
      },
    }
  },
  created() {
    if (this.postData.id) {
      this.getInfo()
    }
  },
  methods: {
    getInfo() {
      this.$service
        .post('/web/advert/ad_detail', { id: this.postData.id })
        .then((res) => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            Object.keys(this.postData).forEach((key) => {
              this.postData[key] = data[key]
            })
          } else {
            this.$message.error(res.data.errormsg)
          }
        })
        .catch((err) => {
          console.error(err)
        })
    },
    clickSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.saveEdit()
        } else {
          this.$message.error('请先正确填写信息')
        }
      })
    },
    saveEdit() {
      this.$service.post('/web/advert/ad_upsert', this.postData).then((res) => {
        if (res.data.errorcode === 0) {
          this.$router.back()
        } else {
          this.$message.error(res.data.errormsg)
        }
      })
    },
  },
}
</script>
<style lang="less">
.tips {
  font-size: 12px;
  color: #999;
  margin-left: 5px;
}
</style>
