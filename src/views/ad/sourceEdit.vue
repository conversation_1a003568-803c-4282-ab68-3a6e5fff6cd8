<template>
  <div class="container">
    <el-form class="form-frame" ref="form" :model="postData" label-width="150px">
      <el-form-item label="素材名称" prop="name" :rules="[{ required: true, message: '请填写名称', trigger: 'blur' }]">
        <el-input v-model="postData.name" class="w-320" placeholder="素材名称" />
      </el-form-item>
      <el-form-item label="开屏广告">
        <Cropper
          v-model="postData.screen_banner"
          class="banner-cropper"
          :width="375"
          :height="812"
          outputWidth="750"
          outputHeight="1624"
          :maxSize="1"
          :bool="true"
          :options="{ aspectRatio: 750 / 1624 }"
        ></Cropper>
        <div class="tips">建议图片大小：<1MB 尺寸：750*1624px</div>
      </el-form-item>
      <el-form-item label="弹窗广告">
        <Cropper
          v-model="postData.pop_banner"
          class="banner-cropper"
          :width="285"
          :height="270"
          outputWidth="570"
          outputHeight="540"
          :maxSize="1"
          :bool="true"
          :options="{ aspectRatio: 57 / 54 }"
        ></Cropper>
        <div class="tips">建议图片大小：<1MB 尺寸：570*540px</div>
      </el-form-item>
      <el-form-item label="信息流广告">
        <Cropper
          v-model="postData.corset_banner"
          class="banner-cropper"
          :width="345"
          :height="120"
          outputWidth="690"
          outputHeight="240"
          :maxSize="1"
          :bool="true"
          :options="{ aspectRatio: 69 / 24 }"
        ></Cropper>
        <div class="tips">建议图片大小：<1MB 尺寸：690*240px</div>
      </el-form-item>
      <el-form-item label="详情页标题">
        <el-input v-model="postData.detail_name" class="w-320" placeholder="详情页标题（显示在标题栏）8个字内" />
      </el-form-item>

      <el-form-item label="详情页内容">
        <Cropper
          v-model="postData.detail_img"
          class="banner-cropper"
          :width="375"
          height="auto"
          outputWidth="750"
          :maxSize="5"
          :bool="true"
          :options="{ aspectRatio: 0 }"
        ></Cropper>
         <div class="tips">建议图片大小：<5MB 宽度：750px</div>
      </el-form-item>
      <el-form-item label="备注">
        <el-input type="textarea" :rows="2" placeholder="备注" v-model="postData.remark" class="w-320"></el-input>
      </el-form-item>
      <el-form-item style="padding-top: 50px">
        <el-button type="success" @click="clickSave">保存</el-button>
        <el-button type="info" @click="$router.back()" style="margin-left: 100px">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import Cropper from 'components/form/cropper'

export default {
  name: 'SourceEdit',
  components: {
    Cropper,
  },
  data() {
    return {
      postData: {
        id: this.$route.query.id || '',
        name: '',
        screen_banner: '',
        pop_banner: '',
        corset_banner: '',
        detail_name: '',
        detail_img: '',
        remark: '',
      },
    }
  },
  created() {
    if (this.postData.id) {
      this.getInfo()
    }
  },
  methods: {
    getInfo() {
      this.$service
        .post('/web/advert/source_detail', { id: this.postData.id })
        .then((res) => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            Object.keys(this.postData).forEach((key) => {
              this.postData[key] = data[key]
            })
          } else {
            this.$message.error(res.data.errormsg)
          }
        })
        .catch((err) => {
          console.error(err)
        })
    },
    clickSave() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.saveEdit()
        } else {
          this.$message.error('请先正确填写信息')
        }
      })
    },
    saveEdit() {
      this.$service.post('/web/advert/source_upsert', this.postData).then((res) => {
        if (res.data.errorcode === 0) {
          this.$router.back()
        } else {
          this.$message.error(res.data.errormsg)
        }
      })
    },
  },
}
</script>
<style lang="less">
.tips {
  font-size: 12px;
  color: #999;
  margin-left: 5px;
}
</style>
