<template>
  <div class="container business">
    <header>
      <el-input placeholder="广告ID" v-model="postData.ad_no" class="w-150 m-r-10"></el-input>
      <el-input placeholder="广告名称" v-model="postData.ad_name" class="w-150 m-r-10"></el-input>
      <el-select v-model="postData.ad_type" placeholder="广告类型" class="w-150 m-r-10" clearable>
        <el-option v-for="(value, key) in typeObj" :value="key" :label="value" :key="key" />
      </el-select>
      <el-date-picker
        v-model="rangeArray"
        :clearable="true"
        class="m-r-10"
        type="daterange"
        @change="pickChange"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
      ></el-date-picker>
      <el-button type="success" icon="search" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column prop="date_time" align="center" label="日期"></el-table-column>
      <el-table-column prop="ad_no" align="center" label="广告ID"></el-table-column>
      <el-table-column prop="ad_name" align="center" label="广告名称" show-overflow-tooltip></el-table-column>
      <el-table-column prop="typeName" align="center" label="广告类型"></el-table-column>
      <el-table-column prop="show_num" align="center" label="展示数量">
        <template slot="header" slot-scope="scope">
          展示数量
          <Info value="该广告当日展示的总次数" />
        </template>
      </el-table-column>
      <el-table-column prop="click_num" align="center" label="点击数量">
        <template slot="header" slot-scope="scope">
          点击数量
          <Info value="该广告当日被点击的总次数" />
        </template>
      </el-table-column>
      <el-table-column prop="user_show_num" align="center" label="展示用户数">
        <template slot="header" slot-scope="scope">
          展示用户数
          <Info value="当日展示该广告的用户数（用户去重）" />
        </template>
      </el-table-column>
      <el-table-column prop="user_click_num" align="center" label="点击用户数">
        <template slot="header" slot-scope="scope">
          点击用户数
          <Info value="当日点击该广告的用户数（用户去重）" />
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
        <el-button type="success" @click="handleExportCsv" size="small">导出Excel</el-button>
      </div>
      <el-pagination
        layout="total, prev, pager, next, sizes"
        background
        @size-change="sizeChange"
        @current-change="pageChange"
        :page-size="postData.page_size"
        :current-page.sync="postData.page_no"
        :page-sizes="[10, 20, 30, 40]"
        :total="total"
      ></el-pagination>
    </footer>
  </div>
</template>
<script>
import Info from 'src/components/info'
import ExportCsv from 'src/components/form/csvExport';

export default {
  name: 'adStatistics',
  components: {
    Info,
  },
  data() {
    return {
      typeObj: {
        1: '开屏广告',
        2: '首页弹窗',
        3: '信息流',
      },
      rangeArray: [],
      postData: {
        page_no: 1,
        page_size: 10,
        ad_type: '',
        ad_name: '',
        ad_no: '',
        begin_time: '',
        end_time: '',
      },
      total: 0,
      tableData: [],
    }
  },
  created() {
    this.getList()
  },
  methods: {
    pickChange(val) {
      this.postData.begin_time = val ? _g.formatDate(val[0], 'yyyy-MM-dd') : ''
      this.postData.end_time = val ? _g.formatDate(val[1], 'yyyy-MM-dd') : ''
    },
    search() {
      this.postData.page_no = 1
      this.getList()
    },
    pageChange(page) {
      this.postData.page_no = page
      this.getList()
    },
    sizeChange(e) {
      this.postData.page_no = 1
      this.postData.page_size = e
      this.getList()
    },
    getList() {
      this.$service.post('/web/advert/adStatistics', this.postData).then((res) => {
        if (res.data.errorcode === 0) {
          const data = res.data.data
          this.total = data.count
          this.tableData = data.list.map((item) => {
            return {
              ...item,
              typeName: this.typeObj[item.ad_type],
            }
          })
        } else {
          this.$message.error(res.data.errormsg)
        }
      })
    },
     handleExportCsv() {
      const fakePost = {
        ...this.postData,
        page_no: 1,
        page_size: this.total,
        is_export: 1,
      }
      this.$service.post('/web/advert/adStatistics', fakePost).then((res) => {
        if (res.data.errorcode == 0) {
          const list = res.data.data.list.map((item) => {
            return {
              ...item,
              typeName: this.typeObj[item.ad_type],
            }
          })
          ExportCsv(list, [
            { prop: 'date_time', label: '日期' },
            { prop: 'ad_no', label: '广告ID' },
            { prop: 'ad_name', label: '广告名称' },
            { prop: 'typeName', label: '广告类型' },
            { prop: 'show_num', label: '展示数量' },
            { prop: 'click_num', label: '点击数量' },
            { prop: 'user_show_num', label: '展示用户数' },
            { prop: 'user_click_num', label: '点击用户数' },
          ], '广告统计')
        }
      })
    },
  },
}
</script>
<style lang="less" scoped></style>
