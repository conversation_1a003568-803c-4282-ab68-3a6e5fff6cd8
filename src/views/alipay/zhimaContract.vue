<template>
  <el-tabs v-model="activeName" type="border-card">
    <el-tab-pane label="安心付" name="comeDown">
      <transition name="el-zoom-in-center">
        <contracts-by-come-down-pay v-show="activeName === 'comeDown'" />
      </transition>
    </el-tab-pane>
    <el-tab-pane label="先享后付" name="monthly">
      <transition name="el-zoom-in-center">
        <contracts-by-monthly-pay v-show="activeName === 'monthly'" />
      </transition>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import ContractsByComeDownPay from './components/ContractsByComeDownPay.vue'
import ContractsByMonthlyPay from './components/ContractsByMonthlyPay.vue'

export default {
  name: 'zhimaContracts',
  components: {
    ContractsByComeDownPay,
    ContractsByMonthlyPay
  },
  data() {
    return {
      activeName: 'comeDown'
    }
  },
}
</script>
