<template>
  <div class="container">
    <header>
     <AppidSelect v-model="postData.auth_app_id"/>
     <el-select v-model="postData.biz_status" class="w-200" placeholder="状态" clearable>
        <el-option
        v-for="item in statusInfoArr"
        :key="item.key"
        :label="item.name"
        :value="item.key">
      </el-option>
      </el-select>
      <el-button type="success" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="auth_app_id" label="appId" align="center"></el-table-column>
      <el-table-column prop="app_name" label="名称"  align="center"></el-table-column>
      <el-table-column prop="create_time" label="提报时间" align="center"></el-table-column>
      <el-table-column prop="biz_status_txt" label="状态" align="center"></el-table-column>
      <el-table-column prop="reject_reason" label="补充说明" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column align="center" label="操作">
        <template scope="scope">
          <el-button size="small" v-if="scope.row.biz_status=='AUDITED'||scope.row.biz_status=='CREATE_AUDIT_REJECT'" @click="handleDelete(scope.row.promo_record_id)" type="danger">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
     <footer>
      <div class="left">
        <el-button type="success" size="small" @click="handleUplodVersion">
         素材提报
        </el-button>
      </div>
      <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
      </el-pagination>
    </footer>
     <ApplyPromo v-model="showUploadVersion" @on-confirm="getList"/>
  </div>
</template>

<script>
  import ApplyPromo from './components/ApplyPromo.vue'
   import AppidSelect from './components/AppidSelect.vue'
  export default {
    name: 'promoRecord',
    components: {
      AppidSelect,
      ApplyPromo
    },
    data() {
      return {
        statusInfoArr: [{
          key:'AGREE',
          name: '通过' 
        },{
          key:'AUDITING',
          name: '审核中' 
        },{
          key:'REJECT',
          name: '审核驳回' 
        }],
        postData: {
          auth_app_id: '',
          category_id: '',
          biz_status: '',
          page_size: 10,
          page_no: 1
        },
        tableData: [],
        dataCount: 5,
        appIdList: [],
        showAddService: false,
        showUploadVersion: false
      }
    },
    methods: {
      search() {
        this.postData.page_no = 1;
        this.getList();
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.getList();
      },
      getList() {
        this.$service.post('Web/AlipayIsv/servicePromoList', this.postData).then(res => {
          if (res.data.errorcode == 0) {
            this.tableData = res.data.data.list;
            this.dataCount = res.data.data.count;
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      handleDelete(id) {
        this.$confirm('确定删除?', '提示', {
          type: 'warning'
        }).then(() => {
          this.$service
            .post('/Web/AlipayIsv/servicePromoDelete', { promo_record_id: id})
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$message.success(res.data.errormsg);
                this.getList();
              } else {
                this.$message.error(res.data.errormsg);
              }
            })
        })
      },
      handleEdit(info) {
        this.curInfo = info || {}
        this.showAddService = true
      },
      handleUplodVersion() {
        this.showUploadVersion = true
      }
    },
    created() {
      this.getList()
    }
  };
</script>

<style scoped>
  .table-zoom-image {
    height: 30px;
  }
  .table-zoom-image > img {
    height: 100%;
  }
  .oneline {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    margin-top: 10px;
  }
  .oneline:first-child {
    margin-top: 0;
  }
  .dynamadd i {
    width: 10%;
    height: 100%;
    margin-left: 10px;
  }
  .w90 {
    width: 90%;
  }
  .bluefont {
    color: #409eff;
    cursor: pointer;
    width: 100%;
    display: inline-block;
  }
  .overtext {
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .qrpic {
    border: none;
    width: 32px;
    height: 32px;
    background: url(../../assets/images/qrcode.png) no-repeat center;
    background-size: 32px 32px;
  }
  .flex-center {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
</style>








