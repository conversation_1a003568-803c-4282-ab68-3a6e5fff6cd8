<template>
  <el-tabs v-model="activeName" type="border-card">
    <el-tab-pane label="安心付" name="comeDown">
      <transition name="el-zoom-in-center">
        <products-by-come-down-pay v-show="activeName === 'comeDown'" />
      </transition>
    </el-tab-pane>
    <el-tab-pane label="先享后付" name="monthly">
      <transition name="el-zoom-in-center">
        <products-by-monthly-pay v-show="activeName === 'monthly'" />
      </transition>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import ProductsByComeDownPay from './components/ProductsByComeDownPay'
import ProductsByMonthlyPay from './components/ProductsByMonthlyPay'

export default {
  name: 'zhimaProducts',
  components: {
    ProductsByComeDownPay,
    ProductsByMonthlyPay
  },
  data() {
    return {
      activeName: 'comeDown'
    }
  },
}
</script>
