<template>
  <div class="container">
    <header>
     <AppidSelect v-model="postData.auth_app_id"/>
      <el-button type="success" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="auth_app_id" label="appId" align="center"></el-table-column>
      <el-table-column prop="app_name" label="名称"  align="center"></el-table-column>
      <el-table-column prop="template_version" label="模板版本" align="center"></el-table-column>
      <el-table-column prop="app_version" label="小程序版本" align="center"></el-table-column>
      <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
      <el-table-column prop="remark" label="描述" align="center"></el-table-column>
      <el-table-column prop="result" label="状态" align="center">
        <template scope="{row}">
          <div>
            {{row.result==1?'成功':'失败'}}
          </div>
        </template>
      </el-table-column>
    </el-table>
     <footer>
       <div class="left">
        <el-button type="success" size="small" @click="handleUplodVersion">
          上传版本
        </el-button>
      </div>
      <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
      </el-pagination>
    </footer>
    <div v-if="showUploadVersion">
     <UploadVersion v-model="showUploadVersion" @on-confirm="getList"/>
    </div>
  </div>
</template>

<script>
  import UploadVersion from './components/UploadVersion.vue'
   import AppidSelect from './components/AppidSelect.vue'
  export default {
    name: 'version',
    components: {
      AppidSelect,
      UploadVersion
    },
    data() {
      return {
        postData: {
          search: '',
          mer_id: '',
          page_size: 10,
          page_no: 1
        },
        tableData: [],
        dataCount: 5,
        appIdList: [],
        showUploadVersion: false
      }
    },
    methods: {
      search() {
        this.postData.page_no = 1;
        this.getList();
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.getList();
      },
      getList() {
        this.$service.post('Web/AlipayIsv/getVersionList', this.postData).then(res => {
          if (res.data.errorcode == 0) {
            this.tableData = res.data.data.list;
            this.dataCount = res.data.data.count;
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      handleUplodVersion() {
        this.showUploadVersion = true
      }
    },
    created() {
      this.getList()
    }
  };
</script>

<style scoped>
  .table-zoom-image {
    height: 30px;
  }
  .table-zoom-image > img {
    height: 100%;
  }
  .oneline {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    margin-top: 10px;
  }
  .oneline:first-child {
    margin-top: 0;
  }
  .dynamadd i {
    width: 10%;
    height: 100%;
    margin-left: 10px;
  }
  .w90 {
    width: 90%;
  }
  .bluefont {
    color: #409eff;
    cursor: pointer;
    width: 100%;
    display: inline-block;
  }
  .overtext {
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .qrpic {
    border: none;
    width: 32px;
    height: 32px;
    background: url(../../assets/images/qrcode.png) no-repeat center;
    background-size: 32px 32px;
  }
  .flex-center {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
</style>








