<template>
  <div class="container">
    <header>
      <AppidSelect v-model="postData.auth_app_id" />
      <BusSearchSelect v-model="postData.bus_id" />
      <el-button type="success" @click="search">搜索</el-button>
      <el-button size="small" class="qrpic" @click="openQr" type="primary"></el-button>
    </header>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="merchant_no" label="支付宝pid" width="170" align="center"></el-table-column>
      <el-table-column prop="auth_app_id" label="appId" width="170" align="center"></el-table-column>
      <el-table-column prop="app_name" label="名称" width="110" align="center"></el-table-column>
      <el-table-column prop="app_slogan" label="简介" align="center"></el-table-column>
      <el-table-column prop="category_names" label="类目" align="center"></el-table-column>
      <el-table-column prop="head_img" label="头像" width="80" align="center">
        <template scope="{row}">
          <div class="table-zoom-image">
            <img :src="row.app_logo" alt="" />
          </div>
        </template>
      </el-table-column>
      <el-table-column label="蚂蚁门店" prop="store_id" align="center">
        <template scope="{row}">
          <span v-if="row.bus_name" class="bluefont overtext" size="medium" @click="handleStoreSet(row)" type="text">设置</span>
        </template>
      </el-table-column>
      <el-table-column label="关联场馆" prop="bus_name" align="center">
        <template scope="{row}">
          <span class="bluefont overtext" v-if="row.bus_name" size="medium" @click="handleBusSet(row)" type="text">
            {{ row.bus_name }}
          </span>
          <span class="bluefont" v-else size="medium" @click="handleBusSet(row)" type="text">--</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="强制返回关联场馆">
        <template slot="header" slot-scope="scope">
          强制返回关联场馆
          <Info
            value="审核期间可开启此配置，开启后首次进入小程序时首页不论是否同意授权地理定位都会强制返回关联场馆，关闭后在授权地理定位的情况下会返回距离最近的场馆"
          />
        </template>
        <template scope="scope">
          <el-switch
            v-model="scope.row.forced_return"
            :active-value="1"
            :inactive-value="0"
            @change="statusChange(scope.row.forced_return, scope.row.auth_app_id)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="人员" prop="bus_names" align="center">
        <template scope="{row}">
          <span
            v-if="row.experience_json && row.experience_json.length"
            class="bluefont overtext"
            size="medium"
            @click="handlePeopleSet(row)"
            type="text"
          >
            {{ row.experience_json.length }}
          </span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column label="域白名单" prop="bus_names" align="center">
        <template scope="{row}">
          <span
            v-if="row.safe_domain_list"
            class="bluefont overtext"
            size="medium"
            @click="handleLinkSet(row)"
            type="text"
            :title="row.safe_domain_list"
          >
            {{ row.safe_domain_list }}
          </span>
          <el-button v-else type="primary" @click="handleLinkInit(row)" size="small">初始化</el-button>
        </template>
      </el-table-column>
      <el-table-column label="更多操作" align="center">
        <template scope="scope">
          <el-dropdown>
            <el-button type="primary" size="small">
              操作
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="PeopleAdd" @click.native="handlePhoneAuth(scope.row)">手机号权限申请</el-dropdown-item>
              <el-dropdown-item command="PeopleAdd" @click.native="handlePeopleAdd(scope.row)">人员添加</el-dropdown-item>
              <el-dropdown-item command="releaseDetail" @click.native="handleDelete(scope.row)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <footer>
      <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount"
      ></el-pagination>
    </footer>
    <div v-if="showStoreSet">
      <StoreSet v-model="showStoreSet" :info="curInfo" @on-confirm="getList" on-success="getList" />
    </div>
    <div v-if="showAuthQrcode">
      <AuthQrcode v-model="showAuthQrcode" />
    </div>
    <div v-if="showBusSet">
      <BusSet v-model="showBusSet" :info="curInfo" @on-confirm="getList" />
    </div>
    <div v-if="showServiceSet">
      <DomainSet :info="curInfo" v-model="showServiceSet" @on-confirm="getList" />
    </div>
    <div v-if="showPeopleSet">
      <PeopleSet :info="curInfo" v-model="showPeopleSet" />
    </div>
    <div v-if="showPeopleAdd">
      <PeopleAdd :info="curInfo" v-model="showPeopleAdd" @on-confirm="getList" />
    </div>
  </div>
</template>

<script>
import AuthQrcode from './components/AuthQrcode.vue'
import AppidSelect from './components/AppidSelect.vue'
import PeopleSet from './components/PeopleSet.vue'
import BusSearchSelect from './components/BusSearchSelect.vue'
import BusSet from './components/BusSet.vue'
import DomainSet from './components/DomainSet.vue'
import StoreSet from './components/StoreSet.vue'
import Info from 'src/components/info'
import PeopleAdd from './components/PeopleAdd.vue'
export default {
  name: 'minConfig',
  components: {
    AuthQrcode,
    AppidSelect,
    BusSearchSelect,
    BusSet,
    PeopleSet,
    PeopleAdd,
    StoreSet,
    Info,
    DomainSet,
  },
  data() {
    return {
      postData: {
        auth_app_id: '',
        bus_id: '',
        page_size: 10,
        page_no: 1,
      },
      curInfo: {},
      tableData: [],
      dataCount: 0,
      appIdList: [],
      showPeopleAdd: false,
      showPeopleSet: false,
      showBusSet: false,
      showStoreSet: false,
      showAuthQrcode: false,
      showServiceSet: false,
    }
  },
  methods: {
    search() {
      this.postData.page_no = 1
      this.getList()
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage
      this.getList()
    },
    getList() {
      this.$service.post('Web/AlipayIsv/getAppList', this.postData).then((res) => {
        if (res.data.errorcode == 0) {
          this.tableData = res.data.data.list
          this.dataCount = res.data.data.count
        } else {
          _g.toastMsg('warning', res.data.errormsg)
        }
      })
    },
    // 一键获取手机号登录权限申请
    handlePhoneAuth(info) {
      this.$service
        .post('Web/AlipayIsv/setAppApiFieldApply', {
          field_name: 'mobile',
          api_name: 'alipay.user.info.share',
          scene_code: '14',
          package_code: '20161008134238569099',
          auth_app_id: info.auth_app_id,
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            _g.toastMsg('success', '申请成功')
            this.getList()
          } else {
            _g.toastMsg('warning', res.data.errormsg)
          }
        })
    },
    handlePeopleAdd(info) {
      this.curInfo = info
      this.showPeopleAdd = true
    },
    handlePeopleSet(info) {
      this.curInfo = info
      this.showPeopleSet = true
    },
    handleBusSet(info) {
      this.curInfo = info
      this.showBusSet = true
    },
    handleStoreSet(info) {
      this.curInfo = info
      this.showStoreSet = true
    },
    handleLinkInit(info) {
      this.$service
        .post('Web/AlipayIsv/setSafeDomain', {
          action: 3,
          auth_app_id: info.auth_app_id,
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            _g.toastMsg('success', '初始化成功')
            this.getList()
          } else {
            _g.toastMsg('warning', res.data.errormsg)
          }
        })
    },
    statusChange(forced_return, auth_app_id) {
      this.$service
        .post('Web/AlipayIsv/setForcedReturn', {
          auth_app_id,
          forced_return,
        })
        .then((res) => {
          if (res.data.errorcode == 0) {
            _g.toastMsg('success', '设置成功')
          } else {
            _g.toastMsg('warning', res.data.errormsg)
            this.getList()
          }
        })
    },
    handleDelete(info) {
      this.$confirm('请谨慎操作！确定删除?', '提示', {
        type: 'warning',
      }).then(() => {
        this.$service
          .post('Web/AlipayIsv/deleteAppSetting', {
            auth_app_id: info.auth_app_id,
          })
          .then((res) => {
            if (res.data.errorcode == 0) {
              _g.toastMsg('success', '删除成功')
              this.getList()
            } else {
              _g.toastMsg('warning', res.data.errormsg)
            }
          })
      })
    },
    handleLinkSet(info) {
      this.curInfo = info
      this.showServiceSet = true
    },
    openQr(info) {
      this.showAuthQrcode = true
    },
  },
  created() {
    this.getList()
  },
}
</script>

<style scoped>
.table-zoom-image {
  height: 30px;
}
.table-zoom-image > img {
  height: 100%;
}
.bluefont {
  color: #409eff;
  cursor: pointer;
  width: 100%;
  display: inline-block;
}
.overtext {
  text-overflow: ellipsis;
  white-space: nowrap;
  overflow: hidden;
}
.qrpic {
  border: none;
  width: 32px;
  height: 32px;
  background: url(../../assets/images/qrcode.png) no-repeat center;
  background-size: 32px 32px;
}
</style>
