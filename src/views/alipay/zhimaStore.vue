<template>
  <el-tabs v-model="activeName" type="border-card">
    <el-tab-pane label="安心付" name="comeDown">
      <transition name="el-zoom-in-center">
        <stores-by-come-down-pay v-show="activeName === 'comeDown'"></stores-by-come-down-pay>
      </transition>
    </el-tab-pane>
    <el-tab-pane label="先享后付" name="monthly">
      <transition name="el-zoom-in-center">
        <stores-by-monthly-pay v-show="activeName === 'monthly'"></stores-by-monthly-pay>
      </transition>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import StoresByComeDownPay from './components/StoresByComeDownPay.vue'
import StoresByMonthlyPay from './components/StoresByMonthlyPay.vue'

export default {
  name: 'zhimaStore',
  components: {
    StoresByComeDownPay,
    StoresByMonthlyPay
  },
  data() {
    return {
      activeName: 'comeDown'
    }
  },
}
</script>
