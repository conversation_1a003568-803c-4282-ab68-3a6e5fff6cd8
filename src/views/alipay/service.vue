<template>
  <div class="container">
    <header>
     <ServiceCategory v-model="postData.category_id"/>
      <el-button type="success" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="service_name" label="服务名称" align="center"></el-table-column>
      <el-table-column prop="category_name" label="服务类型"  align="center"></el-table-column>
      <el-table-column prop="service_desc" label="服务描述" align="center"></el-table-column>
      <el-table-column prop="service_url" label="服务链接" align="center"></el-table-column>
      <el-table-column align="center" label="操作">
        <template scope="scope">
          <el-button size="small" @click="handleEdit(scope.row)" type="primary">编辑</el-button>
          <el-button size="small" @click="handleDelete(scope.row.id)" type="danger">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
     <footer>
      <div class="left">
        <el-button type="success" size="small" @click="handleAddService">
          添加服务
        </el-button>
      </div>
      <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
      </el-pagination>
    </footer>
    <div v-if="showAddService">
      <AddService :info="curInfo"  v-model="showAddService" @on-confirm="getList"/>
    </div>
  </div>
</template>

<script>
  import AddService from './components/AddService.vue'
   import ServiceCategory from './components/ServiceCategory.vue'
  export default {
    name: 'version',
    components: {
      ServiceCategory,
      AddService
    },
    data() {
      return {
        postData: {
          category_id: '',
          page_size: 10,
          page_no: 1
        },
        curInfo: {},
        tableData: [],
        dataCount: 5,
        appIdList: [],
        showAddService: false
      }
    },
    methods: {
      search() {
        this.postData.page_no = 1;
        this.getList();
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.getList();
      },
      getList() {
        this.$service.post('Web/AlipayIsv/getSchemaCategoryTemplate', this.postData).then(res => {
          if (res.data.errorcode == 0) {
            this.tableData = res.data.data.list;
            this.dataCount = res.data.data.count;
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      handleDelete(id) {
        this.$confirm('确定删除?', '提示', {
          type: 'warning'
        }).then(() => {
          this.$service
            .post('/Web/AlipayIsv/deleteSchemaCategoryTemplate', { id })
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$message.success(res.data.errormsg);
                this.getList();
              } else {
                this.$message.error(res.data.errormsg);
              }
            })
        })
      },
      handleEdit(info) {
        this.curInfo = info || {}
        this.showAddService = true
      },
      handleAddService() {
         this.curInfo = {}
        this.showAddService = true
      }
    },
    created() {
      this.getList()
    }
  };
</script>

<style scoped>
  .table-zoom-image {
    height: 30px;
  }
  .table-zoom-image > img {
    height: 100%;
  }
  .oneline {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    margin-top: 10px;
  }
  .oneline:first-child {
    margin-top: 0;
  }
  .dynamadd i {
    width: 10%;
    height: 100%;
    margin-left: 10px;
  }
  .w90 {
    width: 90%;
  }
  .bluefont {
    color: #409eff;
    cursor: pointer;
    width: 100%;
    display: inline-block;
  }
  .overtext {
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .qrpic {
    border: none;
    width: 32px;
    height: 32px;
    background: url(../../assets/images/qrcode.png) no-repeat center;
    background-size: 32px 32px;
  }
  .flex-center {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
</style>








