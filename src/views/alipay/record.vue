<template>
  <div class="container">
    <header>
      <AppidSelect v-model="postData.auth_app_id"/>
      <el-select v-model="postData.status" class="w-200" clearable>
        <el-option
        v-for="item in statusInfoArr"
        :key="item.key"
        :label="item.name"
        :value="item.key">
      </el-option>
      </el-select>
      <el-button type="success" @click="search">搜索</el-button>
      <el-button type="primary" @click="handleSync">同步</el-button>
    </header>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="auth_app_id" label="appId" align="center"></el-table-column>
      <el-table-column prop="app_name" label="名称"  align="center"></el-table-column>
      <el-table-column prop="app_version" label="小程序版本" align="center"></el-table-column>
      <el-table-column prop="create_time" label="版本创建时间"  align="center"></el-table-column>
      <el-table-column prop="version_description" label="版本描述"  align="center"></el-table-column>
      <el-table-column prop="remark" label="构建描述"  align="center"></el-table-column>
      <el-table-column prop="version_status_txt" label="状态" align="center">
      </el-table-column>
      <el-table-column label="更多操作" align="center">
        <template scope="scope">
          <el-dropdown>
            <el-button type="primary" size="small">
              操作
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item v-if="scope.row.app_version" command="releaseDetail" @click.native="handleShowReleaseDetail(scope.row)">查看详情</el-dropdown-item>
              <el-dropdown-item command="unRelase" @click.native="handleReleaseVersion(1, scope.row)">发布</el-dropdown-item>
              <el-dropdown-item command="unRelase" @click.native="handleReleaseVersion(2, scope.row)">下架</el-dropdown-item>
              <el-dropdown-item command="cancel" @click.native="handleCancelAudit(scope.row)">撤销审核</el-dropdown-item>
              <el-dropdown-item command="back" @click.native="handleReleaseVersion(3, scope.row)">回滚</el-dropdown-item>
              <el-dropdown-item command="unRelase" @click.native="handleReleaseVersion(4, scope.row)">退回开发</el-dropdown-item>
              <el-dropdown-item command="expCode" v-if="scope.row.app_version" @click.native="handleExpCode(scope.row)">体验版二维码</el-dropdown-item>
              <el-dropdown-item command="backAll" @click.native="handleBackDevBeforePubByMini(scope.row)">恢复开发</el-dropdown-item>
              <el-dropdown-item command="versionList" @click.native="handleGetAllVersionByMini(scope.row)">版本列表</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <footer>
       <div class="left">
        <el-button type="success" @click="handleCommitAudit">
          提交审核
        </el-button>
         <el-button type="success" @click="handleReleaseVersion(1)">
          发布
        </el-button>
      </div>
      <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
      </el-pagination>
    </footer>
    <div v-if="showReleaseDetail">
      <VersionDetail :info="curInfo" v-model="showReleaseDetail" />
    </div>
    <div v-if="showCommitAudit">
      <CommitAudit v-model="showCommitAudit" @on-confirm="getList" />
    </div>
    <div v-if="showReleaseVersion">
      <ReleaseVersion :info="curInfo" :action="releaseAction" v-model="showReleaseVersion" @on-confirm="getList" />
    </div>
    <div v-if="showExpQrcode">
      <ExpQrcode :info="curInfo" v-model="showExpQrcode" />
    </div>
    <div v-if="showVersionList">
      <VersionList :info="curInfo" v-model="showVersionList" />
    </div>
  </div>
  
</template>

<script>
  import VersionDetail from './components/VersionDetail.vue'
  import AppidSelect from './components/AppidSelect.vue'
  import CommitAudit from './components/CommitAudit.vue'
  import ExpQrcode from './components/ExpQrcode.vue'
  import ReleaseVersion from './components/ReleaseVersion.vue'
  import VersionList from './components/VersionList.vue'
  export default {
    name: 'record',
    components: {
      VersionDetail,
      AppidSelect,
      CommitAudit,
      ExpQrcode,
      ReleaseVersion,
      VersionList
    },
    data() {
      return {
        statusInfoArr: [{
          key:'INIT',
          name: '开发中' 
        },{
          key:'AUDITING',
          name: '审核中' 
        },{
          key:'AUDIT_REJECT',
          name: '审核驳回' 
        },{
          key:'BASE_AUDIT_PASS',
          name: '不可营销' 
        },{
          key:'WAIT_RELEASE',
          name: '待上架' 
        },{
          key:'GRAY',
          name: '灰度中' 
        },{
          key:'RELEASE',
          name: '已上架' 
        },{
          key:'OFFLINE',
          name: '已下架' 
        },{
          key:'AUDIT_OFFLINE',
          name: '已下架' 
        }],
        postData: {
          status: '',
          auth_app_id: '',
          page_size: 10,
          page_no: 1
        },
        curInfo: {},
        releaseAction: 1,
        tableData: [],
        dataCount: 5,
        appIdList: [],
        showExpQrcode: false,
        showReleaseDetail: false,
        showCommitAudit: false,
        showReleaseVersion: false,
        showVersionList:false
      }
    },
    methods: {
      search() {
        this.postData.page_no = 1;
        this.getList();
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.getList();
      },
      getList() {
        this.$service.post('Web/AlipayIsv/getAuditReleaseStatusList', this.postData).then(res => {
          if (res.data.errorcode == 0) {
            this.tableData = res.data.data.list;
            this.dataCount = res.data.data.count;
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      handleCommitAudit() {
        this.showCommitAudit = true
      },
      handleShowReleaseDetail(info) {
        this.curInfo = info || {}
        this.showReleaseDetail = true
      },
      handleReleaseVersion(type, info) {
        this.releaseAction = type
        this.curInfo = info || {}
        this.showReleaseVersion = true
      },
      handleExpCode(info) {
        this.curInfo = info || {}
        this.showExpQrcode = true
      },
      handleCancelAudit(info) {
        this.$confirm('确定撤销?', '提示', {
          type: 'warning'
        }).then(() => {
          this.$service.post('Web/AlipayIsv/cancelAudit', {
            auth_app_ids: [info.auth_app_id],
            app_version: info.app_version
          }).then(res => {
            if (res.data.errorcode == 0) {
              this.tableData = res.data.data.list;
              this.dataCount = res.data.data.count;
            } else {
              _g.toastMsg('warning', res.data.errormsg);
            }
          });
        })
      },
      handleBackDevBeforePubByMini(info){
        this.$confirm('确定将该小程序的所有未上架过的版本退回开发?', '提示', {
          type: 'warning'
        }).then(() => {
          this.$service.post('Web/AlipayIsv/backDevBeforePubByMini', {
            auth_app_id: info.auth_app_id
          }).then(res => {
            if (res.data.errorcode == 0) {
              _g.toastMsg('normal', res.data.errormsg);
            } else {
              _g.toastMsg('warning', res.data.errormsg);
            }
          });
        })
      },
      handleGetAllVersionByMini(info){
        this.$service.post('Web/AlipayIsv/getAllVersionByMini', {
            auth_app_id: info.auth_app_id
          }).then(res => {
            if (res.data.errorcode == 0) {
              this.curInfo = info || {}
              this.showVersionList = true
            } else {
              _g.toastMsg('warning', res.data.errormsg);
            }
          });
      },
      handleSync() {
        this.$confirm('同步时间较长，是否继续?', '提示', {
          type: 'warning'
        }).then(() => {
          this.$service.post('/Web/AlipayIsv/syncMiniVersion').then(res => {
            if (res.data.errorcode == 0) {
              _g.toastMsg('normal', '同步开始, 请稍后查看');
            } else {
              _g.toastMsg('warning', res.data.errormsg);
            }
          });
        })
      }
    },
    created() {
      this.getList()
    }
  };
</script>

<style scoped>
  .table-zoom-image {
    height: 30px;
  }
  .table-zoom-image > img {
    height: 100%;
  }
  .oneline {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    margin-top: 10px;
  }
  .oneline:first-child {
    margin-top: 0;
  }
  .dynamadd i {
    width: 10%;
    height: 100%;
    margin-left: 10px;
  }
  .w90 {
    width: 90%;
  }
  .bluefont {
    color: #409eff;
    cursor: pointer;
    width: 100%;
    display: inline-block;
  }
  .overtext {
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .qrpic {
    border: none;
    width: 32px;
    height: 32px;
    background: url(../../assets/images/qrcode.png) no-repeat center;
    background-size: 32px 32px;
  }
  .flex-center {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
</style>








