<template>
  <div class="container">
    <div class="box">
      <el-tabs v-model="tabName" @tab-click="handleTabChange">
        <el-tab-pane :label="tab.name + ' ' + tab.date" :name="tab.name" v-for="tab in tabList" :key="tab.name">
          <div class="card-box">
            <el-card v-for="card in cardList" :key="card.label" shadow="hover"
              style="min-width: 400px; width: 22%; margin: 20px">
              <div slot="header" class="clearfix">
                <span>{{ card.label }}</span>
              </div>
              <div class="value"> {{ card.value }} </div>
              <div class="desc" v-if="card.tender === 'up'">
                较{{ trendName }}上涨
                <i class="el-icon-top-right" style="color: red; font-size: 16px" /> {{ card.count }} （{{ card.percentage
                }}%）
              </div>
              <div class="desc" v-else-if="card.tender === 'none'">
                和{{ trendName }}持平
                <i class="el-icon-right" style="color: blue; font-size: 16px" /> {{ card.count }} （{{ card.percentage }}%）
              </div>
              <div class="desc" v-else-if="card.tender === 'down'">
                较{{ trendName }}下滑
                <i class="el-icon-bottom-right" style="color: green; font-size: 16px" /> {{ card.count }}
                （{{ card.percentage }}%）
              </div>
            </el-card>
          </div>
        </el-tab-pane>
      </el-tabs>
      <el-divider>本年度各项指标月度增长趋势</el-divider>
      <div class="chart-box">
        <el-card shadow="hover" style="min-width: 400px; width: 22%; height: 350px; margin: 20px" :padding="0"
          v-for="chart in chartList" :key="chart.label">
          <div slot="header" class="clearfix">
            <span>{{ chart.label }}</span>
          </div>
          <LineChart v-if="!loadingChart" :options="chart.options" />
        </el-card>
      </div>
    </div>
  </div>
</template>

<script>
import LineChart from './components/LineChart.vue'
import { formatDate } from '@/utils'

// chart
const monthList = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
const thisMonth = new Date().getMonth() + 1
const chartOptions = {
  xAxis: {
    type: 'category',
    data: monthList.slice(0, thisMonth),
  },
  yAxis: {
    type: 'value',
  },
  tooltip: {
    trigger: 'axis',
    formatter: '{b}月: {c}',
  },
}
const chartData = {
  type: 'line',
  smooth: true,
  symbol: 'circle', // 显示折线点
  itemStyle: {
    color: '#007BFF', // 折线颜色
  },
  emphasis: {
    focus: 'series',
    blurScope: 'coordinateSystem',
  },
}

export default {
  name: 'AlipayDashboard',
  components: {
    LineChart
  },
  data() {
    // this year and today
    const today = new Date()
    // this week
    const monday = new Date()
    monday.setDate(monday.getDate() - (monday.getDay() - 1))
    const sunday = new Date()
    sunday.setDate(sunday.getDate() - (sunday.getDay() - 1) + 6)
    // this month
    const firstDayOfThisMonth = new Date(today.getFullYear(), today.getMonth(), 1)
    const lastDayOfThisMonth = new Date(today.getFullYear(), today.getMonth() + 1, 0)
    // last year
    const lastYear = new Date(today.getFullYear() - 1, today.getMonth(), today.getDate())
    // yesterday
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000)
    // last week
    const lastMonday = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
    lastMonday.setDate(lastMonday.getDate() - (lastMonday.getDay() - 1))
    const lastSunday = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000)
    lastSunday.setDate(lastSunday.getDate() - (lastSunday.getDay() - 1) + 6)
    // last month
    const firstDayOfLastMonth = new Date(today.getFullYear(), today.getMonth() - 1, 1)
    const lastDayOfLastMonth = new Date(today.getFullYear(), today.getMonth(), 0)
    return {
      tabName: '本年度',
      trendName: '上年度',
      tabList: [
        {
          name: '本年度',
          trendName: '上年度',
          date: formatDate(today, 'yyyy年'),
          start_date: formatDate(today, 'yyyy-01-01'),
          end_date: formatDate(today, 'yyyy-12-31'),
          type: 1,
          child_type: 4
        },
        {
          name: '今日',
          trendName: '昨日',
          date: formatDate(today, 'MM月dd日'),
          start_date: formatDate(today, 'yyyy-MM-dd'),
          end_date: formatDate(today, 'yyyy-MM-dd'),
          type: 1,
          child_type: 1
        },
        {
          name: '本周',
          trendName: '上周',
          date: formatDate(monday, 'MM月dd日') + ' ~ ' + formatDate(sunday, 'MM月dd日'),
          start_date: formatDate(monday, 'yyyy-MM-dd'),
          end_date: formatDate(sunday, 'yyyy-MM-dd'),
          type: 1,
          child_type: 2
        },
        {
          name: '本月',
          trendName: '上月',
          date: formatDate(today, 'MM月'),
          start_date: formatDate(firstDayOfThisMonth, 'yyyy-MM-dd'),
          end_date: formatDate(lastDayOfThisMonth, 'yyyy-MM-dd'),
          type: 1,
          child_type: 3
        },
        {
          name: '上年度',
          trendName: '上年度',
          date: formatDate(lastYear, 'yyyy年'),
          start_date: formatDate(lastYear, 'yyyy-01-01'),
          end_date: formatDate(lastYear, 'yyyy-12-31'),
          type: 2,
          child_type: 4
        },
        {
          name: '昨日',
          trendName: '昨日',
          date: formatDate(yesterday, 'MM月dd日'),
          start_date: formatDate(yesterday, 'yyyy-MM-dd'),
          end_date: formatDate(yesterday, 'yyyy-MM-dd'),
          type: 2,
          child_type: 1
        },
        {
          name: '上周',
          trendName: '上周',
          date: formatDate(lastMonday, 'MM月dd日') + ' ~ ' + formatDate(lastSunday, 'MM月dd日'),
          start_date: formatDate(lastMonday, 'yyyy-MM-dd'),
          end_date: formatDate(lastSunday, 'yyyy-MM-dd'),
          type: 2,
          child_type: 2
        },
        {
          name: '上月',
          trendName: '上月',
          date: formatDate(firstDayOfLastMonth, 'MM月'),
          start_date: formatDate(firstDayOfLastMonth, 'yyyy-MM-dd'),
          end_date: formatDate(lastDayOfLastMonth, 'yyyy-MM-dd'),
          type: 2,
          child_type: 3
        }
      ],
      cardList: [
        {
          label: '签约商家',
          code: 'merchant_count',
          value: 0,
          count: 0,
          percentage: 0,
          tender: 'none',
        },
        {
          label: '签约总数',
          code: 'all_subscription_count',
          value: 0,
          count: 0,
          percentage: 0,
          tender: 'none',
        },
        {
          label: '合约履约数',
          code: 'exercise_subscription_count',
          value: 0,
          count: 0,
          percentage: 0,
          tender: 'none',
        },
        {
          label: '扣款失败订单总数',
          code: 'pay_failed_subscription_count',
          value: 0,
          count: 0,
          percentage: 0,
          tender: 'none',
        },
        {
          label: '合约终止数',
          code: 'abort_subscription_count',
          value: 0,
          count: 0,
          percentage: 0,
          tender: 'none',
        },
        {
          label: '签约总金额',
          code: 'all_subscription_money',
          value: 0,
          count: 0,
          percentage: 0,
          tender: 'none',
        },
        {
          label: '已扣款金额(￥)',
          code: 'pay_subscription_money',
          value: 0,
          count: 0,
          percentage: 0,
          tender: 'none',
        },
        {
          label: '未扣款总金额(￥)',
          code: 'no_pay_subscription_money',
          value: 0,
          count: 0,
          percentage: 0,
          tender: 'none',
        },
        {
          label: '解约总金额(￥)',
          code: 'surrender_subscription_money',
          value: 0,
          count: 0,
          percentage: 0,
          tender: 'none',
        },
        {
          label: '手续费(￥)',
          code: 'transaction_subscription_money',
          value: 0,
          count: 0,
          percentage: 0,
          tender: 'none',
        }
      ],
      loadingChart: false,
      chartList: [
        {
          label: '签约商家增长趋势',
          code: 'merchant_count',
          options: {}
        },
        {
          label: '签约总数增长趋势',
          code: 'all_subscription_count',
          options: {}
        },
        {
          label: '合约履约数增长趋势',
          code: 'exercise_subscription_count',
          options: {}
        },
        {
          label: '扣款失败订单数增长趋势',
          code: 'pay_failed_subscription_count',
          options: {}
        },
        {
          label: '合约终止数增长趋势',
          code: 'abort_subscription_count',
          options: {}
        },
        {
          label: '签约总金额增长趋势',
          code: 'all_subscription_money',
          options: {}
        },
        {
          label: '已扣款总金额增长趋势',
          code: 'pay_subscription_money',
          options: {}
        },
        {
          label: '未扣款总金额增长趋势',
          code: 'no_pay_subscription_money',
          options: {}
        },
        {
          label: '解约总金额增长趋势',
          code: 'surrender_subscription_money',
          options: {}
        },
        {
          label: '手续费增长趋势',
          code: 'transaction_subscription_money',
          options: {}
        }
      ]
    }
  },
  methods: {
    handleTabChange() {
      const tab = this.tabList.find(item => item.name === this.tabName)
      this.trendName = tab.trendName
      this.getCount()
    },
    getCount() {
      const tab = this.tabList.find(item => item.name === this.tabName)
      return this.$service.post('/Web/ZhimaFitPay/getStatistics', {
        type: tab.type, // 1实时（今日本周本月本年度） 2扎帐数据（昨日上周上月上年度）
        start_date: tab.start_date,
        end_date: tab.end_date,
        child_type: tab.child_type, // 1每日统计 2每周统计 3每月统计 4每年统计
      }).then(({ data }) => {
        if (data.errorcode === 0) {
          this.cardList.forEach(card => {
            card.value = data.data[card.code] || 0
            card.count = Number(data.data[card.code + '_rate'].up_count) || 0
            if (card.count > 0) {
              card.tender = 'up'
              card.percentage = Number(data.data[card.code + '_rate'].chain) || 0
            } else if (card.count === 0) {
              card.tender = 'none'
              card.percentage = 0
            } else {
              card.tender = 'down'
              card.percentage = Number(data.data[card.code + '_rate'].chain) || 0
            }
          })
        } else {
          this.$message.error(data.errormsg)
        }
      })
    },
    getChart() {
      this.loadingChart = true
      return this.$service.get('/Web/ZhimaFitPay/getChartLists').then(({ data }) => {
        if (data.errorcode === 0) {
          this.chartList.forEach(chart => {
            chart.options = {
              ...chartOptions,
              series: [
                {
                  ...chartData,
                  data: data.data[chart.code]
                }
              ]
            }
          })
          this.loadingChart = false
        } else {
          this.$message.error(data.errormsg)
        }
      })
    }
  },
  created() {
    this.getCount()
    this.getChart()
  }
}
</script>

<style lang="less" scoped>
.box {
  margin: 20px;

  .card-box,
  .chart-box {
    display: flex;
    flex-wrap: wrap;
    flex-direction: row;

    .value {
      font-size: 40px;
      font-weight: bold;
      text-align: center;
    }

    .desc {
      font-size: 12px;
      text-align: center;
    }
  }
}
</style>