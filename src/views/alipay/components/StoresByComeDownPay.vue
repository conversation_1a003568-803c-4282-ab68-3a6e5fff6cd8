<template>
  <div class="come-down-box">
    <header>
      <BusSearchSelect v-model="postData.store_no" />
      <el-input placeholder="商户名称" v-model="postData.merchant_name" @keyup.enter.native="search" class="search-item"
        clearable></el-input>
      <el-button type="success" @click="search">搜索</el-button>
    </header>
    <main>
      <el-table :data="tableData" stripe>
        <el-table-column prop="store_title" label="场馆名称" align="center" />
        <el-table-column prop="store_no" label="场馆 ID" align="center" />
        <el-table-column prop="merchant_name" label="商户名称" align="center" />
        <el-table-column prop="create_time" label="创建时间" align="center" />
        <el-table-column prop="comment" label="备注" align="center">
          <template scope="scope">
            <span v-if="!scope.row.comment_short">{{ scope.row.comment || '-' }}</span>
            <el-tooltip v-else :content="scope.row.comment" raw-content>
              <span>{{ scope.row.comment_short }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template scope="scope">
            <el-button type="primary" size="mini" style="margin: 0" @click="handleEdit(scope.row)">
              编辑
            </el-button>
          </template>
        </el-table-column>
      </el-table>
    </main>
    <footer>
      <el-button type="success" size="small" @click="handleAdd"> 添加 </el-button>
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="postData.page_no" :page-sizes="[10, 20, 30, 40]" :page-size="postData.page_size"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </footer>

    <el-dialog title="门店信息" :visible.sync="dialogVisible" width="min(40%, 600px)" :close-on-click-modal="false"
      :close-on-press-escape="false" :show-close="false">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="100px">
        <el-form-item label="SaaS 场馆" prop="store_no">
          <BusSearchSelect v-model="formData.store_no" :isEdit="formData.store_no ? true : false"
            @on-change="handleStoreChange" style="width: 100%" />
        </el-form-item>
        <el-form-item label="商户名称" prop="open_merchant_id">
          <el-select v-model="formData.open_merchant_id" filterable placeholder="请选择" style="width: 100%">
            <el-option v-for="item in merchantList" :key="item.id" :label="item.merchant_name"
              :value="item.open_merchant_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" prop="comment">
          <el-input type="textarea" v-model="formData.comment" :autosize="{ minRows: 4, maxRows: 10 }" maxlength="500"
            show-word-limit></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleSave" :disabled="submitting">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { Message } from 'element-ui';
import BusSearchSelect from './BusSearchSelect.vue';
import _ from 'lodash';

const NONE_FORM_DATA = {
  id: '',
  open_merchant_id: null,
  store_no: null,
  store_title: '',
  comment: '',
  merchant_type: 2
}

export default {
  name: 'StoresByComeDownPay',
  components: {
    BusSearchSelect
  },
  data() {
    return {
      merchantList: [],
      postData: {
        merchant_name: '',
        store_no: '',
        page_size: 10,
        page_no: 1,
        merchant_type: 2
      },
      tableData: [],
      total: 0,
      dialogVisible: false,
      formData: _.cloneDeep(NONE_FORM_DATA),
      formRules: {
        store_no: [
          { required: true, message: '请选择场馆', trigger: 'blur' },
        ],
        open_merchant_id: [
          { required: true, message: '请选择商户', trigger: 'blur' },
        ],
      },
      submitting: false
    }
  },
  methods: {
    search() {
      this.postData.page_no = 1;
      this.getList();
    },
    getList() {
      this.$service.post('Web/ZhimaFitPay/stores', this.postData).then(res => {
        if (res.data.errorcode === 0) {
          const list = res.data.data.list;
          list.forEach(item => {
            const comment_short = String(item.comment || '').substring(0, 10);
            if (String(item.comment || '').length > 10) {
              item.comment_short = comment_short + '...';
            }
          })

          this.tableData = list;
          this.total = res.data.data.count;
        } else {
          Message.error(res.data.errormsg);
        }
      })
    },
    handleSizeChange(val) {
      this.postData.page_no = 1;
      this.postData.page_size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.postData.page_no = val;
      this.getList();
    },
    getMerchantList() {
      this.$service.post('/Web/ZhimaFitPay/merchants', {
        _no_paginate: true,
        merchant_type: 2
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.merchantList = res.data.data;
        } else {
          Message.error(res.data.errormsg);
        }
      }).catch(e => {
        throw new Error(e)
      })
    },
    handleAdd() {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.formRef.clearValidate();
      })
    },
    handleEdit(row) {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.formData = {
          ...row,
          store_no: Number(row.store_no),
        }
      })
    },
    handleStoreChange(row) {
      this.formData.store_title = row.name;
    },
    handleSave: _.throttle(function () {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.submitting = true
          this.$service.post('Web/ZhimaFitPay/bindStore', {
            ...this.formData
          }).then(res => {
            if (res.data.errorcode === 0) {
              this.dialogVisible = false
              Message.success(res.data.errormsg)
              this.getList();
            } else {
              Message.error(res.data.errormsg)
            }
          }).finally(() => {
            this.submitting = false
          })
        }
      })
    }, 3000),
    handleCancel() {
      this.formData = _.cloneDeep(NONE_FORM_DATA)
      this.dialogVisible = false
    }
  },
  created() {
    this.getList();
    this.getMerchantList();
  }
}
</script>

<style lang="less" scoped>
.come-down-box {
  padding: 20px 0;

  header {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 15px;
    padding: 0 20px;

    .search-item {
      width: 200px;
    }
  }

  main {
    margin-top: 20px;
    border-top: 1px solid #dddee1;
  }

  footer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 0 20px;
  }
}
</style>