<template>
  <el-dialog
    ref="info"
    title="基于模板上传版本"
    :visible.sync="showDialog"
    width="45%"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <el-form ref="form" label-width="100px" :model="infoData" class="info-dialog-form">
      <el-form-item label="小程序选择">
        <el-radio-group v-model="infoData.auth_app_type" @change="appTypeChange">
          <el-radio :label="2">部分</el-radio>
          <el-radio :label="1">全部</el-radio>
        </el-radio-group>
        <div v-if="infoData.auth_app_type == 2">
        <AppidSelect v-model="infoData.auth_app_ids" :multiple="true" />
        </div>
      </el-form-item>
      <el-form-item label="模板" prop="template_version">
        <el-select v-model="infoData.template_version" placeholder="不选默认为线上最新版本"  class="w-200" @change="templateChange">
          <el-option v-for="item in versionList" :label="item.app_version" :value="item.app_version" :key="item.app_version"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="小程序版本" prop="app_version":rules="{ required: true, message: '请填写代码版本号'}">
        <el-input v-model="infoData.app_version" placeholder="x.y.z" class="h-40 w-200"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input type="textarea" :rows="5" v-model="infoData.remark" class="w-200">
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button style="margin-right:20px;" @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import AppidSelect from './AppidSelect'
export default {
  name: 'UploadVersion',
  components: {
    AppidSelect
  },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      versionList: [],
      infoData: {
        auth_app_type: 2,
        auth_app_ids: [],
        template_version: '',
        app_version: '',
        ext: '',
        remark: ''
      }
    }
  },
  watch: {
    showDialog(val) {
      if(!val) {
        this.infoData = {
          auth_app_type: 2,
          auth_app_ids: [],
          template_version: '',
          app_version: '',
          ext: '',
          remark: ''
        }
      }
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  methods: {
    appTypeChange(value) {
      if(value == 1) {
        this.infoData.auth_app_ids = [];
      } 
    },
    templateChange(value) {
      this.infoData.app_version = value;
    },
    onDialogClose() {
      this.$emit('input', false)
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
    getVersionList() {
      this.$service.post('Web/AlipayIsv/getTemplateVersionList').then(res => {
        if (res.data.errorcode == 0) {
          const info = res.data.data.list
          this.versionList = info.app_version_infos;
          this.$nextTick(()=> {
            this.infoData.template_version = this.versionList[0].app_version;
            this.infoData.app_version = this.versionList[0].app_version;
          })
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    },
    handleConfirm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.$service.post('Web/AlipayIsv/uploadVersion', this.infoData).then(res => {
            if (res.data.errorcode == 0) {
              this.showDialog = false
              this.$emit('on-confirm')
              _g.toastMsg('success', res.data.errormsg);
            } else {
              _g.toastMsg('warning', res.data.errormsg);
            }
          });
        }
      });
    }
  },
  created () {
    this.getVersionList()
  }
}
</script>
<style lang="less" scoped>

</style>



