<template>
  <el-dialog
    ref="info"
    title="编辑"
    :visible.sync="showDialog"
    width="800px"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <el-form label-width="100px" class="info-dialog-form">
       <el-form-item label="蚂蚁门店id" prop="old_pwd">
         <el-input v-model="infoData.ali_shop_id" class="w-300"></el-input>
        </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button style="margin-right:20px;" @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      infoData: {
        product_no: '',
        ali_shop_id: '',
      }
    }
  },
  watch: {
    showDialog(val) {
      if(!val) {
        this.infoData = {
          product_no: '',
          ali_shop_id: '',
        }
      } else {
        if (this.info && typeof this.info === 'object') {
          this.infoData.product_no = this.info.product_no || ''
          this.infoData.ali_shop_id = this.info.ali_shop_id || ''
        }
      }
    },
    info: {
      handler(val) {
        if (val && typeof val === 'object') {
          this.infoData.product_no = this.info.product_no || ''
          this.infoData.ali_shop_id = this.info.ali_shop_id || ''
        }
      },
      immediate: true
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  methods: {
    onDialogClose() {
      this.$emit('input', false)
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
    handleConfirm() {
      this.$service.post('Web/ZhimaFitPay/editProductShopId', this.infoData).then(res => {
        if (res.data.errorcode == 0) {
          this.showDialog = false
          this.$emit('on-confirm')
           _g.toastMsg('success', '操作成功');
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    }
  },
  created () {
  }
}
</script>

<style lang="less" scoped>

</style>



