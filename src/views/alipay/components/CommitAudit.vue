<template>
  <el-dialog
    ref="info"
    title="提交审核"
    :visible.sync="showDialog"
    width="45%"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <el-form ref="form" label-width="120px" :model="infoData" class="info-dialog-form">
      <el-form-item label="小程序选择">
        <el-radio-group v-model="infoData.auth_app_type" @change="appTypeChange">
          <el-radio :label="2">部分</el-radio>
          <el-radio :label="1">全部</el-radio>
        </el-radio-group>
        <div v-if="infoData.auth_app_type == 2">
        <AppidSelect v-model="infoData.auth_app_ids" :multiple="true" />
        </div>
      </el-form-item>
     
      <el-form-item label="版本号" prop="app_version" :rules="{ required: true, message: '请选择版本号'}">
        <el-select v-model="infoData.app_version" class="w-200">
          <el-option v-for="item in versionList" :label="item.app_version" :value="item.app_version" :key="item.app_version"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="客服电话" prop="service_phone" :rules="{ required: true, message: '请填写客服电话'}">
        <el-input type="text" v-model="infoData.service_phone" class="w-200">
        </el-input>
      </el-form-item>
      <el-form-item label="版本介绍" prop="version_desc" :rules="[{ required: true, message: '请填写版本介绍'},{min:15, message: '至少15个中文字符'}]">
        <el-input type="textarea" :rows="3" v-model="infoData.version_desc" class="w-200">
        </el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input type="textarea" :rows="3" v-model="infoData.remark" class="w-200">
        </el-input>
      </el-form-item>
      <el-form-item v-if="infoData.auth_app_type == 2" label="通过后自动上架" prop="auto_online">
        <el-switch v-model="infoData.auto_online" active-value="2" inactive-value="1"></el-switch>
      </el-form-item>
      <el-form-item v-if="infoData.auth_app_type == 2" label="交易单号" prop="memo">
        <el-input type="textarea" :rows="3" v-model="infoData.memo" placeholder="非必填，特殊情况下验证JSAPI支付需要" class="w-200">
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button style="margin-right:20px;" @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import AppidSelect from './AppidSelect'
export default {
  name: 'CommitAudit',
  components: {
    AppidSelect
  },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      versionList: [],
      infoData: {
        auth_app_type: 2,
        auto_online: '1',
        auth_app_ids: [],
        app_version: '',
        service_email: '',
        memo: '',
        service_phone: '023-67397805',
        version_desc: '',
        remark: '',
        speed_up: true
      }
    }
  },
  watch: {
    showDialog(val) {
      if(!val) {
        this.infoData = {
          auth_app_type: 2,
          auto_online: '1',
          auth_app_ids: [],
          app_version: '',
          service_email: '',
          service_phone: '023-67397805',
          version_desc: '',
          remark: '',
          speed_up: true
        }
      }
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  created () {
    this.getVersionList()
  },
  methods: {
    appTypeChange(value) {
      if(value == 1) {
        this.infoData.auth_app_ids = [];
        this.infoData.memo = '';
      } 
    },
    onDialogClose() {
      this.$emit('input', false)
    },
    getVersionList() {
      this.$service.post('Web/AlipayIsv/getAuditReleaseVersionList', {
        type: 1
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.versionList = res.data.data.list;
          //此处不需要初始化版本信息和版本介绍
          // this.$nextTick(()=> {
          //   this.infoData.app_version = this.versionList[0].app_version;
          //   this.infoData.version_desc = this.versionList[0].remark;
          // })
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
    handleConfirm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.$service.post('Web/AlipayIsv/commitAudit', {
            ...this.infoData,
            auto_online: this.infoData.auth_app_type == 2 ? this.infoData.auto_online : '1', 
          }).then(res => {
            if (res.data.errorcode == 0) {
              this.showDialog = false
              this.$emit('on-confirm')
              _g.toastMsg('success', res.data.errormsg);
            } else {
              _g.toastMsg('warning', res.data.errormsg);
            }
          });
        }
      });
    }
  }
}
</script>
<style lang="less" scoped>

</style>



