<template>
  <div class="come-down-box">
    <header>
      <el-input placeholder="商户名称" v-model="postData.merchant_name" @keyup.enter.native="search" class="search-item"
        clearable></el-input>
      <!-- <el-input placeholder="场馆名称" v-model="postData.store_no" @keyup.enter.native="search" class="search-item"
        clearable></el-input> -->
      <BusSearchSelect v-model="postData.store_no" />
      <el-input placeholder="产品名称" v-model="postData.product_title" @keyup.enter.native="search" class="search-item"
        clearable></el-input>
      <el-input placeholder="卡课名称" v-model="postData.card_name" @keyup.enter.native="search" class="search-item"
        clearable></el-input>
      <el-select placeholder="审批状态" v-model="postData.approve_status" class="search-item" clearable>
        <el-option v-for="item in approveStatusList" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
      <el-button type="success" @click="search">搜索</el-button>
    </header>
    <main>
      <el-table :data="tableData" stripe>
        <el-table-column prop="merchant_name" label="商户名称" align="center" />
        <el-table-column prop="store_title" label="场馆名称" align="center" />
        <el-table-column prop="product_title" label="产品名称" align="center" />
        <el-table-column prop="card_name" label="卡课名称" align="center" />
        <el-table-column prop="periods" label="期数" align="center" />
        <el-table-column prop="down_payment" label="首期服务费" align="center" />
        <el-table-column prop="deduction_amount" label="单月服务费" align="center" />
        <el-table-column prop="sell_type" label="类型" align="center" />
        <el-table-column prop="status_label" label="状态" align="center" />
        <el-table-column prop="approve_status_label" label="审批状态" align="center" />
        <el-table-column prop="ali_shop_id" label="蚂蚁门店id" align="center" show-overflow-tooltip>
          <template scope="scope">
            <el-button type="text" size="mini" style="margin: 0" @click="handleEditAliId(scope.row)">{{scope.row.ali_shop_id ? scope.row.ali_shop_id : '编辑'}}</el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template scope="scope">
            <div class="btn-box">
              <el-button
                v-if="scope.row.status == 1 && (scope.row.approve_status == 0 || scope.row.approve_status == 2)"
                type="success" size="mini" style="margin: 0" @click="handleAction(scope.row, 1)">上架</el-button>
              <el-button v-if="scope.row.status == 1 && scope.row.approve_status == 0" type="warning" size="mini"
                style="margin: 0" @click="handleAction(scope.row, 2)">下架</el-button>
              <el-button v-if="scope.row.status == 1 && scope.row.approve_status == 1" type="danger" size="mini"
                style="margin: 0" @click="handleAction(scope.row, 3)">不通过</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </main>
    <footer>
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="postData.page_no" :page-sizes="[10, 20, 30, 40]" :page-size="postData.page_size"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </footer>
    <AliIdEdit v-model="showAliDialog" :info="curInfo" />
  </div>
</template>

<script>
import { Message } from 'element-ui';
import _ from 'lodash';
import AliIdEdit from './AliIdEdit.vue';
import BusSearchSelect from './BusSearchSelect.vue'

export default {
  name: 'ProductsByComeDownPay',
  components: {
    AliIdEdit,
    BusSearchSelect
  },
  data() {
    return {
      approveStatusList: [
        {
          value: 0,
          label: '待审批'
        },
        {
          value: 1,
          label: '上架'
        },
        {
          value: 2,
          label: '下架'
        },
        {
          value: 3,
          label: '未通过'
        }
      ],
      postData: {
        merchant_name: '',
        store_no: '',
        product_title: '',
        card_name: '',
        approve_status: '',
        page_size: 10,
        page_no: 1,
        merchant_type: 2
      },
      showAliDialog: false,
      curInfo: {},
      tableData: [],
      total: 0,
    }
  },
  methods: {
    search() {
      this.postData.page_no = 1;
      this.getList();
    },
    handleEditAliId(info) {
      this.curInfo = info;
      this.showAliDialog = true;
    },
    getList() {
      this.$service.post('Web/ZhimaFitPay/products', this.postData).then(res => {
        if (res.data.errorcode === 0) {
          const list = res.data.data.list;
          list.forEach(item => {
            const comment_short = String(item.comment || '').substring(0, 10);
            if (String(item.comment || '').length > 10) {
              item.comment_short = comment_short + '...';
            }

            const approveItem = this.approveStatusList.find(status => status.value === item.approve_status);
            if (approveItem) {
              item.approve_status_label = approveItem.label;
            } else {
              item.approve_status_label = '-';
            }

            const statusItem = '-';
            if (item.status === 1) {
              item.status_label = '启用';
            } else {
              item.status_label = '禁用';
            }

            item.sell_type = '连续包月';
          })

          this.tableData = list;
          this.total = res.data.data.count;
        } else {
          Message.error(res.data.errormsg);
        }
      })
    },
    handleSizeChange(val) {
      this.postData.page_no = 1;
      this.postData.page_size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.postData.page_no = val;
      this.getList();
    },
    handleAction: _.throttle(function (info, status) {
      //status 1—上架，2—下架， 3—未通过
      this.$confirm(`确定${status == 2 ? '下架' : status == 3 ? '未通过' : '上架'}?`, '提示', {
        type: 'warning'
      }).then(() => {
        this.$service
          .post('/Web/ZhimaFitPay/productSetApproveStatus', {
            open_merchant_id: info.open_merchant_id,
            store_no: info.store_no,
            product_no: info.product_no,
            approve_status: status,
            merchant_type: 2
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$message.success(res.data.errormsg);
              this.getList();
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
      }).catch(() => {
      })
    }, 3000)
  },
  created() {
    this.getList();
  }
}
</script>

<style lang="less" scoped>
.come-down-box {
  padding: 20px 0;

  header {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 15px;
    padding: 0 20px;

    .search-item {
      width: 200px;
    }
  }

  main {
    margin-top: 20px;
    border-top: 1px solid #dddee1;

    .btn-box {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      gap: 10px;
      flex-wrap: nowrap;
    }
  }

  footer {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    margin-top: 20px;
    padding: 0 20px;
  }
}
</style>