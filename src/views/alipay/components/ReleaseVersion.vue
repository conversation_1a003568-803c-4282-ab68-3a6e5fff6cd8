<template>
  <el-dialog
    ref="info"
    :title="action==4?'退回开发':(action==2?'下架':action==3?'回滚':'发布')+'版本'"
    :visible.sync="showDialog"
    width="45%"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <el-form ref="form" label-width="120px" :model="infoData" class="info-dialog-form">
      <el-form-item label="小程序选择">
        <el-radio-group v-model="infoData.auth_app_type" @change="appTypeChange">
          <el-radio :label="2">部分</el-radio>
          <el-radio :label="1" v-if="action != 2">全部</el-radio>
        </el-radio-group>
        <div v-if="infoData.auth_app_type == 2">
        <AppidSelect v-model="infoData.auth_app_ids" :multiple="true" />
        </div>
      </el-form-item>
      <el-form-item label="版本号" prop="app_version" :rules="{ required: true, message: '请选择版本号'}">
        <el-select v-model="infoData.app_version" class="w-200">
          <el-option v-for="item in versionList" :label="item.app_version" :value="item.app_version" :key="item.app_version"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="action == 1 && infoData.auth_app_type == 2" label="未备案受限上架" prop="permit_registration_limit_release">
        <el-switch v-model="infoData.permit_registration_limit_release" active-value="2" inactive-value="1"></el-switch>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button style="margin-right:20px;" @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import AppidSelect from './AppidSelect'
export default {
  name: 'ReleaseVersion',
  components: {
    AppidSelect
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    action: {
      type: Number
    },
    info: {
      type: Object
    }
  },
  data() {
    return {
      versionList: [],
      infoData: {
        action: 1,
        permit_registration_limit_release: '1',
        auth_app_type: 2,
        auth_app_ids: [],
        app_version: ''
      }
    }
  },
  watch: {
    showDialog(val) {
      if(!val) {
        this.infoData = {
          action: 1,
          auth_app_type: 2,
          auth_app_ids: [],
          app_version: ''
        }
      }
    },
    info: {
      handler(info) {
        if(info && info.auth_app_id) {
          this.infoData.auth_app_type = 2
          this.infoData.auth_app_ids = [info.auth_app_id]
          this.infoData.app_version = info.app_version
        }
      },
      immediate: true
    },
    action: {
      handler(val) {
        this.infoData.action = val || 1 //1上架2下架3回滚 缺省值1
      },
      immediate: true
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  created () {
    this.getVersionList()
  },
  methods: {
    getVersionList() {
      this.$service.post('Web/AlipayIsv/getAuditReleaseVersionList', {
        type: 2
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.versionList = res.data.data.list;
          this.$nextTick(()=> {
            this.infoData.app_version = this.versionList[0].app_version;
          })
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    },
    appTypeChange(value) {
      if(value == 1) {
        this.infoData.auth_app_ids = [];
      } 
    },
    onDialogClose() {
      this.$emit('input', false)
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
    handleConfirm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.$service.post(this.action==4?'Web/AlipayIsv/rollbackDev':'Web/AlipayIsv/releaseUpDown', {
            ...this.infoData,
            permit_registration_limit_release: this.infoData.auth_app_type == 2 ? this.infoData.permit_registration_limit_release : '1'
          }).then(res => {
            if (res.data.errorcode == 0) {
              this.showDialog = false
              this.$emit('on-confirm')
              _g.toastMsg('success', res.data.errormsg);
            } else {
              _g.toastMsg('warning', res.data.errormsg);
            }
          });
        }
      });
    }
  }
}
</script>
<style lang="less" scoped>

</style>



