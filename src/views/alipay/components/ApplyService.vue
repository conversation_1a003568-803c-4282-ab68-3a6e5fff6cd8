<template>
  <el-dialog
    ref="info"
    title="服务提报"
    :visible.sync="showDialog"
    width="600px"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <el-form ref="form" label-width="100px" :model="infoData" class="info-dialog-form">
      <el-form-item label="小程序选择">
        <el-radio-group v-model="infoData.auth_app_type" @change="appTypeChange">
          <el-radio :label="2">部分</el-radio>
          <el-radio :label="1">全部</el-radio>
        </el-radio-group>
        <div v-if="infoData.auth_app_type == 2">
        <AppidSelect v-model="infoData.auth_app_ids" :multiple="true" />
        </div>
      </el-form-item>
      <el-form-item label="服务名称" prop="template_version">
        <el-select v-model="infoData.id" class="w-200">
          <el-option v-for="item in versionList" :label="item.service_name" :value="item.id" :key="item.id"></el-option>
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button style="margin-right:20px;" @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import AppidSelect from './AppidSelect'
export default {
  name: 'ApplyService',
  components: {
    AppidSelect
  },
  props: {
    value: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      versionList: [],
      infoData: {
        auth_app_type: 2,
        auth_app_ids: [],
        id: ''
      }
    }
  },
  watch: {
    showDialog(val) {
      if(!val) {
        this.infoData = {
          auth_app_type: 2,
          auth_app_ids: [],
          id: ''
        }
      }
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  methods: {
    appTypeChange(value) {
      if(value == 1) {
        this.infoData.auth_app_ids = [];
      } 
    },
    onDialogClose() {
      this.$emit('input', false)
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
    getVersionList() {
      this.$service.post('Web/AlipayIsv/getSchemaCategoryTemplate', {
        select: true
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.versionList = res.data.data.list;
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    },
    handleConfirm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.$service.post('Web/AlipayIsv/serviceSchemaApply', this.infoData).then(res => {
            if (res.data.errorcode == 0) {
              this.showDialog = false
              this.$emit('on-confirm')
              _g.toastMsg('success', res.data.errormsg);
            } else {
              _g.toastMsg('warning', res.data.errormsg);
            }
          });
        }
      });
    }
  },
  created () {
    this.getVersionList()
  }
}
</script>
<style lang="less" scoped>

</style>



