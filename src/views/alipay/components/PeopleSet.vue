<template>
  <el-dialog
    ref="info"
    title="人员"
    :visible.sync="showDialog"
    width="800px"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <el-card class="box-card">
      <el-table :data="list" stripe style="width: 100%">
        <el-table-column prop="logon_id" label="账号" align="center"></el-table-column>
        <el-table-column prop="role" label="角色" align="center">
          <template scope="scope">
            <div>{{roleDesObj[scope.row.role] || scope.row.role}}</div>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <div slot="footer"></div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      roleDesObj: {
        EXPERIENCER: '体验者',
        DEVELOPER: '开发者'
      },
      list: [],
    }
  },
  watch: {
    showDialog(val) {
      if (!val) {
        this.list = []
      }
    },
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  created() {
    this.list = this.info.experience_json
  },
  methods: {
    onDialogClose() {
      this.$emit('input', false)
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
  }
}
</script>



