<template>
  <el-dialog
    ref="info"
    title="关联场馆"
    :visible.sync="showDialog"
    width="45%"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <el-form label-width="100px" class="info-dialog-form">
      <el-form-item label="APPID" prop="old_pwd">
         {{infoData.auth_app_id}}
        </el-form-item>
       <el-form-item label="名称" prop="old_pwd">
         {{infoData.app_name}}
        </el-form-item>
        <el-form-item label="场馆选择" prop="bus_id" v-if="showDialog">
            <BusSearchSelect :isEdit="infoData.bus_id?true:false" v-model="infoData.bus_id" />
        </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button style="margin-right:20px;" @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import BusSearchSelect from './BusSearchSelect.vue'
export default {
  name: 'BusSet',
  components: {
    BusSearchSelect
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      infoData: {
        bus_id: '',
        auth_app_id: '',
        app_name: '',
        bus_name: ''
      }
    }
  },
  watch: {
    showDialog(val) {
      if(!val) {
        this.infoData = {
          bus_id: '',
          auth_app_id: '',
          app_name: '',
          bus_name: ''
        }
      }
    },
    info(val) {
      if (val && typeof val === 'object'){
        Object.keys(this.infoData).forEach((key)=>{
          this.infoData[key] = this.info[key]
        })
      }
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  methods: {
    onDialogClose() {
      this.$emit('input', false)
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
    handleConfirm() {
      this.$service.post('Web/AlipayIsv/setBusInfo', this.infoData).then(res => {
        if (res.data.errorcode == 0) {
          this.showDialog = false
          this.$emit('on-confirm')
           _g.toastMsg('success', '修改成功');
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    }
  },
  created () {
    if(this.info.auth_app_id) {
      Object.keys(this.infoData).forEach((key)=>{
        this.infoData[key] = this.info[key]
      })
    }
  }
}
</script>

<style lang="less" scoped>

</style>



