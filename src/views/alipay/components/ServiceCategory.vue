<template>
  <el-select
    v-model="adString"
    class="filter-item"
    :placeholder="placeholder"
    :filterable="filterable"
    :clearable="clearable"
    :disabled="disabled"
    @change="adChange"
  >
    <slot />
    <el-option
      v-for="item in adOptions"
      :key="item.id"
      :label="item.name"
      :value="item.id"
    />
  </el-select>
</template>

<script>
export default {
  name: 'ServiceCategory',
  props: {
    value: {
      required: true
    },
    placeholder: {
      type: String,
      default: '请选择类型'
    },
    filterable: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    }
  },
  data() {
    return {
      adOptions: [{
        id: 'C000003546',
        name: '休闲娱乐/健身房/瑜伽/舞蹈/在线预约'
      },{
        id: 'C000003547',
        name: '休闲娱乐/健身房/瑜伽/舞蹈/在线订购'
      },{
        id: 'C000003548',
        name: '休闲娱乐/健身房/瑜伽/舞蹈/预约私教'
      }]
    }
  },
  computed: {
    adString: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
  },
  created() {
  },
  methods: {
    adChange(id) {
      let info = ''
        this.adOptions.forEach((item, index) => {
          if (item.id === this.adString) {
            info = item
          }
        })
        this.$emit('on-change', info)
    }
  }
}
</script>
