<template>
  <el-dialog
    ref="info"
    title="域名白名单"
    :visible.sync="showDialog"
    width="45%"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{info.app_name}}域名列表</span>
        <el-button style="float: right; padding: 3px 0;margin-left: 30px;" @click="addService" type="text">添加</el-button>
        <el-button v-if="info.auth_app_id!=='2021003117609651'" style="float: right; padding: 3px 0;color:red" @click="handleSave(3)" type="text">重新初始化</el-button>
      </div>
       <el-table :data="list" stripe style="width: 100%">
        <el-table-column prop="name" label="域名"align="center">
          <template scope="scope">
            <div v-if="scope.row.action!=='add'">{{scope.row.name}}</div>
            <el-input v-else v-model="list[scope.$index].name" placeholder="请输入" />
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template scope="scope">
            <el-button v-if="scope.row.action!=='add'" style="margin-right:20px;color:red" type="text" @click="handleSave(2, scope.row.name, scope.$index)">删除</el-button>
            <el-button v-else style="margin-right:20px;" type="text" @click="handleSave(1, list[scope.$index].name, scope.$index)">保存</el-button>
          </template>
        </el-table-column>
        </el-table>
    </el-card>
    <div slot="footer">
      
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'DomainSet',
  components: {
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      list: []
    }
  },
  watch: {
    showDialog(val) {
      if(!val) {
        this.list = []
      }
    },
    'info.safe_domain_list': {
      handler(val) {
        if(val) {
          this.list = val.split(',').map((item)=>{
            return {
              name: item
            }
          })
        } 
      },
      immediate: true
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  methods: {
    addService() {
      this.list.splice(0, 0, {name: '', action: 'add'});
    },
    onDialogClose() {
      this.$emit('input', false)
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
    handleSave(action, name, index) {
      this.$service.post('Web/AlipayIsv/setSafeDomain', {
        action: action,
        auth_app_id: this.info.auth_app_id,
        safe_domain: action==3?'':name
      }).then(res => {
        if (res.data.errorcode == 0) {
          if(action == 1) {
            this.list[index].action = ''
          }
          if(action == 2) {
            this.list.splice(index, 1);
          }
          this.$emit('on-success')
          _g.toastMsg('success', res.data.errormsg);
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    },
    handleConfirm() {
      this.showDialog = false
      this.$emit('on-confirm')
    }
  }
}
</script>

<style lang="less" scoped>

</style>



