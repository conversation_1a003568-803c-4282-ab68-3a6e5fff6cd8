<template>
  <el-dialog
    ref="info"
    title="门店信息"
    :visible.sync="showDialog"
    width="600px"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <el-form ref="form" label-width="100px" :model="infoData" class="info-dialog-form">
      <el-form-item label="SaaS场馆" prop="merchant_name">
        <BusSearchSelect @on-change="busChange" :isEdit="infoData.store_no?true:false"  v-model="infoData.store_no" class="w-300" />
      </el-form-item>
      <el-form-item label="商户名称" prop="phone">
        <el-select v-model="infoData.open_merchant_id" class="w-300">
          <el-option v-for="item in merchantsList" :label="item.merchant_name" :value="item.open_merchant_id" :key="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注" prop="comment">
        <el-input type="textarea" :rows="3" v-model="infoData.comment" class="w-300">
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button style="margin-right:20px;" @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import BusSearchSelect from './BusSearchSelect.vue'
export default {
  name: 'EditStore',
  components: {
    BusSearchSelect
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    action: {
      type: Number
    },
    info: {
      type: Object
    }
  },
  data() {
    return {
      merchantsList: [],
      infoData: {
        id: '',
        open_merchant_id: '',
        store_no: '',
        store_title: '',
        comment: ''
      }
    }
  },
  watch: {
    showDialog(val) {
      if(!val) {
        this.infoData = this.$options.data().infoData
      }
    },
    info: {
      handler(info) {
        if(info && info.open_merchant_id) {
          this.infoData.id =  info.id || ''
          this.infoData.open_merchant_id =  info.open_merchant_id || ''
          this.infoData.comment =  info.comment || ''
          this.infoData.store_title =  info.store_title || ''
          this.infoData.store_no =  +info.store_no
        }
      },
      immediate: true
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  created () {
    this.getMerchantsList()
  },
  methods: {
      busChange(info) {
        this.infoData.store_title = info.name
      },
     getMerchantsList() {
      this.$service.post('Web/ZhimaFitPay/merchants', {
        _no_paginate: true
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.merchantsList = res.data.data;
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    },
    onDialogClose() {
      this.$emit('input', false)
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
    handleConfirm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.$service.post('Web/ZhimaFitPay/bindStore', this.infoData).then(res => {
            if (res.data.errorcode == 0) {
              this.showDialog = false
              this.$emit('on-confirm')
              _g.toastMsg('success', '操作成功');
            } else {
              _g.toastMsg('warning', res.data.errormsg);
            }
          });
        }
      });
    }
  }
}
</script>
<style lang="less" scoped>

</style>



