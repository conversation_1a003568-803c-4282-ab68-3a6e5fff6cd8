<template>
  <div class="box">
    <el-row class="box-head">
      <el-col :offset="1" :span="22" class="head-option">
        <el-input v-model="postData.merchant_name" class="option-input" placeholder="请输入商家名称..."
          @keyup.enter.native="handleSearch" clearable></el-input>
        <el-input v-model="postData.app_name" class="option-input" placeholder="请输入支付宝小程序名称..."
          @keyup.enter.native="handleSearch" clearable></el-input>
        <el-select v-model="postData.sale_id" class="option-select" placeholder="请选择签约销售..." clearable>
          <el-option v-for="sale in saleList" :value="sale.id" :label="sale.sale_name" :key="sale.id"></el-option>
        </el-select>
        <el-date-picker class="option-select" v-model="signDateRange" type="daterange" range-separator="至"
          start-placeholder="签约" end-placeholder="时间">
        </el-date-picker>
        <el-date-picker class="option-select" v-model="deadlineDateRange" type="daterange" range-separator="至"
          start-placeholder="到期" end-placeholder="时间">
        </el-date-picker>
        <el-button type="success" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-col>
    </el-row>
    <el-row class="box-body table-wrap">
      <el-col :span="24">
        <el-table ref="table" stripe :data="list" @sort-change="onSortChange">
          <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
          <el-table-column label="商家名称" prop="merchant_name"></el-table-column>
          <el-table-column label="支付宝小程序名称" prop="app_name"></el-table-column>
          <el-table-column label="签约销售" prop="sale_name"></el-table-column>
          <el-table-column label="签约金额" prop="contract_money" sortable="custom"></el-table-column>
          <el-table-column label="签约时间" prop="sign_time"></el-table-column>
          <el-table-column label="到期时间" prop="end_time"></el-table-column>
          <el-table-column label="支付宝成本费率" prop="alipay_cost_rate">
            <template slot-scope="scope">
              {{ scope.row.alipay_cost_rate }}%
            </template>
          </el-table-column>
          <el-table-column label="实际签约费率" prop="signed_rate">
            <template slot-scope="scope">
              {{ scope.row.signed_rate }}%
            </template>
          </el-table-column>
          <el-table-column label="毛利费率" prop="profit_rate">
            <template slot-scope="scope">
              {{ scope.row.profit_rate }}%
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="handleEdit(scope.row)">编辑</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-row class="box-foot">
      <el-col :offset="1" :span="22" class="foot-option">
        <div class="option-ctrl">
          <!-- <el-button @click="handleExcel">导出Excel</el-button> -->
        </div>
        <el-pagination @current-change="handleCurrentChange" @size-change="handleSizeChange"
          layout="prev, pager, next, total, sizes, jumper" :page-size="postData.page_size"
          :current-page="postData.page_no" :total="total">
        </el-pagination>
      </el-col>
    </el-row>

    <el-dialog title="编辑" :visible.sync="dialogVisible" width="40%" :before-close="handleClose">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="160px" size="mini" label-position="left">
        <el-form-item label="商家名称">
          {{ formData.merchant_name }}
        </el-form-item>
        <el-form-item label="签约销售">
          {{ formData.sale_name }}
        </el-form-item>
        <el-form-item label="是否开放月付组合方案">
          <el-switch v-model="formData.product_combine_open" :active-value="2" :inactive-value="1"></el-switch>
        </el-form-item>
        <el-form-item label="支付宝小程序ID">
          <el-input v-model="formData.appid" disabled></el-input>
          <div style="font-size: 12px; margin-left: 15px">{{ formData.app_name }}</div>
        </el-form-item>
        <el-form-item label="签约金额" prop="contract_money">
          <el-input-number v-model="formData.contract_money" :min="0" :precision="2" :step="0.01"
            controls-position="right"></el-input-number>
        </el-form-item>
        <el-form-item label="签约时间" prop="sign_time">
          <el-date-picker v-model="formData.sign_time" type="date" placeholder="选择日期"
            :clearable="false"></el-date-picker>
        </el-form-item>
        <el-form-item label="到期时间" prop="end_time">
          <el-date-picker v-model="formData.end_time" type="date" placeholder="选择日期"
            :clearable="false"></el-date-picker>
        </el-form-item>
        <el-form-item label="支付宝成本费率" prop="alipay_cost_rate">
          <el-input-number v-model="formData.alipay_cost_rate" :min="0" :max="100" :precision="2" step-strictly
            :step="0.01" controls-position="right"></el-input-number>
          <span class="tips">%</span>
        </el-form-item>
        <el-form-item label="实际签约费率" prop="signed_rate">
          <el-input-number v-model="formData.signed_rate" :min="0" :max="100" :precision="2" step-strictly :step="0.01"
            controls-position="right"></el-input-number>
          <span class="tips">%</span>
        </el-form-item>
        <el-form-item label="毛利费率">
          {{ grossProfit }}%
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSave">确认</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import Big from 'big.js'
import { formatDate } from '@/utils'

const NONE_POST_DATA = {
  merchant_name: '',
  sale_id: '',
  sign_time_start: '',
  sign_time_end: '',
  end_time_start: '',
  end_time_end: '',
  app_name: '',
  sort: {
    name: '',
    type: ''
  },
  page_no: 1,
  page_size: 50
}

export default {
  name: 'AlipayRates',
  data() {
    return {
      postData: {
        ...NONE_POST_DATA
      },
      total: 0,
      list: [],
      saleList: [],
      dialogVisible: false,
      formData: {
        open_merchant_id: '',
        merchant_name: '',
        sale_id: '',
        sale_name: '',
        alipay_cost_rate: '',
        signed_rate: '',
        profit_rate: '',
        contract_money: '',
        sign_time: '',
        end_time: '',
        product_combine_open: 1,
        appid: '',
        app_name: ''
      },
      formRules: {
        contract_money: [
          { required: true, message: '请输入签约金额', trigger: 'blur' },
        ],
        sign_time: [
          { required: true, message: '请选择签约时间', trigger: 'change' }
        ],
        end_time: [
          { required: true, message: '请选择到期时间', trigger: 'change' }
        ],
        alipay_cost_rate: [
          { required: true, message: '请输入支付宝成本费率', trigger: 'blur' },
        ],
        signed_rate: [
          { required: true, message: '请输入实际签约费率', trigger: 'blur' },
        ]
      },
      signDateRange: [],
      deadlineDateRange: []
    }
  },
  computed: {
    grossProfit() {
      if (this.dialogVisible) {
        return new Big(this.formData.signed_rate).minus(this.formData.alipay_cost_rate)
      } else {
        return 0
      }
    }
  },
  methods: {
    handleSearch() {
      this.postData.page_no = 1
      if (Array.isArray(this.signDateRange) && this.signDateRange.length === 2) {
        this.postData.sign_time_start = formatDate(this.signDateRange[0], 'yyyy-MM-dd')
        this.postData.sign_time_end = formatDate(this.signDateRange[1], 'yyyy-MM-dd')
      } else {
        this.postData.sign_time_start = ''
        this.postData.sign_time_end = ''
      }
      if (Array.isArray(this.deadlineDateRange) && this.deadlineDateRange.length === 2) {
        this.postData.end_time_start = formatDate(this.deadlineDateRange[0], 'yyyy-MM-dd')
        this.postData.end_time_end = formatDate(this.deadlineDateRange[1], 'yyyy-MM-dd')
      } else {
        this.postData.end_time_start = ''
        this.postData.end_time_end = ''
      }
      this.getList()
    },
    handleReset() {
      this.postData = {
        ...NONE_POST_DATA
      }
      this.signDateRange = []
      this.deadlineDateRange = []
      this.getList()
    },
    getList() {
      this.$service.post('/Web/ZhimaFitPay/rates', this.postData).then(({ data }) => {
        if (data.errorcode === 0) {
          this.list = data.data.list
          this.total = Number(data.data.count)
        } else {
          this.$message.error(data.errormsg)
        }
      })
    },
    getSaleList() {
      this.$service.post('/Web/ZhimaFitPay/sales', {
        _no_paginate: true,
        status: 0
      }).then(({ data }) => {
        if (data.errorcode === 0) {
          this.saleList = data.data
        } else {
          this.$message.error(data.errormsg)
        }
      })
    },
    handleCurrentChange(val) {
      this.postData.page_no = val
      this.getList()
    },
    handleSizeChange(val) {
      this.postData.page_no = 1
      this.postData.page_size = val
      this.getList()
    },
    onSortChange({ column, prop, order }) {
      let sortType = ''
      let sortKey = prop
      if (order === 'ascending') {
        sortType = 'asc'
      } else if (order === 'descending') {
        sortType = 'desc'
      } else {
        sortKey = ''
      }
      this.postData.sort = {
        type: sortType,
        name: sortKey
      }
      this.getList()
    },
    handleEdit(row) {
      this.formData = {
        open_merchant_id: row.open_merchant_id,
        merchant_name: row.merchant_name,
        sale_id: row.sale_id,
        sale_name: row.sale_name,
        alipay_cost_rate: Number(row.alipay_cost_rate),
        signed_rate: Number(row.signed_rate),
        profit_rate: Number(row.profit_rate),
        contract_money: Number(row.contract_money),
        sign_time: row.sign_time,
        end_time: row.end_time,
        product_combine_open: row.product_combine_open,
        appid: row.appid,
        app_name: row.app_name
      }
      this.dialogVisible = true
    },
    handleClose() {
      this.$refs.formRef.resetFields()
      this.dialogVisible = false
    },
    handleSave() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          // the end_time must be greater than sign_time
          if (new Date(this.formData.end_time).getTime() < new Date(this.formData.sign_time).getTime()) {
            this.$message.error('到期时间必须大于签约时间')
            return
          } else {
            this.formData.end_time = formatDate(this.formData.end_time, 'yyyy-MM-dd')
            this.formData.sign_time = formatDate(this.formData.sign_time, 'yyyy-MM-dd')
          }
          this.formData.profit_rate = this.grossProfit
          return this.$service.post('/Web/ZhimaFitPay/upsertRate', {
            ...this.formData
          }).then(({ data }) => {
            if (data.errorcode === 0) {
              this.dialogVisible = false
              this.getList()
              this.$message.success('保存成功')
            } else {
              this.$message.error(data.errormsg)
            }
          })
        }
      })
    }
  },
  created() {
    this.getSaleList()
    this.getList()
  }
}
</script>

<style lang="less" scoped>
.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-input,
      .option-select {
        width: 200px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
      }

    }
  }

  .tips {
    font-size: 12px;
    color: #999;
    margin-left: 5px;
  }
}
</style>