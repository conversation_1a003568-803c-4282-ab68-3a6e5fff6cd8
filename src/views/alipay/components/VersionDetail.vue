<template>
  <el-dialog
    ref="info"
    title="版本详情"
    :visible.sync="showDialog"
    width="45%"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <el-form label-width="100px" class="info-dialog-form">
      <el-form-item label="APPID">
         {{info.auth_app_id}}
      </el-form-item>
      <el-form-item label="名称">
         {{infoData.app_name}}
      </el-form-item>
      <el-form-item label="版本号">
         {{infoData.app_version}}
      </el-form-item>
       <el-form-item label="版本描述">
         {{infoData.version_desc}}
      </el-form-item>
       <el-form-item label="版本状态">
         {{infoData.status_txt}}
      </el-form-item>
       <el-form-item label="审核驳回原因">
         {{infoData.reject_reason}}
      </el-form-item>
      <el-form-item label="创建时间">
         {{infoData.gmt_create}}
      </el-form-item>
       <el-form-item label="提审时间">
         {{infoData.gmt_apply_audit}}
      </el-form-item>
      <el-form-item label="上架时间">
         {{infoData.gmt_online}}
      </el-form-item>
      <el-form-item label="下架时间">
         {{infoData.gmt_offline}}
      </el-form-item>
      <el-form-item label="小程序备注">
         {{infoData.memo}}
      </el-form-item>
      <el-form-item label="客服电话">
         {{infoData.service_phone}}
      </el-form-item>
    </el-form>
    <div slot="footer">
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'VersionDetail',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      infoData: {
        auth_app_id: '',
        app_name: '',
        app_version: ''
      }
    }
  },
  watch: {
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  methods: {
    onDialogClose() {
      this.$emit('input', false)
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
    getInfo() {
      const { auth_app_id, app_version } =  this.info
      this.$service.post('Web/AlipayIsv/getAuditReleaseStatusDetail', { auth_app_id, app_version }).then(res => {
        if (res.data.errorcode == 0) {
          this.infoData = res.data.data.info
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    }
  },
  created () {
    if(this.info.auth_app_id) {
      this.getInfo()
    }
  }
}
</script>

<style lang="less" scoped>

</style>



