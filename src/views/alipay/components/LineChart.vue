<template>
  <div ref="chartRef" style="width: 400px; height: 300px;"></div>
</template>

<script>
import * as echarts from 'echarts';

export default {
  name: 'LineChart',
  props: {
    options: {
      type: Object,
      required: true
    }
  },
  mounted() {
    const chart = echarts.init(this.$refs.chartRef);
    chart.setOption(this.options);
  }
}
</script>

<style lang="less" scoped></style>