<template>
  <el-dialog
    ref="info"
    :title="'体验版二维码-'+info.app_version"
    :visible.sync="showDialog"
    width="600px"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
  <div align="center" style="margin:0 auto;">
    <img :src="codeUrl" width="375" height="375" />
</div> 
   
    <div slot="footer"></div>
  </el-dialog>
</template>

<script>
export default {
  components: {},
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      codeUrl: ''
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      },
    },
  },
  created () {
    this.getQRCode()
  },
  methods: {
    getQRCode() {
      this.$service.post('Web/AlipayIsv/miniExperienceCreate', {
        auth_app_id: this.info.auth_app_id,
        app_version: this.info.app_version,
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.codeUrl = res.data.data.exp_qr_code_url
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    },
    onDialogClose() {
      this.$emit('input', false)
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
  },
}
</script>

<style lang="less" scoped>
  .tips {
    margin-left: 90px;
    line-height: 1.75;
    .tit {
      color: red;
    }
  }
</style>
