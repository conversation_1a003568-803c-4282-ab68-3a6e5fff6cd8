<template>
  <div class="container">
    <header>
      <el-input placeholder="商户名称" v-model="postData.merchant_name" class="w-200" clearable></el-input>
      <BusSearchSelect v-model="postData.store_no" />
      <el-input placeholder="产品名称" v-model="postData.product_title" class="w-200" clearable></el-input>
      <el-input placeholder="卡课名称" v-model="postData.card_name" class="w-200" clearable></el-input>
      <el-select v-model="postData.approve_status" class="w-200" placeholder="审批状态" clearable>
        <el-option v-for="item in statusInfoArr" :key="item.key" :label="item.name" :value="item.key">
        </el-option>
      </el-select>
      <el-button type="success" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="merchant_name" label="商户名称" align="center"></el-table-column>
      <el-table-column prop="store_title" label="场馆名称" align="center"></el-table-column>
      <el-table-column prop="product_title" label="产品名称" align="center"></el-table-column>
      <el-table-column prop="card_name" label="卡课名称" align="center"></el-table-column>
      <el-table-column prop="periods" label="期数" align="center"></el-table-column>
      <el-table-column prop="down_payment" label="首期服务费" align="center"></el-table-column>
      <el-table-column prop="deduction_amount" label="单月服务费" align="center"></el-table-column>
      <el-table-column prop="sell_type" label="类型" align="center">
        <template scope="scope">
          连续包月
        </template>
      </el-table-column>
      <el-table-column prop="status" label="状态" align="center">
        <template scope="scope">
          {{ scope.row.status == 1 ? '启用' : '禁用' }}
        </template>
      </el-table-column>
      <el-table-column prop="approve_status" label="审批状态" align="center">
        <template scope="scope">
          {{ statusNameByKey(scope.row.approve_status) }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template scope="scope">
          <el-button v-if="scope.row.status == 1 && (scope.row.approve_status == 0 || scope.row.approve_status == 2)"
            type="primary" size="small" @click="handleAction(scope.row, 1)">
            上架
          </el-button>
          <el-button v-if="scope.row.status == 1 && scope.row.approve_status == 0" type="danger" size="small"
            @click="handleAction(scope.row, 3)">
            不通过
          </el-button>
          <el-button v-if="scope.row.status == 1 && scope.row.approve_status == 1" type="danger" size="small"
            @click="handleAction(scope.row, 2)">
            下架
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <footer>
      <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size"
        :current-page="postData.page_no" :total="dataCount">
      </el-pagination>
    </footer>
    <div v-if="showEditMerchant">
      <EditMerchant :info="curInfo" v-model="showEditMerchant" @on-confirm="getList" />
    </div>
  </div>
</template>

<script>
import EditMerchant from './EditMerchant.vue'
import BusSearchSelect from './BusSearchSelect.vue'
export default {
  name: 'StoresByMonthlyPay',
  components: {
    EditMerchant,
    BusSearchSelect
  },
  data() {
    return {
      statusInfoArr: [{
        key: 0,
        name: '待审批'
      }, {
        key: 1,
        name: '上架'
      }, {
        key: 2,
        name: '下架'
      }, {
        key: 3,
        name: '未通过'
      }],
      postData: {
        status: '',
        merchant_name: '',
        card_name: '',
        store_no: '',
        product_title: '',
        approve_status: '',
        page_size: 10,
        page_no: 1
      },
      curInfo: {},
      tableData: [],
      dataCount: 5,
      showEditMerchant: false
    }
  },
  methods: {
    statusNameByKey(key) {
      for (const iterator of this.statusInfoArr) {
        if (key === iterator.key) {
          return iterator.name
        }
      }
    },
    search() {
      this.postData.page_no = 1;
      this.getList();
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.getList();
    },
    getList() {
      this.$service.post('Web/ZhimaFitPay/products', this.postData).then(res => {
        if (res.data.errorcode == 0) {
          this.tableData = res.data.data.list;
          this.dataCount = res.data.data.count;
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    },
    handleAction(info, status) {
      //status 1—上架，2—下架， 3—未通过
      this.$confirm(`确定${status == 2 ? '下架' : status == 3 ? '未通过' : '上架'}?`, '提示', {
        type: 'warning'
      }).then(() => {
        this.$service
          .post('/Web/ZhimaFitPay/productSetApproveStatus', {
            open_merchant_id: info.open_merchant_id,
            store_no: info.store_no,
            product_no: info.product_no,
            approve_status: status
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$message.success(res.data.errormsg);
              this.getList();
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
      }).catch(() => {
      })
    },
  },
  created() {
    this.getList()
  }
};
</script>

<style scoped>
.table-zoom-image {
  height: 30px;
}

.table-zoom-image>img {
  height: 100%;
}

.oneline {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  margin-top: 10px;
}

.oneline:first-child {
  margin-top: 0;
}

.dynamadd i {
  width: 10%;
  height: 100%;
  margin-left: 10px;
}

.w90 {
  width: 90%;
}

.bluefont {
  color: #409eff;
  cursor: pointer;
  width: 100%;
  display: inline-block;
}

.overtext {
  text-overflow: ellipsis;
  white-space: nowrap;
}

.qrpic {
  border: none;
  width: 32px;
  height: 32px;
  background: url(../../../assets/images/qrcode.png) no-repeat center;
  background-size: 32px 32px;
}

.flex-center {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
</style>
