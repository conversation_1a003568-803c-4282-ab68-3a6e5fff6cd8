<template>
  <el-dialog
    ref="info"
    title="人员添加"
    :visible.sync="showDialog"
    width="800px"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <el-form label-width="100px" class="info-dialog-form">
      <el-form-item label="角色" prop="old_pwd">
        <el-select v-model="infoData.role" class="w-300">
          <el-option v-for="item in roleList" :label="item.label" :value="item.value" :key="item.value"></el-option>
        </el-select>
        </el-form-item>
       <el-form-item label="支付宝账号" prop="old_pwd">
         <el-input v-model="infoData.logon_id" class="w-300"></el-input>
        </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button style="margin-right:20px;" @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  props: {
    value: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      roleList: [{
        label: '体验者',
        value: 'EXPERIENCER'
      }, {
        label: '开发者',
        value: 'DEVELOPER'
      }],
      infoData: {
        logon_id: '',
        auth_app_id: '',
        role: ''
      }
    }
  },
  watch: {
    showDialog(val) {
      if(!val) {
        this.infoData = {
          logon_id: '',
          auth_app_id: '',
          role: ''
        }
      }
    },
    info: {
      handler(val) {
        if (val && typeof val === 'object') {
          this.infoData.auth_app_id = this.info.auth_app_id
        }
      },
      immediate: true
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  methods: {
    onDialogClose() {
      this.$emit('input', false)
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
    handleConfirm() {
      this.$service.post('Web/AlipayIsv/testMembersCreate', this.infoData).then(res => {
        if (res.data.errorcode == 0) {
          this.showDialog = false
          this.$emit('on-confirm')
           _g.toastMsg('success', '添加成功');
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    }
  },
  created () {
  }
}
</script>

<style lang="less" scoped>

</style>



