<template>
  <div class="container">
    <header>
      <BusSearchSelect v-model="postData.store_no" />
      <el-input placeholder="产品名称" v-model="postData.product_title" class="w-200" clearable></el-input>
      <el-input placeholder="产品ID" v-model="postData.product_no" class="w-200" clearable></el-input>
      <el-input placeholder="手机号" v-model="postData.phone" class="w-200" clearable></el-input>
      <el-input placeholder="商户ID" v-model="postData.merchant_pid" class="w-200" clearable></el-input>
      <el-date-picker v-model="createTimeRange" @change="createTimeRangeChange" type="daterange"
        value-format="yyyy-MM-dd"></el-date-picker>
      <el-select v-model="postData.status" class="w-200" placeholder="订购状态" clearable>
        <el-option v-for="item in statusInfoArr" :key="item.key" :label="item.name" :value="item.key">
        </el-option>
      </el-select>
      <el-input placeholder="订购编号" v-model="postData.subscription_no" class="w-200" clearable></el-input>
      <el-button type="success" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="store_title" label="场馆名称" align="center"></el-table-column>
      <el-table-column prop="product_title" label="产品名称" align="center"></el-table-column>
      <el-table-column prop="product_no" label="产品ID" align="center"></el-table-column>
      <el-table-column prop="sign_time" label="签约时间" align="center"></el-table-column>
      <el-table-column prop="phone" label="手机号" align="center"></el-table-column>
      <el-table-column prop="merchant_pid" label="商户ID" align="center"></el-table-column>
      <el-table-column prop="sell_type" label="产品类型" align="center">
        <template scope="scope">
          连续包月
        </template>
      </el-table-column>
      <el-table-column prop="status" label="订购状态" align="center">
        <template scope="scope">
          {{ statusNameByKey(scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column prop="subscription_no" label="订购编号" align="center"></el-table-column>
      <el-table-column prop="periods" label="扣款计划" align="center">
        <template scope="{row}">
          <el-button type="text" @click="handleShowInfo(row)">
            {{ row.done_period }}/{{ row.periods }}
          </el-button>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="220px">
        <template scope="scope">
          <!-- <el-button v-if="scope.row.status=='PAUSED'" type="primary" size="small" @click="handleAction(scope.row, 1)">
              恢复
            </el-button>
            <el-button v-if="scope.row.status=='NORMAL'" type="danger" size="small" @click="handleAction(scope.row, 2)">
              暂停
            </el-button> -->
          <el-button v-if="scope.row.status == 'NORMAL'" type="danger" size="small" @click="handleAction(scope.row, 3)">
            解约
          </el-button>
          <el-button type="success" size="small" @click="handleShowInfo(scope.row)">
            查看
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <footer>
      <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size"
        :current-page="postData.page_no" :total="dataCount">
      </el-pagination>
    </footer>
    <div v-if="showInfo">
      <ContractInfo :info="curInfo" v-model="showInfo" @on-confirm="getList" />
    </div>
  </div>
</template>

<script>
import BusSearchSelect from './BusSearchSelect.vue'
import ContractInfo from './ContractInfo.vue'
export default {
  name: 'ZhimaContract',
  components: {
    BusSearchSelect,
    ContractInfo
  },
  data() {
    return {
      statusInfoArr: [{
        key: 'NORMAL',
        name: '正常'
      }, {
        key: 'PAUSED',
        name: '已暂停'
      }, {
        key: 'SURRENDER',
        name: '已解约'
      }, {
        key: 'CANCEL',
        name: '已取消'
      }, {
        key: 'END',
        name: '已完结'
      }],
      createTimeRange: [],
      postData: {
        store_no: '',
        product_title: '',
        merchant_pid: '',
        sign_time_begin: '',
        sign_time_end: '',
        status: '',
        subscription_no: '',
        page_size: 10,
        page_no: 1
      },
      curInfo: {},
      tableData: [],
      dataCount: 5,
      showInfo: false
    }
  },
  methods: {
    createTimeRangeChange(val) {
      this.postData.sign_time_begin = val[0]
      this.postData.sign_time_end = val[1]
    },
    statusNameByKey(key) {
      for (const iterator of this.statusInfoArr) {
        if (key === iterator.key) {
          return iterator.name
        }
      }
    },
    search() {
      this.postData.page_no = 1;
      this.getList();
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.getList();
    },
    handleShowInfo(info) {
      this.curInfo = info
      this.showInfo = true
    },
    getList() {
      this.$service.post('Web/ZhimaFitPay/subscriptions', this.postData).then(res => {
        if (res.data.errorcode == 0) {
          this.tableData = res.data.data.list;
          this.dataCount = res.data.data.count;
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    },
    handleAction(info, status) {
      const url = `Web/ZhimaFitPay/${status == 1 ? 'subscriptionRegain' : status == 2 ? 'subscriptionPause' : 'subscriptionSurrender'}`
      this.$confirm(`${status == 3 ? '解约后未交费的订单（含欠费订单）将不再扣费，' : ''}确定${status == 1 ? '恢复' : status == 2 ? '暂停' : '解约'}?`, '提示', {
        type: 'warning'
      }).then(() => {
        this.$service
          .post(url, {
            open_merchant_id: info.open_merchant_id,
            store_no: info.store_no,
            subscription_no: info.subscription_no
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$message.success(res.data.errormsg);
              this.getList();
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
      }).catch(() => {
      })
    },
  },
  created() {
    this.getList()
  }
};
</script>

<style scoped>
.table-zoom-image {
  height: 30px;
}

.table-zoom-image>img {
  height: 100%;
}

.oneline {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  margin-top: 10px;
}

.oneline:first-child {
  margin-top: 0;
}

.dynamadd i {
  width: 10%;
  height: 100%;
  margin-left: 10px;
}

.w90 {
  width: 90%;
}

.bluefont {
  color: #409eff;
  cursor: pointer;
  width: 100%;
  display: inline-block;
}

.overtext {
  text-overflow: ellipsis;
  white-space: nowrap;
}

.qrpic {
  border: none;
  width: 32px;
  height: 32px;
  background: url(../../../assets/images/qrcode.png) no-repeat center;
  background-size: 32px 32px;
}

.flex-center {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
</style>
