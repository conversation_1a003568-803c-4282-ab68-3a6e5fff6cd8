<template>
  <el-dialog
    ref="info"
    title="商户信息"
    :visible.sync="showDialog"
    width="700px"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <el-form ref="form" label-width="100px" :model="infoData" :rules="rules" class="info-dialog-form">
      <el-form-item label="商户名称" prop="merchant_name">
       <el-input type="text" v-model="infoData.merchant_name" class="w-300"></el-input>
      </el-form-item>
      <!-- <el-form-item label="别名" prop="principal_name_alias">
       <el-input type="text" v-model="infoData.principal_name_alias" class="w-300"></el-input>
      </el-form-item> -->
      <el-form-item label="电话" prop="phone">
       <el-input type="text" v-model="infoData.phone" class="w-300"></el-input>
      </el-form-item>
      <el-form-item label="直付通ID" prop="smid">
       <el-input type="text" v-model="infoData.smid" class="w-300"></el-input>
      </el-form-item>
      <el-form-item label="支付宝PID" prop="merchant_pid">
       <el-input type="text" v-model="infoData.merchant_pid" class="w-300"></el-input>
      </el-form-item>
      <el-form-item label="APPID" prop="appid">
       <el-input type="text" v-model="infoData.appid" class="w-300"></el-input>
      </el-form-item>
      <el-form-item label="ShopId" prop="shop_id">
       <el-input type="text" v-model="infoData.shop_id" class="w-300"></el-input>
      </el-form-item>
      <el-form-item label="商户登录名" prop="merchant_login_name">
       <el-input type="text" v-model="infoData.merchant_login_name" class="w-300"></el-input>
      </el-form-item>
      <el-form-item label="分账比例" prop="ledger_rate">
       <el-input type="text" v-model="infoData.ledger_rate" class="w-300" placeholder="0~10">
         <template slot="append">%</template>
       </el-input>
      </el-form-item>
      <el-form-item label="备注" prop="comment">
        <el-input type="textarea" :rows="3" v-model="infoData.comment" class="w-300">
        </el-input>
      </el-form-item>
      <el-form-item label="logo" prop="logo_url">
        <Cropper
          v-model="infoData.logo_url"
          :width="200"
          :height="200"
          outputWidth="160"
          outputHeight="160"
          :options="{ aspectRatio: 160 / 160 }"
          url="/Web/ZhimaFitPay/logoUpload"
          :bool="true"
        ></Cropper>
      </el-form-item>
      <el-form-item label="签约销售" prop="sale_id">
        <el-select v-model="infoData.sale_id" placeholder="请选择" class="w-300">
          <el-option v-for="item in saleList" :key="item.id" :label="item.sale_name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="签约时间" prop="sign_time">
        <el-date-picker v-model="infoData.sign_time" type="date" placeholder="选择日期" class="w-300"></el-date-picker>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button style="margin-right:20px;" @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import Cropper from 'components/form/cropper';
import { formatDate } from '@/utils/index'

export default {
  name: 'EditMerchant',
  components: {
    Cropper,
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    action: {
      type: Number
    },
    info: {
      type: Object
    }
  },
  data() {
    return {
      infoData: {
        open_merchant_id: '',
        merchant_pid: '',
        appid: '',
        shop_id: '',
        merchant_login_name: '',
        smid: '',
        merchant_name: '',
        ledger_rate: '',
        phone: '',
        comment: '',
        logo_url: '',
        // principal_name_alias: '',
        sale_id: '',
        sign_time: '',
      },
      rules: {
        merchant_name: [
          { required: true, message: '请输入商户名称', trigger: 'blur' },
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
        ],
        smid: [
          { required: true, message: '请输入直付通ID', trigger: 'blur' },
        ],
        merchant_pid: [
          { required: true, message: '请输入支付宝PID', trigger: 'blur' },
        ],
        appid: [
          { required: true, message: '请输入APPID', trigger: 'blur' },
        ],
        shop_id: [
          { required: true, message: '请输入ShopId', trigger: 'blur' },
        ],
        merchant_login_name: [
          { required: true, message: '请输入商户登录名', trigger: 'blur' },
        ],
        ledger_rate: [
          { required: true, message: '请输入分账比例', trigger: 'blur' },
        ],
        sale_id: [
          { required: true, message: '请选择签约销售', trigger: 'change' },
        ],
        sign_time: [
          { required: true, message: '请选择签约时间', trigger: 'change' },
        ],
      },
      saleList: []
    }
  },
  watch: {
    showDialog(val) {
      if(!val) {
        this.infoData = this.$options.data().infoData
      }
    },
    info: {
      handler(info) {
        if(info && info.open_merchant_id) {
          this.infoData = info
        }
      },
      immediate: true
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  created () {
    this.getSaleList()
  },
  methods: {
    onDialogClose() {
      this.$emit('input', false)
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
    handleConfirm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.infoData.sign_time = formatDate(this.infoData.sign_time, 'yyyy-MM-dd')
          this.$service.post('Web/ZhimaFitPay/upsertMerchant', this.infoData).then(res => {
            if (res.data.errorcode == 0) {
              this.showDialog = false
              this.$emit('on-confirm')
              _g.toastMsg('success', res.data.errormsg);
            } else {
              _g.toastMsg('warning', res.data.errormsg);
            }
          });
        }
      });
    },
    getSaleList() {
      this.$service.post('/Web/ZhimaFitPay/sales', {
        _no_paginate: true,
        status: 0
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.saleList = res.data.data;
        } else {
          this.$message.error(res.data.errormsg);
        }
      }).catch(e => {
        throw new Error(e)
      })
    },
  }
}
</script>
<style lang="less" scoped>

</style>



