<template>
  <el-dialog
    ref="info"
    title="蚂蚁门店设置"
    :visible.sync="showDialog"
    width="800px"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ info.app_name }}</span>
        <el-button
          :loading="loading"
          style="float: right; padding: 3px 0;color:green"
          @click="handleSaveAll()"
          type="text"
        >批量创建</el-button>
        <el-button
          :loading="loadingDel"
          style="margin-right:15px;float: right; padding: 3px 0;color:red"
          @click="handleDelAll()"
          type="text"
        >批量删除</el-button>
      </div>
      <el-table :data="list" stripe style="width: 100%" @selection-change="handleSelectionChange">
        <el-table-column type="selection" width="55"></el-table-column>
        <el-table-column prop="bus_name" label="场馆名称" align="center">
        </el-table-column>
        <!-- <el-table-column prop="name" label="蚂蚁门店名称" align="center"></el-table-column> -->
        <el-table-column prop="store_id" label="蚂蚁门店编号" align="center">
          <template scope="scope">
            <el-input v-if="list[scope.$index].isEdit" v-model="list[scope.$index].store_id"></el-input>
            <div v-else>{{scope.row.store_id}}</div>
          </template>
        </el-table-column>
        <el-table-column prop="shop_id" label="ShopId" align="center">
        </el-table-column>
        <!-- <el-table-column prop="name" label="地址" align="center"></el-table-column> -->
        <el-table-column label="操作" width="150" align="center">
          <template scope="scope">
            <el-button v-if="!scope.row.store_id" type="text" @click="handleProSave(scope.row)">创建</el-button>
             <el-button
              v-if="!list[scope.$index].isEdit"
              type="text"
              @click="handleProEdit(scope.row, scope.$index)"
            >编辑</el-button>
             <el-button
              v-if="list[scope.$index].isEdit"
              type="text"
              @click="handleEditConfirm(scope.row, scope.$index)"
            >保存</el-button>
            <el-button
              v-if="scope.row.store_id && !list[scope.$index].isEdit"
              style="color: red;"
              type="text"
              @click="handleProDel(scope.row)"
            >删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-card>
    <div slot="footer"></div>
  </el-dialog>
</template>

<script>
export default {
  name: 'StoreSet',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object,
      default: () => null
    }
  },
  data() {
    return {
      list: [],
      multipleSelection: [],
      loading: false,
      loadingDel: false,
      infoData: {
        auth_app_id: ''
      }
    }
  },
  watch: {
    showDialog(val) {
      if (!val) {
        this.infoData = {
          auth_app_id: ''
        }
      }
    },
    info(val) {
      if (val && typeof val === 'object') {
        this.infoData.auth_app_id = this.info.auth_app_id
      }
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  created() {
    this.infoData.auth_app_id = this.info.auth_app_id
    this.getList()
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val
      console.log(val)
    },
    onDialogClose() {
      this.$emit('input', false)
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
    getList() {
      this.$service.post('Web/AlipayIsv/antShopList', this.infoData).then(res => {
        if (res.data.errorcode == 0) {
          this.list = res.data.data.list
        } else {
          _g.toastMsg('warning', res.data.errormsg)
        }
      })
    },
    handleSaveAll() {
      if (!this.multipleSelection.length) {
        _g.toastMsg('warning', '请先选择')
        return false
      }
      let multiplePost = []
      this.multipleSelection.forEach(item => {
        multiplePost.push(this.handleSave(item))
      })

      this.loading = true
      Promise.all(multiplePost).then((result) => {
        let faildAppStr = ''
        result.forEach((res, index) => {
          if (res.data.errorcode != 0) {
            faildAppStr += this.list[index].bus_name + ':' + res.data.errormsg + ' '
          }
        })
        if (faildAppStr) {
          this.$message({
            showClose: true,
            message: faildAppStr,
            type: 'error',
            duration: 5 * 1000
          })
        } else {
          _g.toastMsg('success', '操作成功')
        }
        this.getList()
        this.loading = false
      }).catch((error) => {
        this.loading = false
        console.log(error)
      })
    },
    handleDelAll() {
      if (!this.multipleSelection.length) {
        _g.toastMsg('warning', '请先选择')
        return false
      }
      this.$confirm('确定批量删除?', '提示', {
        type: 'warning'
      }).then(() => {
        let multiplePost = []
        this.multipleSelection.forEach(item => {
          multiplePost.push(this.handleDel(item))
        })

        this.loadingDel = true
        Promise.all(multiplePost).then((result) => {
          let faildAppStr = ''
          result.forEach((res, index) => {
            if (res.data.errorcode != 0) {
              faildAppStr += this.list[index].bus_name + ':' + res.data.errormsg + ' '
            }
          })
          if (faildAppStr) {
            this.$message({
              showClose: true,
              message: faildAppStr,
              type: 'error',
              duration: 5 * 1000
            })
          } else {
            _g.toastMsg('success', '操作成功')
          }
          this.getList()
          this.loadingDel = false
        }).catch((error) => {
          this.loadingDel = false
          console.log(error)
        })
      })
    },
    handleProEdit(rowInfo, index) {
      this.$set(this.list, index, {
        ...rowInfo,
        isEdit: true
      });
    },
    handleEditConfirm(rowInfo, index) {
      this.$service.post('Web/AlipayIsv/updateStoreId', {
        bus_id: rowInfo.bus_id,
        store_id: rowInfo.store_id,
        auth_app_id:this.infoData.auth_app_id
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.$set(this.list, index, {
            ...rowInfo,
            isEdit: false
          });
          _g.toastMsg('success', '操作成功')
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    },
    handleProDel(rowInfo) {
      this.$confirm('确定删除?', '提示', {
        type: 'warning'
      }).then(() => {
        this.handleDel(rowInfo).then((res) => {
          if (res.data.errorcode == 0) {
            _g.toastMsg('success', '操作成功')
            this.getList()
          } else {
            _g.toastMsg('warning', res.data.errormsg)
          }
        })
      })
    },
    handleDel(rowInfo) {
      return this.$service.post('Web/AlipayIsv/antShopDelete', {
        bus_id: rowInfo.bus_id,
        store_id: rowInfo.store_id,
        bus_name: rowInfo.bus_name,
        auth_app_id: this.infoData.auth_app_id
      })
    },
    handleProSave(rowInfo) {
      this.handleSave(rowInfo).then((res) => {
        console.log(res)
        if (res.data.errorcode == 0) {
          _g.toastMsg('success', '操作成功')
          this.getList()
        } else {
          _g.toastMsg('warning', res.data.errormsg)
        }
      })
    },
    handleSave(rowInfo) {
      return this.$service.post('Web/AlipayIsv/antShopCreate', {
        bus_id: rowInfo.bus_id,
        bus_name: rowInfo.bus_name,
        auth_app_id: this.infoData.auth_app_id
      })
    }
  }
}
</script>



