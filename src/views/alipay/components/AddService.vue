<template>
  <el-dialog
    ref="info"
    title="添加服务"
    :visible.sync="showDialog"
    width="600px"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <el-form ref="form" label-width="100px" :model="infoData" class="info-dialog-form">
      <el-form-item label="服务类型" prop="category_id" :rules="{ required: true, message: '请选择类型'}">
        <ServiceCategory v-model="infoData.category_id" class="w-300" />
      </el-form-item>
      <el-form-item label="服务名称" prop="service_name" :rules="{ required: true, message: '请填写'}">
        <el-input type="text" v-model="infoData.service_name" class="w-300" placeholder="字数限制2～50个字">
        </el-input>
      </el-form-item>
      <el-form-item label="服务描述" prop="service_desc" :rules="{ required: true, message: '请填写'}">
        <el-input type="textarea" placeholder="请描述具体的服务内容，字数限制为5-100字" :rows="3" v-model="infoData.service_desc" class="w-300">
        </el-input>
      </el-form-item>
      <el-form-item label="页面地址" prop="service_url" :rules="{ required: true, message: '请填写'}">
        <el-input type="text" placeholder="pages/index/index" v-model="infoData.service_url" class="w-300">
        </el-input>
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button style="margin-right:20px;" @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import ServiceCategory from './ServiceCategory.vue'
export default {
  name: 'AddService',
  components: {
    ServiceCategory
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    type: {
      type: String
    },
    info: {
      type: Object
    }
  },
  data() {
    return {
      categoryList: [{
        id: 'C000003546',
        name: '休闲娱乐/健身房/瑜伽/舞蹈/在线预约'
      },{
        id: 'C000003547',
        name: '休闲娱乐/健身房/瑜伽/舞蹈/在线订购'
      },{
        id: 'C000003548',
        name: '休闲娱乐/健身房/瑜伽/舞蹈/预约私教'
      }],
      infoData: {
        id: '',
        category_id: 'C000003546',
        service_name: '',
        service_desc: '',
        service_url: 'pages/index/index'
      }
    }
  },
  watch: {
    showDialog(val) {
      if(!val) {
        this.infoData = {
          id: '',
          category_id: 'C000003546',
          service_name: '',
          service_desc: '',
          service_url: 'pages/index/index'
        }
      }
    },
    info: {
      handler(val) {
        if (val && val.category_id){
          Object.keys(this.infoData).forEach((key)=>{
            this.infoData[key] = this.info[key]
          })
        }
      },
      immediate: true
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  created () {
  },
  methods: {
    onDialogClose() {
      this.$emit('input', false)
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
    handleConfirm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          const url = this.type=='record'?'Web/AlipayIsv/serviceSchemaUpdate':this.infoData.id?'Web/AlipayIsv/updateSchemaCategoryTemplate':'Web/AlipayIsv/addSchemaCategoryTemplate'
          this.$service.post(url, this.infoData).then(res => {
            if (res.data.errorcode == 0) {
              this.showDialog = false
              this.$emit('on-confirm')
              _g.toastMsg('success', res.data.errormsg);
            } else {
              _g.toastMsg('warning', res.data.errormsg);
            }
          });
        }
      });
    }
  }
}
</script>
<style lang="less" scoped>

</style>



