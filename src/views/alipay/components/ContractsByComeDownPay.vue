<template>
  <div class="come-down-box">
    <header>
      <BusSearchSelect v-model="postData.store_no" />
      <el-input placeholder="产品名称" v-model="postData.product_title" @keyup.enter.native="search" class="search-item"
        clearable></el-input>
      <el-input placeholder="产品 ID" v-model="postData.product_no" @keyup.enter.native="search" class="search-item"
        clearable></el-input>
      <el-input placeholder="手机号" v-model="postData.phone" @keyup.enter.native="search" class="search-item"
        clearable></el-input>
      <el-input placeholder="商户 ID" v-model="postData.merchant_pid" @keyup.enter.native="search" class="search-item"
        clearable></el-input>
      <el-date-picker class="w-200" v-model="createTimeRange" type="daterange" start-placeholder="签约"
        end-placeholder="时间"></el-date-picker>
      <el-select placeholder="订购状态" v-model="postData.status" class="search-item" clearable>
        <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
      <el-input placeholder="订购编号" v-model="postData.subscription_no" @keyup.enter.native="search" class="search-item"
        clearable></el-input>
      <el-button type="success" @click="search">搜索</el-button>
    </header>
    <main>
      <el-table :data="tableData" stripe>
        <el-table-column prop="store_title" label="场馆名称" align="center" />
        <el-table-column prop="product_title" label="产品名称" align="center" />
        <el-table-column prop="product_no" label="产品 ID" align="center" />
        <el-table-column prop="sign_time" label="签约时间" align="center" />
        <el-table-column prop="phone" label="手机号" align="center" />
        <el-table-column prop="merchant_pid" label="商户 ID" align="center" />
        <el-table-column prop="sell_type" label="类型" align="center" />
        <el-table-column prop="status_label" label="订购状态" align="center" />
        <el-table-column prop="subscription_no" label="订购编号" align="center" />
        <el-table-column prop="periods_label" label="扣款计划" align="center">
          <template scope="scope">
            <el-button type="text" @click="handleLook(scope.row)">
              {{ scope.row.done_period }}/{{ scope.row.periods }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template scope="scope">
            <div class="btn-box">
              <el-button v-if="scope.row.status == 'NORMAL'" type="danger" size="mini" style="margin: 0"
                @click="handleAction(scope.row, 3)">解约</el-button>
              <el-button type="success" size="mini" style="margin: 0" @click="handleLook(scope.row)">查看</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </main>
    <footer>
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="postData.page_no" :page-sizes="[10, 20, 30, 40]" :page-size="postData.page_size"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </footer>

    <el-dialog title="扣款计划" :visible.sync="dialogVisible" width="min(60%, 800px)">
      <el-form ref="formRef" :model="formData" label-width="160px">
        <el-form-item label="产品名称">
          {{ formData.product_title }}
        </el-form-item>
        <el-form-item label="手机号">
          {{ formData.phone }}
        </el-form-item>
        <el-form-item label="订购编号">
          {{ formData.subscription_no }}
        </el-form-item>
      </el-form>

      <el-table :data="formData.items" stripe>
        <el-table-column prop="period" label="期数" align="center" />
        <el-table-column prop="plan_deduction_time" label="计划扣款日期" align="center" />
        <el-table-column prop="actual_deduction_time" label="实际扣款日期" align="center" />
        <el-table-column prop="deduction_amount" label="扣款金额" align="center" />
        <el-table-column prop="order_no" label="订单编号" align="center" />
        <el-table-column prop="status" label="订单状态" align="center">
          <template scope="scope">
            {{ orderStatusMap[scope.row.status] }}
          </template>
        </el-table-column>
      </el-table>

      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">关 闭</el-button>
        <!-- <el-button type="primary" @click="handleSave">确 定</el-button> -->
      </span>
    </el-dialog>
  </div>
</template>

<script>
import BusSearchSelect from './BusSearchSelect.vue'
import { Message } from 'element-ui';
import _ from 'lodash';
import dateFormat from 'dateformat';

export default {
  name: 'ContractsByComeDownPay',
  components: {
    BusSearchSelect
  },
  data() {
    return {
      statusList: [
        {
          value: 'NORMAL',
          label: '正常'
        },
        {
          value: 'PAUSED',
          label: '已暂停'
        },
        {
          value: 'SURRENDER',
          label: '已解约'
        },
        {
          value: 'CANCEL',
          label: '已取消'
        },
        {
          value: 'END',
          label: '已完结'
        }
      ],
      createTimeRange: [],
      postData: {
        store_no: '',
        product_title: '',
        merchant_pid: '',
        sign_time_begin: '',
        sign_time_end: '',
        status: '',
        subscription_no: '',
        page_size: 10,
        page_no: 1,
        merchant_type: 2,
        card_type: 'AXF_MERCHANT_PERIOD_PAY'
      },
      tableData: [],
      total: 0,
      dialogVisible: false,
      formData: {},
      orderStatusMap: {
        PAUSED: '已暂停',
        ORDERING: '下单中',
        ORDERED: '下单成功',
        PAID: '支付成功',
        PAY_FAILED: '扣款失败',
        REFUNDED: '已全额退款',
        UNCREATED: '未生成订单',
        CANCEL: '已取消',
      }
    }
  },
  methods: {
    search() {
      this.postData.page_no = 1;
      if (Array.isArray(this.createTimeRange) && this.createTimeRange.length === 2) {
        this.postData.sign_time_begin = dateFormat(this.createTimeRange[0], 'yyyy-mm-dd');
        this.postData.sign_time_end = dateFormat(this.createTimeRange[1], 'yyyy-mm-dd');
      } else {
        this.postData.sign_time_begin = '';
        this.postData.sign_time_end = '';
      }
      this.getList();
    },
    getList() {
      this.$service.post('Web/ZhimaFitPay/subscriptions', this.postData).then(res => {
        if (res.data.errorcode === 0) {
          const list = res.data.data.list;
          list.forEach(item => {
            item.sell_type = '连续包月';

            const statusItem = this.statusList.find(status => status.value === item.status);
            if (statusItem) {
              item.status_label = statusItem.label;
            } else {
              item.status_label = '-';
            }
          })

          this.tableData = list;
          this.total = res.data.data.count;
        } else {
          Message.error(res.data.errormsg);
        }
      })
    },
    handleSizeChange(val) {
      this.postData.page_no = 1;
      this.postData.page_size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.postData.page_no = val;
      this.getList();
    },
    handleLook(row) {
      this.dialogVisible = true;
      this.formData = {
        ...row
      }
    },
    handleCancel() {
      this.formData = {};
      this.dialogVisible = false;
    },
    handleAction: _.throttle(function (info, status) {
      this.$confirm('解约后未交费的订单（含欠费订单）将不再扣费，确定解约', '提示', {
        type: 'warning'
      }).then(() => {
        this.$service
          .post('/Web/ZhimaFitPay/subscriptionSurrender', {
            open_merchant_id: info.open_merchant_id,
            store_no: info.store_no,
            subscription_no: info.subscription_no,
            merchant_type: 2,
            card_type: 'AXF_MERCHANT_PERIOD_PAY'
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$message.success(res.data.errormsg);
              this.getList();
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
      }).catch(() => {
      })
    }, 3000)
  },
  created() {
    this.getList();
  }
}
</script>

<style lang="less" scoped>
.come-down-box {
  padding: 20px 0;

  header {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 15px;
    padding: 0 20px;

    .search-item {
      width: 200px;
    }
  }

  main {
    margin-top: 20px;
    border-top: 1px solid #dddee1;

    .btn-box {
      display: flex;
      flex-direction: row;
      justify-content: center;
      align-items: center;
      gap: 10px;
      flex-wrap: nowrap;
    }
  }

  footer {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    margin-top: 20px;
    padding: 0 20px;
  }
}
</style>