<template>
     <el-dialog
    ref="info"
    title="版本列表"
    :visible.sync="showDialog"
    width="800px"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <el-card class="box-card">
        <el-table :data="list" stripe style="width: 100%">
            <el-table-column prop="app_version" label="版本号" width="180" />
            <el-table-column prop="version_status_txt" label="状态" width="180" />
            <el-table-column prop="create_time" label="创建时间" />
        </el-table>
   </el-card>
   <div slot="footer"></div>
</el-dialog>
</template>
<script>
    export default{
        name:'VersionList',
        props:{
            value: {
            type: Boolean,
            default: false
            },
            info: {
            type: Object,
            default: () => null
            }
        },
        data(){
            return{
                list:[],
            }
                   
        },
        computed: {
            showDialog: {
            get() {
                return this.value
            },
            set(value) {
                this.$emit('input', value)
            }
        }
  },
  created() {
    this.getVersionList()
  },
  methods: {
    onDialogClose() {
      this.$emit('input', false)
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
    getVersionList(){
        console.log(this.info)
        this.$service.post('Web/AlipayIsv/getAllVersionByMini',{
            auth_app_id:this.info.auth_app_id
        }).then(res=>{
            console.log(res)
            if(res.data.errorcode == 0){
                this.list = res.data.data
            }else{
                _g.toastMsg('warning', res.data.errormsg);
            }
        })
    }
  }
  }
</script>