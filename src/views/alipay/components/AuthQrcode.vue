<template>
  <el-dialog
    ref="info"
    title="授权码"
    :visible.sync="showDialog"
    width="600px"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
  <div class="tips">
    <div class="tit">Tips: </div>
    <div class="con">
      1.授权码不可长期保存，一次弹窗只能使用一次。<br/>
      2.个人类型支付宝账号扫码会报系统错误。
    </div>
  </div>
  <div align="center" style="margin:0 auto;">
    <div style="width:375px;height:632px;overflow:hidden;border:0px"> 
    <div style="width:375px;height:632px;margin:-109px 0px 0px -10px;">
    <iframe :src="iframeUrl" frameborder="0" id="frame" width="375" height="680" scrolling="no"></iframe>
   </iFrame>
    </div> 
</div> 
</div> 
   
    <div slot="footer"></div>
  </el-dialog>
</template>

<script>
export default {
  name: 'AuthQrcode',
  components: {},
  props: {
    value: {
      type: Boolean,
      default: false,
    },
    info: {
      type: Object,
      default: () => null,
    },
  },
  data() {
    return {
      iframeUrl: ''
    }
  },
  watch: {
    showDialog(val) {
      if (!val) {
        this.list = []
      }
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      },
    },
  },
  created () {
    const host = window.location.host.split('.')[0]
    const suffix = host === 'my' ? '' : host === 'sim' ? '-sim' : '-beta';
    const URI = encodeURIComponent(`https://am-api${suffix}.rocketbird.cn/Index/auth`);
    this.iframeUrl = `https://fw.alipay.com/alipaymarket/itemQrCodeToIsv.htm?marketCode=OFFLINE_PROMOTION&merchandiseId=OP010301000000260862&ticket=${new Date().getTime()}&callback=${URI}`
  },
  methods: {
    addService() {
      this.list.splice(0, 0, { name: '', action: 'add' })
    },
    onDialogClose() {
      this.$emit('input', false)
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
  },
}
</script>

<style lang="less" scoped>
  .tips {
    margin-left: 90px;
    line-height: 1.75;
    .tit {
      color: red;
    }
  }
</style>
