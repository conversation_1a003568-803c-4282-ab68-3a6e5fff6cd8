<template>
  <div class="come-down-box">
    <header>
      <el-input placeholder="商户名称" v-model="postData.merchant_name" @keyup.enter.native="search" class="search-item"
        clearable></el-input>
      <el-input placeholder="商户 PID" v-model="postData.merchant_pid" @keyup.enter.native="search" class="search-item"
        clearable></el-input>
      <el-select v-model="postData.status" class="search-item" clearable>
        <el-option v-for="item in statusList" :key="item.value" :label="item.label" :value="item.value">
        </el-option>
      </el-select>
      <el-button type="success" @click="search">搜索</el-button>
    </header>
    <main>
      <el-table :data="tableData" stripe>
        <el-table-column prop="merchant_name" label="商户名称" align="center" />
        <el-table-column prop="phone" label="电话" align="center" />
        <el-table-column prop="merchant_pid" label="商户 PID" align="center" />
        <el-table-column prop="merchant_login_name" label="商户登录名" align="center" />
        <el-table-column prop="status_txt" label="状态" align="center" />
        <el-table-column prop="create_time" label="创建时间" align="center" />
        <el-table-column prop="ledger_rate" label="费率" align="center">
          <template scope="scope">
            <span>{{ scope.row.ledger_rate }}%</span>
          </template>
        </el-table-column>
        <el-table-column prop="comment" label="备注" align="center">
          <template scope="scope">
            <span v-if="!scope.row.comment_short">{{ scope.row.comment || '-' }}</span>
            <el-tooltip v-else :content="scope.row.comment" placement="top">
              <span>{{ scope.row.comment || '-' }}</span>
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="170" align="center">
          <template scope="scope">
            <div class="btn-box">
              <!-- <el-button type="primary" size="mini" :disabled="submitting" round @click="handleReSubmit(scope.row)">
                重新提交
              </el-button> -->
              <el-button type="primary" size="mini" style="margin: 0" @click="handleEdit(scope.row)">
                编辑
              </el-button>
              <router-link
                :to="{ name: 'merchantUpdateLogs', params: { open_merchant_id: scope.row.open_merchant_id } }">
                <el-button size="mini" type="info">变更记录</el-button>
              </router-link>
            </div>
          </template>
        </el-table-column>
      </el-table>
    </main>
    <footer>
      <el-button type="success" size="small" @click="handleAdd"> 添加 </el-button>
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="postData.page_no" :page-sizes="[10, 20, 30, 40]" :page-size="postData.page_size"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </footer>

    <el-dialog title="商户信息" :visible.sync="dialogVisible" width="min(40%, 600px)" :close-on-click-modal="false"
      :close-on-press-escape="false" :show-close="false">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="140px">
        <el-form-item label="商户名称" prop="merchant_name">
          <el-input type="text" v-model="formData.merchant_name"></el-input>
        </el-form-item>
        <el-form-item label="电话" prop="phone">
          <el-input type="text" v-model="formData.phone"></el-input>
        </el-form-item>
        <el-form-item label="商户 PID" prop="merchant_pid">
          <el-input type="text" v-model="formData.merchant_pid"></el-input>
        </el-form-item>
        <el-form-item label="商户登录名" prop="merchant_login_name">
          <el-input type="text" v-model="formData.merchant_login_name"></el-input>
        </el-form-item>
        <el-form-item label="是否提交支付宝" prop="is_commit">
          <el-radio-group v-model="formData.is_commit">
            <el-radio :label="1">是</el-radio>
            <el-radio :label="0">否</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="APPID" prop="appid">
          <el-input type="text" v-model="formData.appid"></el-input>
        </el-form-item>
        <el-form-item label="SHOP ID" prop="shop_id">
          <el-input type="text" v-model="formData.shop_id"></el-input>
        </el-form-item>
        <el-form-item label="费率" prop="ledger_rate">
          <div style="display: flex; align-items: center; gap: 15px">
            <el-input-number v-model="formData.ledger_rate" :min="rateRange[0]" :max="rateRange[1]" :step="0.01"
              :precision="2" :placeholder="`${rateRange[0]} ~ ${rateRange[1]}`" style="width: 100%">
              <!-- <template slot="append">%</template> -->
            </el-input-number>
            <el-tag type="info">%</el-tag>
          </div>
        </el-form-item>
        <el-form-item label="备注" prop="comment">
          <el-input type="textarea" v-model="formData.comment" :rows="5" maxlength="500" show-word-limit></el-input>
        </el-form-item>
        <!-- <el-form-item label="签约销售" prop="sale_id">
          <el-select v-model="formData.sale_id" placeholder="请选择" style="width: 100%">
            <el-option v-for="item in saleList" :key="item.id" :label="item.sale_name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="签约时间" prop="sign_time">
          <el-date-picker v-model="formData.sign_time" type="date" placeholder="选择日期"
            style="width: 100%"></el-date-picker>
        </el-form-item> -->
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleSave" :disabled="submitting">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { Message } from 'element-ui';
import dateFormat from 'dateformat';
import _ from 'lodash';

const NONE_FORM_DATA = {
  merchant_name: '',
  phone: '',
  merchant_pid: '',
  merchant_login_name: '',
  is_commit: 0,
  appid: '',
  shop_id: '',
  ledger_rate: undefined,
  sale_id: null,
  sign_time: null,
  comment: '',
};

export default {
  name: 'MerchantsByComeDownPay',
  data() {
    return {
      rateRange: [0.38, 0.6],
      // 商户状态，如果是安心付只支持四种状态：RECOVERING待商户确认 ABNORMAL商户不通过 CLOSED开通失败 NORMAL：开通成功
      statusList: [
        {
          value: 'NORMAL',
          label: '正常'
        },
        {
          value: 'RECOVERING',
          label: '待商户确认'
        },
        {
          value: 'ABNORMAL',
          label: '商户不通过'
        },
        {
          value: 'CLOSED',
          label: '开通失败'
        }
      ],
      saleList: [],
      postData: {
        merchant_name: '',
        merchant_pid: '',
        status: '',
        page_size: 10,
        page_no: 1,
        merchant_type: 2
      },
      tableData: [],
      total: 0,
      dialogVisible: false,
      formData: _.cloneDeep(NONE_FORM_DATA),
      formRules: {
        merchant_name: [
          { required: true, message: '请输入商户名称', trigger: 'blur' },
        ],
        phone: [
          { required: true, message: '请输入手机号', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' },
        ],
        merchant_pid: [
          { required: true, message: '请输入直付通ID', trigger: 'blur' },
        ],
        merchant_login_name: [
          { required: true, message: '请输入商户登录名', trigger: 'blur' },
        ],
        appid: [
          { required: true, message: '请输入APPID', trigger: 'blur' },
        ],
        shop_id: [
          { required: true, message: '请输入SHOP ID', trigger: 'blur' },
        ],
        ledger_rate: [
          { required: true, message: '请输入费率', trigger: 'blur' },
        ],
        sale_id: [
          { required: true, message: '请选择签约销售', trigger: 'blur' },
        ],
        sign_time: [
          { required: true, message: '请选择签约时间', trigger: 'blur' },
        ],
      },
      submitting: false
    }
  },
  methods: {
    search() {
      this.postData.page_no = 1;
      this.getList();
    },
    getList() {
      this.$service.post('Web/ZhimaFitPay/merchants', this.postData).then(res => {
        if (res.data.errorcode === 0) {
          const list = res.data.data.list;
          list.forEach(item => {
            const statusItem = this.statusList.find(status => status.value === item.status);
            if (statusItem) {
              item.status_txt = statusItem.label;
            } else {
              item.status_txt = '未知状态';
            }

            const comment_short = String(item.comment || '').substring(0, 10);
            if (String(item.comment || '').length > 10) {
              item.comment_short = `${comment_short}...`;
            }
          })

          this.tableData = list;
          this.total = res.data.data.count;
        } else {
          Message.error(res.data.errormsg);
        }
      })
    },
    handleSizeChange(val) {
      this.postData.page_size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.postData.page_no = val;
      this.getList();
    },
    getSaleList() {
      this.$service.post('/Web/ZhimaFitPay/sales', {
        _no_paginate: true,
        status: 0
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.saleList = res.data.data;
        } else {
          Message.error(res.data.errormsg);
        }
      }).catch(e => {
        throw new Error(e)
      })
    },
    handleAdd() {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.formRef.clearValidate();
      })
    },
    handleEdit(row) {
      this.formData = { ...row, is_commit: 0 }
      this.dialogVisible = true
    },
    handleReSubmit: _.throttle(function (row) {
      this.formData = { ...row }
      this.save()
    }, 3000),
    handleSave: _.throttle(function () {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.save()
        }
      })
    }, 3000),
    handleCancel() {
      this.formData = _.cloneDeep(NONE_FORM_DATA)
      this.dialogVisible = false
    },
    save() {
      this.submitting = true
      this.$service.post('Web/ZhimaFitPay/upsertMerchantByCommerce', {
        ...this.formData,
        sign_time: dateFormat(this.formData.sign_time, 'yyyy-MM-dd')
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.dialogVisible = false
          Message.success(res.data.errormsg)
          this.getList();
        } else {
          Message.error(res.data.errormsg)
        }
      }).finally(() => {
        this.submitting = false
      })
    }
  },
  created() {
    this.getList();
    this.getSaleList();
  }
}
</script>

<style lang="less" scoped>
.come-down-box {
  padding: 20px 0;

  header {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 15px;
    padding: 0 20px;

    .search-item {
      width: 200px;
    }
  }

  main {
    margin-top: 20px;
    border-top: 1px solid #dddee1;

    .btn-box {
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
      gap: 8px;
    }
  }

  footer {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    margin-top: 20px;
    padding: 0 20px;
  }
}
</style>