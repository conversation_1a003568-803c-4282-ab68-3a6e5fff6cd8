<template>
  <div class="container">
    <header>
      <el-input placeholder="商户名称" v-model="postData.merchant_name" class="w-200"></el-input>
      <el-input placeholder="支付宝PID" v-model="postData.merchant_pid" class="w-200"></el-input>
      <el-select v-model="postData.status" class="w-200" clearable>
        <el-option v-for="item in statusInfoArr" :key="item.key" :label="item.name" :value="item.key">
        </el-option>
      </el-select>
      <el-button type="success" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="merchant_name" label="商户名称" align="center"></el-table-column>
      <el-table-column prop="phone" label="电话" align="center"></el-table-column>
      <el-table-column prop="merchant_pid" label="支付宝PID" align="center"></el-table-column>
      <el-table-column prop="merchant_login_name" label="商户登录名" align="center"></el-table-column>
      <el-table-column prop="status" label="状态" align="center">
        <template scope="scope">
          {{ statusNameByKey(scope.row.status) }}
        </template>
      </el-table-column>
      <el-table-column prop="create_time" label="创建时间" align="center"></el-table-column>
      <el-table-column prop="comment" label="备注" align="center"></el-table-column>
      <el-table-column label="操作" align="center">
        <template scope="scope">
          <div>
            <el-button class='editfont m-r-5' type="primary" size="small" @click="handleEditMerchant(scope.row)">
              编辑
            </el-button>
            <router-link class='editfont m-r-5'
              :to="{ name: 'merchantUpdateLogs', params: { open_merchant_id: scope.row.open_merchant_id } }">
              <el-button size="small" type="primary">变更记录</el-button>
            </router-link>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <footer>
      <div class="left">
        <el-button type="success" size="small" @click="handleAddMerchant">
          添加
        </el-button>
      </div>
      <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size"
        :current-page="postData.page_no" :total="dataCount">
      </el-pagination>
    </footer>
    <div v-if="showEditMerchant">
      <EditMerchant :info="curInfo" v-model="showEditMerchant" @on-confirm="getList" />
    </div>
  </div>
</template>

<script>
import EditMerchant from './EditMerchant.vue'

export default {
  name: 'MerchantsByMonthlyPay',
  components: {
    EditMerchant
  },
  data() {
    return {
      statusInfoArr: [{
        key: 'NORMAL',
        name: '正常'
      }, {
        key: 'ABNORMAL',
        name: '异常'
      }, {
        key: 'PAUSING',
        name: '暂停中'
      }, {
        key: 'PAUSED',
        name: '已暂停'
      }, {
        key: 'CLOSING',
        name: '关闭中'
      }, {
        key: 'CLOSED',
        name: '已关闭'
      }, {
        key: 'RECOVERING',
        name: '恢复中'
      }],
      postData: {
        status: '',
        merchant_name: '',
        merchant_pid: '',
        page_size: 10,
        page_no: 1
      },
      curInfo: {},
      tableData: [],
      dataCount: 5,
      showEditMerchant: false
    }
  },
  methods: {
    statusNameByKey(key) {
      for (const iterator of this.statusInfoArr) {
        if (key === iterator.key) {
          return iterator.name
        }
      }
    },
    search() {
      this.postData.page_no = 1;
      this.getList();
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.getList();
    },
    getList() {
      this.$service.post('Web/ZhimaFitPay/merchants', this.postData).then(res => {
        if (res.data.errorcode == 0) {
          let list = res.data.data.list
          list.forEach(item => {
            item.logo_url = item.pic_data
          })
          this.tableData = list;
          this.dataCount = res.data.data.count;
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    },
    handleEditMerchant(info) {
      this.curInfo = info || {}
      this.showEditMerchant = true
    },
    handleAddMerchant(info) {
      this.curInfo = {}
      this.showEditMerchant = true
    },
    handleMerchantUploadLogs(info) {
      this.curInfo = info || {}
      this.showEditMerchant = true
    },
  },
  created() {
    this.getList()
  }
};
</script>

<style scoped>
.table-zoom-image {
  height: 30px;
}

.table-zoom-image>img {
  height: 100%;
}

.oneline {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  margin-top: 10px;
}

.oneline:first-child {
  margin-top: 0;
}

.dynamadd i {
  width: 10%;
  height: 100%;
  margin-left: 10px;
}

.w90 {
  width: 90%;
}

.bluefont {
  color: #409eff;
  cursor: pointer;
  width: 100%;
  display: inline-block;
}

.overtext {
  text-overflow: ellipsis;
  white-space: nowrap;
}

.qrpic {
  border: none;
  width: 32px;
  height: 32px;
  background: url(../../../assets/images/qrcode.png) no-repeat center;
  background-size: 32px 32px;
}

.flex-center {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}

.m-r-5 {
  margin-right: 5px;
}
</style>
