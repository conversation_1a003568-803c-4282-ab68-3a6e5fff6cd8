<template>
  <div class="come-down-box">
    <header>
      <el-input placeholder="商户名称" v-model="postData.merchant_name" @keyup.enter.native="search" class="search-item"
        clearable></el-input>
      <el-input placeholder="小程序名称" v-model="postData.app_name" @keyup.enter.native="search" class="search-item"
        clearable></el-input>
      <el-select v-model="postData.sale_id" placeholder="签约销售" class="search-item" clearable>
        <el-option v-for="item in saleList" :key="item.id" :label="item.sale_name" :value="item.id"></el-option>
      </el-select>
      <el-date-picker v-model="signDateRange" type="daterange" range-separator="至" start-placeholder="签约"
        end-placeholder="时间" clearable></el-date-picker>
      <el-date-picker v-model="deadlineDateRange" type="daterange" range-separator="至" start-placeholder="到期"
        end-placeholder="时间" clearable></el-date-picker>
      <el-button type="success" @click="search">搜索</el-button>
      <el-button @click="handleReset">重置</el-button>
    </header>
    <main>
      <el-table :data="tableData" @sort-change="onSortChange" stripe>
        <el-table-column type="index" label="序号" align="center" />
        <el-table-column prop="merchant_name" label="商户名称" align="center" />
        <el-table-column prop="app_name" label="支付宝小程序名称" align="center" />
        <el-table-column prop="sale_name" label="签约销售" align="center" />
        <el-table-column prop="contract_money" label="签约金额" sortable="custom" align="center" />
        <el-table-column prop="sign_time" label="签约时间" align="center" />
        <el-table-column prop="end_time" label="到期时间" align="center" />
        <el-table-column prop="alipay_cost_rate_label" label="安心付产品费率" align="center" />
        <el-table-column prop="signed_rate_label" label="佣金比例" align="center" />
        <el-table-column prop="open_transfer_pay" label="是否返佣" align="center">
          <template slot-scope="scope">
            {{scope.row.open_transfer_pay==2?'是':'否'}}
          </template>
        </el-table-column>
        <el-table-column prop="transfer_percent" label="返佣比例" align="center" />
        <el-table-column prop="transfer_account" label="返佣账号" align="center" />
        <el-table-column prop="transfer_name" label="返佣实名" align="center" />
        <el-table-column label="操作" align="center">
          <template scope="scope">
            <el-button type="primary" size="mini" style="margin: 0" @click="handleEdit(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
    </main>
    <footer>
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :current-page="postData.page_no" :page-sizes="[50, 100, 200, 500]" :page-size="postData.page_size"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </footer>

    <el-dialog title="编辑" :visible.sync="dialogVisible" width="min(40%, 600px)" :close-on-click-modal="false"
      :close-on-press-escape="false" :show-close="false">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="160px">
        <el-form-item label="商户名称">
          {{ formData.merchant_name }}
        </el-form-item>
        <el-form-item label="签约销售" prop="sale_id">
          <el-select v-model="formData.sale_id" placeholder="请选择">
            <el-option v-for="item in saleList" :key="item.id" :label="item.sale_name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="是否开放月付组合方案" prop="product_combine_open">
          <el-switch v-model="formData.product_combine_open" :active-value="2" :inactive-value="1"></el-switch>
        </el-form-item>
        <el-form-item label="是否开通立即扣款" prop="open_now_pay">
          <el-switch v-model="formData.open_now_pay" :active-value="2" :inactive-value="1"></el-switch>
        </el-form-item>
        <el-form-item label="支付宝小程序ID" prop="merchant_pid">
          <span>{{ formData.app_name }}</span>
          <el-tag type="info" style="margin-left: 10px">{{ formData.appid }}</el-tag>
        </el-form-item>
        <el-form-item label="签约金额" prop="contract_money">
          <el-input-number v-model="formData.contract_money" :min="0" :precision="2" :step="0.01"
            style="width: 100%"></el-input-number>
        </el-form-item>
        <el-form-item label="签约时间" prop="sign_time">
          <el-date-picker v-model="formData.sign_time" type="date" placeholder="选择日期" :clearable="false"
            style="width: 100%"></el-date-picker>
        </el-form-item>
        <el-form-item label="到期时间" prop="end_time">
          <el-date-picker v-model="formData.end_time" type="date" placeholder="选择日期" :clearable="false"
            style="width: 100%"></el-date-picker>
        </el-form-item>
        <el-form-item label="安心付产品费率" prop="alipay_cost_rate">
          <div style="display: flex; align-items: center; gap: 15px">
            <el-input-number v-model="formData.alipay_cost_rate" :min="0" :max="100" :step="0.01" :precision="2"
              style="width: 100%" disabled>
              <!-- <template slot="append">%</template> -->
            </el-input-number>
            <el-tag type="info">%</el-tag>
          </div>
        </el-form-item>
        <el-form-item label="佣金比例" prop="signed_rate">
          <div style="display: flex; align-items: center; gap: 15px">
            <el-input-number v-model="formData.signed_rate" :min="0" :max="100" :step="0.01" :precision="2"
              style="width: 100%">
              <!-- <template slot="append">%</template> -->
            </el-input-number>
            <el-tag type="info">%</el-tag>
          </div>
        </el-form-item>
         <el-form-item label="是否返佣" prop="open_transfer_pay">
          <el-switch v-model="formData.open_transfer_pay" :active-value="2" :inactive-value="1"></el-switch>
        </el-form-item>
        <el-form-item v-if="formData.open_transfer_pay === 2" label="返佣比例" prop="transfer_percent">
          <div style="display: flex; align-items: center; gap: 15px">
            <el-input-number v-model="formData.transfer_percent" :min="0.01" :max="100" :step="0.01" :precision="2"
              style="width: 100%">
            </el-input-number>
            <el-tag type="info">%</el-tag>
          </div>
        </el-form-item>
        <el-form-item v-if="formData.open_transfer_pay === 2" label="返佣账号" prop="transfer_account">
            <el-input v-model="formData.transfer_account" style="width: 100%" />
        </el-form-item>
        <el-form-item v-if="formData.open_transfer_pay === 2" label="返佣账号实名" prop="transfer_name">
            <el-input v-model="formData.transfer_name" style="width: 100%" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button type="primary" @click="handleSave">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { Message } from 'element-ui';
import dateFormat from 'dateformat';
import Big from 'big.js';
import _ from 'lodash';

const NONE_POST_DATA = {
  merchant_name: '',
  app_name: '',
  sale_id: '',
  sign_time_start: '',
  sign_time_end: '',
  end_time_start: '',
  end_time_end: '',
  open_transfer_pay: 1,
  transfer_account: '',
  transfer_percent: 0.01,
  transfer_name: '',
  sort: {
    name: '',
    type: ''
  },
  page_size: 50,
  page_no: 1,
  merchant_type: 2
};

const NONE_FORM_DATA = {
  product_combine_open: 1,
  open_now_pay: 1,
  contract_money: undefined,
  sign_time: null,
  end_time: null,
  alipay_cost_rate: undefined,
  signed_rate: undefined,
}

export default {
  name: 'RatesByComeDownPay',
  data() {
    return {
      signDateRange: [],
      deadlineDateRange: [],
      saleList: [],
      postData: _.cloneDeep(NONE_POST_DATA),
      tableData: [],
      total: 0,
      dialogVisible: false,
      formData: _.cloneDeep(NONE_FORM_DATA),
      formRules: {
        contract_money: [
          { required: true, message: '请输入签约金额', trigger: 'blur' },
        ],
        sign_time: [
          { required: true, message: '请选择签约时间', trigger: 'change' },
        ],
        end_time: [
          { required: true, message: '请选择到期时间', trigger: 'change' },
        ],
        signed_rate: [
          { required: true, message: '请输入佣金比例', trigger: 'blur' },
        ],
        transfer_account: [
          { required: true, message: '请输入', trigger: 'change' },
        ],
        transfer_percent: [
          { required: true, message: '请输入', trigger: 'change' },
        ],
        transfer_name: [
          { required: true, message: '请输入', trigger: 'change' },
        ],
      }
    }
  },
  // computed: {
  //   grossProfit() {
  //     if (this.dialogVisible) {
  //       return new Big(this.formData.signed_rate).minus(this.formData.alipay_cost_rate)
  //     } else {
  //       return 0
  //     }
  //   }
  // },
  methods: {
    search() {
      this.postData.page_no = 1;
      if (Array.isArray(this.signDateRange) && this.signDateRange.length === 2) {
        this.postData.sign_time_start = dateFormat(this.signDateRange[0], 'yyyy-mm-dd');
        this.postData.sign_time_end = dateFormat(this.signDateRange[1], 'yyyy-mm-dd');
      } else {
        this.postData.sign_time_start = '';
        this.postData.sign_time_end = '';
      }
      if (Array.isArray(this.deadlineDateRange) && this.deadlineDateRange.length === 2) {
        this.postData.end_time_start = dateFormat(this.deadlineDateRange[0], 'yyyy-mm-dd');
        this.postData.end_time_end = dateFormat(this.deadlineDateRange[1], 'yyyy-mm-dd');
      } else {
        this.postData.end_time_start = '';
        this.postData.end_time_end = '';
      }
      this.getList();
    },
    handleReset() {
      this.postData = _.cloneDeep(NONE_POST_DATA);
      this.signDateRange = [];
      this.deadlineDateRange = [];
      this.getList();
    },
    getList() {
      this.$service.post('Web/ZhimaFitPay/rates', this.postData).then(res => {
        if (res.data.errorcode === 0) {
          const list = res.data.data.list;
          list.forEach(item => {
            item.alipay_cost_rate_label = `${item.alipay_cost_rate}%`;
            item.signed_rate_label = `${item.signed_rate}%`;
          })

          this.tableData = list;
          this.total = res.data.data.count;
        } else {
          Message.error(res.data.errormsg);
        }
      })
    },
    handleSizeChange(val) {
      this.postData.page_no = 1;
      this.postData.page_size = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.postData.page_no = val;
      this.getList();
    },
    onSortChange({ column, prop, order }) {
      let sortType = ''
      let sortKey = prop
      if (order === 'ascending') {
        sortType = 'asc'
      } else if (order === 'descending') {
        sortType = 'desc'
      } else {
        sortKey = ''
      }
      this.postData.sort = {
        type: sortType,
        name: sortKey
      }
      this.getList()
    },
    getSaleList() {
      this.$service.post('/Web/ZhimaFitPay/sales', {
        _no_paginate: true,
        status: 0,
        merchant_type: 2
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.saleList = res.data.data;
        } else {
          Message.error(res.data.errormsg);
        }
      }).catch(e => {
        throw new Error(e)
      })
    },
    handleAdd() {
      this.dialogVisible = true;
      this.$nextTick(() => {
        this.$refs.formRef.clearValidate();
      })
    },
    handleEdit(row) {
      this.dialogVisible = true
      this.formData = {
        ...row,
        product_combine_open: Number(row.product_combine_open),
        open_now_pay: Number(row.open_now_pay),
      }
    },
    handleSave: _.throttle(function () {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          if (new Date(this.formData.end_time).getTime() < new Date(this.formData.sign_time).getTime()) {
            this.$message.error('到期时间必须大于签约时间')
            return
          } else {
            this.formData.sign_time = dateFormat(this.formData.sign_time, 'yyyy-mm-dd')
            this.formData.end_time = dateFormat(this.formData.end_time, 'yyyy-mm-dd')
          }
          // this.formData.profit_rate = this.grossProfit

          this.$service.post('Web/ZhimaFitPay/upsertRate', {
            ...this.formData,
            merchant_type: 2
          }).then(res => {
            if (res.data.errorcode === 0) {
              this.dialogVisible = false
              Message.success(res.data.errormsg)
              this.getList();
            } else {
              Message.error(res.data.errormsg)
            }
          })
        }
      })
    }, 3000),
    handleCancel() {
      this.formData = _.cloneDeep(NONE_FORM_DATA);
      this.dialogVisible = false
    }
  },
  created() {
    this.getList();
    this.getSaleList();
  }
}
</script>

<style lang="less" scoped>
.come-down-box {
  padding: 20px 0;

  header {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    gap: 15px;
    padding: 0 20px;

    .search-item {
      width: 200px;
    }
  }

  main {
    margin-top: 20px;
    border-top: 1px solid #dddee1;
  }

  footer {
    display: flex;
    flex-direction: row;
    justify-content: flex-end;
    align-items: center;
    margin-top: 20px;
    padding: 0 20px;
  }
}
</style>