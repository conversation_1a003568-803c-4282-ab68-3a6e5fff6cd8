<template>
  <el-select
    v-model="selectedId"
    @change="changeInfo"
    filterable
    remote
    clearable
    placeholder="场馆名称选择"
    :remote-method="remoteMethod"
    :loading="loading">
    <el-option
      v-for="item in options"
      :key="item.id"
      :label="item.name"
      :value="item.id">
      <span>{{item.name}}</span>
      <span style="float:right;color:#ccc">ID:{{item.id}}</span>
    </el-option>
  </el-select>
</template>
<script>
export default {
  name: 'BusSearchSelect',
  props: {
    value: {
      type: [String, Number],
      default: () => ''
    },
    isEdit: {
      type: Boolean,
      default: () => false
    }
  },
  data() {
    return {
      name: '',
      options: [],
      loading: false,
      busList: []
    }
  },
  computed: {
    selectedId: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  watch: {
     isEdit: {
      handler(val) {
        if (val){
          if(this.value) {
            this.remoteMethod(this.value, true)
          } else {
            this.selectedId = ''
          }
        }
      },
      immediate: true
    }
  },
  created() {
    this.getBusList()
  },
  methods: {
    changeInfo(id) {
      let curInfo = ''
      for (const iterator of this.options) {
        if(iterator.id == id) {
          curInfo = iterator
          break;
        }
      }
      this.$emit('on-change', curInfo)
      console.log(curInfo);
    },
    getBusList() {
      this.$service.post('web/business/bus_list').then(res => {
        if (res.data.errorcode == 0) {
          this.busList = res.data.data.bus_list;
          if(this.isEdit) {
            this.remoteMethod(this.value, true)
          }
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    },
    remoteMethod(query, isEdit) {
      if (query !== '') {
        this.options = this.busList.filter(item => {
          return isEdit?item.id==query:(`${item.name}(${item.id})`).toLowerCase().indexOf(query.toLowerCase()) > -1 && item.is_expire!=1;
        });
      } else {
        this.options = [];
      }
    }
  }
}
</script>
<style lang="less" scoped>

</style>
