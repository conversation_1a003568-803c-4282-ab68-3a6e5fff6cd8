<template>
  <el-dialog
    ref="info"
    title="扣款计划"
    :visible.sync="showDialog"
    width="800px"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
  <div>
    <div>
     产品名称：   {{info.product_title}}
    </div>
    <div>
     手机号：  {{info.phone}}
    </div>
    <div>
     订购编号：   {{info.subscription_no}}
    </div>
  </div>
   <el-table :data="info.items" stripe style="width: 100%">
      <el-table-column prop="period" label="期数"  align="center"></el-table-column>
      <el-table-column prop="plan_deduction_time" label="计划扣款日期"  align="center"></el-table-column>
      <el-table-column prop="actual_deduction_time" label="实际扣款日期"  align="center"></el-table-column>
      <el-table-column prop="deduction_amount" label="扣款金额"  align="center"></el-table-column>
      <el-table-column prop="order_no" label="订单编号"  align="center"></el-table-column>
      <el-table-column prop="status" label="订单状态"  align="center">
        <template scope="scope">
              {{statusNameByKey(scope.row.status)}}
        </template>
      </el-table-column>
      <!-- <el-table-column label="操作" align="center">
        <template scope="scope">
            <el-button v-if="scope.row.status=='ORDERED' || scope.row.status=='UNCREATED'" type="text" size="small" @click="cancelOrder(scope.row)">
              取消订购
            </el-button>
        </template>
      </el-table-column> -->
    </el-table>
   
    <div slot="footer">
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'ContractInfo',
  components: {
  },
  props: {
    value: {
      type: Boolean,
      default: false
    },
    info: {
      type: Object
    }
  },
  data() {
    return {
      statusInfoArr: [{
        key:'PAUSED',
        name: '已暂停' 
      },{
        key:'ORDERING',
        name: '下单中' 
      },{
        key:'ORDERED',
        name: '下单成功' 
      },{
        key:'PAID',
        name: '支付成功' 
      },{
        key:'PAY_FAILED',
        name: '扣款失败' 
      },{
        key:'REFUNDED',
        name: '已全额退款' 
      },{
        key:'UNCREATED',
        name: '未生成订单' 
      },{
        key:'CANCEL',
        name: '已取消' 
      }],
      infoData: {
        items: [],
        merchant_pid: '',
        smid: '',
        merchant_name: '',
        ledger_rate: '',
        phone: '',
        comment: ''
      }
    }
  },
  watch: {
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    }
  },
  created () {
  },
  methods: {
    onDialogClose() {
      this.$emit('input', false)
    },
    statusNameByKey(key) {
      for (const iterator of this.statusInfoArr) {
        if(key === iterator.key) {
          return iterator.name
        }
      }
    },
    cancelOrder(obj) {
      this.$service.post('Web/ZhimaFitPay/subscriptionSpecificCancel', {
        open_merchant_id: this.info.open_merchant_id,
        store_no: this.info.store_no,
        subscription_no: this.info.subscription_no,
        periods: obj.period
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.showDialog = false
          this.$emit('on-confirm')
          _g.toastMsg('success', '操作成功');
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    }
  }
}
</script>
<style lang="less" scoped>

</style>



