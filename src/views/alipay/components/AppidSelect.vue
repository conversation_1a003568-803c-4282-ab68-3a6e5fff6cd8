<template>
  <el-select
    v-model="adString"
    class="filter-item"
    :placeholder="placeholder"
    :filterable="filterable"
    :clearable="clearable"
    :disabled="disabled"
    :multiple="multiple"
    @change="adChange"
  >
    <slot />
    <el-option
      v-for="item in adOptions"
      :key="item.auth_app_id"
      :label="item.app_name"
      :value="item.auth_app_id"
    />
  </el-select>
</template>

<script>
export default {
  name: 'AppidSelect',
  props: {
    value: {
      required: true
    },
    placeholder: {
      type: String,
      default: '请选择小程序'
    },
    filterable: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    },
    multiple:  {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      adOptions: []
    }
  },
  computed: {
    adString: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
  },
  created() {
    this.initData()
  },
  methods: {
    adChange(id) {
      let info = ''
      if (!this.multiple) {
        this.adOptions.forEach((item, index) => {
          if (item.app_name === this.adString) {
            info = item
          }
        })
        this.$emit('on-change', info)
      } else {
        this.$emit('on-change', id)
      }
    },
    initData() {
      this.$service
        .post('/Web/AlipayIsv/getAllAppList', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.adOptions = res.data.data.list
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
     
    }
  }
}
</script>
