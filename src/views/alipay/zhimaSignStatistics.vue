<template>
  <div class="box">
    <el-row class="box-head">
      <el-col :offset="1" :span="22" class="head-option">
        <el-select class="option-select" placeholder="年份" v-model="yearValue">
          <el-option v-for="year in yearList" :value="year" :label="year + ' 年'" :key="year"></el-option>
        </el-select>
        <el-select class="option-select" placeholder="月份" v-model="monthValue">
          <el-option v-for="month in monthList" :value="month" :label="month + ' 月'" :key="month"></el-option>
        </el-select>
        <el-select class="option-select" placeholder="签单销售" v-model="postData.sale_id" clearable>
          <el-option v-for="sale in saleList" :value="sale.id" :label="sale.sale_name" :key="sale.id"></el-option>
        </el-select>
        <el-button type="success" @click="handleSearch">搜索</el-button>
      </el-col>
    </el-row>
    <el-row class="box-body table-wrap">
      <el-col :span="24">
        <el-table ref="table" stripe :data="list" @sort-change="handleSortChange">
          <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
          <el-table-column label="签约商家" prop="merchant_name"></el-table-column>
          <el-table-column label="签约销售" prop="sale_name"></el-table-column>
          <el-table-column label="签约总数" prop="all_subscription_count" sortable="custom"></el-table-column>
          <el-table-column label="合约履约数" prop="exercise_subscription_count" sortable="custom"></el-table-column>
          <el-table-column label="扣款失败订单数" prop="pay_failed_subscription_count" sortable="custom"></el-table-column>
          <el-table-column label="合约终止数" prop="abort_subscription_count" sortable="custom"></el-table-column>
          <el-table-column label="签约总金额" prop="all_subscription_money" sortable="custom">
            <template slot-scope="scope">
              ¥{{ scope.row.all_subscription_money }}
            </template>
          </el-table-column>
          <el-table-column label="支付总金额" prop="pay_subscription_money" sortable="custom">
            <template slot-scope="scope">
              ¥{{ scope.row.pay_subscription_money }}
            </template>
          </el-table-column>
          <el-table-column label="未支付总金额" prop="no_pay_subscription_money" sortable="custom">
            <template slot-scope="scope">
              ¥{{ scope.row.no_pay_subscription_money }}
            </template>
          </el-table-column>
          <el-table-column label="解约总金额" prop="surrender_subscription_money" sortable="custom">
            <template slot-scope="scope">
              ¥{{ scope.row.surrender_subscription_money }}
            </template>
          </el-table-column>
          <el-table-column label="手续费" prop="transaction_subscription_money" sortable="custom">
            <template slot-scope="scope">
              ¥{{ scope.row.transaction_subscription_money }}
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-row class="box-foot">
      <el-col :offset="1" :span="22" class="foot-option">
        <div class="option-ctrl">
          <el-button @click="handleExcel">导出Excel</el-button>
        </div>
        <el-pagination @current-change="handleCurrentChange" @size-change="handleSizeChange"
          layout="prev, pager, next, total, sizes, jumper" :page-size="postData.per_page" :current-page="postData.page"
          :total="total">
        </el-pagination>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="js">
import ExportCsv from 'src/components/form/csvExport';

export default {
  name: 'AlipaySignStatistics',
  data() {
    const startYear = 2022
    const currentYear = new Date().getFullYear()
    const yearList = []
    for (let i = startYear; i <= currentYear; i++) {
      yearList.push(i)
    }
    return {
      yearValue: currentYear,
      yearList,
      monthValue: new Date().getMonth() + 1,
      monthList: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
      saleList: [],
      list: [],
      total: 0,
      postData: {
        date: '',
        sale_id: '',
        page: 1,
        per_page: 10,
        sort: {
          name: '',
          type: '',
        },
      }
    }
  },
  methods: {
    handleSearch() {
      this.postData.page = 1
      this.getList()
    },
    async handleExcel() {
      const prefixMonthValue = this.monthValue < 10 ? '0' + this.monthValue : this.monthValue
      const fakePost = {
        ...this.postData,
        date: this.yearValue + '-' + prefixMonthValue,
        page: 1,
        per_page: this.total,
        is_export: 1
      }
      const fakeData = await this.$service.post('/Web/ZhimaFitPay/getStatisticsListAll', fakePost)
      let fakeList = []
      if (fakeData.data.errorcode === 0 && Array.isArray(fakeData.data.data.list)) {
        fakeList = fakeData.data.data.list
      }

      const columns = [
        {
          label: '签约商家',
          prop: 'merchant_name',
        },
        {
          label: '签约销售',
          prop: 'sale_name',
        },
        {
          label: '签约总数',
          prop: 'all_subscription_count',
          sortable: true
        },
        {
          label: '合约履约数',
          prop: 'exercise_subscription_count',
          sortable: true
        },
        {
          label: '扣款失败订单数',
          prop: 'pay_failed_subscription_count',
          sortable: true
        },
        {
          label: '合约终止数',
          prop: 'abort_subscription_count',
          sortable: true
        },
        {
          label: '签约总金额（￥）',
          prop: 'all_subscription_money',
          sortable: true
        },
        {
          label: '支付总金额（￥）',
          prop: 'pay_subscription_money',
          sortable: true
        },
        {
          label: '未支付总金额（￥）',
          prop: 'no_pay_subscription_money',
          sortable: true
        },
        {
          label: '解约总金额（￥）',
          prop: 'surrender_subscription_money',
          sortable: true
        },
        {
          label: '手续费（￥）',
          prop: 'transaction_subscription_money',
          sortable: true
        }
      ]
      const filename = '签约商户月付数据统计-' + this.yearValue + '-' + prefixMonthValue
      ExportCsv(fakeList, columns, filename);
    },
    handleCurrentChange(val) {
      this.postData.page = val
      this.getList()
    },
    handleSizeChange(val) {
      this.postData.page = 1
      this.postData.per_page = val
      this.getList()
    },
    handleSortChange({ column, prop, order }) {
      let sortType = ''
      if (order === 'ascending') {
        sortType = 'ASC'
      } else if (order === 'descending') {
        sortType = 'DESC'
      }
      this.postData.sort.name = prop
      this.postData.sort.type = sortType
      this.postData.page = 1
      this.getList()
    },
    getList() {
      // if the monthValue less than 10, prefix '0'
      const prefixMonthValue = this.monthValue < 10 ? '0' + this.monthValue : this.monthValue
      this.postData.date = this.yearValue + '-' + prefixMonthValue
      return this.$service.post('/Web/ZhimaFitPay/getStatisticsListAll', this.postData).then(({ data }) => {
        if (data.errorcode === 0) {
          this.list = data.data.list
          this.total = Number(data.data.count)
        } else {
          this.$message.error(data.errormsg)
        }
      })
    },
    getSaleList() {
      return this.$service.post('/Web/ZhimaFitPay/sales', {
        _no_paginate: true,
        status: 0
      }).then(({ data }) => {
        if (data.errorcode === 0) {
          this.saleList = data.data
        } else {
          this.$message.error(data.errormsg)
        }
      })
    }
  },
  created() {
    this.getSaleList()
    this.getList()
  }
}
</script>

<style lang="less" scoped>
.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-select {
        width: 120px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
      }

    }
  }
}
</style>