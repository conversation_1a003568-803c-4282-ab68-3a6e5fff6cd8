<template>
  <div class="container">
    <!-- <header>
       <el-date-picker v-model="rangeArray" :clearable="true" class="m-r-10" type="daterange" @change="pickChange" placeholder="选择日期"></el-date-picker>
        <el-input placeholder="操作账号" v-model="postData.operator_name" class="w-150 m-r-10"></el-input>
        <el-input placeholder="设备ID" v-model="postData.device_id" class="w-150 m-r-10"></el-input>
        <el-button type="success" @click="search">查询</el-button>
    </header> -->
    <el-table :data="tableData" stripe>
      <el-table-column align="center" prop="create_time" label="操作时间"></el-table-column>
      <el-table-column align="center" prop="merchant_pid" label="商户ID"></el-table-column>
      <el-table-column align="center" prop="merchant_name" label="商户名称"></el-table-column>
      <el-table-column align="center" prop="operator_name" label="操作人"></el-table-column>
      <el-table-column align="center" prop="change_info" label="变更信息"></el-table-column>
    </el-table>
     <!-- <footer>
      <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount">
        </el-pagination>
    </footer> -->

  </div>
</template>

<script>

export default {
  name: 'merchantUpdateLogs',
  data() {
    return {
      // dataCount: 0,
      rangeArray: [],
      postData: {
        page_no: 1, //页码
        page_size: 10,
        // operator_name: '',
        open_merchant_id: '',
        // start_time: '',
        // end_time: '',
      },
      tableData: [],
    }
  },
  created() {
    this.postData.open_merchant_id = this.$route.params.open_merchant_id || ''
    this.getList()
  },
  watch: {},
  methods: {
    // search() {
    //   this.postData.page_no = 1
    //   this.getList()
    // },
    // pickChange(val) {
    //   this.postData.start_time = val ? _g.formatDate(val[0], 'yyyy-MM-dd') : '';
    //   this.postData.end_time = val ? _g.formatDate(val[1], 'yyyy-MM-dd') : '';
    // },
    // getDate() {
    //   let d = new Date();
    //   return `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()}`;
    // },
    handleCurrentChange(val) {
      this.postData.page_no = val
      this.getList()
    },
    getList() {
      return this.$service.post('/Web/ZhimaFitPay/merchantUpdateLogs', this.postData).then(res => {
        if (res.data.errorcode == 0) {
          this.tableData = res.data.data;
          // this.dataCount = res.data.data.count;
        } else {
          this.$message.error(res.data.errormsg);
        }
      }).catch(e => {
        console.error(e);
      })
    }
  }
}
</script>