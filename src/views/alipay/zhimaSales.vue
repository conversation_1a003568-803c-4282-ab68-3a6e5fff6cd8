<template>
  <div class="box">
    <el-row class="box-head">
      <el-col :offset="1" :span="22" class="head-option">
        <el-input v-model="postData.sale_name" class="option-input" placeholder="请输入姓名..."
          @keyup.enter.native="handleSearch" clearable></el-input>
        <el-select v-model="postData.status" class="option-select" placeholder="请选择状态..." clearable>
          <!-- 状态 0正常 1离职 -->
          <el-option value="0" label="正常"></el-option>
          <el-option value="1" label="离职"></el-option>
        </el-select>
        <el-button type="success" @click="handleSearch">搜索</el-button>
        <el-button @click="handleReset">重置</el-button>
      </el-col>
    </el-row>
    <el-row class="box-body table-wrap">
      <el-col :span="24">
        <el-table ref="table" stripe :data="list">
          <el-table-column type="index" label="序号" width="80" align="center"></el-table-column>
          <el-table-column label="姓名" prop="sale_name"></el-table-column>
          <el-table-column label="联系方式" prop="phone"></el-table-column>
          <el-table-column label="签约客户数" prop="merchants_count"></el-table-column>
          <el-table-column label="状态" prop="status">
            <template slot-scope="scope">
              <span v-if="scope.row.status == 0" style="color:#67C23A">正常</span>
              <span v-else style="color:#F56C6C">离职</span>
            </template>
          </el-table-column>
          <el-table-column label="操作">
            <template slot-scope="scope">
              <el-button type="text" @click="handleEdit(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </el-col>
    </el-row>
    <el-row class="box-foot">
      <el-col :offset="1" :span="22" class="foot-option">
        <div class="option-ctrl">
          <el-button type="success" size="mini" @click="handleAdd">添加</el-button>
        </div>
        <el-pagination @current-change="handleCurrentChange" @size-change="handleSizeChange"
          layout="prev, pager, next, total, sizes, jumper" :page-size="postData.page_size"
          :current-page="postData.page_no" :total="total">
        </el-pagination>
      </el-col>
    </el-row>

    <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="30%" :before-close="handleClose">
      <el-form ref="formRef" :model="formData" :rules="formRules" label-width="120px" size="mini" label-position="left">
        <el-form-item label="姓名" prop="sale_name">
          <el-input v-model="formData.sale_name" :disabled="!dialogEdit"></el-input>
        </el-form-item>
        <el-form-item label="联系方式" prop="phone">
          <el-input v-model="formData.phone" :disabled="!dialogEdit"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <template v-if="formData.status == 0">
          <el-button v-if="!dialogEdit" type="primary" @click="dialogEdit = true"
            style="margin-right: 20px;">编辑</el-button>
          <el-popover v-if="!dialogEdit" placement="top" width="160" v-model="popoverVisible">
            <h4>签约客户转移</h4>
            <p>因销售人员 "{{ formData.sale_name }}" 离职，请将销售人员的签约客户关系转移到其他已在职的销售人员上</p>
            <el-select v-model="resignData.new_sale_id" placeholder="请选择销售人员...">
              <el-option v-for="sale in saleList" :value="sale.id" :label="sale.sale_name" :key="sale.id"
                :disabled="sale.id === formData.id || sale.status == 1"></el-option>
            </el-select>
            <div style="text-align: right; margin-top: 20px;">
              <el-button size="mini" type="text" @click="popoverVisible = false">取消</el-button>
              <el-button type="primary" size="mini" @click="handleResign">确定</el-button>
            </div>
            <el-button slot="reference" type="danger" style="margin-right: 20px;">离职</el-button>
          </el-popover>
          <el-button v-else type="primary" @click="handleSave" style="margin-right: 20px;">确认</el-button>
        </template>
        <el-button @click="handleClose">取消</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
const NONE_POST_DATA = {
  sale_name: '',
  status: '',
  page_no: 1,
  page_size: 50
}

export default {
  name: 'AlipaySales',
  data() {
    return {
      postData: {
        ...NONE_POST_DATA
      },
      total: 0,
      list: [],
      dialogVisible: false,
      dialogEdit: false,
      formData: {
        id: '',
        sale_name: '',
        phone: '',
      },
      formRules: {
        sale_name: [
          { required: true, message: '请输入姓名', trigger: 'blur' },
        ],
        phone: [
          { required: true, message: '请输入联系方式', trigger: 'blur' },
          { pattern: /^1[3456789]\d{9}$/, message: '请输入正确的联系方式', trigger: 'blur' }
        ]
      },
      popoverVisible: false,
      saleList: [],
      resignData: {
        sale_id: '',
        new_sale_id: ''
      }
    }
  },
  computed: {
    dialogTitle() {
      if (this.dialogEdit) {
        if (this.formData.id) {
          return '编辑销售人员'
        } else {
          return '添加销售人员'
        }
      } else {
        return '查看销售人员'
      }
    }
  },
  methods: {
    handleSearch() {
      this.postData.page_no = 1
      this.getList()
    },
    handleReset() {
      this.postData = {
        ...NONE_POST_DATA
      }
      this.getList()
    },
    getList() {
      this.$service.post('/Web/ZhimaFitPay/sales', this.postData).then(({ data }) => {
        if (data.errorcode === 0) {
          this.list = data.data.list
          this.total = Number(data.data.count)
        } else {
          this.$message.error(data.errormsg)
        }
      })
    },
    getSaleList() {
      this.$service.post('/Web/ZhimaFitPay/sales', {
        _no_paginate: true,
        status: 0
      }).then(({ data }) => {
        if (data.errorcode === 0) {
          this.saleList = data.data
        } else {
          this.$message.error(data.errormsg)
        }
      })
    },
    handleCurrentChange(val) {
      this.postData.page_no = val
      this.getList()
    },
    handleSizeChange(val) {
      this.postData.page_no = 1
      this.postData.page_size = val
      this.getList()
    },
    handleAdd() {
      this.formData = {
        id: '',
        sale_name: '',
        phone: '',
        status: 0
      }
      this.dialogEdit = true
      this.dialogVisible = true
    },
    handleEdit(row) {
      this.resignData = {
        sale_id: '',
        new_sale_id: ''
      }
      this.getSaleList()
      this.formData = {
        id: row.id,
        sale_name: row.sale_name,
        phone: row.phone,
        status: row.status
      }
      this.dialogEdit = false
      this.dialogVisible = true
    },
    handleClose() {
      this.$refs.formRef.resetFields()
      this.dialogEdit = false
      this.dialogVisible = false
    },
    handleSave() {
      this.$refs.formRef.validate((valid) => {
        if (valid) {
          this.formData.profit_rate = this.grossProfit
          this.$service.post('/Web/ZhimaFitPay/upsertSale', {
            ...this.formData
          }).then(({ data }) => {
            if (data.errorcode === 0) {
              this.dialogVisible = false
              this.getList()
              this.$message.success('保存成功')
            } else {
              this.$message.error(data.errormsg)
            }
          })
        }
      })
    },
    handleResign() {
      if (!this.resignData.new_sale_id) {
        this.$message.error('请选择销售人员')
        return
      }
      this.resignData.sale_id = this.formData.id
      return this.$service.post('/Web/ZhimaFitPay/depart', {
        ...this.resignData
      }).then(({ data }) => {
        if (data.errorcode === 0) {
          this.popoverVisible = false
          this.dialogEdit = false
          this.dialogVisible = false
          this.getList()
          this.$message.success('离职成功')
        } else {
          this.$message.error(data.errormsg)
        }
      })
    }
  },
  created() {
    this.getList()
  }
}
</script>

<style lang="less" scoped>
.box {
  background-color: #fff;

  .box-head {
    border: 1px solid #dddee1;

    .head-option {
      height: 75px;
      display: flex;
      flex-direction: row;
      align-items: center;

      .option-input,
      .option-select {
        width: 200px;
        margin-right: 20px;
      }
    }
  }

  .box-body {
    border-left: 1px solid #dddee1;
  }

  .box-foot {
    border: 1px solid #dddee1;
    border-top: none;

    .foot-option {
      height: 80px;
      display: flex;
      flex-direction: row;
      justify-content: space-between;
      align-items: center;

      .option-ctrl {
        display: flex;
        flex-direction: row;
      }

    }
  }

  .tips {
    font-size: 12px;
    color: #999;
    margin-left: 5px;
  }
}
</style>