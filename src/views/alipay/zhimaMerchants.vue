<template>
  <el-tabs v-model="activeName" type="border-card">
    <el-tab-pane label="安心付" name="comeDown">
      <transition name="el-zoom-in-center">
        <merchants-by-come-down-pay v-show="activeName === 'comeDown'" />
      </transition>
    </el-tab-pane>
    <el-tab-pane label="先享后付" name="monthly">
      <transition name="el-zoom-in-center">
        <merchants-by-monthly-pay v-show="activeName === 'monthly'" />
      </transition>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import MerchantsByComeDownPay from './components/MerchantsByComeDownPay.vue'
import MerchantsByMonthlyPay from './components/MerchantsByMonthlyPay.vue'

export default {
  name: 'zhimaMerchants',
  components: {
    MerchantsByComeDownPay,
    MerchantsByMonthlyPay
  },
  data() {
    return {
      activeName: 'comeDown'
    }
  },
}
</script>
