<template>
  <div class="container">
    <header>
     <AppidSelect v-model="postData.auth_app_id"/>
     <ServiceCategory v-model="postData.category_id"/>
     <el-select v-model="postData.status" class="w-200" placeholder="状态" clearable>
        <el-option
        v-for="item in statusInfoArr"
        :key="item.key"
        :label="item.name"
        :value="item.key">
      </el-option>
      </el-select>
      <el-button type="success" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="auth_app_id" label="appId" align="center"></el-table-column>
      <el-table-column prop="app_name" label="名称"  align="center"></el-table-column>
      <el-table-column prop="service_name" label="服务名称" align="center"></el-table-column>
      <el-table-column prop="service_code" label="服务编码" align="center"></el-table-column>
      <el-table-column prop="create_time" label="提报时间" align="center"></el-table-column>
      <el-table-column prop="audit_status_txt" label="状态" align="center"></el-table-column>
      <el-table-column prop="remark" label="补充说明" align="center" show-overflow-tooltip></el-table-column>
      <el-table-column align="center" label="操作">
        <template scope="scope">
          <el-button size="small" @click="handleEdit(scope.row)" type="primary">编辑</el-button>
          <el-button size="small" v-if="scope.row.audit_status == 4" @click="handleBindStore(scope.row)" type="primary">内容同步</el-button>
          <el-button size="small" v-if="scope.row.audit_status!=2" @click="handleDelete(scope.row.id)" type="danger">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
     <footer>
      <div class="left">
        <el-button type="success" size="small" @click="handleUplodVersion">
          服务提报
        </el-button>
      </div>
      <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
      </el-pagination>
    </footer>
    <div v-if="showAddService">
      <AddService type="record" :info="curInfo"  v-model="showAddService" @on-confirm="getList"/>
    </div>
     <ApplyService v-model="showUploadVersion" @on-confirm="getList"/>
  </div>
</template>

<script>
  import AddService from './components/AddService.vue'
  import ApplyService from './components/ApplyService.vue'
  import ServiceCategory from './components/ServiceCategory.vue'
   import AppidSelect from './components/AppidSelect.vue'
  export default {
    name: 'serviceRecord',
    components: {
      AppidSelect,
      ServiceCategory,
      AddService,
      ApplyService
    },
    data() {
      return {
         statusInfoArr: [{
          key:'1',
          name: '编辑中' 
        },{
          key:'2',
          name: '审核中' 
        },{
          key:'3',
          name: '审核驳回' 
        },{
          key:'4',
          name: '可推广' 
        },{
          key:'5',
          name: '停止推广' 
        },{
          key:'6',
          name: '失效' 
        },{
          key:'7',
          name: '处罚' 
        }],
        postData: {
          auth_app_id: '',
          category_id: '',
          status: '',
          page_size: 10,
          page_no: 1
        },
        tableData: [],
        dataCount: 5,
        appIdList: [],
        showAddService: false,
        showUploadVersion: false
      }
    },
    methods: {
      search() {
        this.postData.page_no = 1;
        this.getList();
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.getList();
      },
      getList() {
        this.$service.post('Web/AlipayIsv/serviceSchemaList', this.postData).then(res => {
          if (res.data.errorcode == 0) {
            this.tableData = res.data.data.list;
            this.dataCount = res.data.data.count;
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      handleDelete(id) {
        this.$confirm('确定删除?', '提示', {
          type: 'warning'
        }).then(() => {
          this.$service
            .post('/Web/AlipayIsv/serviceSchemaDelete', { ids: [id] })
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$message.success(res.data.errormsg);
                this.getList();
              } else {
                this.$message.error(res.data.errormsg);
              }
            })
        })
      },
      handleEdit(info) {
        this.curInfo = info || {}
        this.showAddService = true
      },
      handleBindStore(info) {
        this.$confirm('确定通过内容同步功能将服务与该商家下蚂蚁门店绑定?', '提示', {
          type: 'warning'
        }).then(() => {
          this.$service
            .post('/Web/AlipayIsv/serviceContentSync', { auth_app_id: info.auth_app_id, service_code: info.service_code })
            .then(res => {
              if (res.data.errorcode === 0) {
                this.$message.success(res.data.errormsg);
              } else {
                this.$message.error(res.data.errormsg);
              }
            })
        })
      },
      handleUplodVersion() {
        this.showUploadVersion = true
      }
    },
    created() {
      this.getList()
    }
  };
</script>

<style scoped>
  .table-zoom-image {
    height: 30px;
  }
  .table-zoom-image > img {
    height: 100%;
  }
  .oneline {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    margin-top: 10px;
  }
  .oneline:first-child {
    margin-top: 0;
  }
  .dynamadd i {
    width: 10%;
    height: 100%;
    margin-left: 10px;
  }
  .w90 {
    width: 90%;
  }
  .bluefont {
    color: #409eff;
    cursor: pointer;
    width: 100%;
    display: inline-block;
  }
  .overtext {
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .qrpic {
    border: none;
    width: 32px;
    height: 32px;
    background: url(../../assets/images/qrcode.png) no-repeat center;
    background-size: 32px 32px;
  }
  .flex-center {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
</style>








