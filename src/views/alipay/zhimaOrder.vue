<template>
  <div class="container">
    <header>
      <el-input placeholder="手机号" v-model="postData.phone" class="w-200" clearable></el-input>
      <el-input placeholder="订单编号" v-model="postData.order_no" class="w-200" clearable></el-input>
      <el-input placeholder="芝麻订单号" v-model="postData.credit_biz_order_id" class="w-200" clearable></el-input>
      <el-input placeholder="订购编号" v-model="postData.subscription_no" class="w-200" clearable></el-input>
      <BusSearchSelect v-model="postData.store_no" />
       <el-select v-model="postData.status" class="w-200" placeholder="订单状态" clearable>
        <el-option
          v-for="item in statusInfoArr"
          :key="item.key"
          :label="item.name"
          :value="item.key">
        </el-option>
      </el-select>
      <el-button type="success" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="order_no" label="订单编号"  align="center"></el-table-column>
      <el-table-column prop="credit_biz_order_id" label="芝麻后付订单号"  align="center"></el-table-column>
      <el-table-column prop="subscription_no" label="订购编号"  align="center"></el-table-column>
      <el-table-column prop="phone" label="手机号"  align="center"></el-table-column>
      <el-table-column prop="product_title" label="产品名称"  align="center"></el-table-column>
      <el-table-column prop="store_title" label="场馆名称"  align="center"></el-table-column>
      <el-table-column prop="status" label="订单状态"  align="center">
        <template scope="scope">
            {{statusNameByKey(scope.row.status)}}
        </template>
      </el-table-column>
      <el-table-column prop="order_date" label="下单时间"  align="center"></el-table-column>
      <el-table-column prop="plan_deduction_time" label="计划扣款时间"  align="center"></el-table-column>
      <el-table-column prop="actual_deduction_time" label="实际扣款时间"  align="center"></el-table-column>
      <el-table-column prop="deduction_amount" label="扣款金额"  align="center"></el-table-column>
      <el-table-column prop="deduction_fail_times" label="扣款失败次数"  align="center"></el-table-column>
      <el-table-column prop="deduction_fail_reason" label="扣款失败原因"  align="center"></el-table-column>
      <!-- <el-table-column label="操作" align="center">
        <template scope="scope">
            <el-button v-if="scope.row.can_refund" type="primary" size="small" @click="handleRefund(scope.row)">
              退款
            </el-button>
        </template>
      </el-table-column> -->
    </el-table>

    <footer>
      <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
      </el-pagination>
    </footer>
  </div>
</template>

<script>
  import BusSearchSelect from './components/BusSearchSelect.vue'
  export default {
    name: 'ZhimaOrder',
    components: {
      BusSearchSelect
    },
    data() {
      return {
        statusInfoArr: [{
          key:'PAUSED',
          name: '已暂停' 
        },{
          key:'ORDERING',
          name: '下单中' 
        },{
          key:'ORDERED',
          name: '下单成功' 
        },{
          key:'PAID',
          name: '支付成功' 
        },{
          key:'PAY_FAILED',
          name: '扣款失败' 
        },{
          key:'REFUNDED',
          name: '已全额退款' 
        },{
          key:'UNCREATED',
          name: '未生成订单' 
        },{
          key:'CANCEL',
          name: '已取消' 
        }],
        postData: {
          user_id: '',
          phone: '',
          store_no	: '',
          order_no	: '',
          subscription_no	: '',
          credit_biz_order_id	: '',
          status	: '',
          page_size: 10,
          page_no: 1
        },
        curInfo: {},
        tableData: [],
        dataCount: 5,
        showEditMerchant: false
      }
    },
    methods: {
      statusNameByKey(key) {
        for (const iterator of this.statusInfoArr) {
          if(key === iterator.key) {
            return iterator.name
          }
        }
      },
      search() {
        this.postData.page_no = 1;
        this.getList();
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.getList();
      },
      getList() {
        this.$service.post('Web/ZhimaFitPay/orders', this.postData).then(res => {
          if (res.data.errorcode == 0) {
            this.tableData = res.data.data.list;
            this.dataCount = res.data.data.count;
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      handleRefund(info) {
        this.$service.post('Web/ZhimaFitPay/orderRefund', {
          merchant_pid: info.open_merchant_id,
          order_no: info.store_no,
          subscription_no: info.subscription_no
        }).then(res => {
          if (res.data.errorcode == 0) {
            _g.toastMsg('success', '操作成功');
            this.getList()
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      handleAddMerchant(info) {
        this.curInfo = {}
        this.showEditMerchant = true
      }
    },
    created() {
      this.getList()
    }
  };
</script>

<style scoped>
  .table-zoom-image {
    height: 30px;
  }
  .table-zoom-image > img {
    height: 100%;
  }
  .oneline {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    margin-top: 10px;
  }
  .oneline:first-child {
    margin-top: 0;
  }
  .dynamadd i {
    width: 10%;
    height: 100%;
    margin-left: 10px;
  }
  .w90 {
    width: 90%;
  }
  .bluefont {
    color: #409eff;
    cursor: pointer;
    width: 100%;
    display: inline-block;
  }
  .overtext {
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .qrpic {
    border: none;
    width: 32px;
    height: 32px;
    background: url(../../assets/images/qrcode.png) no-repeat center;
    background-size: 32px 32px;
  }
  .flex-center {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
</style>








