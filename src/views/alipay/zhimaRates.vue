<template>
  <el-tabs v-model="activeName" type="border-card">
    <el-tab-pane label="安心付" name="comeDown">
      <transition name="el-zoom-in-center">
        <rates-by-come-down-pay v-show="activeName === 'comeDown'" />
      </transition>
    </el-tab-pane>
    <el-tab-pane label="先享后付" name="monthly">
      <transition name="el-zoom-in-center">
        <rates-by-monthly-pay v-show="activeName === 'monthly'" />
      </transition>
    </el-tab-pane>
  </el-tabs>
</template>

<script>
import RatesByComeDownPay from './components/RatesByComeDownPay.vue'
import RatesByMonthlyPay from './components/RatesByMonthlyPay.vue'

export default {
  name: 'zhimaProducts',
  components: {
    RatesByComeDownPay,
    RatesByMonthlyPay
  },
  data() {
    return {
      activeName: 'comeDown'
    }
  },
}
</script>
