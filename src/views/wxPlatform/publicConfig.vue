<template>
  <div class="container twoTabs">
    <header>
      <el-select
        @change="search"
        clearable
        v-model="postData.search"
        placeholder="app列表"
        class="w-150 m-r-10"
        filterable
      >
        <el-option
          v-for="item in appIdList"
          :label="item.nick_name"
          :value="item.auth_appid"
          :key="item.auth_appid"
        ></el-option>
      </el-select>
      <el-select
        @change="search"
        clearable
        v-model="postData.mer_id"
        placeholder="商家列表"
        class="w-200 m-r-10"
        filterable
      >
        <el-option
          v-for="item in merList"
          :label="item.mer_name"
          :value="item.mer_id"
          :key="item.mer_id"
        ></el-option>
      </el-select>
      <el-button
        size="small"
        class="qrpic"
        @click="openQr"
        type="primary"
      ></el-button>
    </header>
    <el-table :data="tableData" stripe style="width: 100%" :key="tableKey">
      <el-table-column
        prop="auth_appid"
        label="appId"
        width="170"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="nick_name"
        label="昵称"
        width="110"
        align="center"
      ></el-table-column>
      <el-table-column prop="head_img" label="头像" width="80" align="center">
        <template scope="scope">
          <div class="table-zoom-image">
            <img :src="scope.row.head_img" alt="" />
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column
        prop="privacy_status"
        label="账号隐私"
        width="90"
        align="center"
      >
        <template scope="scope">
          <el-switch
            v-model="scope.row.privacy_status"
            @change="switchStatus(scope.row)"
            active-color="#13ce66"
            inactive-value="1"
            active-value="0"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column
        prop="staff_num"
        label="成员个数"
        width="90"
        align="center"
      >
        <template scope="scope">
          <router-link
            :to="{
              name: 'wxmemberControl',
              query: { appId: scope.row.auth_appid },
            }"
          >
            <el-button size="medium" class="qricon" type="text">{{
              scope.row.staff_num
            }}</el-button>
          </router-link>
        </template>
      </el-table-column> -->
      <el-table-column
        prop="description"
        label="简介"
        width="120"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="staff_num"
        label="主/副行业"
        width="90"
        align="center"
      >
        <template scope="scope">
          <template v-if="scope.row.industry_id1 || scope.row.industry_id2">
            {{ scope.row.industry_id1 || scope.row.industry_id2 }}
          </template>
          <template v-else>
            <el-button size="medium" class="qricon" type="text" @click="handleSetDefaultTrade(scope.row)">设置默认所属行业</el-button>
          </template>
        </template>
      </el-table-column>
      <el-table-column
        label="模板消息"
        prop="template_names"
        align="center"
      >
        <template scope="scope">
          <template v-if="scope.row.template_names">
            {{ scope.row.template_names }}
          </template>
          <template v-else>
            <el-button size="medium" class="qricon" type="text" @click="handleSetDefaultTemplate(scope.row)">设置默认模板消息</el-button>
          </template>
        </template>
      </el-table-column>
      <!-- <el-table-column
        prop="categories"
        label="类目"
        width="120"
        align="center"
      >
        <template scope="scope">
          <el-tag
            v-for="category in scope.row.categories"
            :label="category.first_name + '-' + category.second_name"
            :key="category.first + ',' + category.second"
            effect="plain"
            >{{ category.first_name + '-' + category.second_name }}</el-tag
          >
        </template>
      </el-table-column>
      <el-table-column
        prop="interfaces"
        label="接口权限"
        width="250"
        align="center"
      >
        <template scope="scope">
          <el-tag
            v-for="wxInterface in scope.row.interfaces"
            :label="wxInterface.api_name"
            :key="wxInterface.api_name"
            effect="plain"
            :type="tabletagtype[wxInterface.status - 1]"
            >{{ wxInterface.api_name }}</el-tag
          >
        </template>
      </el-table-column> -->
      <el-table-column
        label="关联商家"
        prop="mer_names"
        align="center"
      >
        <template scope="scope">
          <span
            class="bluefont overtext"
            v-if="scope.row.mer_names"
            size="medium"
            @click="linkMer(scope.row)"
            type="text"
            >{{ scope.row.mer_names }}</span
          >
          <span
            class="bluefont"
            v-else
            size="medium"
            @click="linkMer(scope.row)"
            type="text"
            >--</span
          >
        </template>
      </el-table-column>
      <el-table-column
        label="关联小程序"
        prop="bind_applet_list"
        align="center"
      >
        <template scope="scope">
            <!-- v-if="scope.row.order_names" -->
          <!-- <div
            class="bluefont overtext"
            size="medium"
            @click="handleLinkBusList(scope.row)"
            type="text"
          >
            {{ scope.row.order_names || '默认小程序名字' }}
          </div> -->
          <el-tag v-for="(ele, index) in scope.row.bind_applet_list" :label="ele.nickname" :key="ele.nickname" effect="plain" :type="index%2 == 0 ? 'success' : 'warning'" closable @close="handleClose(ele, scope.row)">{{ele.nickname}}</el-tag>
          <div
            class="bluefont"
            size="medium"
            @click="handleLinkBusList(scope.row)"
            type="text"
            :style="{'margin-top': scope.row.bind_applet_list.length > 0 ? '12px' : '0px'}"
          >
            关联小程序
          </div>
        </template>
      </el-table-column>
      <el-table-column
        prop="open_appid"
        label="开放平台"
        width="100"
        align="center"
      >
        <template scope="scope">
          <span
            class="bluefont"
            v-if="scope.row.open_appid"
            size="medium"
            @click="deBind(scope.row)"
            type="text"
            >{{ scope.row.open_appid }}</span
          >
          <div v-else>
            <el-dropdown>
              <el-button type="primary" size="small">
                创建/绑定
                <i class="el-icon-arrow-down el-icon--right"></i>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item
                  command="aa"
                  @click.native="createBind(scope.row)"
                  >创建</el-dropdown-item
                >
                <el-dropdown-item
                  command="bb"
                  @click.native="showBindPlat(scope.row)"
                  >绑定</el-dropdown-item
                >
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </template>
      </el-table-column>
      <!-- <el-table-column
        prop="countTimes"
        label="剩余次数"
        width="120"
        align="center"
      >
        <template scope="scope">
          <el-button
            v-if="scope.row.countTimes.speedup_rest == null"
            @click="handleCountTimesClick(scope.$index)"
            >获取</el-button
          >
          <div v-else>
            <div>剩余{{ scope.row.countTimes.rest }}次</div>
            <div>加急{{ scope.row.countTimes.speedup_rest }}次</div>
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="id" align="center" label="隐私指引">
        <template scope="scope">
          <el-button
            size="small"
            type="primary"
            @click.native="goPrivacy(scope.row.auth_appid)"
            >编辑</el-button
          >
          <el-button
            size="small"
            @click.native="copyPrivacy(scope.row)"
            type="success"
            >复制到</el-button
          >
        </template>
      </el-table-column>
      <el-table-column label="更多操作" align="center">
        <template scope="scope">
          <el-dropdown>
            <el-button type="primary" size="small">
              更多操作
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item
                command="a"
                @click.native="checkStatus(scope.row)"
                >最新审核状态</el-dropdown-item
              >
              <el-dropdown-item command="b" @click.native="showDe(scope.row)"
                >详情</el-dropdown-item
              >
              <el-dropdown-item command="c" @click.native="showEd(scope.row)"
                >编辑</el-dropdown-item
              >
              <el-dropdown-item command="d" @click.native="showExper(scope.row)"
                >体验二维码</el-dropdown-item
              >
              <el-dropdown-item
                command="e"
                @click.native="showAlreadyCa(scope.row)"
                >已设置类目</el-dropdown-item
              >
              <el-dropdown-item
                command="f"
                @click.native="showCanSetCa(scope.row)"
                >可设置类目</el-dropdown-item
              >
              <el-dropdown-item
                command="g"
                @click.native="showPrivacyIn(scope.row)"
                >接口权限申请</el-dropdown-item
              >
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column> -->
    </el-table>

    <div class="pos-rel p-t-20 ovf-hd flexend" v-if="dataCount > 0">
      <div class="block">
        <el-pagination
          @current-change="handleCurrentChange"
          layout="prev, pager, next"
          :page-size="postData.page_size"
          :current-page="postData.page_no"
          :total="dataCount"
        >
        </el-pagination>
      </div>
    </div>

    <el-dialog
      :title="showDetail ? '详情' : '域名编辑'"
      :visible="showDia"
      :show-close="false"
      width="43%"
    >
      <el-form ref="form" :model="rowdata" label-width="170px">
        <el-form-item label="AppId">
          <el-input
            class="w90"
            disabled
            v-model="rowdata.auth_appid"
          ></el-input>
        </el-form-item>
        <el-form-item label="昵称">
          <el-input class="w90" disabled v-model="rowdata.nick_name"></el-input>
        </el-form-item>
        <el-form-item label="头像">
          <div class="table-zoom-image">
            <img :src="rowdata.head_img" alt="" />
          </div>
        </el-form-item>
        <el-form-item label="简介">
          <el-input
            class="w90"
            disabled
            v-model="rowdata.description"
          ></el-input>
        </el-form-item>
        <el-form-item label="隐私">
          <el-input
            class="w90"
            disabled
            :placeholder="
              rowdata.privacy_status == '0' ? '可以搜索' : '不可搜索'
            "
          ></el-input>
        </el-form-item>
        <el-form-item
          label="request合法域名"
          prop="request_domain"
          class="dynamadd"
        >
          <div
            v-for="(item, index) in rowdata.request_domain"
            class="oneline"
            :key="index"
          >
            <el-input
              class="w90"
              :disabled="showDetail"
              v-model="rowdata.request_domain[index]"
            >
              <template slot="prepend">Https://</template>
            </el-input>
            <i
              v-if="
                showEdit &&
                (index == rowdata.request_domain.length - 1 ||
                  rowdata.request_domain.length == 1)
              "
              class="el-icon-circle-plus-outline"
              @click="requestAdd"
            ></i>
            <i
              v-if="showEdit && index != rowdata.request_domain.length - 1"
              class="el-icon-remove-outline"
              @click="requestDel(index)"
            ></i>
          </div>
        </el-form-item>
        <el-form-item label="socket合法域名" class="dynamadd">
          <div
            v-for="(item, index) in rowdata.ws_request_domain"
            class="oneline"
            :key="index"
          >
            <el-input
              class="w90"
              :disabled="showDetail"
              v-model="rowdata.ws_request_domain[index]"
            >
              <template slot="prepend">Https://</template>
            </el-input>
            <i
              v-if="
                showEdit &&
                (index == rowdata.ws_request_domain.length - 1 ||
                  rowdata.ws_request_domain.length == 1)
              "
              class="el-icon-circle-plus-outline"
              @click="socketAdd"
            ></i>
            <i
              v-if="showEdit && index != rowdata.ws_request_domain.length - 1"
              class="el-icon-remove-outline"
              @click="socketDel(index)"
            ></i>
          </div>
        </el-form-item>
        <el-form-item
          label="uploadFile合法域名"
          prop="upload_domain"
          class="dynamadd"
        >
          <div
            v-for="(item, index) in rowdata.upload_domain"
            class="oneline"
            :key="index"
          >
            <el-input
              class="w90"
              :disabled="showDetail"
              v-model="rowdata.upload_domain[index]"
            >
              <template slot="prepend">Https://</template>
            </el-input>
            <i
              v-if="
                showEdit &&
                (index == rowdata.upload_domain.length - 1 ||
                  rowdata.upload_domain.length == 1)
              "
              class="el-icon-circle-plus-outline"
              @click="uploadAdd"
            ></i>
            <i
              v-if="showEdit && index != rowdata.upload_domain.length - 1"
              class="el-icon-remove-outline"
              @click="uploadDel(index)"
            ></i>
          </div>
        </el-form-item>
        <el-form-item
          label="downloadFile合法域名"
          prop="download_domain"
          class="dynamadd"
        >
          <div
            v-for="(item, index) in rowdata.download_domain"
            class="oneline"
            :key="index"
          >
            <el-input
              class="w90"
              :disabled="showDetail"
              v-model="rowdata.download_domain[index]"
            >
              <template slot="prepend">Https://</template>
            </el-input>
            <i
              v-if="
                showEdit &&
                (index == rowdata.download_domain.length - 1 ||
                  rowdata.download_domain.length == 1)
              "
              class="el-icon-circle-plus-outline"
              @click="downloadAdd"
            ></i>
            <i
              v-if="showEdit && index != rowdata.download_domain.length - 1"
              class="el-icon-remove-outline"
              @click="downloadDel(index)"
            ></i>
          </div>
        </el-form-item>
        <el-form-item label="业务域名" prop="web_view_domain" class="dynamadd">
          <div
            v-for="(item, index) in rowdata.web_view_domain"
            class="oneline"
            :key="index"
          >
            <el-input
              class="w90"
              :disabled="showDetail"
              v-model="rowdata.web_view_domain[index]"
            >
              <template slot="prepend">Https://</template>
            </el-input>
            <i
              v-if="
                showEdit &&
                (index == rowdata.web_view_domain.length - 1 ||
                  rowdata.web_view_domain.length == 1)
              "
              class="el-icon-circle-plus-outline"
              @click="webviewAdd"
            ></i>
            <i
              v-if="showEdit && index != rowdata.web_view_domain.length - 1"
              class="el-icon-remove-outline"
              @click="webviewDel(index)"
            ></i>
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDia">取 消</el-button>
        <el-button type="primary" @click="editDomain" v-if="showEdit"
          >保存</el-button
        >
      </div>
    </el-dialog>
    <el-dialog
      :title="`${copyData.nick_name}隐私指引`"
      :visible="showCopyPrivacy"
      :show-close="false"
      width="43%"
    >
      <el-form ref="form" :model="rowdata" label-width="100px">
        <el-form-item label="复制到">
          <el-select
            v-model="copyData.copy_appid"
            placeholder="app列表"
            class="w-200"
            multiple
          >
            <el-option
              v-for="item in appIdList"
              v-if="item.auth_appid != copyData.auth_appid"
              :label="item.nick_name"
              :value="item.auth_appid"
              :key="item.auth_appid"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelCopy">取 消</el-button>
        <el-button type="primary" @click="saveCopy">保存</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="商家关联"
      :visible="showMerLink"
      :show-close="false"
      width="35%"
    >
      <el-form ref="merform" :model="merData" label-width="100px">
        <el-form-item label="AppId">
          <el-input
            class="w90"
            disabled
            v-model="merData.pub_appid"
          ></el-input>
        </el-form-item>
        <el-form-item label="授权方昵称">
          <el-input class="w90" disabled v-model="merData.nick_name"></el-input>
        </el-form-item>
        <el-form-item label="关联商家" prop="mer_ids">
          <el-select
            multiple
            v-model="merData.mer_ids"
            placeholder="商家列表"
            class="w90 m-r-10"
            filterable
          >
            <el-option
              v-for="item in merList"
              :label="item.mer_name"
              :value="item.mer_id"
              :key="item.mer_id"
            ></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelMer">取 消</el-button>
        <el-button type="primary" @click="editMer">保存</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="小程序关联"
      :visible="showBusLink"
      :show-close="false"
      width="35%"
    >
      <el-form ref="busform" :model="orderData" label-width="100px">
        <el-form-item label="AppId">
          <el-input
            class="w90"
            disabled
            v-model="orderData.pub_appid"
          ></el-input>
        </el-form-item>
        <el-form-item label="授权方昵称">
          <el-input class="w90" disabled v-model="orderData.nick_name"></el-input>
        </el-form-item>
        <el-form-item label="小程序appId" prop="bus_ids">
          <el-input class="w90" v-model="orderData.appid" placeholder="请填写需要关联的小程序appId"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelBus">取 消</el-button>
        <el-button type="primary" @click="editBus">保存</el-button>
      </div>
    </el-dialog>

    <el-dialog
      title="绑定已有开放平台"
      :visible="showBind"
      :show-close="false"
      width="35%"
    >
      <el-form ref="bindform" :model="bindData" label-width="120px">
        <el-form-item
          label="开放平台AppId"
          prop="open_appid"
          :rules="{ required: true, message: '请填写AppId' }"
        >
          <el-input class="w90" v-model="bindData.open_appid"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showBind = false">取 消</el-button>
        <el-button type="primary" @click="bindPlat">保存</el-button>
      </div>
    </el-dialog>

    <el-dialog title="获取体验二维码" :visible.sync="showExp" width="35%">
      <el-form ref="expform" :model="expData" label-width="80px">
        <el-form-item
          label="路径"
          prop="path"
          :rules="{ required: true, message: '请填写路径' }"
        >
          <div class="oneline">
            <el-input class="w70" v-model="expData.path"></el-input>
            <el-button class="m-l-15" type="primary" @click="getExpQr"
              >获取</el-button
            >
          </div>
        </el-form-item>
        <div class="flex-center">
          <img
            v-if="expQrAddr"
            style="width: 70%; height: 70%"
            :src="expQrAddr"
          />
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer"></div>
    </el-dialog>

    <el-dialog :visible.sync="showQrcode" width="30%">
      <img style="width: 100%; height: 100%" :src="qraddr" />
    </el-dialog>

    <el-dialog :visible.sync="showAlreadyCategories" title="已设置类目">
      <!-- <el-checkbox-group v-model="checkCategories" size="medium">
        <el-checkbox-button v-for="category in categories" :label="category.first_name+'-'+category.second_name" :key="category.first+','+category.second">{{category.first_name+'-'+category.second_name}}</el-checkbox-button>
      </el-checkbox-group> -->
      <el-tag
        v-for="category in categories"
        :label="category.first_name + '-' + category.second_name"
        :key="category.first + ',' + category.second"
        :effect="
          checkDeleteCategory.first === category.first &&
          checkDeleteCategory.second === category.second
            ? 'dark'
            : 'plain'
        "
        @click="onCheckDeleteCategoryTag(category)"
        >{{ category.first_name + '-' + category.second_name }}</el-tag
      >
      <span slot="footer" class="dialog-footer">
        <el-button @click="showAlreadyCategories = false">取 消</el-button>
        <el-button type="primary" @click="deleteCategory">删 除</el-button>
      </span>
    </el-dialog>

    <el-dialog
      :visible.sync="showCanSetCategories"
      title="可设置类目(无需资质)"
    >
      <div
        class="tag-group"
        v-for="fatherCategory in categories"
        :key="fatherCategory.id"
      >
        <div class="tag-title">{{ fatherCategory.name }}</div>
        <el-tag
          v-for="category in fatherCategory.categories"
          :label="category.name"
          :key="category.father + ',' + category.id"
          :effect="category.checked ? 'dark' : 'plain'"
          @click="
            onCheckCategoryTag(category, category.father + ',' + category.id)
          "
          >{{ category.name }}</el-tag
        >
        <!-- <el-checkbox-group v-model="checkCategories" size="medium">
        <el-checkbox-button v-for="category in fatherCategory.categories" :label="category.name" :key="category.father+','+category.id">{{category.name}}</el-checkbox-button>
      </el-checkbox-group> -->
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="showCanSetCategories = false">取 消</el-button>
        <el-button type="primary" @click="addCategories">添 加</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="showPrivacyInterface" title="接口权限申请">
      <el-select
        v-model="selectInterface"
        value-key="api_name"
        placeholder="请选择"
        style="width: 500px"
        @change="onInterfaceChange"
      >
        <el-option
          v-for="item in interfaces"
          :key="item.api_name"
          :label="item.api_ch_name + '(' + item.api_name + ')'"
          :value="item"
        >
          <span style="float: left">{{ item.api_ch_name }}</span>
          <span style="float: right; color: #8492a6; font-size: 13px">{{
            item.api_name
          }}</span>
        </el-option>
      </el-select>
      <div>
        <el-tag :type="tagType">{{ tagTypeStr }}</el-tag>
      </div>
      <div v-if="selectInterface.fail_reason">
        {{ selectInterface.fail_reason }}
      </div>
      <el-input
        v-if="selectInterface.status === 1 || selectInterface.status === 4"
        v-model="interefaceContent"
        style="margin-top: 10px"
        type="textarea"
        placeholder="申请原因(不超过300字)"
        maxlength="300"
        :row="3"
      />
      <!-- <el-tag
              v-for="interface in interfaces" :label="interface.api_name" :key="interface.api_name"
              :effect="checkDeleteCategory.first === category.first && checkDeleteCategory.second === category.second?'dark':'plain'"
              @click="onCheckDeleteCategoryTag(category)"
              >{{category.first_name+'-'+category.second_name}}</el-tag> -->
      <span slot="footer" class="dialog-footer">
        <el-button @click="showPrivacyInterface = false">取 消</el-button>
        <el-button
          v-if="selectInterface.status === 1 || selectInterface.status === 4"
          type="primary"
          @click="onApplyInterface"
          >申 请</el-button
        >
      </span>
    </el-dialog>
  </div>
</template>

<script>
import http from 'assets/js/http'
import getAppId from 'assets/js/wxplat'

const TAGTYPE = ['', 'warning', 'info', 'danger', 'success']
const TAGTYPESTR = ['待申请开通', '无权限', '申请中', '申请失败', '已开通']

export default {
  name: 'publicConfig',
  data() {
    return {
      postData: {
        search: '',
        mer_id: '',
        pub_appid: '',
        page_size: 10,
        page_no: 1,
      },
      tableData: [],
      dataCount: 5,
      appIdList: [],
      showDetail: false,
      showEdit: false,
      showAlreadyCategories: false, //已设置所有类目
      showCanSetCategories: false, //可设置的所有类目
      checkCategories: [],
      checkDeleteCategory: {},
      categories: [],
      showPrivacyInterface: false, // 申请接口权限
      selectInterface: {},
      interfaces: [],
      interefaceContent: '',
      tableKey: false,
      rowdata: {
        auth_appid: '',
        nick_name: '',
        head_img: '',
        description: '',
        privacy_status: '',
        request_domain: [],
        ws_request_domain: [],
        upload_domain: [],
        download_domain: [],
        web_view_domain: [],
        serveraction: 'set',
        webaction: 'set',
      },
      merList: [],
      orderList: [],
      showMerLink: false,
      showBusLink: false,
      merData: {
        pub_appid: '',
        nick_name: '',
        comp_appid: '',
        mer_ids: [],
      },
      orderData: {
        comp_appid: '',
        pub_appid: '',
        nick_name: '',
        appid: [],
      },
      copyData: {
        auth_appid: '',
        nick_name: '',
        copy_appid: [],
      },
      showQrcode: false,
      showCopyPrivacy: false,
      showBind: false,
      bindData: {
        auth_type: 1,
        auth_appid: '',
        open_appid: '',
      },
      showExp: false,
      expData: {
        auth_appid: '',
        path: '',
      },
      expQrAddr: '',
      tabletagtype: TAGTYPE,
    }
  },
  computed: {
    tagTypeStr() {
      if (!this.selectInterface) {
        return ''
      }
      return TAGTYPESTR[this.selectInterface.status - 1]
    },
    tagType() {
      if (!this.selectInterface) {
        return ''
      }
      return TAGTYPE[this.selectInterface.status - 1]
    },
    showDia() {
      return this.showDetail || this.showEdit
    },
    qraddr() {
      let host = window.location.host
      let subDomain = host.split('.')[0]
      const wxCompAppid = localStorage.getItem('wxCompAppid') || ''
      if (subDomain === 'bo') {
        return (
          'https://wx-third.rocketbird.cn/mini/Wxnotify/getcode?auth_type=1&comp_appid=' +
          wxCompAppid
        )
      } else {
        return (
          'https://wx-third-beta.rocketbird.cn/mini/Wxnotify/getcode?auth_type=1&comp_appid=' +
          wxCompAppid
        )
      }
    },
  },
  watch: {
    showBind(val) {
      if (!val) {
        this.bindData.auth_appid = ''
        this.bindData.open_appid = ''
      }
    },
    showExp(val) {
      if (!val) {
        this.expData.auth_appid = ''
        this.expData.path = ''
        this.expQrAddr = ''
      }
    },
  },
  methods: {
    handleCountTimesClick(index) {
      let row = this.tableData[index]
      if (!row) {
        return false
      }
      this.apiPost('/web/domain/get_quota', {
        auth_appid: row.auth_appid,
      }).then((res) => {
        if (res.errorcode == 0) {
          row.countTimes = res.data.info
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    showBindPlat(row) {
      this.showBind = true
      this.bindData.auth_appid = row.auth_appid
      this.bindData.open_appid = row.open_appid
      if (this.$refs.bindform != undefined) {
        this.$refs.bindform.resetFields()
      }
    },
    bindPlat() {
      this.$refs.bindform.validate((valid) => {
        if (valid) {
          this.apiPost('/web/domain/bind_open', this.bindData).then((res) => {
            if (res.errorcode == 0) {
              _g.toastMsg('success', res.errormsg)
              this.gettableList()
              this.showBind = false
            } else {
              _g.toastMsg('warning', res.errormsg)
            }
          })
        }
      })
    },
    createBind(row) {
      this.$confirm('确认创建开放平台并绑定App吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.apiPost('/web/domain/create_open', {
          auth_type: 1,
          auth_appid: row.auth_appid,
        }).then((res) => {
          if (res.errorcode == 0) {
            _g.toastMsg('success', '创建并绑定成功')
            this.gettableList()
          } else {
            _g.toastMsg('warning', res.errormsg)
          }
        })
      })
    },
    // 已绑定开放平台 -> 再次点击 -> 触发解绑事件
    deBind(row) {
      this.$confirm('确认解绑吗?', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.apiPost('/web/domain/unbind_open', {
          auth_type: 1,
          auth_appid: row.auth_appid,
          open_appid: row.open_appid,
        }).then((res) => {
          if (res.errorcode == 0) {
            _g.toastMsg('success', '解绑成功')
            this.gettableList()
          } else {
            _g.toastMsg('warning', res.errormsg)
          }
        })
      })
    },
    openQr() {
      this.showQrcode = true
    },
    cancelMer() {
      this.showMerLink = false
    },
    // 关联商家/小程序 -> 取消
    cancelBus() {
      this.showBusLink = false
    },
    // 关联商家 -> 保存
    editMer() {
      this.$refs.merform.validate((valid) => {
        if (valid) {
          let post = this.merData
          post.mer_ids = post.mer_ids.toString()
          this.apiPost('/Web/Publicnum/setMerchant', post).then((res) => {
            if (res.errorcode == 0) {
              _g.toastMsg('success', res.errormsg)
              this.gettableList()
              this.cancelMer()
            } else {
              _g.toastMsg('warning', res.errormsg)
            }
          })
        }
      })
    },
    // 关联小程序 -> 保存
    editBus() {
      this.$refs.busform.validate((valid) => {
        if (valid) {
          let post = this.orderData
          post.appid = post.appid.toString()
          this.apiPost(
            '/Web/Publicnum/linkMiniProgram',
            post
          ).then((res) => {
            if (res.errorcode == 0) {
              _g.toastMsg('success', res.errormsg)
              this.gettableList()
              this.cancelBus()
            } else {
              _g.toastMsg('warning', res.errormsg)
            }
          })
        }
      })
    },
    // 具体行关联商家的点击触发事件
    linkMer(row) {
      this.showMerLink = true
      if (this.$refs.merform != undefined) {
        this.$refs.merform.resetFields()
      }
      this.merData.pub_appid = row.auth_appid
      this.merData.comp_appid = row.comp_appid
      this.merData.nick_name = row.nick_name
      this.merData.mer_ids = row.mer_ids ? row.mer_ids.split(',') : []
    },
    // 具体行关联小程序的点击触发事件
    handleLinkBusList(row) {
      this.getOrderList(row).then(()=>{
        this.showBusLink = true
        if (this.$refs.busform != undefined) {
          this.$refs.busform.resetFields()
        }
        this.orderData.comp_appid = row.comp_appid
        this.orderData.pub_appid = row.auth_appid
        this.orderData.nick_name = row.nick_name
        this.orderData.appid = []
        row.bind_applet_list.forEach((item) => {
          this.orderData.appid.push(item.appid)
        })
      })
    },
    showExper(row) {
      this.showExp = true
      this.expData.auth_appid = row.auth_appid
      if (this.$refs.expform != undefined) {
        this.$refs.expform.resetFields()
      }
    },
    getExpQr() {
      this.$refs.expform.validate((valid) => {
        if (valid) {
          this.apiPost('/web/coding/get_qrcode', this.expData).then((res) => {
            if (res.errorcode == 0) {
              this.expQrAddr = res.data.img
            } else {
              _g.toastMsg('warning', res.errormsg)
            }
          })
        }
      })
    },
    checkStatus(row) {
      this.apiPost('/web/coding/get_latest_audit_status', {
        auth_appid: row.auth_appid,
      }).then((res) => {
        if (res.errorcode == 0) {
          _g.toastMsg('success', res.errormsg)
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    editDomain() {
      this.serverSubmit()
      this.webviewSubmit()
    },
    serverSubmit() {
      let postd = {}
      postd.request_domain = this.rowdata.request_domain.filter((elem) => {
        return elem.replace('/s', '') != ''
      })
      postd.ws_request_domain = this.rowdata.ws_request_domain.filter(
        (elem) => {
          return elem.replace('/s', '') != ''
        }
      )
      postd.upload_domain = this.rowdata.upload_domain.filter((elem) => {
        return elem.replace('/s', '') != ''
      })
      postd.download_domain = this.rowdata.download_domain.filter((elem) => {
        return elem.replace('/s', '') != ''
      })
      if (
        postd.request_domain.concat(
          postd.ws_request_domain,
          postd.upload_domain,
          postd.download_domain
        ).length == 0
      ) {
        postd.serveraction = 'delete'
      } else {
        postd.serveraction = this.rowdata.serveraction
      }
      let ssub = {
        auth_appid: this.rowdata.auth_appid,
        request_domain: postd.request_domain,
        ws_request_domain: postd.ws_request_domain,
        upload_domain: postd.upload_domain,
        download_domain: postd.download_domain,
        action: postd.serveraction,
      }
      this.apiPost('/web/domain/modify_domain', ssub).then((res) => {
        if (res.errorcode == 0) {
          _g.toastMsg('success', res.errormsg)
          this.cancelDia()
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    webviewSubmit() {
      let postd = {}
      postd.web_view_domain = this.rowdata.web_view_domain.filter((elem) => {
        return elem.replace('/s', '') != ''
      })
      if (postd.web_view_domain.length == 0) {
        postd.webaction = 'delete'
      } else {
        postd.web_view_domain = postd.web_view_domain.map((elem) => {
          if (elem.indexOf('http') < 0) {
            return 'https://'.concat(elem)
          } else {
            return elem
          }
        })
        postd.webaction = this.rowdata.webaction
      }
      let ssub = {
        auth_appid: this.rowdata.auth_appid,
        web_view_domain: postd.web_view_domain,
        action: postd.webaction,
      }
      this.apiPost('/web/domain/modify_web_domain', ssub).then((res) => {
        if (res.errorcode == 0) {
          _g.toastMsg('success', res.errormsg)
          this.cancelDia()
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    requestAdd() {
      this.rowdata.request_domain.unshift('')
    },
    requestDel(index) {
      this.rowdata.request_domain.splice(index, 1)
    },
    socketAdd() {
      this.rowdata.ws_request_domain.unshift('')
    },
    socketDel(index) {
      this.rowdata.ws_request_domain.splice(index, 1)
    },
    uploadAdd() {
      this.rowdata.upload_domain.unshift('')
    },
    uploadDel(index) {
      this.rowdata.upload_domain.splice(index, 1)
    },
    downloadAdd() {
      this.rowdata.download_domain.unshift('')
    },
    downloadDel(index) {
      this.rowdata.download_domain.splice(index, 1)
    },
    webviewAdd() {
      this.rowdata.web_view_domain.unshift('')
    },
    webviewDel(index) {
      this.rowdata.web_view_domain.splice(index, 1)
    },
    showEd(row) {
      this.apiPost('/web/domain/get_domains', {
        auth_appid: row.auth_appid,
      }).then((res) => {
        if (res.errorcode == 0) {
          let backdata = res.data
          this.showEdit = true
          this.editDataProcess(backdata)
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    showAlreadyCa(row) {
      this.categories = []
      this.checkDeleteCategory = {}
      this.rowdata.auth_appid = row.auth_appid
      this.apiPost('/Web/Coding/getSettingCategories', {
        auth_appid: row.auth_appid,
      }).then((res) => {
        if (res.errorcode == 0) {
          this.categories = res.data.categories
          this.showAlreadyCategories = true
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    showCanSetCa(row) {
      this.checkCategories = []
      this.categories = []
      this.rowdata.auth_appid = row.auth_appid
      this.apiPost('/Web/Coding/getAllCategories', {
        auth_appid: row.auth_appid,
      }).then((res) => {
        if (res.errorcode == 0) {
          this.filterCategories(res.data)
          this.showCanSetCategories = true
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    filterCategories(categories) {
      categories.forEach((category) => {
        if (category.sensitive_type !== 1) {
          if (category.father != undefined && category.father === 0) {
            this.categories.push({
              id: category.id,
              name: category.name,
              categories: [],
            })
          }
          if (category.father != undefined && category.father !== 0) {
            let tempFatherCategory = this.categories.find(
              (item) => item.id === category.father
            )
            // console.log('this.categories',this.categories,'category',category,'tempFatherCategory',tempFatherCategory)
            tempFatherCategory.categories.push({
              id: category.id,
              name: category.name,
              father: category.father,
            })
          }
        }
      })
    },
    onCheckCategoryTag(category, key) {
      category.checked = !category.checked
      if (category.checked) {
        this.checkCategories.push(key)
      } else {
        this.checkCategories = this.checkCategories.filter(
          (item) => item !== key
        )
      }
      this.$forceUpdate()
    },
    onCheckDeleteCategoryTag(category) {
      this.checkDeleteCategory.first = category.first
      this.checkDeleteCategory.second = category.second
      this.$forceUpdate()
    },
    addCategories() {
      let tempCategories = []
      console.log('this.checkCategories', this.checkCategories)
      this.checkCategories.forEach((item) => {
        let tempIds = item.split(',')
        tempCategories.push({
          first: tempIds[0],
          second: tempIds[1],
        })
      })
      this.apiPost('/Web/Coding/addCategory', {
        auth_appid: this.rowdata.auth_appid,
        categories: tempCategories,
      }).then((res) => {
        if (res.errorcode == 0) {
          _g.toastMsg('success', '添加成功')
          this.showCanSetCategories = false
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    deleteCategory() {
      this.apiPost('/Web/Coding/deleteCategory', {
        auth_appid: this.rowdata.auth_appid,
        first_id: this.checkDeleteCategory.first,
        second_id: this.checkDeleteCategory.second,
      }).then((res) => {
        if (res.errorcode == 0) {
          _g.toastMsg('success', '删除成功')
          this.showAlreadyCategories = false
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    showPrivacyIn(row) {
      this.interfaces = []
      this.selectInterface = {}
      this.rowdata.auth_appid = row.auth_appid
      this.apiPost('/Web/Coding/getPrivacyInterfaceByAppId', {
        auth_appid: row.auth_appid,
      }).then((res) => {
        if (res.errorcode == 0) {
          this.interfaces = res.data
          this.showPrivacyInterface = true
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    onApplyInterface() {
      if (!this.interefaceContent) {
        return _g.toastMsg('warning', '申请理由不能为空')
      }
      this.apiPost('/Web/Coding/applyPrivacyInterface', {
        auth_appid: this.rowdata.auth_appid,
        api_name: this.selectInterface.api_name,
        content: this.interefaceContent,
      }).then((res) => {
        if (res.errorcode == 0) {
          _g.toastMsg('success', '申请成功,请等待审核')
          this.showPrivacyInterface = false
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    onInterfaceChange(val) {
      console.log(
        'onInterfaceChange',
        val,
        'selectInterface',
        this.selectInterface
      )
    },
    editDataProcess(backdata) {
      this.rowdata.auth_appid = backdata.auth_appid
      this.rowdata.nick_name = backdata.nick_name
      this.rowdata.head_img = backdata.head_img
      this.rowdata.description = backdata.description
      this.rowdata.privacy_status = backdata.privacy_status
      this.rowdata.request_domain = backdata.request_domain.concat()
      this.rowdata.ws_request_domain = backdata.ws_request_domain.concat()
      this.rowdata.upload_domain = backdata.upload_domain.concat()
      this.rowdata.download_domain = backdata.download_domain.concat()
      this.rowdata.web_view_domain = backdata.web_view_domain.concat()

      this.rowdata.serveraction =
        backdata.request_domain.concat(
          backdata.ws_request_domain,
          backdata.upload_domain,
          backdata.download_domain
        ).length == 0
          ? 'add'
          : 'set'
      this.rowdata.request_domain =
        this.rowdata.request_domain.length == 0
          ? ['']
          : this.rowdata.request_domain
      this.rowdata.ws_request_domain =
        this.rowdata.ws_request_domain.length == 0
          ? ['']
          : this.rowdata.ws_request_domain
      this.rowdata.upload_domain =
        this.rowdata.upload_domain.length == 0
          ? ['']
          : this.rowdata.upload_domain
      this.rowdata.download_domain =
        this.rowdata.download_domain.length == 0
          ? ['']
          : this.rowdata.download_domain

      this.rowdata.webaction =
        backdata.web_view_domain.length == 0 ? 'add' : 'set'
      this.rowdata.web_view_domain = this.rowdata.web_view_domain.map(
        (elem) => {
          if (elem != '') {
            return elem.replace('https://', '')
          }
        }
      )
      this.rowdata.web_view_domain =
        this.rowdata.web_view_domain.length == 0
          ? ['']
          : this.rowdata.web_view_domain
    },
    copyPrivacy(row) {
      this.showCopyPrivacy = true
      this.copyData.auth_appid = row.auth_appid
      this.copyData.nick_name = row.nick_name
    },
    cancelCopy() {
      this.showCopyPrivacy = false
    },
    saveCopy() {
      this.apiPost('/Web/Domain/copy_privacy', this.copyData).then((res) => {
        if (res.errorcode == 0) {
          this.showCopyPrivacy = false
          _g.toastMsg('success', res.errormsg)
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    showDe(row) {
      this.apiPost('/web/domain/get_domains', {
        auth_appid: row.auth_appid,
      }).then((res) => {
        if (res.errorcode == 0) {
          let backdata = res.data
          this.rowdata = Object.assign(backdata)
          this.showDetail = true
          this.rowdata.web_view_domain = this.rowdata.web_view_domain.map(
            (elem) => {
              if (elem != '') {
                return elem.replace('https://', '')
              }
            }
          )
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    cancelDia() {
      this.rowdata = {
        auth_appid: '',
        nick_name: '',
        head_img: '',
        description: '',
        privacy_status: '',
        request_domain: [],
        ws_request_domain: [],
        upload_domain: [],
        download_domain: [],
        web_view_domain: [],
        serveraction: 'set',
        webaction: 'set',
      }
      if (this.showDetail) {
        this.showDetail = false
      } else {
        this.showEdit = false
      }
    },
    // switchStatus(row) {
    //   let switchPost = {
    //     auth_appid: row.auth_appid,
    //     privacy_status: row.privacy_status,
    //   }
    //   this.apiPost('/web/domain/update_privacy_status', switchPost).then(
    //     (res) => {
    //       if (res.errorcode == 0) {
    //         _g.toastMsg('success', res.errormsg)
    //       } else {
    //         _g.toastMsg('warning', res.errormsg)
    //         this.gettableList()
    //       }
    //     }
    //   )
    // },
    // 获取商家列表
    getMerList() {
      this.apiGet('/web/domain/mer_list').then((res) => {
        if (res.errorcode == 0) {
          this.merList = res.data.list
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    // 获取小程序列表
    getOrderList(row) {
      let post = {
        comp_appid: row.comp_appid,
        pub_appid: row.auth_appid
      }
      return this.apiGet('/Web/Publicnum/getLinkMiniProgram', post).then((res) => {
        if (res.errorcode == 0) {
          this.orderList = res.data.list
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    // 获取table显示数据
    gettableList() {
      this.apiPost('/Web/Publicnum/getList', this.postData).then((res) => {
        if (res.errorcode == 0) {
          let list = res.data.list
          list.forEach((elem) => {
            elem.privacy_status = String(elem.privacy_status)
            elem.countTimes = {
              rest: null,
              speedup_rest: null,
            }
            // 已绑定的小程序
            elem.order_names = []
            elem.bind_applet_list.forEach(ele => {
              elem.order_names.push(ele.nickname)
            });
            elem.order_names = elem.order_names.toString()
            // 模板消息
            elem.template_names = []
            elem.template_list.forEach(ele => {
              elem.template_names.push(ele.title)
            });
            elem.template_names = elem.template_names.toString()
          })
          this.tableData = list
          this.dataCount = res.data.count
          // this.queryTableCategory()
          // this.queryTableInterface()
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    // queryTableCategory() {
    //   let multiplePost = []
    //   this.tableData.forEach((item) => {
    //     multiplePost.push(
    //       this.apiPost('/Web/Coding/getSettingCategories', {
    //         auth_appid: item.auth_appid,
    //       })
    //     )
    //   })
    //   Promise.all(multiplePost)
    //     .then((result) => {
    //       let faildAppStr = ''
    //       result.forEach((res, index) => {
    //         if (res.errorcode != 0) {
    //           faildAppStr +=
    //             this.tableData[index].auth_appid + ' ' + res.errormsg + ' '
    //         } else {
    //           this.tableData[index].categories = res.data.categories
    //         }
    //       })
    //       if (faildAppStr) {
    //         // this.$message({
    //         //   showClose: true,
    //         //   message: faildAppStr,
    //         //   type: 'error',
    //         //   duration: 0
    //         // });
    //       } else {
    //         this.tableKey = !this.tableKey
    //         // _g.toastMsg('success', '查询成功');
    //         // this.$router.back()
    //       }
    //       this.loading = false
    //     })
    //     .catch((error) => {
    //       this.loading = false
    //       console.log(error)
    //     })
    // },
    // queryTableInterface() {
    //   let multiplePost = []
    //   this.tableData.forEach((item) => {
    //     multiplePost.push(
    //       this.apiPost('/Web/Coding/getPrivacyInterfaceByAppId', {
    //         auth_appid: item.auth_appid,
    //       })
    //     )
    //   })
    //   Promise.all(multiplePost)
    //     .then((result) => {
    //       let faildAppStr = ''
    //       result.forEach((res, index) => {
    //         if (res.errorcode != 0) {
    //           faildAppStr +=
    //             this.tableData[index].auth_appid + ' ' + res.errormsg + ' '
    //         } else {
    //           this.tableData[index].interfaces = res.data
    //         }
    //       })
    //       this.tableKey = !this.tableKey
    //       if (faildAppStr) {
    //         // this.$message({
    //         //   showClose: true,
    //         //   message: faildAppStr,
    //         //   type: 'error',
    //         //   duration: 0
    //         // });
    //       } else {
    //         // _g.toastMsg('success', '查询成功');
    //         // this.$router.back()
    //       }
    //       this.loading = false
    //     })
    //     .catch((error) => {
    //       this.loading = false
    //       console.log(error)
    //     })
    // },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage
      this.gettableList()
    },
    search() {
      this.postData.page_no = 1
      this.gettableList()
    },
    goPrivacy(auth_appid) {
      console.log('auth_appid', auth_appid)
      this.$router.push({
        path: '/wxPlatform/wxPrivacy',
        query: { auth_appid },
      })
    },
    // 设置默认所属行业
    handleSetDefaultTrade(row){
      let post = {
        comp_appid: row.comp_appid,
        pub_appid: row.auth_appid
      }
      this.apiPost('/Web/Publicnum/setIndustry', post).then((res) => {
        if (res.errorcode == 0) {
          _g.toastMsg('success', res.errormsg)
          this.gettableList()
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    // 顶部筛选APP列表
    getappIdLists() {
      this.apiGet('/web/coding/auth_app_list', {auth_type: 1}).then(res => {
        if (res.errorcode == 0) {
          this.appIdList = res.data
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    // 设置默认模板消息
    handleSetDefaultTemplate(row){
      let post = {
        comp_appid: row.comp_appid,
        pub_appid: row.auth_appid
      }
      this.apiPost('/Web/Publicnum/apiAddTemplate', post).then((res) => {
        if (res.errorcode == 0) {
          _g.toastMsg('success', res.errormsg)
          this.gettableList()
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    // 删除已绑定的小程序
    handleClose(item, row){
      console.log(item)
      let post = {
        comp_appid: row.comp_appid,
        pub_appid: row.auth_appid,
        appid: item.appid
      }
      this.apiPost('/Web/Publicnum/unlinkMiniProgram', post).then((res) => {
        if (res.errorcode == 0) {
          _g.toastMsg('success', res.errormsg)
          this.gettableList()
        }else if(res.errorcode == 89018){
          _g.toastMsg('warning', '请公众号管理员确认解绑该小程序!')
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    }
  },

  created() {
    // 获取商家列表
    this.getMerList()
    // mixins 获取app列表
    this.getappIdLists()
    // 获取table显示数据
    this.gettableList()
  },
  mixins: [http, getAppId],
}
</script>

<style scoped>
.table-zoom-image {
  height: 30px;
}
.table-zoom-image > img {
  height: 100%;
}
.oneline {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  margin-top: 10px;
}
.oneline:first-child {
  margin-top: 0;
}
.dynamadd i {
  width: 10%;
  height: 100%;
  margin-left: 10px;
}
.w90 {
  width: 90%;
}
.bluefont {
  color: #409eff;
  cursor: pointer;
  width: 100%;
  display: inline-block;
}
.overtext {
  text-overflow: ellipsis;
  white-space: nowrap;
}
.qrpic {
  border: none;
  width: 32px;
  height: 32px;
  background: url(../../assets/images/qrcode.png) no-repeat center;
  background-size: 32px 32px;
}
.flex-center {
  width: 100%;
  display: flex;
  flex-direction: row;
  justify-content: center;
  align-items: center;
}
.el-tag {
  margin-top: 10px;
}
.el-tag + .el-tag {
  margin-left: 10px;
}
.tag-group {
  padding: 10px 10px;
}
</style>
