<template>
  <div class="container twoTabs">
    <el-tabs type="border-card" v-model='tabSwitch' @tab-click="handleSwitch">
      <el-tab-pane label="小程序草稿箱管理" name="draft">
        <draft-table :tabSwitch="tabSwitch" v-on:updatemodel="updateModel" v-if="loadArr.includes('0')"></draft-table>
      </el-tab-pane>
      <el-tab-pane label="模版管理" name="model">
        <model-table :tabSwitch="tabSwitch" v-on:updatedraft="updateDraft" v-if="loadArr.includes('1')"></model-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  import codeTable from './components/codeTable.vue';
  export default {
    name: 'modeControl',
    data() {
      return {
        tabSwitch: 'draft',
        loadArr: ['0']
      };
    },
    components: {
      draftTable: codeTable,
      modelTable: codeTable
    },
    methods: {
      updateModel() {
        if (this.loadArr.includes('1')) {
          this.loadArr.splice(this.loadArr.indexOf('1'), 1);
        }
      },
      updateDraft() {
        if (this.loadArr.includes('0')) {
          this.loadArr.splice(this.loadArr.indexOf('0'), 1);
        }
      },
      handleSwitch(tab, event) {
        if (!this.loadArr.includes(tab.index)) {
          this.loadArr.push(tab.index);
        }
      }
    },

    created() {},
    mixins: [http]
  };
</script>

<style scoped>
</style>








