<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="100px">
      <el-row :gutter="20">
        <el-col :span="8">
          <div class="leftside">
            <el-form-item label="App列表" prop="auth_app_type">
              <el-radio-group v-model="postData.auth_app_type">
                <el-radio :label="2">部分</el-radio>
                <el-radio :label="1">全部</el-radio>
              </el-radio-group>
              <div style="width: 100%; margin-top: 10px;">
                <el-select  v-if="postData.auth_app_type === 2" v-model="postData.auth_appid" placeholder="app列表" class="w-150 m-r-10" @change="chooseApp" multiple filterable>
                <el-option v-for="item in appIdList" :label="item.nick_name" :value="item.auth_appid" :key="item.auth_appid"></el-option>
              </el-select>
              </div>
              
            </el-form-item>
          </div>
        </el-col>
        <el-col :span="14">
          <div class="right">
            <div class="rightside" v-for="(item, index) in postData.item_list" :key="index">
              <el-card class="box-card">
                <el-form-item label="页面选择" :prop="'item_list.' + index + '.address'" :rules="{ required: true, message: '请选择页面'}">
                  <el-select v-model="item.address" class="w100 m-r-10">
                    <el-option v-for="ite in indexList" :label="ite" :value="ite" :key="ite"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="一级类目" :prop="'item_list.' + index + '.first_id'" :rules="{ required: true, message: '请选择一级类目'}">
                  <el-select v-model="item.first_id" class="w100 m-r-10" @change="firstIdChange(index)">
                    <el-option v-for="ite in configList" :label="ite.first_class" :value="ite.first_id" :key="ite.first_id"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="二级类目" :prop="'item_list.' + index + '.second_id'" :rules="{ required: true, message: '请选择二级类目'}">
                  <el-select v-model="item.second_id" class="w100 m-r-10" @change="secondIdChange(index)">
                    <el-option v-for="ite in configList" :label="ite.second_class" :value="ite.second_id" :key="ite.second_id"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="三级类目" :prop="'item_list.' + index + '.third_id'" :rules="{ required: true, message: '请选择三级类目'}">
                  <el-select v-model="item.third_id" class="w100 m-r-10" @change="thirdIdChange(index)">
                    <el-option v-for="ite in configList" :label="ite.third_class" :value="ite.third_id" :key="ite.third_id"></el-option>
                  </el-select>
                </el-form-item>
                <el-form-item label="标题" :prop="'item_list.' + index + '.title'" :rules="{ required: true, message: '请填写标题'}">
                  <el-input v-model="item.title"></el-input>
                </el-form-item>
                <el-form-item label="标签" :prop="'item_list.' + index + '.tag'" :rules="{ required: true, message: '请填写标签'}">
                  <el-input v-model="item.tag"></el-input>
                </el-form-item>
              </el-card>
              <div class="configoper">
                <i v-if="index==(postData.item_list.length-1)||postData.item_list.length==1" class="el-icon-circle-plus-outline" @click="configAdd"></i>
                <i v-else class="el-icon-remove-outline" @click="configDel(index)"></i>
              </div>
            </div>
          </div>
        </el-col>
      </el-row>
      <el-form-item class='m-t-15'>
        <el-button type="primary" @click="submitApproval" :loading="loading">保存</el-button>
        <el-button class="m-l-150" @click="cancelApproval">取消</el-button>
      </el-form-item>
    </el-form>

  </div>
</template>

<script>
  import http from 'assets/js/http';
  import getAppId from 'assets/js/wxplat';
  export default {
    name: 'massApproval',
    data() {
      return {
        loading: false,
        postData: {
          auth_appid: [],
          auth_app_type: 2,
          item_list: [
            {
              address: 'pages/train/index',
              first_id: 674,
              first_class: '体育',
              second_id: 682,
              second_class: '在线健身',
              third_id: '0',
              third_class: '',
              title: '首页',
              tag: '健身  运动  勤鸟  健身馆'
            }
          ]
        },
        appIdList: [],
        indexList: ['pages/train/index', 'pages/bus/gym', 'pages/my/center', 'pages/login'],
        configList: [{
          first_id: 674,
          first_class: '体育',
          second_id: 682,
          second_class: '在线健身',
          third_id: '0',
          third_class: '',
        }]
      };
    },

    methods: {
      chooseApp(ids) {
        console.log(ids)
        // this.checkValid();
      },
      // checkValid() {
      //   this.apiPost('/web/coding/check_category', { auth_appid: this.postData.auth_appid }).then(res => {
      //     if (res.errorcode == 0) {
      //       if (res.data.status != 1) {
      //         let nickname = this.appIdList.filter(elem => {
      //           if (elem.auth_appid == this.postData.auth_appid) {
      //             return elem;
      //           }
      //         })[0].nick_name;
      //         this.postData.auth_appid = '';
      //         _g.toastMsg('warning', nickname + '类目设置错误');
      //       } else {
      //         this.getPageIndex();
      //         this.getConfigList();
      //       }
      //     } else {
      //       _g.toastMsg('warning', res.errormsg);
      //     }
      //   });
      // },
      // firstIdChange(index) {
      //   this.postData.item_list[index].first_class = this.configList.filter(elem => {
      //     if (elem.first_id == this.postData.item_list[index].first_id) {
      //       return elem;
      //     }
      //   })[0].first_class;
      // },
      // secondIdChange(index) {
      //   this.postData.item_list[index].second_class = this.configList.filter(elem => {
      //     if (elem.second_id == this.postData.item_list[index].second_id) {
      //       return elem;
      //     }
      //   })[0].second_class;
      // },
      // thirdIdChange(index) {
      //   this.postData.item_list[index].third_class = this.configList.filter(elem => {
      //     if (elem.third_id == this.postData.item_list[index].third_id) {
      //       return elem;
      //     }
      //   })[0].third_class;
      // },
      configAdd() {
        let item = {
          address: '',
          first_id: '',
          first_class: '',
          second_id: '',
          second_class: '',
          third_id: '',
          third_class: '',
          title: '',
          tag: ''
        };
        this.postData.item_list.unshift(item);
        let temp = this.postData.auth_appid;
        if (this.$refs['form'] != undefined) {
          this.$refs['form'].resetFields();
        }
        this.postData.auth_appid = temp;
      },
      configDel(index) {
        this.postData.item_list.splice(index, 1);
      },
      getPageIndex() {
        this.apiPost('/web/coding/get_page', { auth_appid: this.postData.auth_appid }).then(res => {
          if (res.errorcode == 0) {
            this.indexList = res.data;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      getConfigList() {
        this.apiPost('/web/coding/get_category', { auth_appid: this.postData.auth_appid }).then(res => {
          if (res.errorcode == 0) {
            this.configList = res.data;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      cancelApproval() {
        this.postData.auth_appid = '';
        this.postData.item_list = [
          {
            address: '',
            first_id: '',
            first_class: '',
            second_id: '',
            second_class: '',
            third_id: '',
            third_class: '',
            title: '',
            tag: ''
          }
        ];
        if (this.$refs['form'] !== undefined) {
          this.$refs['form'].resetFields();
        }
      },
      submitApproval() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true
            let multiplePost = []
            if(this.postData.auth_app_type === 1) {
              this.postData.auth_appid = this.appIdList.map(elem => elem.auth_appid);
            }
            this.postData.auth_appid.forEach(item => {
              multiplePost.push(this.apiPost('/web/coding/submit_audit', {
                ...this.postData,
                auth_appid: item
              }))
            });
            Promise.all(multiplePost).then((result) => {
              let faildAppStr = ''
              result.forEach((res, index) => {
                if (res.errorcode != 0) {
                  faildAppStr += this.postData.auth_appid[index] + ' ' + res.errormsg + ' '
                }
              })
              if (faildAppStr) {
                this.$message({
                  showClose: true,
                  message: faildAppStr,
                  type: 'error',
                  duration: 0
                });
              } else {
                _g.toastMsg('success', '操作成功');
                this.$router.back()
              }
              this.loading = false
            }).catch((error) => {
              this.loading = false
              console.log(error)
            })
          }
        });
      }
    },
    created() {
      this.getappIdList();
      if (this.$route.query.appId) {
        this.postData.auth_appid = this.$route.query.appId;
        this.checkValid();
      }
    },
    mixins: [http, getAppId]
  };
</script>

<style scoped>
  .el-col {
    min-height: 550px;
  }
  .leftside {
    padding-left: 10px;
  }
  .right {
    padding-left: 10px;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
  }
  .rightside {
    margin-top: 20px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }
  .rightside:first-child {
    margin-top: 0;
  }
  .box-card {
    width: 85%;
  }
  .w100 {
    width: 100%;
  }
  .configoper {
    width: 50px;
    height: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
</style>








