<template>
  <div>
    <header class="fl">
      <el-select @change="search" clearable v-model="postData.auth_appid" placeholder="小程序列表" class="w-150 m-r-10" filterable>
        <el-option v-for="item in appIdList" :label="item.source_miniprogram" :value="item.source_miniprogram_appid" :key="item.source_miniprogram_appid"></el-option>
      </el-select>
      <el-input placeholder="版本号" @keyup.enter.native="search" v-model="postData.version" class="w-150 m-r-10"></el-input>
      <el-button type="primary" icon="search" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="template_id" label="模版ID" width="110" align="center" v-if="tabSwitch=='model'" key="model-zz"></el-table-column>
      <el-table-column prop="source_miniprogram_appid" label="appId" width="180" align="center"></el-table-column>
      <el-table-column prop="source_miniprogram" label="名称" width="120" align="center"></el-table-column>
      <el-table-column prop="user_version" label="版本号" width="110" align="center"></el-table-column>
      <el-table-column prop="user_desc" label="描述" width="180" align="center"></el-table-column>
      <el-table-column prop="create_time" label="上传时间" width="160" align="center" v-if="tabSwitch=='draft'" key="draft-ee"></el-table-column>
      <el-table-column prop="create_time" label="选为模版时间" width="160" align="center" v-else key="model-ee"></el-table-column>
      <el-table-column label="操作" align="center">
        <template scope="scope">
          <el-button size="small" class="qricon" @click="addtoModel(scope.row)" type="primary" v-if="tabSwitch=='draft'">添加到模版</el-button>
          <el-button size="small" class="qricon" @click="deleteModel(scope.row)" type="primary" v-else>删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pos-rel p-t-20 ovf-hd flexend" v-if="dataCount>0">
      <div class="block">
        <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  export default {
    name: 'codeTable',
    data() {
      return {
        postData: {
          auth_appid: '',
          version: '',
          page_size: 10,
          page_no: 1
        },
        appIdList: [],
        tableData: [],
        dataCount: 5
      };
    },

    props: {
      tabSwitch: String
    },

    methods: {
      addtoModel(row) {
        this.$confirm('确认添加到模版吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.apiPost('/Web/Template/addtotemplate', { draft_id: row.draft_id }).then(res => {
              if (res.errorcode == 0) {
                _g.toastMsg('success', '添加成功');
                this.$emit('updatemodel');
                this.gettableList();
              } else {
                _g.toastMsg('warning', res.errormsg);
              }
            });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消添加'
            });
          });
      },
      deleteModel(row) {
        this.$confirm('确认删除模板吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.apiPost('/Web/Template/deletetemplate', { template_id: row.template_id }).then(res => {
              if (res.errorcode == 0) {
                _g.toastMsg('success', '删除成功');
                this.$emit('updatedraft');
                this.gettableList();
              } else {
                _g.toastMsg('warning', res.errormsg);
              }
            });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消删除'
            });
          });
      },
      gettableList() {
        if (this.tabSwitch == 'model') {
          this.getModelData();
        } else {
          this.getDraftData();
        }
      },
      getDraftData() {
        this.apiPost('/Web/Template/gettemplatedraftlist', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.tableData = res.data.list;
            this.dataCount = res.data.count;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      getModelData() {
        this.apiPost('/Web/Template/gettemplatelist', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.tableData = res.data.list;
            this.dataCount = res.data.count;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.gettableList();
      },
      search() {
        this.postData.page_no = 1;
        this.gettableList();
      },
      getappIdList() {
        this.apiGet('/web/coding/dev_app_list').then(res => {
          if (res.errorcode == 0) {
            this.appIdList = res.data;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      }
    },
    created() {
      this.getappIdList();
      this.gettableList();
    },
    mixins: [http]
  };
</script>

<style scoped>
  header {
    width: 100%;
  }
</style>








