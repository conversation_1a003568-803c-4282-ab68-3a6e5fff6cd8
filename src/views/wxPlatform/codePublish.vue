<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="150px">
      <el-form-item label="所属环境" prop="is_use">
        <el-radio-group v-model="postData.is_use">
          <el-radio class="radio" :label="2">线上</el-radio>
          <el-radio class="radio" :label="1">灰度体验</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="App列表" prop="auth_app_type">
          <el-radio-group v-model="postData.auth_app_type">
            <el-radio :label="2">部分</el-radio>
            <el-radio :label="1">全部</el-radio>
          </el-radio-group>
        </el-form-item>
      <el-form-item v-if="postData.auth_app_type == 2" label="" prop="auth_appids">
        <el-select multiple v-model="postData.auth_appids" placeholder="app列表" class="w-150 m-r-10" filterable>
          <el-option v-for="item in appIdList" :label="item.nick_name" :value="item.auth_appid" :key="item.auth_appid"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit" :loading="loading">保存</el-button>
        <el-button type="primary" @click="cancelSubmit">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  import getAppId from 'assets/js/wxplat';
  export default {
    name: 'codePublish',
    data() {
      return {
        loading: false,
        postData: {
          auth_appids: [],
          auth_app_type: 2,
          is_use: 2
        },
        appIdList: []
      };
    },
    methods: {
      cancelSubmit() {
        if (this.$refs['form'] != undefined) {
          this.$refs['form'].resetFields();
        }
        this.postData.auth_appids = [];
        this.postData.auth_app_type = 2;
        this.postData.is_use = 2;
      },
      onSubmit() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.loading = true;
            if (this.postData.auth_app_type === 1) {
              this.postData.auth_appids = this.appIdList.map(elem => elem.auth_appid);
            }
            this.apiPost('/web/coding/release', this.postData).then(res => {
              if (res.errorcode == 0) {
                _g.toastMsg('success', res.errormsg);
                this.postData.auth_appids = [];

                this.postData.is_use = 2;
              } else {
                _g.toastMsg('warning', res.errormsg);
              }
              this.loading = false;
            });
          }
        });
      }
    },
    created() {
      this.getappIdList();
    },
    mixins: [http, getAppId]
  };
</script>

<style scoped>
  .maright-10 {
    margin-right: 10px;
  }
</style>








