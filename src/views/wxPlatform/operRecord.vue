<template>
  <div class="container twoTabs">
    <el-tabs type="border-card" v-model='tabSwitch' @tab-click="handleSwitch">
      <el-tab-pane label="审核记录列表" name="approval">
        <draft-table :tabSwitch="tabSwitch" v-if="loadArr.includes('0')"></draft-table>
      </el-tab-pane>
      <el-tab-pane label="发布记录列表" name="publish">
        <model-table :tabSwitch="tabSwitch" v-if="loadArr.includes('1')"></model-table>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  import codeTable from './components/recordTable.vue';
  export default {
    name: 'operRecord',
    data() {
      return {
        tabSwitch: 'approval',
        loadArr: ['0']
      };
    },
    components: {
      draftTable: codeTable,
      modelTable: codeTable
    },
    methods: {
      handleSwitch(tab, event) {
        if (!this.loadArr.includes(tab.index)) {
          this.loadArr.push(tab.index);
        }
      }
    },

    created() {},
    mixins: [http]
  };
</script>

<style scoped>
</style>








