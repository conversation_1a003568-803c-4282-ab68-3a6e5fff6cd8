<template>
  <div class="form-frame">
    <el-form ref="form" label-width="150px">
      <el-form-item label="小程序名称">
         <el-select @change="getInfo" v-model="postData.auth_appid" placeholder="小程序名称" class="w-300">
          <el-option v-for="item in appIdList" :label="item.nick_name" :value="item.auth_appid" :key="item.auth_appid"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="隐私指引版本">
         <el-select v-model="postData.privacy_ver" class="w-300">
          <el-option label="开发版" :value="2"></el-option>
          <el-option label="现网版" :value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="电话">
         <el-input v-model="owner_setting.contact_phone" class="w-300"></el-input>
      </el-form-item>
      <el-form-item label="通知方式">
         <el-input v-model="owner_setting.notice_method" class="w-300"></el-input>
      </el-form-item>
      <el-form-item v-if="isNewPlatform" label="登录协议弹窗提醒">
        <el-radio-group v-model="owner_setting.acceptedTerms">
          <el-radio :label="0">关闭</el-radio>
          <el-radio :label="1">开启</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-if="isNewPlatform" label="用户注销">
        <el-radio-group v-model="owner_setting.unregisterUser">
          <el-radio :label="0">关闭</el-radio>
          <el-radio :label="1">开启</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="隐私信息说明" v-if="setting_list && setting_list.length">
        <div v-for="(item, index) in setting_list" :key="item.privacy_key" style="marginBottom: 25px">
          为了 <el-input v-model="setting_list[index].privacy_text" class="w-200" :key="item.privacy_key"></el-input>，需要获取您的{{item.privacy_label}}
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="setPrivacy">保存</el-button>
        <el-button class="m-l-150" @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
export default {
  name: 'wxPrivacy',
  data() {
    return {
      desList: [],
      info: {},
      title: [],
      appIdList: [],
      isNewPlatform: true,
      owner_setting: {
        contact_phone: '023-67397805',
        notice_method: '短信或者网络通知',
        acceptedTerms: 0,
        unregisterUser: 0,
      },
      setting_list: [],
      postData: {
        auth_appid: '',
        privacy_ver: 2
      }
    }
  },
  methods: {
    setPrivacy() {
      this.postData.owner_setting = JSON.stringify(this.owner_setting)
      this.postData.setting_list = JSON.stringify(this.setting_list)
      this.$service
        .post('/Web/Domain/set_privacy', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$message.success(res.data.errormsg);
            this.$router.back()
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
    },
    getInfo(id) {
      this.postData.auth_appid = id
      this.$service
        .post('/Web/Domain/get_privacy', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const resData = res.data.data.info
            this.info = resData
            const defaulSettingList = [{
                "privacy_key": "UserInfo",
                "privacy_text": "用户登录",
                "privacy_label": ""
              },
              {
                "privacy_key": "Location",
                "privacy_text": "精准签到",
                "privacy_label": ""
              },
              {
                "privacy_key": "Invoice",
                "privacy_text": "给您开电子发票",
                "privacy_label": ""
              },
              {
                "privacy_key": "Album",
                "privacy_text": "上传头像",
                "privacy_label": ""
              },
              {
                "privacy_key": "PhoneNumber",
                "privacy_text": "快捷登录",
                "privacy_label": ""
              },
              {
                "privacy_key": "AlbumWriteOnly",
                "privacy_text": "保存活动分享图片",
                "privacy_label": ""
              }]
            const resSettingList = resData.setting_list
            // 比较resSettingList与defaulSettingList,将不同的项添加到defaulSettingList中
            resSettingList.forEach((item, index) => {
              if(defaulSettingList.findIndex(val => val.privacy_key === item.privacy_key) < 0) {
                defaulSettingList.push(item)
              }
            })
            this.setting_list = defaulSettingList
            this.owner_setting.contact_phone = resData.owner_setting.contact_phone || '023-67397805'
            this.owner_setting.notice_method = resData.owner_setting.notice_method || '短信或者网络通知'
            this.owner_setting.acceptedTerms = Number(res.data.data.appInfo.acceptedTerms) || 0
            this.owner_setting.unregisterUser = Number(res.data.data.appInfo.unregisterUser) || 0
            this.desList = resData.privacy_desc.privacy_desc_list
            this.setSettingLabel()
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
    },
    setSettingLabel() {
      this.setting_list.forEach((element, index)=> {
        if(!element.privacy_label) {
          for (const item of this.desList) {
            if(element.privacy_key === item.privacy_key) {
              this.$set(this.setting_list, index, {
                ...this.setting_list[index],
                privacy_label:item.privacy_desc
              })
            }
          }
        }
      });
    },
    getappIdList() {
      this.$service
        .get('/web/coding/auth_app_list')
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.appIdList = data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
    }
  },
  created() {
    this.postData.auth_appid = this.$route.query.auth_appid;
    this.getappIdList()
    if(this.postData.auth_appid) {
      this.getInfo(this.postData.auth_appid)
    }

    const wxCompAppid = localStorage.getItem('wxCompAppid') || ''
    if (wxCompAppid === 'wx7cc254d36cecba59') {
      this.isNewPlatform = true
    } else {
      this.isNewPlatform = false
    }
  }
}
</script>
