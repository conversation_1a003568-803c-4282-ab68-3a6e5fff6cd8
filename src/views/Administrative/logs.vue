<template>
  <div class="table-wrap container">
    <header>
      <el-input placeholder="场馆名称" @keydown.enter.native="doSearch" v-model="postData.bus_name"></el-input>
      <el-date-picker
        v-model="dateRange"
        type="daterange"
        unlink-panels
        range-separator="至"
        start-placeholder="开始日期"
        end-placeholder="结束日期"
        value-format="yyyy-MM-dd"
        placement="bottom-start"
        :picker-options="pickerOptions"
        :clearable="false"
      ></el-date-picker>
      <el-select v-model="postData.service_type" placeholder="所属业务" clearable>
        <el-option v-for="item in versionList" :key="item.value" :label="item.label" :value="item.value"></el-option>
      </el-select>
      <el-input
        placeholder="操作人"
        style="margin-left: 20px"
        @keydown.enter.native="doSearch"
        v-model="postData.realname"
      ></el-input>
      <el-button type="success" @click="doSearch">搜索</el-button>
    </header>
    <el-table :data="tableData" style="width: 100%">
      <el-table-column align="center" prop="create_time" label="操作时间"></el-table-column>
      <el-table-column align="center" prop="realname" label="操作人"></el-table-column>
      <el-table-column align="center" prop="mer_name" label="商家名称"></el-table-column>
      <el-table-column align="center" prop="bus_name" label="所属场馆"></el-table-column>
      <el-table-column align="center" prop="service_type_text" label="所属业务"></el-table-column>
      <el-table-column align="center" prop="operation_text" label="操作类型"></el-table-column>
      <el-table-column align="center" prop="version_name" label="当前版本"></el-table-column>
      <el-table-column align="center" label="操作内容" width="200">
        <template scope="scope">
          <div>
            <el-tooltip
              v-if="scope.row.content.length !== scope.row.short_content.length"
              :content="scope.row.content"
              raw-content
            >
              <span>{{ scope.row.short_content }}</span>
            </el-tooltip>
            <span v-else>{{ scope.row.content }}</span>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="备注" width="200">
        <template scope="scope">
          <div>
            <el-tooltip v-if="scope.row.remark.length !== scope.row.short_remark.length" :content="scope.row.remark" raw-content>
              <span>{{ scope.row.short_remark }}</span>
            </el-tooltip>
            <span v-else>{{ scope.row.remark }}</span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <footer class="pos-rel p-t-20 ovf-hd flexend">
      <div class="left">
        <el-button type="success" @click="handleExportCsv" size="small">导出Excel</el-button>
      </div>
      <el-pagination
        @current-change="pageChange"
        @size-change="sizeChange"
        layout="prev, pager, next, sizes, total"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount"
      ></el-pagination>
    </footer>
  </div>
</template>

<script>
import dateFormat from 'dateformat'
import ExportCsv from './../../components/form/csvExport'

export default {
  name: 'logs',
  data() {
    // 产品线 1 saas账户权限 2 ivep私教 3 ivep泳教
    const versionList = [
      {
        value: '1',
        label: '版本控制',
      },
      {
        value: '3',
        label: '泳教 IVEP',
      },
      {
        value: '2',
        label: '私教 IVEP',
      },
    ]
    return {
      tableData: [],
      dataCount: 0,
      postData: {
        bus_name: '',
        realname: '',
        service_type: '',
        begin_time: '',
        end_time: '',
        page_no: 1,
        page_size: 10,
      },
      dateRange: [],
      pickerOptions: {
        shortcuts: [
          {
            text: '本月',
            onClick(picker) {
              // set current month
              const today = new Date()
              const start = new Date(today.getFullYear(), today.getMonth(), 1)
              const end = new Date(today.getFullYear(), today.getMonth() + 1, 0)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近三个月',
            onClick(picker) {
              // set recent 3 months
              const today = new Date()
              const start = new Date(today.getFullYear(), today.getMonth() - 3, 1)
              const end = new Date(today.getFullYear(), today.getMonth() + 1, 0)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近半年',
            onClick(picker) {
              // set recent 6 months
              const today = new Date()
              const start = new Date(today.getFullYear(), today.getMonth() - 6, 1)
              const end = new Date(today.getFullYear(), today.getMonth() + 1, 0)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '最近一年',
            onClick(picker) {
              // set recent 12 months
              const today = new Date()
              const start = new Date(today.getFullYear(), today.getMonth() - 12, 1)
              const end = new Date(today.getFullYear(), today.getMonth() + 1, 0)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },
      versionList,
    }
  },
  methods: {
    doSearch() {
      this.postData.page_no = 1
      if (Array.isArray(this.dateRange) && this.dateRange.length === 2) {
        this.postData.begin_time = this.dateRange[0]
        this.postData.end_time = this.dateRange[1]
      } else {
        this.postData.begin_time = ''
        this.postData.end_time = ''
      }
      this.getList()
    },
    pageChange(pageNo) {
      this.postData.page_no = pageNo
      this.getList()
    },
    sizeChange(pageSize) {
      this.postData.page_no = 1
      this.postData.page_size = pageSize
      this.getList()
    },
    packageList(list) {
      list.forEach((item) => {
        const versionItem = this.versionList.find((v) => v.value == item.service_type)
        if (versionItem) {
          item.service_type_text = versionItem.label
        } else {
          item.service_type_text = '-'
        }
        // <!-- 操作 1 添加 2 编辑 -->
        if (item.operation == 1) {
          item.operation_text = '添加'
        } else if (item.operation == 2) {
          item.operation_text = '编辑'
        }
        if (item.content.length > 40) {
          item.short_content = item.content.substring(0, 40) + '...'
        } else {
          item.short_content = item.content
        }
        if (item.remark.length > 40) {
          item.short_remark = item.remark.substring(0, 40) + '...'
        } else {
          item.short_remark = item.remark
        }
      })
    },
    handleExportCsv() {
      const fakePost = {
        ...this.postData,
        page_no: 1,
        page_size: this.dataCount,
        is_export: 1,
      }
      this.$service.post('/Web/AllOperation/getList', fakePost).then((res) => {
        if (res.data.errorcode == 0) {
          const list = res.data.data.list
          this.packageList(list)
          ExportCsv(list, [
            { prop: 'create_time', label: '操作时间' },
            { prop: 'realname', label: '操作人' },
            { prop: 'mer_name', label: '商家名称' },
            { prop: 'bus_name', label: '所属场馆' },
            { prop: 'service_type_text', label: '所属业务' },
            { prop: 'operation_text', label: '操作类型' },
            { prop: 'version_name', label: '当前版本' },
            { prop: 'content', label: '操作内容' },
            { prop: 'remark', label: '备注' },
          ])
        }
      })
    },
    getList() {
      this.$service
        .get('/Web/AllOperation/getList', {
          params: this.postData,
        })
        .then((res) => {
          if (res.data.errorcode === 0) {
            const list = res.data.data.list
            this.packageList(list)
            this.tableData = list
            this.dataCount = res.data.data.count
          } else {
            this.$message.error(res.data.errormsg)
          }
        })
        .catch((e) => {
          throw new Error(e)
        })
    },
  },
  created() {
    // dateRange default value is current month
    const today = new Date()
    const start = new Date(today.getFullYear(), today.getMonth(), 1)
    const end = new Date(today.getFullYear(), today.getMonth() + 1, 0)
    this.postData.begin_time = dateFormat(start, 'yyyy-mm-dd')
    this.postData.end_time = dateFormat(end, 'yyyy-mm-dd')
    this.dateRange = [this.postData.begin_time, this.postData.end_time]

    this.getList()
  },
}
</script>
