<template>
  <div class="table-wrap container">
    <header>
      <router-link v-show="addShow" class="btn-link-large add-btn" style="margin-left: 30px;" to="add">
        <i class="el-icon-plus"></i>&nbsp;&nbsp;添加用户
      </router-link>
      <el-input placeholder="请输入用户名" style="margin-left: 50px;" @keydown.enter.native="doSearch" v-model="postData.keywords"></el-input>
      <el-button type="success" @click="doSearch">搜索</el-button>
      <!-- <ChannelList v-model="channel_id"></ChannelList> -->
    </header>
    <el-table :data="tableData"
              style="width: 100%"
              @selection-change="selectItem">
      <el-table-column align="center" type="selection"
                       width="50">
      </el-table-column>
      <el-table-column align="center" prop="s_name"
                       label="所属组织架构">
      </el-table-column>
      <el-table-column align="center" prop="channel_name"
                       label="所在区域">
      </el-table-column>
      <el-table-column align="center" label="用户名"
                       prop="username"
                       width="200">
      </el-table-column>
      <el-table-column align="center" label="真实姓名"
                       prop="realname"
                       width="200">
      </el-table-column>
      <el-table-column align="center" label="备注"
                       prop="remark"
                       width="200">
      </el-table-column>
      <el-table-column align="center" label="状态"
                       width="100">
        <template scope="scope">
          <div>
            {{ scope.row.status | status }}
          </div>
        </template>
      </el-table-column>
      <el-table-column label="操作"
                       width="200">
        <template scope="scope">
          <div>
            <span>
              <router-link :to="{ name: 'usersEdit', params: { id: scope.row.id }}"
                           class="btn-link edit-btn">
                编辑
              </router-link>
            </span>
            <span>
              <el-button size="small"
                         type="danger"
                         @click="confirmDelete(scope.row)">删除</el-button>
            </span>
          </div>
        </template>
      </el-table-column>
    </el-table>
    <div class="pos-rel p-t-20">
      <btnGroup :selectedData="multipleSelection"
                :type="'users'"></btnGroup>
      <div class="block pages">
        <!--<el-pagination @current-change="handleCurrentChange"-->
                       <!--layout="prev, pager, next"-->
                       <!--:page-size="limit"-->
                       <!--:current-page="currentPage"-->
                       <!--:total="dataCount">-->
        <!--</el-pagination>-->
        <Pager :post-data="postData" :total="dataCount" @on-change="pageChange"></Pager>
      </div>
    </div>
  </div>
</template>

<script>
  import btnGroup from 'components/Common/btn-group.vue';
  import http from 'assets/js/http';
  import ChannelList from 'src/components/form/channelList';
  import Pager from 'src/components/pager'

  export default {
    name: 'list',
    components: {
      btnGroup,
      ChannelList,
      Pager
    },
    data() {
      return {
        tableData: [],
        dataCount: 0,
        currentPage: 1,
        keywords: '',
        multipleSelection: [],
        limit: 15,
        postData: {
          keywords: '',
          page_no: 1,
          page_size: 10
        }
      };
    },
    methods: {
      doSearch() {
        this.postData.page_no = 1;
        this.getAllUsers()
      },
      pageChange(postData) {
        const { page_size, page_no } = postData
        this.postData = {...this.postData, page_no, page_size}
        this.getAllUsers();
      },
      selectItem(val) {
        this.multipleSelection = val;
      },
      confirmDelete(item) {
        this.$confirm('确认删除该用户?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消',
            type: 'warning'
          })
          .then(() => {
            _g.openGlobalLoading();
            this.apiDelete('admin/users/', item.id).then(res => {
              _g.closeGlobalLoading();
              this.handelResponse(res, data => {
                _g.toastMsg('success', '删除成功');
                setTimeout(() => {
                  _g.shallowRefresh(this.$route.name);
                }, 1500);
              });
            });
          })
          .catch(() => {
            // catch error
          });
      },
      getAllUsers() {
        this.loading = true;
        const { page_no: page, page_size: limit, keywords } = this.postData
        const postData = {
          params: {
            keywords,
            page,
            limit
          }
        };
        this.$service.get('admin/users', postData).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.tableData = data.list;
            this.dataCount = data.dataCount;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
        // this.apiGet('admin/users', data).then(res => {
        //   // console.log('res = ', _g.j2s(res))
        //   this.handelResponse(res, data => {
        //     this.tableData = data.list;
        //     this.dataCount = data.dataCount;
        //   });
        // });
      },
      getCurrentPage() {
        let data = this.$route.query;
        if (data) {
          if (data.page) {
            this.currentPage = parseInt(data.page);
          } else {
            this.currentPage = 1;
          }
        }
      },
      getKeywords() {
        let data = this.$route.query;
        if (data) {
          if (data.keywords) {
            this.keywords = data.keywords;
          } else {
            this.keywords = '';
          }
        }
      },
      init() {
        this.getKeywords();
        this.getCurrentPage();
        this.getAllUsers();
      }
    },
    created() {
      this.init();
    },
    computed: {
      addShow() {
        return _g.getHasRule('users-save');
      },
      editShow() {
        return _g.getHasRule('users-update');
      },
      deleteShow() {
        return _g.getHasRule('users-delete');
      }
    },
    watch: {
      $route(to, from) {
        this.init();
      }
    },
    mixins: [http]
  };
</script>
