<template>
  <div class="container twoTabs">
    <header>
      <el-select @change="search" clearable v-model="postData.auth_appid" placeholder="app列表" class="w-150 m-r-10" filterable>
        <el-option v-for="item in appIdList" :label="item.auth_app_name" :value="item.auth_appid" :key="item.auth_appid"></el-option>
      </el-select>
      <!-- <el-button type="primary"
                  icon="search"
                  @click="search">搜索</el-button> -->
    </header>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="auth_app_name" label="小程序名称" width="180" align="center"></el-table-column>
      <el-table-column prop="auth_appid" label="appId" width="180" align="center"></el-table-column>
      <el-table-column prop="template_id" label="模板ID" width="140" align="center"></el-table-column>
      <el-table-column prop="ext_json" label="第三方自定义配置" width="140" align="center">
        <template scope="scope">
          <el-button type="text" size="medium" @click="showConf(scope.row)">查看</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="user_version" label="代码版本号" width="140" align="center"></el-table-column>
      <el-table-column prop="user_desc" label="描述" width="150" align="center"></el-table-column>
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template scope="scope">
          <span>{{scope.row.status=='1'?'成功':'失败'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template>
          <router-link :to="{path:'/dyPlatform/massApproval'}">
            <el-button size="small" class="qricon" type="primary">发起审核</el-button>
          </router-link>
          <!-- <el-button size="small" class="qricon" @click="showDet(scope.row)" type="primary">详情</el-button> -->
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div>
        <el-button size="medium" class="qricon m-l-10" @click="showUpld" type="primary">上传代码</el-button>
      </div>
      <div class="pos-rel ovf-hd flexend" v-if="dataCount>0">
        <div class="block">
          <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
          </el-pagination>
        </div>
      </div>
    </footer>

    <el-dialog title="第三方自定义配置" :visible.sync="showConfig" width="35%">
      <el-form label-width="130px">
        <el-form-item label="自定义配置">
          <el-input disabled type="textarea" :rows="5" v-model="configData" class="w-200">
          </el-input>
        </el-form-item>
      </el-form>
    </el-dialog>

    <el-dialog title="详情" :visible.sync="showDetail" width="43%">
      <div class="detailine">
        <span class="detaillabel">模板ID</span>
        <span class="detailcontent">{{detailData.template_id}}</span>
      </div>
      <div class="detailine">
        <span class="detaillabel">开发小程序AppId</span>
        <span class="detailcontent">{{detailData.auth_appid}}</span>
      </div>
      <div class="detailine">
        <span class="detaillabel">开发者昵称</span>
        <span class="detailcontent">{{detailData.nick_name}}</span>
      </div>
      <div class="detailine">
        <span class="detaillabel">模板版本</span>
        <span class="detailcontent">{{detailData.tc_user_version}}</span>
      </div>
      <div class="detailine">
        <span class="detaillabel">模板描述</span>
        <span class="detailcontent">{{detailData.tc_user_desc}}</span>
      </div>
      <div class="detailine">
        <span class="detaillabel">代码版本</span>
        <span class="detailcontent">{{detailData.user_version}}</span>
      </div>
      <div class="detailine">
        <span class="detaillabel">代码描述</span>
        <span class="detailcontent">{{detailData.user_desc}}</span>
      </div>
      <div class="detailine flextop">
        <span class="detaillabel">第三方自定义配置</span>
        <span class="detailcontent">{{detailData.ext_json}}</span>
      </div>
      <div class="detailine">
        <span class="detaillabel">代码状态</span>
        <span class="detailcontent">{{detailData.status=='1'?'成功':'失败'}}</span>
      </div>
    </el-dialog>

    <el-dialog title="上传代码" :visible="showUpload" :show-close='false' width="35%">
      <el-form ref="form" :model="uploadData" label-width="130px">
        <el-form-item label="App列表" prop="auth_app_type">
          <el-radio-group v-model="uploadData.auth_app_type">
            <el-radio :label="2">部分</el-radio>
            <el-radio :label="1">全部</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item v-if="uploadData.auth_app_type == 2" label="" prop="auth_appids">
          <el-select v-model="uploadData.auth_appids" multiple class="w-200">
            <el-option v-for="item in appIdList" :label="item.auth_app_name" :value="item.auth_appid" :key="item.auth_appid"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="模板" prop="template_id" :rules="{ required: true, message: '请选择模板'}">
          <el-select v-model="uploadData.template_id" class="w-200" @change="templateChange">
            <el-option v-for="item in versionList" :label="item.template_id + '|' + item.user_version" :value="item.template_id" :key="item.template_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="代码版本号" prop="user_version" :rules="{ required: true, message: '请填写代码版本号'}">
          <el-input v-model="uploadData.user_version" class="h-40 w-200"></el-input>
        </el-form-item>
        <el-form-item label="描述" prop="user_desc" :rules="{ required: true, message: '请填写描述'}">
          <el-input type="textarea" :rows="5" v-model="uploadData.user_desc" class="w-200">
          </el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="showUpload = false">取 消</el-button>
        <el-button type="primary" :loading="uploading" @click="uploadCode">保存</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  import getAppId from 'assets/js/wxplat';
  export default {
    name: 'codeControl',
    data() {
      return {
        postData: {
          auth_appid: '',
          page_size: 10,
          page_no: 1
        },
        appIdList: [],
        versionList: [],
        tableData: [],
        dataCount: 5,
        showConfig: false,
        configData: {},
        showDetail: false,
        detailData: {},
        showUpload: false,
        uploading: false,
        uploadData: {
          auth_appids: [],
          auth_app_type: 2,
          template_id: '',
          user_version: '',
          user_desc: ''
        }
      };
    },
    methods: {
      templateChange() {
        let versionitem = this.versionList.filter(elem => {
          if (elem.template_id == this.uploadData.template_id) {
            return elem;
          }
        });
        this.uploadData.user_version = versionitem[0].user_version;
        this.uploadData.user_desc = versionitem[0].user_desc;
      },
      showConf(row) {
        this.showConfig = true;
        this.configData = row.ext_json;
      },
      showDet(row) {
        this.detailData = {};
        this.showDetail = true;
        this.getDetailData(row.id);
      },
      getDetailData(id) {
        this.apiPost('/web/coding/coding_detail', { id: id }).then(res => {
          if (res.errorcode == 0) {
            this.detailData = res.data.info;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      uploadCode() {
        this.$refs['form'].validate(valid => {
          if (valid) {
            this.uploading = true;
            if (this.uploadData.auth_app_type === 1) {
              this.uploadData.auth_appids = this.appIdList.map(elem => elem.auth_appid);
            }
            // console.log(this.uploadData)
            // return false;
            this.apiPost('/Web/DouyinThird/uploadCode', this.uploadData).then(res => {
              if (res.errorcode == 0) {
                _g.toastMsg('success', res.errormsg);
                this.showUpload = false;
                this.gettableList();
              } else {
                _g.toastMsg('warning', res.errormsg);
              }
              this.uploading = false;
            });
          }
        });
      },
      showUpld() {
        this.uploadData = {
          auth_appids: [],
          template_id: '',
          user_version: '',
          auth_app_type: 2,
          user_desc: ''
        };
        this.showUpload = true;
        if (this.$refs['form'] !== undefined) {
          this.$refs['form'].resetFields();
        }
      },
      gettableList() {
        this.apiPost('/Web/DouyinThird/getCodeList', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.tableData = res.data.list;
            this.dataCount = res.data.count;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.gettableList();
      },
      search() {
        this.postData.page_no = 1;
        this.gettableList();
      },
      getAuthAppList() {
        this.apiPost('/Web/DouyinThird/getAuthAppList', {type: 2}).then(res => {
          if (res.errorcode == 0) {
            this.appIdList = res.data
          } else {
            _g.toastMsg('warning', res.errormsg)
          }
        })
      },
      getTemplateList() {
        this.apiPost('/Web/DouyinThird/getTemplateCode', {type: 2}).then(res => {
          if (res.errorcode == 0) {
            this.versionList = res.data
          } else {
            _g.toastMsg('warning', res.errormsg)
          }
        })
      }
    },

    created() {
      this.getAuthAppList();
      this.getTemplateList();
      this.gettableList();
    },
    mixins: [http, getAppId]
  };
</script>

<style scoped>
  .detailine {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
  }
  .detaillabel {
    width: 120px;
    text-align: right;
  }
  .flextop {
    align-items: flex-start;
  }
  .detailcontent {
    margin-left: 25px;
    width: 350px;
    word-wrap: break-word;
  }
</style>








