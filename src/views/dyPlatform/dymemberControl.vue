<template>
  <div class="container twoTabs">
    <header>
      <el-select @change="search" clearable v-model="postData.auth_appid" placeholder="app列表" class="w-150 m-r-10" filterable>
        <el-option v-for="item in appIdList" :label="item.nick_name" :value="item.auth_appid" :key="item.auth_appid"></el-option>
      </el-select>
    </header>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="auth_appid" label="appId" width="200" align="center"></el-table-column>
      <el-table-column prop="nick_name" label="昵称" width="120" align="center"></el-table-column>
      <el-table-column prop="head_img" label="头像" width="100" align="center">
        <template scope="scope">
          <div class="table-zoom-image">
            <img :src="scope.row.head_img" alt="">
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="signature" label="简介" width="150" align="center"></el-table-column>
      <el-table-column prop="members" label="成员" width="150" align="center"></el-table-column>
      <el-table-column label="操作" align="center">
        <template scope="scope">
          <el-button size="small" class="qricon" @click="addMemberShow(scope.row)" type="primary">添加成员</el-button>
          <el-button size="small" class="qricon" @click="deleteMemberShow(scope.row)" type="primary">删除成员</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pos-rel p-t-20 ovf-hd flexend" v-if="dataCount>0">
      <div class="block">
        <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
        </el-pagination>
      </div>
    </div>

    <el-dialog :title="showAdd?'添加成员':'删除成员'" :visible="showDia" :show-close='false' width="43%">
      <div class="dialogin" v-if="showAdd">
        <el-input placeholder="请输入wechat id" @keyup.enter.native="filterMember" v-model="searchMember" class="m-r-10"></el-input>
        <el-button type="primary" icon="search" @click="filterMember">添加</el-button>
      </div>
      <el-transfer v-model="selectIds" :data="memberPool" :titles="showAdd?['搜索项','添加项']:['已绑定成员','删除项']">
      </el-transfer>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDia">取 消</el-button>
        <el-button type="primary" @click="addMember" v-if="showAdd">保存</el-button>
        <el-button type="primary" @click="delMember" v-if="showDelete">保存</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
  import http from 'assets/js/http';
  import getAppId from 'assets/js/wxplat';
  export default {
    name: 'wxmemberControl',
    data() {
      return {
        postData: {
          auth_appid: '',
          page_size: 10,
          page_no: 1
        },
        appIdList: [],
        tableData: [],
        dataCount: 5,
        selectAppid: '',
        showAdd: false,
        searchMember: '',
        selectIds: [],
        memberPool: [],
        showDelete: false
      };
    },
    computed: {
      showDia() {
        return this.showAdd || this.showDelete;
      }
    },
    methods: {
      cancelDia() {
        this.searchMember = '';
        this.selectAppid = '';
        this.selectIds = [];
        this.memberPool = [];
        if (this.showAdd) {
          this.showAdd = false;
        } else {
          this.showDelete = false;
        }
      },
      delMember() {
        var ids = [];
        this.memberPool.forEach(elem => {
          if (this.selectIds.includes(elem.key)) {
            ids.push(elem.key);
          }
        });
        let postd = {
          auth_appid: this.selectAppid,
          user_str: ids
        };
        this.apiPost('/web/member/unbind_member', postd).then(res => {
          if (res.errorcode == 0) {
            _g.toastMsg('success', res.errormsg);
            this.selectIds = [];
            this.memberPool = [];
            this.showDelete = false;
            this.gettableList();
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      deleteMemberShow(row) {
        this.selectAppid = row.auth_appid;
        this.apiPost('/web/member/member_list', { auth_appid: row.auth_appid }).then(res => {
          if (res.errorcode == 0) {
            this.showDelete = true;
            this.memberPool = res.data.list.map(elem => {
              return { label: elem.wechat_id, key: elem.user_str };
            });
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      filterMember() {
        this.searchMember = this.searchMember.replace(/\s+/g, '');
        if (!this.searchMember) {
          return false;
        }
        let item = { label: this.searchMember, key: this.searchMember };
        this.memberPool.push(item);
        this.searchMember = '';
      },
      addMember() {
        let postd = {
          auth_appid: this.selectAppid,
          wechat_id: this.selectIds
        };
        this.apiPost('/web/member/bind_member', postd).then(res => {
          if (res.errorcode == 0) {
            _g.toastMsg('success', res.errormsg);
            this.searchMember = '';
            this.selectIds = [];
            this.memberPool = [];
            this.showAdd = false;
            this.gettableList();
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      addMemberShow(row) {
        this.selectAppid = row.auth_appid;
        this.showAdd = true;
      },
      gettableList() {
        this.apiPost('/web/member/memberauthalllist', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.tableData = res.data.list;
            this.dataCount = res.data.count;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.gettableList();
      },
      search() {
        this.postData.page_no = 1;
        this.gettableList();
      }
    },

    created() {
      this.getappIdList();
      if (this.$route.query.appId) {
        this.postData.auth_appid = this.$route.query.appId;
      }
      this.gettableList();
    },

    mixins: [http, getAppId]
  };
</script>

<style scoped>
  .dialogin {
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    margin-bottom: 15px;
  }
  .table-zoom-image {
    height: 30px;
  }
  .table-zoom-image > img {
    height: 100%;
  }
</style>








