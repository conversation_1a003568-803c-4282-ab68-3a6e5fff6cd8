<template>
  <div>
    <header>
      <el-select @change="search" clearable v-model="postData.auth_appid" placeholder="app列表" class="w-150 m-r-10" filterable>
        <el-option v-for="item in appIdList" :label="item.auth_app_name" :value="item.auth_appid" :key="item.auth_appid"></el-option>
      </el-select>
    </header>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="auth_appid" label="appId" width="180" align="center"></el-table-column>
      <el-table-column prop="auth_app_name" label="授权方昵称" width="120" align="center"></el-table-column>
      <el-table-column prop="template_id" label="模版ID" width="100" align="center"></el-table-column>
      <el-table-column prop="audit_status" label="审核状态" width="100" align="center" v-if="tabSwitch=='approval'" key="approval-dd">
        <template scope="scope">
          <span>{{scope.row.audit_status=='0'?'审核中':scope.row.audit_status=='1'?'审核失败':scope.row.audit_status=='2'?'审核成功':scope.row.audit_status=='3'?'审核撤回':'审核提交失败'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="reason" label="原因" width="150" align="center" v-if="tabSwitch=='approval'" key="approval-ee">
         <template scope="scope">
          <div v-html="scope.row.reason"></div>
        </template>
      </el-table-column>
      <el-table-column prop="create_time" label="发起审核时间" width="180" align="center" v-if="tabSwitch=='approval'" key="approval-ff"></el-table-column>
      <el-table-column prop="user_version" label="代码版本" width="100" align="center" v-if="tabSwitch=='publish'" key="publish-dd"></el-table-column>
      <el-table-column prop="user_desc" label="描述" width="100" align="center" v-if="tabSwitch=='publish'" key="publish-ee"></el-table-column>
      <el-table-column prop="status" label="发布状态" width="100" align="center" v-if="tabSwitch=='publish'" key="publish-hh">
        <template scope="scope">
          <span>{{scope.row.status=='0'?'未发布':scope.row.status=='1'?'已发布':'发布后回退'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="create_time" label="发布时间" width="160" align="center" v-if="tabSwitch=='publish'" key="publish-ii"></el-table-column>
      <el-table-column prop="edit_time" label="更新时间" width="160" align="center" v-if="tabSwitch=='publish'" key="publish-ii"></el-table-column>

      <el-table-column prop="is_use" label="所属环境" width="100" align="center">
        <template scope="scope">
          <span>{{scope.row.is_use=='0'?'未使用':scope.row.is_use=='1'?'灰度体验':'线上使用'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template scope="scope">
          <el-button size="small" :disabled="scope.row.audit_status!='0'" class="qricon" @click="callBack(scope.row)" type="primary" v-if="tabSwitch=='approval'">撤回</el-button>
          <el-button size="small" class="qricon" @click="rollBack(scope.row)" type="primary" v-if="tabSwitch=='publish'">回退</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <router-link class="btn-link-large add-btn" :to="{path:'/dyPlatform/massApproval'}" v-if="tabSwitch=='approval'">
        &nbsp;&nbsp;发起审核
      </router-link>
      <router-link class="btn-link-large add-btn" :to="{path:'/dyPlatform/codePublish'}" v-else>
        &nbsp;&nbsp;添加发布
      </router-link>
      <div class="pos-rel ovf-hd flexend" v-if="dataCount>0">
        <div class="block">
          <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
          </el-pagination>
        </div>
      </div>
    </footer>

  </div>
</template>

<script>
  import http from 'assets/js/http';
  import getAppId from 'assets/js/wxplat';
  export default {
    name: 'recordTable',
    data() {
      return {
        postData: {
          auth_appid: '',
          page_size: 10,
          page_no: 1
        },
        appIdList: [],
        tableData: [],
        dataCount: 5
      };
    },

    props: {
      tabSwitch: String
    },

    methods: {
      callBack(row) {
        this.$confirm('确认撤回吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.apiPost('/Web/DouyinThird/revokeAuditCode', { auth_appid: row.auth_appid }).then(res => {
              if (res.errorcode == 0) {
                _g.toastMsg('success', res.errormsg);
                this.gettableList();
              } else {
                _g.toastMsg('warning', res.errormsg);
              }
            });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消撤回'
            });
          });
      },
      rollBack(row) {
        this.$confirm('确认回退吗?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.apiPost('/Web/DouyinThird/rollbackCode', { auth_appid: row.auth_appid }).then(res => {
              if (res.errorcode == 0) {
                _g.toastMsg('success', res.errormsg);
                this.gettableList();
              } else {
                _g.toastMsg('warning', res.errormsg);
              }
            });
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消回退'
            });
          });
      },
      gettableList() {
        if (this.tabSwitch == 'publish') {
          this.getPubData();
        } else {
          this.getApproData();
        }
      },
      getPubData() {
        this.apiPost('/Web/DouyinThird/getReleaseList', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.tableData = res.data.list;
            this.dataCount = res.data.count;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      getApproData() {
        this.apiPost('/Web/DouyinThird/getAuditList', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.tableData = res.data.list;
            this.dataCount = res.data.count;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.gettableList();
      },
      search() {
        this.postData.page_no = 1;
        this.gettableList();
      },
      getAuthAppList() {
        this.apiPost('/Web/DouyinThird/getAuthAppList', {type: 2}).then(res => {
          if (res.errorcode == 0) {
            this.appIdList = res.data
          } else {
            _g.toastMsg('warning', res.errormsg)
          }
        })
      },
    },
    created() {
      this.getAuthAppList();
      this.gettableList();
    },
    mixins: [http, getAppId]
  };
</script>

<style scoped>
  header {
    width: 100%;
  }
</style>








