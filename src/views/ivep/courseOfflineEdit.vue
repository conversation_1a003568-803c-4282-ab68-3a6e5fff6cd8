<template>
  <div class="container">
    <el-form class="form-frame"
             ref="form"
             :model="postData"
             label-width="150px">
      <el-form-item label="课程名称"
                    prop="name"
                    :rules="[{required: true, message: '请填写学院名称'}]">
        <el-input v-model="postData.name"
                  class="w-320"
                  placeholder="课程名称"></el-input>
      </el-form-item>
      <el-form-item label="副标题"
                    prop="title">
        <el-input v-model="postData.title"
                  class="w-320"
                  placeholder="副标题"></el-input>
      </el-form-item>
      <el-form-item label="归属学院"
                    :rules="[{required: true, message: '请选择'}]"
                    prop="school_id">
        <school-select class="w-320"
                       v-model="postData.school_id" />
      </el-form-item>
      <el-form-item label="课程封面"
                    prop="image"
                    :rules="[{required: true, message: '请上传'}]">
        <Cropper v-model="postData.image"
                  :width="502"
                  :height="220"
                 :options="{aspectRatio: 502 / 220}"></Cropper>
      </el-form-item>
      <el-form-item label="展示状态"
                    prop="status">
        <el-radio-group class="w-320"
                        v-model="postData.status">
          <el-radio :label="1">展示</el-radio>
          <el-radio :label="0">不展示</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item prop="content"
                    :rules="[{required: true, message: '请填写'}]"
                    label="课程介绍">
        <Editor v-model="postData.content" />
      </el-form-item>
      <el-form-item style="padding-top: 50px">
        <el-button type="success"
                   @click="clickSave">保存</el-button>
        <el-button type="info"
                   @click="$router.back()"
                   style="margin-left: 100px;">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
  <script>
import Editor from 'components/form/Editor';
import Cropper from 'components/form/cropper';
import SchoolSelect from './components/SchoolSelect';

export default {
  name: 'CourseOfflineEdit',
  components: {
    Editor,
    Cropper,
    SchoolSelect
  },
  data () {
    return {
      postData: {
        name: '',
        title: '',
        school_id: '',
        image: '',
        status: 1,
        content: ''
      },
      courseOptions: []
    };
  },
  created () {
    this.id = this.$route.query.id;
    if (this.id) {
      this.getInfo();
    }

  },
  methods: {
    getInfo () {
      this.$service
        .post('/ivep/TrainCourseOffline/getInfo', { id: this.id })
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.postData = data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
    },
    clickSave () {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.id) {
            this.saveEdit();
          } else {
            this.addNews();
          }
        } else {
          this.$message.error('表单错误');
        }
      });
    },
    addNews () {
      this.$service
        .post('/ivep/TrainCourseOffline/addData', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$router.back();
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    saveEdit () {
      this.$service
        .post('/ivep/TrainCourseOffline/updateData', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$router.back();
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    }
  }
};
</script>
