<template>
  <div class="container">
    <el-form class="form-frame"
             ref="form"
             :model="postData"
             label-width="150px">
      <el-form-item label="课程名称"
                    prop="name"
                    :rules="[{required: true, message: '请填写学院名称'}]">
        <el-input v-model="postData.name"
                  class="w-320"
                  placeholder="课程名称"></el-input>
      </el-form-item>
      <el-form-item label="副标题"
                    prop="title">
        <el-input v-model="postData.title"
                  class="w-320"
                  placeholder="副标题"></el-input>
      </el-form-item>
      <el-form-item label="归属学院"
                    :rules="[{required: true, message: '请选择'}]"
                    prop="school_id">
        <school-select class="w-320"
                       v-model="postData.school_id" />
      </el-form-item>
      <el-form-item label="课程标签"
                    prop="tags"
                    :rules="[{required: true, message: '请添加'}]">
        <TagsAdd class="w-320"
                 v-model="postData.tags" />
      </el-form-item>
      <el-form-item label="讲师"
                    prop="lecturer"
                    :rules="[{required: true, message: '请选择'}]">
        <el-input v-model="postData.lecturer"
                  class="w-320"
                  placeholder="讲师"></el-input>
      </el-form-item>
      <el-form-item label="课程封面"
                    prop="image"
                    :rules="[{required: true, message: '请上传'}]">
        <Cropper v-model="postData.image"
                 :width="546"
                 :height="150" 
                 :options="{aspectRatio: 546 / 150}"></Cropper>
      </el-form-item>
      <el-form-item label="课程类型"
                    prop="type">
        <el-radio-group class="w-320"
                        v-model="postData.type">
          <el-radio :label="1">图文</el-radio>
          <el-radio :label="2">视频</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="价格"
                    prop="price"
                    :rules="[{required: true, message: '请填写'}]">
        <el-input-number v-model="postData.price"
                         :precision="2"
                         class="w-320"
                         :step="0.1"></el-input-number>
      </el-form-item>

      <el-form-item label="课程视频"
                    prop="video"
                    :required="postData.type==2?true:false"
                    :rules="[{required: postData.type==2?true:false, message: '请填写'}]">
        <OssUploaderVideo v-model="postData.video" />
      </el-form-item>

      <el-form-item label="关联课程"
                    prop="join_id">
        <el-select v-model="postData.join_id"
                   class="w-320"
                   placeholder="关联课程"
                   filterable
                   multiple
                   clearable>
          <slot />
          <el-option v-for="item in courseOptions"
                     :key="item.id"
                     :label="item.name"
                     :value="item.id" />
        </el-select>
      </el-form-item>
      <el-form-item label="展示状态"
                    prop="status">
        <el-radio-group class="w-320"
                        v-model="postData.status">
          <el-radio :label="1">展示</el-radio>
          <el-radio :label="0">不展示</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item prop="content"
                    :rules="[{required: true, message: '请填写'}]"
                    label="课程介绍">
        <Editor v-model="postData.content" />
      </el-form-item>
      <el-form-item style="padding-top: 50px">
        <el-button type="success"
                   @click="clickSave">保存</el-button>
        <el-button type="info"
                   @click="$router.back()"
                   style="margin-left: 100px;">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
  <script>
import Editor from 'components/form/Editor';
import TagsAdd from 'components/form/TagsAdd';
import OssUploaderVideo from 'components/form/OssUploaderVideo';
import Cropper from 'components/form/cropper';
import SchoolSelect from './components/SchoolSelect';

export default {
  name: 'CourseEdit',
  components: {
    Editor,
    Cropper,
    SchoolSelect,
    OssUploaderVideo,
    TagsAdd
  },
  data () {
    return {
      postData: {
        name: '',
        title: '',
        school_id: '',
        image: '',
        type: 1,
        price: '',
        video: '',
        join_id: '',
        status: 1,
        tags: [],
        content: '',
        lecturer: ''
      },
      courseOptions: []
    };
  },
  created () {
    this.id = this.$route.query.id;
    if (this.id) {
      this.getInfo();
    }

  },
  watch: {
    'postData.school_id' (val, oldVal) {
      if (val !== oldVal) {
        this.getCourseOptions(val)
      }
    }
  },
  methods: {
    getCourseOptions (schoolId) {
      this.$service
        .post('/ivep/TrainCourse/getCourseAllBySchool', {
          school_id: schoolId,
          unset_course_id: this.id || ''
        })
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.courseOptions = data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
    },
    getInfo () {
      this.$service
        .post('/ivep/TrainCourse/getCourseInfo', { id: this.id })
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.postData = data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
    },
    clickSave () {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.id) {
            this.saveEdit();
          } else {
            this.addNews();
          }
        } else {
          this.$message.error('表单错误');
        }
      });
    },
    addNews () {
      this.$service
        .post('/ivep/TrainCourse/addData', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$router.back();
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    saveEdit () {
      this.$service
        .post('/ivep/TrainCourse/updateData', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$router.back();
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    }
  }
};
</script>
