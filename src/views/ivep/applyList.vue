<template>
  <div class="container business">
    <header>
      <el-input placeholder="场馆" v-model="postData.name" class="w-150 m-r-10"></el-input>
      <el-button type="success" icon="search"  @click="search">搜索</el-button>
    </header>

    <el-table :data="tableData"
              stripe>
      <el-table-column prop="create_time"
                       align="center"
                       label="时间"></el-table-column>
      <el-table-column prop="bus_name"
                       align="center"
                       label="场馆"></el-table-column>
      <el-table-column prop="coach_name"
                       align="center"
                       label="咨询人"></el-table-column>
      <el-table-column prop="coach_phone"
                       align="center"
                       label="电话"></el-table-column>
      <el-table-column prop="school_name"
                       align="center"
                       label="咨询学院"></el-table-column>
      <el-table-column prop="course_name"
                       align="center"
                       label="访问课程"></el-table-column>
    </el-table>
    <footer>
      <el-pagination layout="total, prev, pager, next, sizes"
                     background
                     @size-change="sizeChange"
                     @current-change="pageChange"
                     :page-size="postData.page_size"
                     :current-page.sync="postData.page_no"
                     :page-sizes="[10, 20, 30, 40]"
                     :total="total"></el-pagination>
    </footer>
  </div>
</template>
  <script>
export default {
  name: 'ApplyList',
  data () {
    return {
      postData: {
        page_no: 1,
        page_size: 10,
        name: '',
        school_id: ''
      },
      total: 0,
      tableData: []
    };
  },
  created () {
    this.getList();
  },
  methods: {
    pageChange (page) {
      this.postData.page_no = page;
      this.getList();
    },
    search () {
      this.postData.page_no = 1;
      this.getList();
    },
    sizeChange (e) {
      this.postData.page_no = 1;
      this.postData.page_size = e;
      this.getList();
    },
    getList () {
      this.$service
        .post('/ivep/TrainCourseOffline/applyData', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = data.count;
            this.tableData = data.data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    }
  }
};
</script>
<style lang="less" scoped>
.banner-img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}
</style>