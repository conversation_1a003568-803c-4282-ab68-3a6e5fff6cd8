<template>
  <div class="container">
    <el-form
      class="form-frame"
      ref="form"
      :model="postData"
      label-width="150px"
    >
      <el-form-item
        label="主题名称"
        prop="title"
        :rules="[{ required: true, message: '请填写主题名称' }]"
      >
        <el-input
          v-model="postData.title"
          class="w-320"
          :disabled="!!id"
          placeholder="主题名称"
        ></el-input>
      </el-form-item>
      <el-form-item
        label="封面"
        prop="img_url"
        :rules="[{required: true, message: '请上传'}]"
      >
        <Cropper
          v-model="postData.img_url"
          :width="450"
          :height="100"
          :disabled="!!id"
          :options="{ aspectRatio: 450 / 100 }"
        ></Cropper>
      </el-form-item>

      <el-form-item label="发送门店" prop="receive_type">
        <el-radio-group
          class="w-320"
          v-model="postData.receive_type"
          :disabled="!!id"
          @change="typeChange"
        >
          <el-radio :label="1">全部门店</el-radio>
          <el-radio :label="2">指定门店</el-radio>
        </el-radio-group>
       
      </el-form-item>
      <el-form-item prop="receive_type" v-if="postData.receive_type === 2">
        <el-select
          v-model="postData.receive_bus_id"
          multiple
          filterable
          class="w-320"
          :disabled="!!id"
          placeholder="请选择"
        >
          <el-option
            v-for="item in busList"
            :key="item.id"
            :label="item.name"
            :value="item.id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item prop="description" label="描述介绍">
        <el-input
          class="w-320"
          type="textarea"
          :disabled="!!id"
          :autosize="{ minRows: 3, maxRows: 8}"
          v-model="postData.description"
        ></el-input>
      </el-form-item>
      <el-form-item prop="details" label="详情">
        <Editor v-model="postData.content" :disabled="!!id"/>
      </el-form-item>
      <el-form-item style="padding-top: 50px" v-if="!id">
        <el-button type="success" @click="clickSave">保存</el-button>
        <el-button
          type="info"
          @click="$router.back()"
          style="margin-left: 100px;"
          >取消</el-button
        >
      </el-form-item>
      <el-form-item style="padding-top: 50px" v-else>
        <el-button type="success" @click="$router.back()">确定</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
import Editor from 'components/form/Editor'
import Cropper from 'components/form/cropper'
import SchoolSelect from './components/SchoolSelect'

export default {
  name: 'CourseOfflineEdit',
  components: {
    Editor,
    Cropper,
    SchoolSelect
  },
  data() {
    return {
      postData: {
        title: '',
        img_url: '',
        receive_type: 1,
        receive_bus_id: [],
        description: '',
        content: ''
      },
      busList: []
    }
  },
  created() {
    this.id = this.$route.query.id
    this.getBusList()
    if (this.id) {
      this.getInfo()
    }
  },
  methods: {
    typeChange(val) {
      if (val == 1) {
        this.postData.bus_list = []
      }
    },
    getInfo() {
      this.$service
        .post('/ivep/SystemMessage/getMessageData', { id: this.id })
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data
            this.postData = data
          } else {
            this.$message.error(res.data.errormsg)
          }
        })
    },
    clickSave() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.addNews()
        } else {
          this.$message.error('表单错误')
        }
      })
    },
    getBusList() {
      this.$service.post('/web/business/bus_list').then(res => {
        if (res.data.errorcode == 0) {
          const data = res.data.data
          this.busList = data.bus_list
        } else {
          this.$message.error(res.data.errormsg)
        }
      })
    },
    addNews() {
      this.$service
        .post('/ivep/SystemMessage/addData', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$router.back()
          } else {
            this.$message.error(res.data.errormsg)
          }
        })
        .catch(err => {
          console.error(err)
        })
    }
  }
}
</script>
