<template>
  <div class="container business">
    <header>
      <el-input placeholder="通知主题" v-model="postData.title" class="w-150 m-r-10"></el-input>
      <el-button type="success" icon="search"  @click="search">搜索</el-button>
    </header>

    <el-table :data="tableData"
              stripe>
      <el-table-column prop="create_time"
                       align="center"
                       label="时间"></el-table-column>
      <el-table-column prop="title"
                       align="center"
                       label="通知主题"></el-table-column>
      <el-table-column prop="receive_bus_names"
                       align="center"
                       label="通知场馆"></el-table-column>
      <el-table-column prop="id"
                       align="center"
                       label="操作">
        <template scope="scope">
          <el-button size="small"
                     @click="toDetail(scope.row.id)"
                     type="primary">详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
        <el-button type="success"
                   @click="toDetail('')"
                   size="small"
                   icon="el-icon-plus">
          添加
        </el-button>
      </div>
      <el-pagination layout="total, prev, pager, next, sizes"
                     background
                     @size-change="sizeChange"
                     @current-change="pageChange"
                     :page-size="postData.page_size"
                     :current-page.sync="postData.page_no"
                     :page-sizes="[10, 20, 30, 40]"
                     :total="total"></el-pagination>
    </footer>
  </div>
</template>
  <script>
export default {
  name: 'message',
  data () {
    return {
      postData: {
        page_no: 1,
        page_size: 10,
        name: ''
      },
      total: 0,
      tableData: []
    };
  },
  created () {
    this.getList();
  },
  methods: {
    pageChange (page) {
      this.postData.page_no = page;
      this.getList();
    },
    search () {
      this.postData.page_no = 1;
      this.getList();
    },
    toDetail (id = '') {
      this.$router.push({ path: '/ivep/editMessage', query: { id } });
    },
    sizeChange (e) {
      this.postData.page_no = 1;
      this.postData.page_size = e;
      this.getList();
    },
    getList () {
      this.$service
        .post('/ivep/SystemMessage/index', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = data.count;
            this.tableData = data.list;
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    }
  }
};
</script>
<style lang="less" scoped>
.banner-img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}
</style>