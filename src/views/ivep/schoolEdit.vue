<template>
  <div class="container">
    <el-form class="form-frame"
             ref="form"
             :model="postData"
             label-width="150px">
      <el-form-item label="学院名称"
                    prop="name"
                    :rules="[{required: true, message: '请填写学院名称'}]">
        <el-input v-model="postData.name"
                  class="w-320"
                  placeholder="主题名称"></el-input>
      </el-form-item>
      <el-form-item label="学院logo"
                    prop="logo"
                    :rules="[{required: true, message: '请上传学院logo'}]">
        <Cropper v-model="postData.logo"></Cropper>
      </el-form-item>
      <el-form-item label="场馆标签">
        <TagsAdd class="w-320"
                 v-model="postData.tags" />
      </el-form-item>
      <el-form-item label="展示状态"
                    prop="status">
        <el-radio-group class="w-320"
                        v-model="postData.status">
          <el-radio :label="1">展示</el-radio>
          <el-radio :label="0">不展示</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="学院电话"
                    prop="school_tel"
                    :rules="[{required: true, message: '请填写'}]">
        <el-input v-model="postData.school_tel"
                  class="w-320"
                  placeholder="学院电话"></el-input>
      </el-form-item>
      <el-form-item label="咨询老师电话"
                    prop="teacher_tel"
                    :rules="[{required: true, message: '请填写'}]">
        <el-input v-model="postData.teacher_tel"
                  class="w-320"
                  placeholder="咨询老师电话"></el-input>
      </el-form-item>
      <el-form-item label="地址"
                    prop="address"
                    :rules="[{required: true, message: '请填写'}]">
        <el-input v-model="postData.address"
                  class="w-320"
                  placeholder="地址"></el-input>
      </el-form-item>
      <el-form-item label="二维码图片"
                    prop="code_image"
                    :rules="[{required: true, message: '请上传二维码图片'}]">
        <Cropper v-model="postData.code_image"
                 placeholder="二维码图片"></Cropper>
      </el-form-item>
      <el-form-item prop="introduction"
                    label="学院简介">
        <Editor v-model="postData.introduction" />
      </el-form-item>
      <el-form-item style="padding-top: 50px">
        <el-button type="success"
                   @click="clickSave">保存</el-button>
        <el-button type="info"
                   @click="$router.back()"
                   style="margin-left: 100px;">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
  <script>
import Editor from 'components/form/Editor';
import TagsAdd from 'components/form/TagsAdd';
import Cropper from 'components/form/cropper';
import ImgUploader from 'components/form/imgUploader';

export default {
  name: 'SchoolEdit',
  components: {
    Editor,
    Cropper,
    TagsAdd,
    ImgUploader
  },
  data () {
    return {
      postData: {
        name: '',
        logo: '',
        status: 1,
        tags: [],
        school_tel: '',
        teacher_tel: '',
        address: '',
        code_image: '',
        introduction: ''
      }
    };
  },
  created () {
    this.id = this.$route.query.id;
    if (this.id) {
      this.getInfo();
    }
  },
  methods: {
    getInfo () {
      this.$service
        .post('/ivep/TrainSchool/getSchoolInfo', { id: this.id })
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.postData = data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    clickSave () {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.id) {
            this.saveEdit();
          } else {
            this.addNews();
          }
        } else {
          this.$message.error('表单错误');
        }
      });
    },
    addNews () {
      this.$service
        .post('/ivep/TrainSchool/addData', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$router.back();
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    saveEdit () {
      this.$service
        .post('/ivep/TrainSchool/updateData', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$router.back();
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    }
  }
};
</script>
