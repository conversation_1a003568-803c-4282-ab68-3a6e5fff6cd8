<template>
  <div class="m-b-20 ovf-hd">
    <el-tabs type="border-card" v-model="tabSwitch" @tab-click="handleSwitch">
      <el-tab-pane label="私教配置" name="privatecoach">
        <private-service-list :tabSwitch="tabSwitch" v-if="loadArr.includes('0')"></private-service-list>
      </el-tab-pane>
      <el-tab-pane label="泳教配置" name="swimcoach">
        <swim-service-list :tabSwitch="tabSwitch" v-if="loadArr.includes('1')"></swim-service-list>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import http from 'assets/js/http'
import ServiceList from './serviceList.vue'

export default {
  name: 'serviceList',
  components: {
    ServiceList,
  },
  data() {
    return {
      tabSwitch: 'privatecoach',
      loadArr: ['0'],
    }
  },
  components: {
    privateServiceList: ServiceList,
    swimServiceList: ServiceList,
  },
  methods: {
    handleSwitch(tab, event) {
      console.log('handleSwitch' + this.loadArr)
      // console.log("handleSwitch"+ tab.index);
      if (!this.loadArr.includes(tab.index)) {
        this.loadArr.push(tab.index)
      }
    },
  },
}
</script>

<style scoped></style>
