<template>
  <div class="container">
    <header>
      <el-date-picker class="m-r-10" v-model="value6" type="daterange" range-separator="~" start-placeholder="开始时间" end-placeholder="结束时间" value-format="yyyy-MM-dd" @change="pickChange">
      </el-date-picker>
      <el-select v-model="postData.bus_id" placeholder="场馆" @change="getCoachList" filterable clearable> 
        <el-option v-for="bus in busList" :key="bus.bus_id" :value="bus.bus_id" :label="bus.bus_name"></el-option>
      </el-select>
      <el-select v-model="postData.coach_id" placeholder="教练" filterable clearable>
        <el-option v-for="coach in coachList" :key="coach.coach_id" :value="coach.coach_id" :label="coach.coach_name"></el-option>
      </el-select>
      <el-button type="primary" icon="search" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe ref="table">
      <el-table-column type="expand">
      <template slot-scope="props">
        <el-form label-position="left" inline class="demo-table-expand">
          <el-form-item label="人脸识别返回值">
            <span>{{ props.row.res_detect }}</span>
          </el-form-item>
          <el-form-item label="人脸检验返回值">
            <span>{{ props.row.res }}</span>
          </el-form-item>
        </el-form>
      </template>
    </el-table-column>
      <el-table-column align="center" type="index" label="编号" width="80">
        <template scope="scope">
          {{scope.$index + postData.page_size*(postData.page_no-1) + 1}}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="operator_name" label="教练名称"></el-table-column>
       <el-table-column align="center" label="状态">
        <template scope="scope">
          {{scope.row.status == 0?"成功":"失败"}}
        </template>
      </el-table-column>
      <el-table-column align="center" label="识别照片">
        <template scope="scope">
          <img @click="showImgModal(scope.row.url)" style="width: 100px; cursor: pointer"
               :src="scope.row.url" alt="">
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size"
                     :current-page="postData.page_no" :total="dataCount">
      </el-pagination>
    </footer>

    <el-dialog :visible.sync="showModal" title="图片">
      <img :src="modalImg" style="width: 100%" alt="">
    </el-dialog>
  </div>
</template>

<script>
  import http from 'assets/js/http';

  export default {
    name: 'IvepLog',
    data() {
      return {
        busList: [],
        coachList: [],
        modalImg: '',
        showModal: false,
        tableData: [],
        value6: '',
        postData: {
          start_time: '',
          end_time: '',
          coach_id: '',
          bus_id: '',
          page_size: 10,
          page_no: 1,
        },
        dataCount: 0
      };
    },
    methods: {
      showImgModal(img) {
        this.modalImg = img;
        this.showModal = true;
      },
      getBusList() {
        const url = '/ivep/IvepBase/getBusList'
        this.$service.post(url).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.busList = data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      getCoachList(bus_id) {
        if (!bus_id) return false;
        this.postData.coach_id = '';
        const url = '/ivep/IvepBase/getCoachList'
        this.$service.post(url, { bus_id, type: 1,service_type:this.postData.service_type }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.coachList = data;
            if (!data.length) {
              this.$message.error('该场馆无管理员账号')
            }
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      pickChange(val) {
        this.postData.start_time = val ? val[0] : '';
        this.postData.end_time = val ? val[1] : '';
      },
      getList() {
        this.apiGet('/ivep/IvepBase/getIvepSearchFaceLog', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.dataCount = res.data.count;
            this.tableData = res.data.list
          } else {
            _g.toastMsg('success', res.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.getList();
      },
      search() {
        this.postData.page_no = 1;
        this.getList();
      }
    },
    created() {
      this.getList();
      this.getBusList()
    },
    mixins: [http]
  };
</script>
