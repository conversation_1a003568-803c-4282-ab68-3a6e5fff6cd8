<template>
  <el-dialog
    title="批量延期"
    :visible="show"
    width="500px"
    :close-on-click-modal="false"
    @close="handleCancel"
  >
    <el-form
      ref="form"
      label-width="80px"
      :model="formData"
      class="info-dialog-form">
      <el-form-item label="已选门店">
        <div class="names-row">{{ names }}（共{{ selected.length }}家）</div>
      </el-form-item>
      <template>
        <el-form-item
          label="延期方式"
          :rules="{ type: 'string', required: true, message: '请选择延期方式', trigger: 'change' }"
          prop="expire_type">
          <el-radio-group v-model="formData.expire_type" class="radiogroup">
            <el-radio class="radio radioitem" label="1">顺延有效期天数</el-radio>
            <el-radio class="radio radioitem" label="0">延期到指定日期</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-show="formData.expire_type === '1'"
          label="延期天数"
          prop="expire_days"
          :rules="{ type: 'number', required: formData.expire_type === '1', message: '请填写延期天数', trigger: 'blur' }" >
          <el-input-number
            class="expire-input-number"
            v-model="formData.expire_days"
            :min="1"
            :max="3650"
            controls-position="right"
            step-strictly
            placeholder="请填写延期天数"
          ></el-input-number>
        </el-form-item>
        <el-form-item
          v-show="formData.expire_type === '0'"
          label="到期时间"
          prop="expire_date"
          :rules="{ type: 'date', required: formData.expire_type === '0', message: '请选择到期时间', trigger: 'change' }" >
          <el-date-picker
            v-model="formData.expire_date"
            class="w-100p"
            type="date"
            placeholder="请选择到期时间"
            :editable="false"
            :clearable="false"
            :picker-options="datePickerOption">
          </el-date-picker>
        </el-form-item>
      </template>
    </el-form>

    <div slot="footer">
      <el-button @click="handleCancel">关闭</el-button>
      <!-- <el-button class="m-l-20" @click="$refs.form.resetFields()">重置</el-button> -->
      <el-button class="m-l-20" type="primary" :disabled="!selected.length" @click="handleConfirm">提交</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import http from 'assets/js/http';
  import { mapState } from 'vuex';

  export default {
    name: 'PackageLengthenDialog',
    mixins: [http],

    props: {
      show: {
        type: Boolean,
        required: true
      },
      selected: {
        type: Array,
        default: () => []
      },
      nameKey: {
        type: String,
        default: 'business_name'
      },
      idKey: {
        type: String,
        default: 'bus_id'
      },
      serviceType: {
        type: Number,
        required: true
      }
    },
    data() {
      return {
        names: '',
        formData: {
          expire_type: '1', // 延期方式 '0' 为延期到指定日期 '1'为顺延有效天数
          expire_date: null, // 到期时间
          expire_days: undefined, // 延期天数
          version_id: '', // 版本id
          version_type: '' // '0'清空后赋权, '1'智能赋权
        },
        versionList: [], // 赋权时的可选版本
        datePickerOption: {
          shortcuts: [
            {
              text: '今天',
              onClick(picker) {
                picker.$emit('pick', new Date())
              }
            },
            {
              text: '30天后',
              onClick(picker) {
                const date = new Date(Date.now() + 30 * 24 * 3600 * 1000);
                picker.$emit('pick', date)
              }
            },
            {
              text: '60天后',
              onClick(picker) {
                const date = new Date(Date.now() + 60 * 24 * 3600 * 1000);
                picker.$emit('pick', date)
              }
            },
            {
              text: '90天后',
              onClick: (picker) => {
                const date = new Date(Date.now() + 90 * 24 * 3600 * 1000);
                picker.$emit('pick', date)
              }
            },
            {
              text: '增加30天',
              onClick: (picker) => {
                const old = new Date(this.formData.expire_date || Date.now());
                const date = new Date(old.getTime() + 30 * 24 * 3600 * 1000);
                picker.$emit('pick', date)
              }
            },
            {
              text: '增加60天',
              onClick: (picker) => {
                const old = new Date(this.formData.expire_date || Date.now());
                const date = new Date(old.getTime() + 60 * 24 * 3600 * 1000);
                picker.$emit('pick', date)
              }
            },
            {
              text: '增加90天',
              onClick: (picker) => {
                const old = new Date(this.formData.expire_date || Date.now());
                const date = new Date(old.getTime() + 90 * 24 * 3600 * 1000);
                picker.$emit('pick', date)
              }
            }
          ]
        }
      }
    },
    computed: {
      ...mapState(['userInfo'])
    },

    watch: {
      show(val) {
        if (val) {
          const { selected, nameKey } = this
          this.names = selected.length === 0
            ? 'null'
            : selected.map(v => v[nameKey]).join('、');
        } else {
          Object.assign(this.$data.formData, this.$options.data().formData)
          this.$refs.form.clearValidate()
        }
      }
    },
    methods: {
      handleCancel() {
        this.$emit('update:show', false)
      },

      handleConfirm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            const { selected, idKey, formData } = this
            const url = '/ivep/IvepBase/setBusinessesExpireDate';
            const params = {
              bus_ids: this.selected.map(v => v[idKey]),
              service_type: this.serviceType,
              type: formData.expire_type,
              [formData.expire_type === '1' ? 'day' : 'expire_date']: formData.expire_type === '1'
                ? formData.expire_days
                : _g.formatDate(formData.expire_date, 'yyyy-MM-dd'),
            }
            this.apiPost(url, params).then(res => {
              if (res.errorcode == 0) {
                _g.toastMsg('success', res.errormsg);
                this.$emit('on-success')
                this.handleCancel();
              } else {
                _g.toastMsg('warning', res.errormsg);
              }
            });
          }
        });
      },
    },
  }
</script>

<style lang="less" scoped>
.names-row {
  margin-top: 8px;
  line-height: 1.8;
}

.expire-input-number {
  width: 100%;
  /deep/.el-input__inner {
    text-align: left;
  }
}
</style>
