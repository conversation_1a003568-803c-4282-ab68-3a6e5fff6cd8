<template>
  <el-select
    v-model="adString"
    class="filter-item"
    :placeholder="placeholder"
    :filterable="filterable"
    :clearable="clearable"
    :disabled="disabled"
    :multiple="multiple"
    @change="adChange"
  >
    <slot />
    <el-option
      v-for="item in adOptions"
      :key="item.id"
      :label="item.name"
      :value="item.id"
    />
  </el-select>
</template>

<script>
export default {
  name: 'SchoolSelect',
  props: {
    value: {
      required: true
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    filterable: {
      type: Boolean,
      default: true
    },
    clearable: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: <PERSON>olean,
      default: false
    },
    multiple: Boolean
  },
  data() {
    return {
      adOptions: []
    }
  },
  computed: {
    adString: {
      get() {
        return this.value
      },
      set(val) {
        this.$emit('input', val)
      }
    }
  },
  watch: {
  },
  created() {
    this.initData()
  },
  methods: {
    adChange(id) {
      let info = ''
      if (!this.multiple) {
        this.adOptions.forEach((item, index) => {
          if (item.ad_string === this.adString) {
            info = item
          }
        })
        this.$emit('on-change', info)
      } else {
        this.$emit('on-change', id)
      }
    },
    initData() {
      this.$service
        .post('/ivep/TrainSchool/getSchoolAll', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.adOptions = res.data.data
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
     
    }
  }
}
</script>
