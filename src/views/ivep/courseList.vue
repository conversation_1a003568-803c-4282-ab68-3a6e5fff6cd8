<template>
  <div class="container business">
    <header>
      <el-input placeholder="课程名称" v-model="postData.name" class="w-150 m-r-10"></el-input>
      <school-select class="w-150"
                       v-model="postData.school_id" />
      <el-button type="success" icon="search"  @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData"
              stripe>
      <el-table-column prop="name"
                       align="center"
                       label="课程名称"></el-table-column>
      <el-table-column prop="school_name"
                       align="center"
                       label="归属学院"></el-table-column>
      <el-table-column prop="price"
                       align="center"
                       label="价格"></el-table-column>
      <el-table-column prop="tags"
                       align="center"
                       label="课程标签"></el-table-column>
      <el-table-column prop="create_time"
                       align="center"
                       label="创建时间"></el-table-column>
      <el-table-column prop="join"
                       align="center"
                       label="关联课程"></el-table-column>
      <el-table-column prop="status"
                       align="center"
                       label="状态"></el-table-column>
      <el-table-column prop="read_num"
                       align="center"
                       label="阅读人数"></el-table-column> 
      <el-table-column prop="read_count"
                       align="center"
                       label="阅读次数"></el-table-column>  
      <el-table-column prop="comment1"
                       align="center"
                       label="有用"></el-table-column> 
      <el-table-column prop="comment2"
                       align="center"
                       label="没用"></el-table-column>  
      <el-table-column prop="id"
                       align="center"
                       label="操作">
        <template scope="scope">
          <el-button size="small"
                     @click="toDetail(scope.row.id)"
                     type="primary">编辑</el-button>
          <el-button size="small"
                     type="danger"
                     @click="deleteNews(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
        <el-button type="success"
                   @click="toDetail('')"
                   size="small"
                   icon="el-icon-plus">
          添加
        </el-button>
      </div>
      <el-pagination layout="total, prev, pager, next, sizes"
                     background
                     @size-change="sizeChange"
                     @current-change="pageChange"
                     :page-size="postData.page_size"
                     :current-page.sync="postData.page_no"
                     :page-sizes="[10, 20, 30, 40]"
                     :total="total"></el-pagination>
    </footer>
  </div>
</template>
  <script>
import SchoolSelect from './components/SchoolSelect';
export default {
  name: 'CourseList',
  data () {
    return {
      postData: {
        page_no: 1,
        page_size: 10,
        name: '',
        school_id: ''
      },
      total: 0,
      tableData: []
    };
  },
  components: {
    SchoolSelect
  },
  created () {
    this.getList();
  },
  methods: {
    pageChange (page) {
      this.postData.page_no = page;
      this.getList();
    },
    search () {
      this.postData.page_no = 1;
      this.getList();
    },
    toDetail (id = '') {
      this.$router.push({ path: '/ivep/courseEdit', query: { id } });
    },
    deleteNews (id) {
      this.$confirm('确定删除?', '提示', {
        type: 'warning'
      }).then(() => {
        this.$service
          .post('/ivep/TrainCourse/deleteData', { id })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$message.success(res.data.errormsg);
              this.getList();
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
      })
    },
    sizeChange (e) {
      this.postData.page_no = 1;
      this.postData.page_size = e;
      this.getList();
    },
    getList () {
      this.$service
        .post('/ivep/TrainCourse/index', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = data.count;
            this.tableData = data.data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    }
  }
};
</script>
<style lang="less" scoped>
.banner-img {
  width: 60px;
  height: 60px;
  border-radius: 50%;
}
</style>