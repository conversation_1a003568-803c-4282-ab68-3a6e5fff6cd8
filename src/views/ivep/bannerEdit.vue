<template>
  <div class="container">
    <el-form class="form-frame"
             ref="form"
             :model="postData"
             label-width="150px">
      <el-form-item label="主题名称"
                    prop="title"
                    :rules="[{required: true, message: '请填写主题名称', trigger: 'blur'}]">
        <el-input v-model="postData.title" class="w-320" placeholder="主题名称"></el-input>
      </el-form-item>
      <el-form-item label="封面上传">
        <Cropper v-model="postData.image"
                 class="banner-cropper"
                 :width="657"
                 :height="264"
                 :options="{aspectRatio: 219 / 88}"></Cropper>
      </el-form-item>
      <el-form-item label="展示状态"
                    prop="status">
        <el-radio-group v-model="postData.status">
          <el-radio :label="1">展示</el-radio>
          <el-radio :label="0">不展示</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="details"
                    label="子页面内容">
        <Editor v-model="postData.content" />
      </el-form-item>
      <el-form-item style="padding-top: 50px">
        <el-button type="success"
                   @click="clickSave">保存</el-button>
        <el-button type="info"
                   @click="$router.back()"
                   style="margin-left: 100px;">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
  <script>
import Editor from 'components/form/Editor';
import Cropper from 'components/form/cropper';
import ImgUploader from 'components/form/imgUploader';

export default {
  name: 'BannerEdit',
  components: {
    Editor,
    Cropper,
    ImgUploader
  },
  data () {
    return {
      postData: {
        title: '',
        image: '',
        status: 1,
        content: ''
      }
    };
  },
  created () {
    this.id = this.$route.query.id;
    if (this.id) {
      this.getInfo();
    }
  },
  methods: {
    getInfo () {
      this.$service
        .post('/ivep/Train/getBannerOne', { id: this.id })
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.postData = data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    clickSave () {
      this.$refs.form.validate(valid => {
        if (valid) {
          if (this.id) {
            this.saveEdit();
          } else {
            this.addNews();
          }
        } else {
          this.$message.error('表单错误');
        }
      });
    },
    addNews () {
      this.$service
        .post('/ivep/Train/addBanner', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$router.back();
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    saveEdit () {
      this.$service
        .post('/ivep/Train/saveBannerData', this.postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            this.$router.back();
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    }
  }
};
</script>
<style lang="less">
// .banner-cropper {
//   .image-wrapper {
//     width: 657px !important;
//     height: 264px !important;
//   }
// }
</style>
