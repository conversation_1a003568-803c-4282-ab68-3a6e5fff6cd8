<template>
  <div class="container business">
    <el-table :data="tableData"
              stripe>
      <el-table-column prop="image"
                       align="center"
                       label="封面">
        <template scope="{row}">
          <img class="banner-img"
               :src="row.image"
               alt="" />
        </template>
      </el-table-column>
      <el-table-column prop="title"
                       align="center"
                       label="主题名称"></el-table-column>
      <el-table-column prop="create_time"
                       align="center"
                       label="创建时间"></el-table-column>
      <el-table-column prop="status"
                       align="center"
                       label="状态"></el-table-column>
      <el-table-column prop="id"
                       align="center"
                       label="操作">
        <template scope="scope">
          <el-button size="small"
                     @click="toDetail(scope.row.id)"
                     type="primary">编辑</el-button>
          <el-button size="small"
                     type="danger"
                     @click="deleteNews(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
        <el-button type="success"
                   @click="toDetail('')"
                   size="small"
                   icon="el-icon-plus">
          添加
        </el-button>
        <!-- <el-dropdown size="small" @command="handleDropdown" placement="top" type="info"><el-button type="info" size="small" style="margin-left: 20px">
            其他操作
            <i class="el-icon-arrow-down el-icon--right"></i></el-button><el-dropdown-menu slot="dropdown"><el-dropdown-item command="0">批量删除</el-dropdown-item></el-dropdown-menu></el-dropdown> -->
      </div>
      <el-pagination layout="total, prev, pager, next, sizes"
                     background
                     @size-change="sizeChange"
                     @current-change="pageChange"
                     :page-size="pageSize"
                     :current-page.sync="page"
                     :page-sizes="[10, 20, 30, 40]"
                     :total="total"></el-pagination>
    </footer>
  </div>
</template>
  <script>

export default {
  name: 'BannerList',
  data () {
    return {
      page: 1,
      pageSize: 10,
      total: 0,
      tableData: []
    };
  },
  created () {
    this.getList();
  },
  methods: {
    pageChange (page) {
      this.page = page;
      this.getList();
    },
    toDetail (id = '') {
      this.$router.push({ path: '/ivep/bannerEdit', query: { id } });
    },
    deleteNews (id) {
      this.$confirm('确定删除?', '提示', {
        type: 'warning'
      }).then(() => {
        this.$service
          .post('/ivep/Train/deletedBanner', { id })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$message.success(res.data.errormsg);
              this.getList();
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
      })
    },
    sizeChange (e) {
      this.page = 1;
      this.pageSize = e;
      this.getList();
    },
    getList () {
      this.$service
        .post('/ivep/Train/getBannerList', { page_no: this.page, page_size: this.pageSize })
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = data.count;
            this.tableData = data.data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    }
  }
};
</script>
<style lang="less" scoped>
.banner-img {
  width: 219px;
  height: 88px;
}
</style>