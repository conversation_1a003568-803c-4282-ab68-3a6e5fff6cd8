<template>
 <div class="container">
    <header>
      <el-input placeholder="场馆名称" v-model="postData.bus_name" @keydown.enter.native="doSearch"></el-input>
      <el-date-picker type="daterange" v-model="dateRange" value-format="yyyy-MM-dd" @change="dateChange"></el-date-picker>
      <el-button type="success" @click="doSearch">查询</el-button>
    </header>
    <el-table ref="tableRef" :data="tableData" row-key="bus_id" stripe style="width: 100%" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" reserve-selection></el-table-column>
      <el-table-column prop="bus_name" label="场馆名称" align="center"></el-table-column>
      <el-table-column prop="mer_name" label="所属商家" align="center"></el-table-column>
      <el-table-column prop="create_time" label="开通时间" align="center"></el-table-column>

      <el-table-column prop="admin_count" label="账号数量" align="center"></el-table-column>
      <el-table-column prop="active_count" label="绑定账号数" align="center"></el-table-column>
      <el-table-column prop="end_time" label="到期时间" align="center"></el-table-column>
      <el-table-column prop="renew_status" label="使用状态" align="center">
        <!-- 0为空字符串 1为试用版 2为首次付费 3为临时延期 4为续费 -->
        <template scope="scope">
          <span v-if="scope.row.renew_status == 0"> - </span>
          <span v-if="scope.row.renew_status == 1">试用版</span>
          <span v-if="scope.row.renew_status == 2">首次付费</span>
          <span v-if="scope.row.renew_status == 3">临时延期</span>
          <span v-if="scope.row.renew_status == 4">续费</span>
        </template>
      </el-table-column>
    
      <el-table-column prop="" label="操作" align="center">
        <template scope="scope">
          <router-link :to="{ path: 'serviceDetail', query: { id: scope.row.id, bus_id: scope.row.bus_id, service_type: postData.service_type } }">
            <el-button type="text">编辑</el-button>
          </router-link>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <router-link :to="{ path: 'serviceDetail', query: { id: '', bus_id: '', service_type: postData.service_type } }"><el-button type="primary">新开通场馆</el-button></router-link>
      <el-button style="margin-left: 15px;" @click="handleShowBatched">批量延期</el-button>
      <el-button style="margin-left: 15px;" type="success" @click="exportCsv">
        导出Excel
      </el-button>
      <Pager :total="total" :post-data="postData" @on-change="pageChange"></Pager>
     
      <PackageBatchedDialog
        :show.sync="showBatched"
        :selected="multipleSelection"
        nameKey="bus_name"
        :service-type="tabSwitch==='privatecoach'?0:1"
        @on-success="handleClearSelection"
      />
    </footer>
  </div>
  </template>
  
  <script>

  import Pager from 'src/components/pager';
  import ExportCsv from 'src/components/form/csvExport';
  import PackageBatchedDialog from './components/packageBatchedDialog';
  
  export default {
    name: 'serviceList',
    components: { 
      Pager,
      PackageBatchedDialog,
    },
    data() {
      return {
        dateRange: [],
        postData: {
          begin_time: '',
          end_time: '',
          bus_name: '',
          service_type: 0
        },
        total: 0,
        columns: [
          {
            title: '',
            key: '',
          }
        ],
        tableData: [],
        multipleSelection: [],
        showBatched: false, // 批量操作弹窗
      };
    },
    created() {
      this.getList()
    },
    props: {
      tabSwitch: String
    },
    methods: {
      dateChange(date) {
        const [s, e] = date || [];
        this.postData.begin_time = s;
        this.postData.end_time = e;
      },
      pageChange(postData) {
        const { page_no, page_size } = postData
        this.postData = { ...this.postData, page_no, page_size }
        this.getList()
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      getList() {
        const url = '/ivep/IvepBase/getIvepList';
        // this.postData.service_type = this.tabSwitch;
        if (this.tabSwitch == 'swimcoach') {
          this.postData.service_type = 1;
        } else {
          this.postData.service_type = 0;
        }
        return this.$service.post(url, this.postData).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = data.count;
            this.tableData = data.list;
            return data.list;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e);
        });
      },
      exportCsv() {
        const columns = [
          { prop: 'bus_name', label: '场馆名称' },
          { prop: 'mer_name', label: '所属商家' },
          { prop: 'create_time', label: '开通时间' },
          { prop: 'admin_count', label: '账号数量' },
          { prop: 'active_count', label: '绑定账号数' },
          { prop: 'end_time', label: '到期时间' },
          { prop: 'renew_status_text', label: '使用状态' },
        ];
        this.exportDataList(columns);
      },
      exportDataList(columns) {
        let postd = { is_export: 1 };
        postd = Object.assign(postd, this.postData);
        postd.page_size = this.total;
        this.$service.post('ivep/IvepBase/getIvepList', postd).then(res => {
          if (res.data.errorcode == 0) {
            const list = res.data.data.list;
            list.forEach(item => {
              if (item.renew_status == 0) {
                item.renew_status_text = '-';
              } else if (item.renew_status == 1) {
                item.renew_status_text = '试用版';
              } else if (item.renew_status == 2) {
                item.renew_status_text = '首次付费';
              } else if (item.renew_status == 3) {
                item.renew_status_text = '临时延期';
              } else if (item.renew_status == 4) {
                item.renew_status_text = '续费';
              }
            })
            ExportCsv(list, columns);
          } else {
            this.$message.error(res.data.errormsg);
          }
        });
      },
      handleShowBatched() {
        if (this.multipleSelection.length) {
          this.showBatched = true
        } else {
          this.$message.warning('请勾选需要延期的商家');
        }
      },
      handleClearSelection() {
        this.$refs.tableRef.clearSelection();
        this.getList();
      },
      handleSelectionChange(val) {
        this.multipleSelection = val
      },
    },
  };
  </script>
  
  