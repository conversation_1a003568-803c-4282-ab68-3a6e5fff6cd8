<template>
  <div>

    <el-card>
      <el-form class="form" label-width="120px" :model="postData" :rules="rules" ref="form">
        <el-form-item label="场馆" v-if="!isEdit">
          <el-select v-model="postData.bus_id" @change="getCoachList" filterable>
            <el-option v-for="bus in busList" :key="bus.bus_id" :value="bus.bus_id" :label="bus.bus_name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="泳教账号数量" v-if="isSwim">
          <el-input-number v-model="postData.admin_count" controls-position="right" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="私教账号数量" v-else>
          <el-input-number v-model="postData.admin_count" controls-position="right" :min="0"></el-input-number>
        </el-form-item>
        <el-form-item label="管理员账号">
          <el-select v-model="postData.coach_id" placeholder="仅可选择教练场馆权限">
            <el-option v-for="coach in coachTypeList" :key="coach.coach_id" :value="coach.coach_id" :label="coach.coach_name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="到期时间" prop="end_time">
          <el-date-picker v-model="postData.end_time" value-format="yyyy-MM-dd"></el-date-picker>
        </el-form-item>
        <el-form-item label="团操课功能" prop="course_schedule">
          <el-radio-group v-model="postData.course_schedule" class="radiogroup">
            <el-radio class="radio radioitem" :label="1">启用</el-radio>
            <el-radio class="radio radioitem" :label="0">不启用</el-radio>
          </el-radio-group>
        </el-form-item>
        <!-- 0为空字符串 1为试用版 2为首次付费 3为临时延期 4为续费 -->
        <el-form-item label="当前状态" prop="renew_status">
          <el-radio-group v-model="postData.renew_status" class="radiogroup">
            <template v-if="!isEdit || isTry">
              <el-radio class="radio radioitem" :label="1">试用</el-radio>
              <el-radio class="radio radioitem" :label="2">首次付费</el-radio>
            </template>
            <template v-else>
              <el-radio v-if="isEdit" class="radio radioitem" :label="3">临时延期</el-radio>
              <el-radio v-if="isEdit" class="radio radioitem" :label="4">续费</el-radio>
            </template>
          </el-radio-group>
        </el-form-item>

        <el-form-item label="备注" v-if="isEdit">
          <el-input v-model="postData.remark" type="textarea"></el-input>
        </el-form-item>
        <el-form-item style="padding-top: 30px;" label=" ">
          <el-button type="primary" @click="handleSave">确定</el-button>
          <router-link to="serviceManagement"><el-button style="margin-left: 30px;">取消</el-button></router-link>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card style="margin-top: 30px;" header="操作记录" v-if="isEdit">
      <div class="container">
        <el-table :data="logList">
          <el-table-column type="index"></el-table-column>
          <el-table-column prop="createTime" label="操作时间"></el-table-column>
          <el-table-column prop="username" label="操作人"></el-table-column>

          <el-table-column prop="change_admin_count" label="泳教账号数量变更" v-if="isSwim"></el-table-column>
          <el-table-column prop="change_admin_count" label="私教账号数量变更" v-else></el-table-column>

          <el-table-column prop="change_end_time" label="泳教到期时间变更" v-if="isSwim"></el-table-column>
          <el-table-column prop="change_end_time" label="私教到期时间变更" v-else></el-table-column>
          <!-- <el-table-column prop="change_swim_admin_count" label="泳教账号数量变更"></el-table-column>
          <el-table-column prop="change_swim_end_time" label="泳教到期时间变更"></el-table-column> -->
          <el-table-column prop="remark" label="说明"></el-table-column>
        </el-table>
        <footer>
          <Pager :total="total" @on-change="pageChange"></Pager>
        </footer>
      </div>
    </el-card>
  </div>
</template>

<script>
  import Pager from 'src/components/pager'
  import { formatDate } from 'src/utils'

  export default {
    name: 'serviceDetail',
    components: { Pager },
    data() {
      return {
        isTry: true,
        busList: [],
        coachList: [],
        postData: {
          bus_id: '',
          admin_count: 0,
          coach_id: '',
          end_time: '',
          remark: '',
          service_type: 0,
          id: '',
          renew_status: 0,
          course_schedule: 0,
        },
        rules: {
          end_time: [
            { required: true, message: '请选择到期时间', trigger: 'change' }
          ],
          renew_status: [
            { required: true, message: '请选择状态', trigger: 'change' }
          ]
        },
        logList: [],
        total: 0,
        logPostData: {
          bus_id: '',
          page_no: 1,
          page_size: 10,
          service_type:0
        }
      }
    },
    created() {
      if (this.$route.query.id) {
        const { id, bus_id,service_type} = this.$route.query;
        this.logPostData.bus_id = bus_id;
        this.postData.id = id;
        this.postData.service_type = service_type;
        this.logPostData.service_type = service_type;
        this.getCoachList(this.logPostData.bus_id)
        this.getInfo()
        this.getLogList()
      } else {
        this.postData.service_type = this.$route.query.service_type;
        this.logPostData.service_type = this.$route.query.service_type;
        this.getBusList()
      }
    },
    computed: {
      isEdit() {
        return !!this.$route.query.id
      },
      isSwim() {
        return !!this.$route.query.service_type
      },
      coachTypeList(){
        // console.log(this.coachList);
        return this.coachList.filter((item)=>{
          // console.log(item.coach_type);
          // console.log(this.postData.service_type);
          return item.coach_type == this.postData.service_type;
        });
        // console.log(arr);
        // return arr;
      }
    },
    methods: {
      handleSave() {
        this.$refs.form.validate((valid) => {
          if (this.postData.renew_status == 0) {
            this.postData.renew_status = ''
            return false;
          }
          if (valid) {
            let url = '/ivep/IvepBase/addIvepSetting'
            if (this.$route.query.id) {
              url = '/ivep/IvepBase/updateIvepInfo'
            }
            this.$service.post(url, this.postData).then(res => {
              if (res.data.errorcode === 0) {
                this.$message.success(res.data.errormsg)
                this.$router.back();
              } else {
                this.$message.error(res.data.errormsg);
              }
            }).catch(e => {
              throw new Error(e)
            })
          }
        })
      },
      pageChange({ page_no, page_size }) {
        this.logPostData = { ...this.logPostData, page_no, page_size }
        this.getLogList()
      },
      getLogList() {
        const url = '/ivep/IvepBase/getRecordList'
        this.$service.post(url, this.logPostData).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = +data.count;
            this.logList = data.list.map(item => {
              return {
                ...item,
                createTime: formatDate(new Date(item.create_time * 1000), 'yyyy-MM-dd HH:mm')
              }
            });
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      getInfo() {
        const url = '/ivep/IvepBase/getIvepInfo'
        this.$service.post(url, { id: this.$route.query.id ,service_type:this.$route.query.service_type}).then(res => {
          if (res.data.errorcode === 0) {
            let service_type = this.postData.service_type;
            const data = res.data.data;
            this.postData = data;
            this.postData.service_type = service_type;
            this.isTry = data.renew_status == 1;
            this.postData.course_schedule = Number(data.course_schedule);
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      getBusList() {
        const url = '/ivep/IvepBase/getBusList'
        this.$service.post(url).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.busList = data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      getCoachList(bus_id) {
        this.postData.coach_id = '';
        const url = '/ivep/IvepBase/getCoachList'
        this.$service.post(url, { bus_id, service_type:this.postData.service_type}).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            
            this.coachList = data;
            if (!data.length) {
              this.$message.error('该场馆无管理员账号')
            }
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      }
    },
  }
</script>

<style scoped>

</style>
