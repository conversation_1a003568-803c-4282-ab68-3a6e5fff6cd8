<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="150px">
      <el-form-item label="健身仓编号" prop="number" :rules="{ required: true, message: '请填写健身仓编号'}">
        <el-input class="w-320 maright-20" v-model="postData.number"></el-input>
        <el-button type="primary" class="veri" @click="veriNum"></el-button>
      </el-form-item>
      <el-form-item label="健身仓类型" prop="type" :rules="{ required: true, message: '请选择健身仓类型'}">
        <el-select v-model="postData.type" clearable class="w-320">
          <el-option label="有氧器械仓" value="有氧器械仓"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="区域" prop="district_id" :rules="{ required: true, message: '请选择区域'}">
        <el-select clearable placeholder="请选择" v-model="postData.district_id" class="w-320">
          <el-option v-for="item in districtList" :label="item.district_name" :value="item.district_id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="商圈" prop="tradingarea" :rules="{ required: true, message: '请填写商圈'}">
        <el-input class="w-320" v-model="postData.tradingarea"></el-input>
      </el-form-item>
      <el-form-item label="地址" prop="address" :rules="{ required: true, message: '请填写地址'}">
        <el-input class="w-320" v-model="postData.address"></el-input>
      </el-form-item>
      <el-form-item label="描述" prop="desc">
        <el-input class="w-320" v-model="postData.desc"></el-input>
      </el-form-item>
      <el-form-item label="坐标" prop="lng_lat_str" :rules="{ required: true, message: '请输入经纬度'}">
        <el-input class="w-320 maright-20" v-model="postData.lng_lat_str"></el-input>
        <!-- <el-button type="primary" @click="locateData">定位</el-button> -->
      </el-form-item>
      <el-form-item label="门禁ID" prop="door_device_id" :rules="{ required: true, message: '请填写门禁ID'}">
        <el-input class="w-320" v-model="postData.door_device_id"></el-input>
      </el-form-item>
      <el-form-item label="运动设备ID" prop="run_device_id" :rules="{ required: true, message: '请填写运动设备ID'}">
        <el-input class="w-320" v-model="postData.run_device_id"></el-input>
      </el-form-item>
      <el-form-item label="运营状态">
        <el-radio-group v-model="postData.status">
          <el-radio class="radio" :label="1">空闲</el-radio>
          <el-radio class="radio" :label="2">运营中</el-radio>
          <el-radio class="radio" :label="3">维护中</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">保存</el-button>
        <el-button class="m-l-150" @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>

    <!-- 地图定位弹窗 -->
    <div class="meng" v-show="posilocateShow">
      <el-dialog width="50%" title="坐标拾取" :modal="false" visible :show-close="false" @open="init">
        <el-form :model="positemp" class="mapform">
          <el-form-item class="nomb" label="场馆地址" label-width="100px">
            <el-input class="inputlen" id="posinput" size="mini" v-model="positemp.bus_addr" auto-complete="off"></el-input>
          </el-form-item>
          <el-form-item class="nomb" label="场馆坐标" label-width="100px">
            <el-input id="lnglatinput" size="mini" class="inputlen" v-model="positemp.lng_lat_str" auto-complete="off"></el-input>
          </el-form-item>
        </el-form>
        <div class="maps">
          <div id="map_container">
            <div id="amap-vue" class="amap-demo"></div>
          </div>
          <div id="panel"></div>
        </div>
        <div slot="footer" id="bugbug" class="dialog-footer">
          <el-button @click="cancelLocate">取 消</el-button>
          <el-button type="primary" @click="posiLocate">确 定</el-button>
        </div>
      </el-dialog>
    </div>

  </div>
</template>

<script>
import http from 'assets/js/http';
export default {
  name: 'cabinAdd',
  data() {
    return {
      postData: {
        action: 'add',
        id: '',
        number: '',
        type: '',
        district_id: '',
        district_name: '',
        tradingarea: '',
        address: '',
        lng_lat_str: '',
        lng: '',
        lat: '',
        status: 1,
        desc: '',
        door_device_id: '',
        run_device_id: ''
      },
      districtList: [
        {district_name: '南岸区', district_id: '3329'},
        {district_name: '渝北区', district_id: '3330'},
        {district_name: '大渡口区', district_id: '3332'},
        {district_name: '北碚区', district_id: '3334'},
        {district_name: '沙坪坝区', district_id: '3335'},
        {district_name: '巴南区', district_id: '3336'},
        {district_name: '江北区', district_id: '3338'},
        {district_name: '九龙坡区', district_id: '3339'},
        {district_name: '渝中区', district_id: '3340'}
      ],
      posilocateShow: false,
      positemp: {
        bus_addr: '',
        lng_lat_str: ''
      },
      placeSearch: {}
    }
  },
  methods: {
    cancelLocate() {
      this.positemp.lng_lat_str = this.postData.lng_lat_str;
      if(this.positemp.lng_lat_str){
        if(Object.keys(this.placeSearch).length>0){
          this.placeSearch.clear();
        }
      }
      this.posilocateShow = false;
    },
    posiLocate() {
      this.postData.lng_lat_str = this.positemp.lng_lat_str;
      if(this.positemp.lng_lat_str){
        if(Object.keys(this.placeSearch).length>0){
          this.placeSearch.clear();
        }
      }
      this.posilocateShow = false;

    },
    locateData() {
      let _this = this;
      this.positemp.bus_addr = this.postData.address;
      if(this.postData.lng_lat_str==''){
        //经纬度为空，用地址定位
        AMap.plugin('AMap.Geocoder',function(){
          var geocoder = new AMap.Geocoder({
              city: 394//城市，默认：重庆
          });
          let poaddr = '重庆'+_this.positemp.bus_addr;
          geocoder.getLocation(poaddr, function(status, result) {
            if (status === 'complete' && result.info === 'OK') {
              _this.positemp.lng_lat_str = result.geocodes[0].location.lng + '|' + result.geocodes[0].location.lat;
              if(Object.keys(_this.placeSearch).length>0){
                _this.placeSearch.clear();
              }
              _this.init();
            }else{
              _g.toastMsg('warning','获取位置失败');
              geocoder.getLocation('重庆', function(status, result) {
                if (status === 'complete' && result.info === 'OK') {
                  _this.positemp.bus_addr = result.geocodes[0].formattedAddress;
                  _this.positemp.lng_lat_str = result.geocodes[0].location.lng + '|' + result.geocodes[0].location.lat;
                  _this.init();
                }
              })
            }
          });
        })
      }else{
        //经纬度不为空，用经纬度定位
        this.positemp.lng_lat_str = this.postData.lng_lat_str;
        let numre = /^[-+]?(\d+)$|^[-+]?(\d+\.\d+)$/;
        let index = this.positemp.lng_lat_str.indexOf('|');
        let lngtemp = this.positemp.lng_lat_str.substr(0,index);
        let lattemp = this.positemp.lng_lat_str.substr(index+1);
        let test = numre.test(lngtemp)&&numre.test(lattemp);
        if(!test||index<0) {
          _g.toastMsg('warning','经纬度格式错误');
          AMap.plugin('AMap.Geocoder',function(){
            var geocoder = new AMap.Geocoder({
                city: 394//城市，默认：“全国”
            });
            geocoder.getLocation('重庆', function(status, result) {
              if (status === 'complete' && result.info === 'OK') {
                _this.positemp.bus_addr = result.geocodes[0].formattedAddress;
                _this.positemp.lng_lat_str = result.geocodes[0].location.lng + '|' + result.geocodes[0].location.lat;
                _this.init();
              }
            })
          })
        }else {
          if(Object.keys(_this.placeSearch).length>0){
            _this.placeSearch.clear();
          }
          this.init();
        }
      }
      this.posilocateShow = true;
    },
    init() {
      let _this = this;
      var map = new AMap.Map('amap-vue', {
        center: _this.positemp.lng_lat_str.split('|'),
        resizeEnable: true,
        zoom: 13
      })
      AMap.plugin(['AMap.ToolBar', 'AMap.Scale'], function () {
        map.addControl(new AMap.ToolBar())
        map.addControl(new AMap.Scale())
      });
      AMap.plugin('AMap.Geocoder',function(){
        var geocoder = new AMap.Geocoder({
            city: 394//城市，默认：“全国”
        });
        var marker = new AMap.Marker({
            map:map,
            bubble:true
        })
        if(!_this.positemp.bus_addr){
          geocoder.getAddress(_this.positemp.lng_lat_str.split('|'), function(status, result) {
            if (status === 'complete' && result.info === 'OK') {
              _this.positemp.bus_addr = result.regeocode.formattedAddress;
            }else{
              _g.toastMsg('warning','获取地址失败');
            }
          });
        }
        map.on('click',function(e){ 
            marker.setPosition(e.lnglat);
            _this.positemp.lng_lat_str = e.lnglat.lng + '|' + e.lnglat.lat;
            var lnglatXY= _this.positemp.lng_lat_str.split('|');
            geocoder.getAddress(lnglatXY, function(status, result) {
              if (status === 'complete' && result.info === 'OK') {
                _this.positemp.bus_addr = result.regeocode.formattedAddress;
                map.setCenter(marker.getPosition())
              }else{
                _g.toastMsg('warning','获取地址失败');
              }
            });
        })
        lnglatinput.onchange = function(e){
          let index = _this.positemp.lng_lat_str.indexOf('|');
          let numre = /^[-+]?(\d+)$|^[-+]?(\d+\.\d+)$/;
          let lngtemp = _this.positemp.lng_lat_str.substr(0,index);
          let lattemp = _this.positemp.lng_lat_str.substr(index+1);
          let test = numre.test(lngtemp)&&numre.test(lattemp);
          if(!test||index<0) {
            _g.toastMsg('warning','经纬度输入格式错误');
            return;
          }
          var lnglatXY= _this.positemp.lng_lat_str.split('|');//地图上所标点的坐标
          geocoder.getAddress(lnglatXY, function(status, result) {
            if (status === 'complete' && result.info === 'OK') {
              _this.positemp.bus_addr = result.regeocode.formattedAddress;
              marker.setPosition(lnglatXY);
              map.setCenter(marker.getPosition())
            }else{
              _g.toastMsg('warning','获取地址失败');
            }
          });
        }
      });
      AMap.service(["AMap.PlaceSearch"], function() {
        _this.placeSearch = new AMap.PlaceSearch({ //构造地点查询类
            pageSize: 5,
            pageIndex: 1,
            city: _this.postData.city_id, //城市
            map: map,
            panel: "panel"
        });
        //关键字查询
        AMap.event.addListener(_this.placeSearch, "markerClick", placeSearch_CallBack);
         function placeSearch_CallBack(data) {   
          _this.positemp.bus_addr = data.data.address;
          _this.positemp.lng_lat_str = data.data.location.lng + '|' + data.data.location.lat;
        }
        AMap.event.addListener(_this.placeSearch, "listElementClick", placeSearch_CallBack);
         function placeSearch_CallBack(data) {
          _this.positemp.bus_addr = data.data.address;
          _this.positemp.lng_lat_str = data.data.location.lng + '|' + data.data.location.lat;
        }
        posinput.onchange = function(e){
          var posidetail= '重庆' + _this.positemp.bus_addr.replace(/\s+/g,''); 
          _this.placeSearch.search(posidetail,function(status,result){
            if (status === 'complete' && result.info === 'OK') {
              if(result.poiList.pois.length==1){
                _this.positemp.bus_addr = result.poiList.pois[0].address;
                _this.positemp.lng_lat_str = result.poiList.pois[0].location.lng + '|' + result.poiList.pois[0].location.lat;
              }
            }else{
              _g.toastMsg('warning','获取地址失败');
              if(Object.keys(_this.placeSearch).length>0){
                _this.placeSearch.clear();
              } 
            }
          });
        }
      });
    },
    onSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if(this.postData.id==0){
            this.postData.id = '';
          }
          let index = this.postData.lng_lat_str.indexOf('|');
          this.postData.lng = this.postData.lng_lat_str.substr(0,index);
          this.postData.lat = this.postData.lng_lat_str.substr(index+1);
          for(let i=0;i<this.districtList.length;i++){
            if(this.postData.district_id==this.districtList[i].district_id){
              this.postData.district_name = this.districtList[i].district_name;
            }
          }
          this.apiPost('/fitness/House/housesave', this.postData).then((res) => {
            if(res.errorcode ==0 ){
              _g.toastMsg('success',res.errormsg);
              this.$router.back();
            }else{
              _g.toastMsg('warning',res.errormsg);
            }
          })
        }
      })
    },
    getpageInfo() {
      let _this = this;
      this.apiPost('/fitness/House/housegetinfo', {id: _this.postData.id}).then((res) => {
        if(res.errorcode ==0 ){
          _this.postData.number = String(res.data.number);
          _this.postData.type = String(res.data.type);
          _this.postData.district_id = String(res.data.district_id);
          _this.postData.district_name = String(res.data.district_name);
          _this.postData.tradingarea = String(res.data.tradingarea);
          _this.postData.address = String(res.data.address);
          _this.postData.lng = String(res.data.lng);
          _this.postData.lat = String(res.data.lat);
          _this.postData.status = Number(res.data.status);
          _this.postData.desc = String(res.data.desc);
          _this.postData.door_device_id = String(res.data.door_device_id);
          _this.postData.run_device_id = String(res.data.run_device_id);
          _this.postData.lng_lat_str = String(res.data.lng) + '|' + String(res.data.lat);
        }else{
          _g.toastMsg('warning',res.errormsg);
        }
      }).catch((error)=> {
        console.log(error)
      })
    },
    veriNum () {
      let veripost = {
        number: this.postData.number,
        id: this.postData.id
      }
      this.apiPost('/fitness/House/checknumber', veripost).then((res) => {
        if(res.errorcode ==0 ){
          _g.toastMsg('success',res.errormsg);
        }else{
          _g.toastMsg('warning',res.errormsg);
        }
      })
    }
  },
  created() {
    this.postData.id = this.$route.params.cabin_id;
    if(this.postData.id&&this.postData.id!=0){
      this.postData.action = 'edit';
      this.getpageInfo();
    }else{
      this.postData.action = 'add';
    }
  },
  mixins: [http]
}
</script>

<style scoped>
.maright-20 {
  margin-right: 20px;
}
.veri {
  background: url(../../assets/images/veri.png);
  background-size: contain;
  width: 32px;
  height: 32px;
  background-repeat: no-repeat;
  border-color: #fff;
}
.meng {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  position: fixed;
  overflow: auto;
  margin: 0;
  background: rgba(0,0,0,0.5);
  z-index: 2005;
}
.mapform {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  height: 30px;
}
.mapform .nomb {
  margin-bottom: 10px;
}
.inputlen {
  width: 180px;
}
.maps {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 300px;
}
.amap-demo {
  height: 300px;
}
#map_container {
  width: 100%;
  height: 300px;
}
#panel {
    background-color: white;
    max-height: 100%;
    overflow-y: auto;
}
</style>
