<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="150px">
      <el-form-item label="单价" prop="price" :rules="{ required: true, pattern: /^\d+(\.\d+)?$/, message: '请填写正确的单价'}">
        <el-input class="w-320 maright-10" v-model="postData.price"></el-input>
        <span>元／分钟</span>
      </el-form-item>
      <el-form-item label="最低扣费" prop="min_price" :rules="{ required: true, pattern: /^\d+(\.\d+)?$/, message: '请填写正确的最低扣费'}">
        <el-input class="w-320 maright-10" v-model="postData.min_price"></el-input>
        <span>元</span>
      </el-form-item>
      <el-form-item label="最高扣费" prop="max_price" :rules="{ required: true, pattern: /^\d+(\.\d+)?$/, message: '请填写正确的最高扣费'}">
        <el-input class="w-320 maright-10" v-model="postData.max_price"></el-input>
        <span>元</span>
      </el-form-item>
      <el-form-item label="单次最长运营时间" prop="max_run_time" :rules="{ required: true, pattern: /^\d+$/, message: '请填写正确的最长运营时间'}">
        <el-input class="w-320 maright-10" v-model="postData.max_run_time"></el-input>
        <span>分钟</span>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">保存</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import http from 'assets/js/http';
export default {
  name: 'cabinPara',
  data() {
    return {
      postData:{
        price: '',
        min_price: 0,
        max_price: 0,
        max_run_time: '',
        action: 'info'
      }
    }
  },
  methods: {
    onSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.postData.action = 'save';
          let _this = this;
          this.apiPost('/fitness/House/houseconf', this.postData).then((res) => {
            if(res.errorcode ==0 ){
              _g.toastMsg('success',res.errormsg);
              _this.postData.action = 'info';
            }else{
              _g.toastMsg('warning',res.errormsg);
            }
          })
        }
      })
    },
    getpageInfo() {
      let _this = this;
      this.apiPost('/fitness/House/houseconf', {action: 'info'}).then((res) => {
        if(res.errorcode ==0 ){
          _this.postData.price = Number(res.data.price);
          _this.postData.min_price = Number(res.data.min_price);
          _this.postData.max_price = Number(res.data.max_price);
          _this.postData.max_run_time = Number(res.data.max_run_time);
        }else{
          _g.toastMsg('warning',res.errormsg);
        }
      }).catch((error)=> {
        console.log(error)
      })
    },
  },
  created() {
    this.getpageInfo();
  },
  mixins: [http]
}
</script>

<style scoped>
.maright-10 {
  margin-right: 10px;
}
</style>
