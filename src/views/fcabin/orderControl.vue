<template>
  <div>
    <div class="m-b-20 ovf-hd">
      <div class="fl">
        <el-date-picker v-model="rangeArray" class="m-r-10" type="daterange" @change="changeDate" placeholder="选择日期">
        </el-date-picker>
        <el-input @keyup.enter.native="search" placeholder="手机号" v-model="postData.mobile" class="w-150 m-r-10"></el-input>
        <el-input placeholder="订单编号" @keyup.enter.native="search" v-model="postData.order_sn" class="w-150 m-r-10"></el-input>
        <el-input placeholder="仓体编号" @keyup.enter.native="search" v-model="postData.number" class="w-150 m-r-10"></el-input>
        <el-select @change="search" clearable v-model="postData.pay_status" placeholder="状态" class="w-150 m-r-10">
          <el-option label="运动中" value="1"></el-option>
          <el-option label="运动完成" value="2"></el-option>
          <el-option label="支付完成" value="3"></el-option>
          <el-option label="取消" value="4"></el-option>
        </el-select>         
        <el-button type="primary" icon="search"  @click="search">搜索</el-button>
      </div>
    </div>
    <!-- 运动中是只显示开始时间，手机号和仓体编号
    待支付不显示成功支付时间
    已支付显示全部
    取消的只显示开始时间，手机号和仓体编号 -->
    <el-table class="shrinkpad" :data="tableData" stripe style="width: 100%">
      <el-table-column type="index" label="序号" width="60" align="center"></el-table-column>
      <el-table-column prop="order_sn" label="订单编号" width="160" align="center">
        <!-- <template scope="scope">
          <span v-if="scope.row.pay_status!=1&&scope.row.pay_status!=4">{{scope.row.order_sn}}</span>
          <span v-else>-</span>
        </template> -->
      </el-table-column>
      <el-table-column prop="start_time" label="开始时间" width="95" align="center"></el-table-column>
      <el-table-column prop="mobile" label="手机号" width="120" align="center"></el-table-column>
      <el-table-column prop="number" label="仓体编号" width="80" align="center"></el-table-column>
      <el-table-column prop="end_time" label="结束时间" width="95" align="center">
        <template scope="scope">
          <span v-if="scope.row.pay_status!=1">{{scope.row.end_time}}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="keep_time" label="使用时长(分钟)" width="80" align="center">
        <template scope="scope">
          <span v-if="scope.row.pay_status!=1">{{scope.row.keep_time}}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="success_time" label="支付时间" width="95" align="center">
        <template scope="scope">
          <span v-if="scope.row.pay_status==3">{{scope.row.success_time}}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="amount" label="金额(元)" width="80" align="center">
        <template scope="scope">
          <span v-if="scope.row.pay_status!=1&&scope.row.pay_status!=4">{{scope.row.amount}}</span>
          <span v-else>-</span>
        </template>
      </el-table-column>
      <el-table-column prop="pay_status" label="状态" width="80" align="center">
        <template scope="scope">
          <span>{{ scope.row.pay_status == 1 ? '运动中' : scope.row.pay_status == 2 ? '运动完成' : scope.row.pay_status == 3 ? '支付完成' : '取消'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template scope="scope">
        <el-button
          size="small"
          type="primary"
          class="eidtfont"
          @click="orderOper(scope.$index,scope.row.order_sn,scope.row.pay_status)" v-if="scope.row.pay_status!='3'&&scope.row.pay_status!='4'">编辑
        </el-button>
        <el-button
          size="small" disabled v-else>编辑
        </el-button>
      </template>
      </el-table-column>
    </el-table>

    <div class="pos-rel p-t-20 ovf-hd flexend" v-if="dataCount>0">
      <div class="block">
        <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount">
        </el-pagination>
      </div>
    </div>

    <!-- 编辑弹窗 -->
    <el-dialog width="40%" title="编辑订单状态" :visible.sync="ordereditDialog">
      <el-form :model="orderPost" class="padrt">
        <el-form-item label="订单状态" label-width="100px">
          <el-select v-model="orderPost.pay_status">
            <el-option label="运动中" value="1"></el-option>
            <el-option label="运动完成" value="2"></el-option>
            <el-option label="支付完成" value="3"></el-option>
            <el-option label="取消" value="4"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="ordereditDialog = false">取 消</el-button>
        <el-button type="primary" @click="orderEdit">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import http from 'assets/js/http'
export default {
  name: 'orderControl',
  data() {
    return {
      rangeArray: [this.getDate(), this.getDate()],
      postData:{
        start_time: this.getDate(),
        end_time: this.getDate(),
        mobile:'',
        order_sn: '',
        number: '',
        pay_status: '',
        page_size: 10,
        page_no: 1
      },
      tableData: [],
      dataCount: 0,
      ordereditDialog: false,
      orderPost: {
        order_sn: '',
        pay_status: ''
      }
    }
  },
  methods: {
    orderEdit() {
      let _this = this;
      this.apiPost('/fitness/Order/orderchangestatus',this.orderPost).then((res) => {
        if(res.errorcode ==0 ){
          _this.postData.page_no = 1;
          _g.toastMsg('success',res.errormsg);
          _this.gettableList();
        }else{
          _g.toastMsg('warning',res.errormsg);
        }
      })
      this.ordereditDialog = false;
    },
    orderOper(index,ordersn,paystatus) {
      this.orderPost.order_sn = ordersn;
      this.orderPost.pay_status = String(paystatus);
      this.ordereditDialog = true;
    },
    getDate() {
      let d = new Date()
      return `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()}`
    },
    changeDate(value) {
      let sTime = value[0];
      let eTime = value[1];
      this.postData.start_time = _g.formatDate(sTime,'yyyy-MM-dd')
      this.postData.end_time = _g.formatDate(eTime,'yyyy-MM-dd')
    },
    gettableList() {
      let _this = this;
      this.apiPost('/fitness/Order/orderlist', this.postData).then((res) => {
        if(res.errorcode ==0 ){
          _this.tableData = res.data.list;
          _this.dataCount = res.data.count;
        }else{
          _g.toastMsg('warning',res.errormsg);
        }
      })
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.gettableList();
    },
    search() {
      this.postData.page_no = 1;
      this.gettableList();
    },
  },
  created() {
    this.gettableList();
  },
  mixins: [http]
}
</script>

<style scoped>
.editfont {
  color: #fff;
}
</style>
<style>
.shrinkpad.el-table .cell, .shrinkpad.el-table th>.cel {
  padding-left: 8px;
  padding-right: 8px;
}
</style>