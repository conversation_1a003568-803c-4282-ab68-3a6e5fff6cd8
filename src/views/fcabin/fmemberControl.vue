<template> 
  <div>
    <div class="m-b-20 ovf-hd">
      <div class="fl">
        <el-date-picker v-model="rangeArray" class="m-r-10" type="daterange" @change="changeDate" placeholder="选择日期">
        </el-date-picker>
        <el-input @keyup.enter.native="search" placeholder="手机号" v-model="postData.phone" class="w-150 m-r-10"></el-input>
        <el-select @change="search" clearable v-model="postData.status" placeholder="状态" class="w-150 m-r-10">
          <el-option label="启用" value="1"></el-option>
          <el-option label="禁用" value="0"></el-option>
        </el-select>         
        <el-button type="primary" icon="search"  @click="search">搜索</el-button>
      </div>
    </div>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column type="index" label="序号" width="120" align="center"></el-table-column>
      <el-table-column prop="create_time" label="注册日期" width="170" align="center"></el-table-column>
      <el-table-column prop="phone" label="手机号" width="170" align="center"></el-table-column>
      <el-table-column prop="nickname" label="昵称" width="170" align="center"></el-table-column>
      <el-table-column label="状态" width="150" align="center">
        <template scope="scope">
          <el-switch v-model="scope.row.status" @change="switchStatus(scope.row)" active-color="#13ce66" active-value="1" inactive-value="0"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template scope="scope">
        <router-link class='editfont' :to="{name: 'fmemberAdd',params: {member_id: scope.row.id}}"> 
          <el-button
            size="small"
            type="primary">编辑</el-button>
        </router-link>
      </template>
      </el-table-column>
    </el-table>

    <div class="pos-rel p-t-20 ovf-hd flexend" v-if="dataCount>0">
      <div class="block">
        <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import http from 'assets/js/http'
export default {
  name: 'fmemberControl',
  data() {
    return {
      rangeArray: [this.getDate(), this.getDate()],
      postData:{
        start_time: this.getDate(),
        end_time: this.getDate(),
        phone:'',
        status: '',
        page_size: 10,
        page_no: 1
      },
      tableData: [],
      dataCount: 0,
      }
  },
  methods: {
    switchStatus(row) {
      let _this = this;
      let finalstatus = '';
      if(row.status=='1'){
        finalstatus = '0';
      }else{
        finalstatus = '1';
      }
      let switchPost = {
        id: row.id,
        status: finalstatus
      }
      this.apiPost('/fitness/User/changestatus', switchPost).then((res) => {
        if(res.errorcode ==0 ){
          _g.toastMsg('success',res.errormsg);
        }else{
          _g.toastMsg('warning',res.errormsg);
        }
      })
    },
    getDate() {
      let d = new Date()
      return `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()}`
    },
    changeDate(value) {
      let sTime = value[0];
      let eTime = value[1];
      this.postData.start_time = _g.formatDate(sTime,'yyyy-MM-dd')
      this.postData.end_time = _g.formatDate(eTime,'yyyy-MM-dd')
    },
    gettableList() {
      let _this = this;
      this.apiPost('/fitness/User/userlist', this.postData).then((res) => {
        if(res.errorcode ==0 ){
          _this.tableData = res.data.list;
          _this.dataCount = res.data.count;
        }else{
          _g.toastMsg('warning',res.errormsg);
        }
      })
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.gettableList();
    },
    search() {
      this.postData.page_no = 1;
      this.gettableList();
    },
  },
  created() {
    this.gettableList();
  },
  mixins: [http]
}
</script>

<style scoped>
.editfont {
  color: #fff;
}
</style>








