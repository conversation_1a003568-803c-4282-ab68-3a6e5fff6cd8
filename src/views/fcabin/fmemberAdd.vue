<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="150px">
      <el-form-item label="手机号" prop="phone" :rules="{ required: true, pattern: /^1[345678]\d{9}$/, message: '请填写正确的手机号'}">
        <el-input class="w-320" v-model="postData.phone"></el-input>
      </el-form-item>
      <el-form-item label="昵称" prop="nickname" :rules="{ type: 'string', max:20, message: '最多20个字符'}">
        <el-input class="w-320 maright-20" v-model="postData.nickname"></el-input>
        <el-button type="primary" class="unbind" @click="unbindWx" v-if="postData.openid"></el-button>
      </el-form-item>
      <el-form-item label="性别">
        <el-radio-group v-model="postData.sex">
          <el-radio class="radio" :label="1">男</el-radio>
          <el-radio class="radio" :label="2">女</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="头像" prop="avatar">
        <div class="profilepicbg">
          <img v-if="postData.avatar" :src="postData.avatar" class="profilepic" />
          <img v-else src="../../assets/images/memberpic.png" class="profilepic" />
        </div>
      </el-form-item>
      <el-form-item label="是否启用">
        <el-switch v-model="postData.status" active-color="#13ce66" inactive-color="#ff4949" active-value="1" inactive-value="0"></el-switch>
      </el-form-item>
      <el-form-item label="注册日期" prop="create_time">
        <el-date-picker v-model="postData.create_time" :editable="false" :clearable="false" class="m-r-10" type="date" @change="changeDate" placeholder="选择日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">保存</el-button>
        <el-button class="m-l-150" @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>

    <!-- 取消绑定弹窗 -->
    <el-dialog
      title="取消微信绑定"
      :visible.sync="unbindShow"
      width="40%"
      :before-close="handleClose"
      :show-close="false">
      <span>确定取消微信绑定？</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="unbindShow=false">取 消</el-button>
        <el-button type="primary" @click="confirmUnbind">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import http from 'assets/js/http';
export default {
  name: 'fmemberAdd',
  data() {
    return {
      postData:{
        id: '',
        phone: '',
        nickname: '',
        openid: '',
        sex:1,
        status: '1',
        avatar: '',
        create_time: this.getDate(),
        action: 'add'
      },
      unbindShow: false
    }
  },
  methods: {
    getDate() {
      let d = new Date()
      return `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()}`
    },
    changeDate(value) {
      this.postData.create_time = _g.formatDate(value,'yyyy-MM-dd')
    },
    unbindWx () {
      this.unbindShow = true;
    },
    confirmUnbind () {
      this.apiPost('/fitness/User/unbind', {id: this.postData.id}).then((res) => {
        if(res.errorcode ==0 ){
          _g.toastMsg('success',res.errormsg);
          this.unbindShow = false;
          this.getpageInfo();
        }else{
          _g.toastMsg('warning',res.errormsg);
        }
      })
    },
    onSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          if(this.postData.id==0){
            this.postData.id = '';
          }
          this.apiPost('/fitness/User/usersave', this.postData).then((res) => {
            if(res.errorcode ==0 ){
              _g.toastMsg('success',res.errormsg);
              this.$router.back();
            }else{
              _g.toastMsg('warning',res.errormsg);
            }
          })
        }
      })
    },
    getpageInfo() {
      let _this = this;
      this.apiPost('/fitness/User/usergetinfo', {id: _this.postData.id}).then((res) => {
        if(res.errorcode ==0 ){
          _this.postData.phone = Number(res.data.phone);
          _this.postData.nickname = String(res.data.nickname);
          _this.postData.openid = String(res.data.openid);
          _this.postData.phone = String(res.data.phone);
          _this.postData.sex = Number(res.data.sex);
          _this.postData.status = String(res.data.status);
          _this.postData.avatar = String(res.data.avatar);
          _this.postData.create_time = String(res.data.create_time);
        }else{
          _g.toastMsg('warning',res.errormsg);
        }
      }).catch((error)=> {
        console.log(error)
      })
    },
  },
  created() {
    this.postData.id = this.$route.params.member_id;
    if(this.postData.id){
      this.postData.action = 'edit';
      this.getpageInfo();
    }else{
      this.postData.action = 'add';
    }
  },
  mixins: [http]
}
</script>

<style scoped>
.maright-20 {
  margin-right: 20px;
}
.unbind {
  background: url(../../assets/images/unbind.png);
  background-size: contain;
  width: 32px;
  height: 32px;
  background-repeat: no-repeat;
  border-color: #fff;
}
.profilepicbg {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  width: 200px;
  height: 180px;
  padding: 10px;
  background: #fff;
}
.profilepic {
  width: 180px;
  height: 160px;
}
</style>
