<template>
  <div>
    <div class="m-b-20 ovf-hd">
      <div class="fl">
        <router-link class="btn-link-large add-btn"
                     :to="{name:'cabinAdd', params:{cabin_id:0}}">
          <i class="el-icon-plus"></i>&nbsp;&nbsp;新增健身仓
        </router-link>
      </div>
      <div class="fl m-l-30">
        <el-input placeholder="仓体编号"
                  @keyup.enter.native="search"
                  v-model="postData.number"
                  class="w-150 m-r-10"></el-input>
        <el-select clearable
                   placeholder="区域"
                   @change="search"
                   v-model="postData.district_name"
                   class="w-150 m-r-10">
          <el-option v-for="item in districtList"
                     :label="item.district_name"
                     :value="item.district_name"></el-option>
        </el-select>
        <el-input placeholder="商圈"
                  @keyup.enter.native="search"
                  v-model="postData.tradingarea"
                  class="w-150 m-r-10"></el-input>
        <el-select @change="search"
                   clearable
                   v-model="postData.status"
                   placeholder="状态"
                   class="w-150 m-r-10">
          <el-option label="空闲"
                     value="1"></el-option>
          <el-option label="运营中"
                     value="2"></el-option>
          <el-option label="维修中"
                     value="3"></el-option>
        </el-select>
        <el-button type="primary"
                   icon="search"
                   @click="search">搜索</el-button>
      </div>
    </div>

    <el-table :data="tableData"
              stripe
              style="width: 100%">
      <el-table-column type="index"
                       label="序号"
                       width="80"
                       align="center"></el-table-column>
      <el-table-column prop="number"
                       label="仓体编号"
                       width="100"
                       align="center"></el-table-column>
      <el-table-column prop="type"
                       label="类型"
                       width="120"
                       align="center"></el-table-column>
      <el-table-column prop="district_name"
                       label="区域"
                       width="100"
                       align="center"></el-table-column>
      <el-table-column prop="tradingarea"
                       label="商圈"
                       width="150"
                       align="center"></el-table-column>
      <el-table-column prop="address"
                       label="地址"
                       width="150"
                       align="center"></el-table-column>
      <el-table-column prop="desc"
                       label="描述"
                       width="120"
                       align="center"></el-table-column>
      <el-table-column prop="status"
                       label="状态"
                       width="100"
                       align="center">
        <template scope="scope">
          <span>{{ scope.row.status == 1 ? '空闲' : scope.row.status == 2 ? '运营中' : '维修中'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作"
                       align="center">
        <template scope="scope">
          <router-link class='editfont'
                       :to="{name: 'cabinAdd',params: {cabin_id: scope.row.id}}">
            <el-button size="small"
                       type="primary">编辑</el-button>
          </router-link>
          <el-button size="small"
                     style="margin-left: 30px"
                     type="success"
                     @click="clickEntryQr(scope.row.id)">进</el-button>
          <el-button size="small"
                     type="info"
                     @click="clickOutQr(scope.row.id)">出</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-dialog title="进门二维码"
               :visible.sync="showEntryQr"></el-dialog>
    <el-dialog title="出门二维码"
               :visible.sync="showOutQr"></el-dialog>

    <div class="pos-rel p-t-20 ovf-hd flexend"
         v-if="dataCount>0">
      <div class="block">
        <el-pagination @current-change="handleCurrentChange"
                       layout="prev, pager, next"
                       :page-size="postData.page_size"
                       :current-page="postData.page_no"
                       :total="dataCount">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'assets/js/http'
  export default {
    name: 'cabinControl',
    data() {
      return {
        postData: {
          number: '',
          district_name: '',
          tradingarea: '',
          status: '',
          page_size: 10,
          page_no: 1
        },
        showEntryQr: false,
        showOutQr: false,
        tableData: [],
        dataCount: 0,
        districtList: [
          { district_name: '南岸区', district_id: '3329' },
          { district_name: '渝北区', district_id: '3330' },
          { district_name: '大渡口区', district_id: '3332' },
          { district_name: '北碚区', district_id: '3334' },
          { district_name: '沙坪坝区', district_id: '3335' },
          { district_name: '巴南区', district_id: '3336' },
          { district_name: '江北区', district_id: '3338' },
          { district_name: '九龙坡区', district_id: '3339' },
          { district_name: '渝中区', district_id: '3340' }
        ]
      }
    },
    methods: {
      clickEntryQr(house_id) {
        const url = 'https://fit-api-beta.rocketbird.cn/fitness/house/get_wx_code'
        const postData = {
          house_id,
          fitness_type: 1,
          pages: 'pages/index/index'
        }
        this.apiPost(url, postData).then(res => {
          if (res.errorcode === 0) {
            console.log(res.data)
          }
        })
      },
      clickOutQr(house_id) {
        const url = 'https://fit-api-beta.rocketbird.cn/fitness/house/get_wx_code'
        const postData = {
          house_id,
          fitness_type: 2,
          pages: 'pages/index/index'
        }
        this.apiPost(url, postData).then(res => {
          console.log(res)
        })
      },
      gettableList() {
        let _this = this
        this.apiPost('/fitness/House/houselist', this.postData).then(res => {
          if (res.errorcode == 0) {
            _this.tableData = res.data.list
            _this.dataCount = res.data.count
          } else {
            _g.toastMsg('warning', res.errormsg)
          }
        })
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage
        this.gettableList()
      },
      search() {
        this.postData.page_no = 1
        this.gettableList()
      }
    },
    created() {
      this.gettableList()
    },
    mixins: [http]
  }
</script>

<style scoped>
  .editfont {
    color: #fff;
  }
</style>








