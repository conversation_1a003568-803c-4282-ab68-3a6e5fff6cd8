<template>
  <div class="container">
    <div class="ovf-hd searchbar">
      <div class="fl m-l-30">
        <el-select v-model="postData.bus_id" filterable placeholder="场馆" class="w-150 m-r-10 p-t-20" clearable>
          <el-option v-for="item in busList" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
        <el-select
          v-model="postData.status"
          class="w-150 m-r-10"
          placeholder="状态"
        >
          <el-option :value="0" label="全部状态"></el-option>
          <el-option :value="1" label="待审批"></el-option>
          <el-option :value="2" label="审批通过"></el-option>
          <el-option :value="3" label="审批驳回"></el-option>
        </el-select>
         <el-date-picker v-model="dateRange" class="m-r-10" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="daterange"
                        @change="dateChange" start-placeholder="到期时间" end-placeholder="到期时间"
                        placeholder="选择日期"></el-date-picker>
        <el-button type="primary" icon="search" @click="search" class="m-t-20"
          >搜索</el-button
        >
      </div>
    </div>

    <el-table :data="tableData" id="infotable" stripe>
      <el-table-column
        prop="create_time"
        label="申请日期"
        align="center"
      ></el-table-column>
      <el-table-column
        prop="bus_name"
        label="申请场馆名称"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="type"
        label="类型"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="now_sign_name"
        label="当前文案"
        align="center"
      >
      </el-table-column>
      <el-table-column
        prop="sign_name"
        label="修改后文案"
        align="center"
      >
      </el-table-column>
      <el-table-column prop="status" label="状态" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.status === 1 ? '待审批' : row.status === 2 ? '审批通过' : '审批驳回'}}</span>
        </template>
      </el-table-column>
      <el-table-column
          prop="approve_info"
          label="驳回备注"
          align="center"
        >
        </el-table-column>
      <el-table-column label="操作" align="center">
        <template scope="scope">
          <div>
            <el-button
              size="small"
              v-if="scope.row.status == 1"
              @click="confirmCur(scope.row)"
              type="primary"
              >通过</el-button
            >
            <el-button
              size="small"
              v-if="scope.row.status == 1"
              @click="cancelCur(scope.row)"
              type="primary"
              >驳回</el-button
            >
          </div>
        </template>
      </el-table-column>
    </el-table>

    <footer>
      <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next, sizes"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
      >
      </el-pagination>
    </footer>
     <el-dialog title="驳回" :visible.sync="showDialog" :close-on-click-modal="false" custom-class="w-500">
      <el-form :model="dialogData" ref="orgForm" label-width="70px">
        <el-form-item prop="approve_info" label="备注" :rules="[{ required: true, message: '请输入'},{ max: 100, message: '最多输入100个字' }]">
          <el-input v-model="dialogData.approve_info" placeholder="请填写" class="w-300"></el-input>
        </el-form-item>
        </el-form-item>
        <el-form-item>
         <el-button type="success" @click="handleSave">提交</el-button>
         <el-button type="info" @click="showDialog = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'msgSign',
  data() {
    return {
      showDialog: false,
      dateRange: [
        _g.formatDate(new Date(), 'yyyy-MM-dd'),
        _g.formatDate(new Date(), 'yyyy-MM-dd')
      ],
      busList: [],
      dialogData: {
        id: '',
        type: '',
        approve_info: ''
      },
      postData: {
        status: 0,
        bus_id: '',
        begin_time: _g.formatDate(new Date(), 'yyyy-MM-dd'),
        end_time: _g.formatDate(new Date(), 'yyyy-MM-dd'),
        page_size: 10,
        page_no: 1
      },
      tableData: [],
      dataCount: 0
    }
  },
  watch: {
    showDialog(val) {
      if(!val) {
        this.dialogData = {
          id: '',
          type: '',
          approve_info: ''
        }
      }
    }
  },
  methods: {
    dateChange(dateRange) {
        dateRange = dateRange || [];
      const [begin_time, end_time] = dateRange;
      this.postData.begin_time = begin_time;
      this.postData.end_time = end_time;
    },
    confirmCur(info) {
      this.$confirm('确认通过审批吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消'
      })
      .then(() => {
        this.dialogData.id = info.id
        this.dialogData.type = 1
        this.sendInfo()
      })
    },
    sendInfo() {
      this.$service.post('/web/sendMessage/messageSignExecute', this.dialogData).then(res => {
        if (res.data.errorcode == 0) {
          this.showDialog = false
          _g.toastMsg('success', '操作成功');
          this.gettableList()
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    },
    cancelCur(info) {
      this.showDialog = true
      this.dialogData.id = info.id
      this.dialogData.type = 2
    },
    handleSave() {
      this.$refs.orgForm.validate(val => {
        if(val) {
          this.sendInfo()
        }
      })
    },
    handleSizeChange(val) {
      this.postData.page_size = val
      this.postData.page_no = 1
      this.gettableList()
    },
    getBusList() {
      this.$service.post('/web/business/bus_list').then(res => {
        this.loading = false;
        if (res.data.errorcode == 0) {
          const reData = res.data.data
          this.busList = reData.bus_list;
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    },
    gettableList() {
      this.$service
        .post('/web/sendMessage/getMessageSignList', this.postData)
        .then(res => {
          if (res.data.errorcode == 0) {
            let reData = res.data.data
            reData.list.forEach(item => {
              if(item.current_text || item.after_modify_text){
                item.type="内容"
                item.now_sign_name = item.current_text
                item.sign_name = item.after_modify_text
              } else {
                item.type="签名"
              }
            });
            this.tableData = reData.list
            this.dataCount = reData.count
          } else {
            _g.toastMsg('warning', res.data.errormsg)
          }
        })
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage
      this.gettableList()
    },
    search() {
      this.postData.page_no = 1
      this.gettableList()
    }
  },
  created() {
    this.gettableList()
    this.getBusList()
  }
}
</script>

<style scoped>
.m-r-5 {
  margin-right: 5px;
}
.editfont {
  color: #fff;
}
.searchbar {
  background-color: #fff;
  min-height: 80px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  border-bottom: 1px solid #ececec;
  padding-bottom: 20px;
}
.el-dialog__footer {
  padding: 0;
}
</style>
