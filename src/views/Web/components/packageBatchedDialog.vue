<template>
  <el-dialog
    :title="type === '1' ? '批量延期' : '批量赋权'"
    :visible="show"
    width="500px"
    :close-on-click-modal="false"
    @close="handleCancel"
  >
    <el-form
      ref="form"
      label-width="100px"
      :model="formData"
      class="info-dialog-form">
      <el-form-item label="已选门店">
        <div class="names-row">{{ names }}（共{{ selected.length }}家）</div>
      </el-form-item>
      <template v-if="type==='1'">
        <el-form-item
          label="延期方式"
          :rules="{ type: 'string', required: true, message: '请选择延期方式', trigger: 'change' }"
          prop="expire_type">
          <el-radio-group v-model="formData.expire_type" class="radiogroup">
            <el-radio class="radio radioitem" label="1">顺延有效期天数</el-radio>
            <el-radio class="radio radioitem" label="0">延期到指定日期</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item
          v-show="formData.expire_type === '1'"
          label="延期天数"
          prop="expire_days"
          :rules="{ type: 'number', required: formData.expire_type === '1', message: '请填写延期天数', trigger: 'blur' }" >
          <el-input-number
            class="expire-input-number"
            v-model="formData.expire_days"
            :min="1"
            :max="3650"
            controls-position="right"
            step-strictly
            placeholder="请填写延期天数"
          ></el-input-number>
        </el-form-item>
        <el-form-item
          v-show="formData.expire_type === '0'"
          label="到期时间"
          prop="expire_date"
          :rules="{ type: 'date', required: formData.expire_type === '0', message: '请选择到期时间', trigger: 'change' }" >
          <el-date-picker
            v-model="formData.expire_date"
            class="w-100p"
            type="date"
            placeholder="请选择到期时间"
            :editable="false"
            :clearable="false"
            :picker-options="datePickerOption">
          </el-date-picker>
        </el-form-item>
      </template>

      <template v-if="type==='2'">
        <el-form-item
          label="系统版本"
           prop="version_id"
          :rules="{ type: 'number', required: !formData.is_open_mer, message: '请选择系统版本', trigger: 'change' }" >
          <el-select
            v-model="formData.version_id"
            class="w-100p"
            filterable
            placeholder="请选择系统版本">
            <el-option
              v-for="item in versionList"
              :label="item.name"
              :key="item.id"
              :value="item.id">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item
          label="商家端版本"
           prop="mer_version"
          :rules="{ required: !formData.version_id || formData.is_open_mer, message: '请选择商家端版本', trigger: 'change' }" >
          <el-switch
            v-model="formData.is_open_mer"/>
          <div v-if="formData.is_open_mer">
            <el-select
              v-model="formData.mer_version"
              class="w-100p"
              filterable
              placeholder="请选择商家端版本">
              <el-option
                v-for="item in merVersionList"
                :label="item.name"
                :key="item.id"
                :value="item.id"></el-option>
            </el-select>
          </div>
        </el-form-item>
        <el-form-item
          label="赋权方式"
          :rules="{ type: 'string', required: true, message: '请选择赋权方式', trigger: 'change' }"
          prop="version_type">
          <el-radio-group v-model="formData.version_type" class="radiogroup">
            <el-radio class="radio radioitem" label="0">清空后赋权</el-radio>
            <el-radio class="radio radioitem" label="1">智能赋权</el-radio>
          </el-radio-group>
        </el-form-item>
      </template>
    </el-form>

    <div slot="footer">
      <el-button @click="handleCancel">关闭</el-button>
      <!-- <el-button class="m-l-20" @click="$refs.form.resetFields()">重置</el-button> -->
      <el-button class="m-l-20" type="primary" :disabled="!selected.length" @click="handleConfirm">提交</el-button>
    </div>
  </el-dialog>
</template>

<script>
  import http from 'assets/js/http';
  import { mapState } from 'vuex';

  export default {
    name: 'PackageLengthenDialog',
    mixins: [http],

    props: {
      show: {
        type: Boolean,
        required: true
      },
      type: { // '1' 批量延期 '2' 批量赋权
        type: String,
        validator: (value) => ['1', '2'].includes(value),
        default: '1'
      },
      selected: {
        type: Array,
        default: () => []
      },
      nameKey: {
        type: String,
        default: 'business_name'
      },
      idKey: {
        type: String,
        default: 'bus_id'
      }
    },
    data() {
      return {
        names: '',
        formData: {
          expire_type: '1', // 延期方式 '0' 为延期到指定日期 '1'为顺延有效天数
          expire_date: null, // 到期时间
          expire_days: undefined, // 延期天数
          version_id: null, // 版本id
          is_open_mer: false, // 是否开启商家版本
          mer_version: '', // 商家版本id
          version_type: '' // '0'清空后赋权, '1'智能赋权
        },
        versionList: [], // 赋权时的可选版本
        merVersionList: [],
        datePickerOption: {
          shortcuts: [
            {
              text: '今天',
              onClick(picker) {
                picker.$emit('pick', new Date())
              }
            },
            {
              text: '30天后',
              onClick(picker) {
                const date = new Date(Date.now() + 30 * 24 * 3600 * 1000);
                picker.$emit('pick', date)
              }
            },
            {
              text: '60天后',
              onClick(picker) {
                const date = new Date(Date.now() + 60 * 24 * 3600 * 1000);
                picker.$emit('pick', date)
              }
            },
            {
              text: '90天后',
              onClick: (picker) => {
                const date = new Date(Date.now() + 90 * 24 * 3600 * 1000);
                picker.$emit('pick', date)
              }
            },
            {
              text: '增加30天',
              onClick: (picker) => {
                const old = new Date(this.formData.expire_date || Date.now());
                const date = new Date(old.getTime() + 30 * 24 * 3600 * 1000);
                picker.$emit('pick', date)
              }
            },
            {
              text: '增加60天',
              onClick: (picker) => {
                const old = new Date(this.formData.expire_date || Date.now());
                const date = new Date(old.getTime() + 60 * 24 * 3600 * 1000);
                picker.$emit('pick', date)
              }
            },
            {
              text: '增加90天',
              onClick: (picker) => {
                const old = new Date(this.formData.expire_date || Date.now());
                const date = new Date(old.getTime() + 90 * 24 * 3600 * 1000);
                picker.$emit('pick', date)
              }
            }
          ]
        }
      }
    },
    computed: {
      ...mapState(['userInfo'])
    },

    watch: {
      show(val) {
        if (val) {
          const { type, selected, nameKey } = this
          this.names = selected.length === 0
            ? 'null'
            : selected.map(v => v[nameKey]).join('、');
            if (type === '2') { // 批量赋权
              this.getversionList(1)
              this.getversionList(2)
            }
        } else {
          Object.assign(this.$data.formData, this.$options.data().formData)
          this.$refs.form.clearValidate()
        }
      }
    },
    methods: {
      // 获取系统版本列表 versionType 1 系统版本 2 商家权限的版本
      getversionList(versionType) {
        const versionPost = {
          type: versionType,
          page_no: '',
          page_size: ''
        };
        this.apiPost('/Web/Version/getVersionList', versionPost)
          .then(res => {
            if (res.errorcode == 0) {
              this[versionType === 2 ? 'merVersionList' : 'versionList'] = res.data.list;
            } else {
              _g.toastMsg('warning', res.errormsg);
            }
          })
      },
      handleCancel() {
        this.$emit('update:show', false)
      },

      handleConfirm() {
        this.$refs.form.validate(valid => {
          if (valid) {
            const { type, selected, idKey, formData } = this
            const url = type === '1' ? '/Web/version/setBusinessesExpireDate' : '/Web/version/setBusinessesVersion'
            const params = {
              bus_ids: this.selected.map(v => v[idKey]),
              user_id: this.userInfo.id,
              ...type === '1' // '1' 批量延期 '2' 批量赋权
                ? {
                    type: formData.expire_type, // 此处type是延期方式
                    [formData.expire_type === '1' ? 'day' : 'expire_date']: formData.expire_type === '1'
                      ? formData.expire_days
                      : _g.formatDate(formData.expire_date, 'yyyy-MM-dd'),
                  }
                : {
                    edition: formData.version_id || 0,
                    type: formData.version_type, // 此处type是赋权方式
                    mer_version: formData.is_open_mer ? formData.mer_version : 0, // 如果没有开启商家端则为0
                  }
            }
            this.apiPost(url, params).then(res => {
              if (res.errorcode == 0) {
                _g.toastMsg('success', res.errormsg);
                this.$emit('on-success')
                this.handleCancel();
              } else {
                _g.toastMsg('warning', res.errormsg);
              }
            });
          }
        });
      },
    },
  }
</script>

<style lang="less" scoped>
.names-row {
  margin-top: 8px;
  line-height: 1.8;
}

.expire-input-number {
  width: 100%;
  /deep/.el-input__inner {
    text-align: left;
  }
}
</style>
