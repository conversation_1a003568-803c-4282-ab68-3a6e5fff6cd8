<template>
  <el-dialog
    ref="info"
    :title="plus ? '充值' : '扣除'"
    :visible.sync="showDialog"
    width="600px"
    :close-on-click-modal="false"
    @close="onDialogClose"
  >
    <el-form ref="form" label-width="100px" :model="infoData" class="info-dialog-form">
      <el-form-item label="当前合同券">
        {{contractNumber}}
      </el-form-item>
      <el-form-item :label="plus ? '充值额度' : '扣除额度'" prop="contract_number" :rules="{ required: true, message: '请填写'}">
        <el-input-number :min="1" v-model="infoData.contract_number" :max="plus ? 999999 : contractNumber" :precision="0" placeholder="请填写contractNumber"></el-input-number>
      </el-form-item>
      <el-form-item :label="`${plus ? '充值' : '扣除'}后合同券`">
        {{surplusNumer}}
      </el-form-item>
    </el-form>
    <div slot="footer">
      <el-button style="margin-right:20px;" @click="handleCancel">取消</el-button>
      <el-button type="primary" @click="handleConfirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
export default {
  name: 'AddContract',
  props: {
    value: {
      type: Boolean,
      default: false
    },
    plus: {
      type: Number,
      default: 1
    },
    contractNumber: {
      type: Number
    },
    busId: {
      type: Number
    }
  },
  data() {
    return {
      infoData: {
        contract_number: '',
      }
    }
  },
  watch: {
    showDialog(val) {
      if(!val) {
        this.infoData = {
          contract_number: ''
        }
      }
    },
    info: {
      handler(val) {
        if (val && val.category_id){
          Object.keys(this.infoData).forEach((key)=>{
            this.infoData[key] = this.info[key]
          })
        }
      },
      immediate: true
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.value
      },
      set(value) {
        this.$emit('input', value)
      }
    },
    surplusNumer() {
      const nowNum = Number(this.contractNumber || 0)
      const num = Number(this.infoData.contract_number || 0)
      let surplus = nowNum - num
      if ( this.plus === 1) {
        surplus = nowNum + num
      }
      return surplus;
    }
  },
  created () {
  },
  methods: {
    onDialogClose() {
      this.$emit('input', false)
    },
    handleCancel() {
      this.showDialog = false
      this.$emit('on-cancel')
    },
    handleConfirm() {
      this.$refs['form'].validate(valid => {
        if (valid) {
          this.$service.post('/web/Esign/update_contract_number', {
            bus_id: this.busId,
            user_id: this.$store.state.userInfo.id,
            plus: this.plus,
            now_contract_number: this.contractNumber,
            ...this.infoData
          }).then(res => {
            if (res.data.errorcode == 0) {
              this.showDialog = false
              this.$emit('on-success')
              _g.toastMsg('success', res.data.errormsg);
            } else {
              _g.toastMsg('warning', res.data.errormsg);
            }
          });
        }
      });
    }
  }
}
</script>
<style lang="less" scoped>

</style>



