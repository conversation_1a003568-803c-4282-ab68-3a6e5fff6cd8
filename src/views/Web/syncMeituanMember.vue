<template>
  <div class="container">
    <!-- Search Form -->
    <header class="search-header">
      <div class="search-left">
        <MerchantSelect v-model="postData.m_id" />
        <BusSelect class="w-200 m-r-10" v-model="postData.bus_id" placeholder="场馆列表" clearable filterable />
        <el-select v-model="postData.sync_user" placeholder="同步状态" class="w-200 m-r-10" clearable>
          <el-option v-for="item in statusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-button type="primary" @click="search">搜索</el-button>
      </div>
      <div class="search-right">
        <el-button type="primary" @click="showSyncRecords">同步记录</el-button>
      </div>
    </header>

    <!-- Table -->
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="mer_name" label="商家" align="center"></el-table-column>
      <el-table-column prop="bus_name" label="场馆" align="center"></el-table-column>
      <el-table-column prop="meituan_id" label="美团ID" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.meituan_id" size="mini">{{ scope.row.meituan_id }}</el-tag>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>
      <el-table-column prop="dianping_id" label="大众点评ID" align="center">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.dianping_id" size="mini">{{ scope.row.dianping_id }}</el-tag>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>
      <el-table-column label="同步状态" align="center">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.sync_user"
            :active-value="1"
            :inactive-value="0"
            @change="handleSyncChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button size="mini" type="primary" @click="handleSync(scope.row)" :disabled="scope.row.sync_user == 0">
            手动同步
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- Pagination -->
    <div class="pagination-container" v-if="dataCount > 0">
      <div class="block">
        <el-pagination
          @current-change="handleCurrentChange"
          layout="prev, pager, next, sizes, jumper, ->, total"
          :page-size="postData.page_size"
          :current-page="postData.page_no"
          :total="dataCount"
          :page-sizes="[10, 20, 50, 100]"
          @size-change="handleSizeChange"
        ></el-pagination>
      </div>
    </div>

    <!-- Sync Records Modal -->
    <el-dialog title="同步记录" :visible.sync="syncRecordsVisible" width="80%" :close-on-click-modal="false">
      <div class="sync-records-container">
        <!-- Search Form in Modal -->
        <div class="sync-records-search">
          <BusSelect class="w-200 m-r-10" v-model="syncRecordsData.bus_id" placeholder="场馆列表" clearable filterable />
          <el-input placeholder="操作人" v-model="syncRecordsData.name" class="w-200 m-r-10" clearable></el-input>
          <el-select v-model="syncRecordsData.status" placeholder="状态" class="w-200 m-r-10" clearable>
            <el-option v-for="item in syncStatusOptions" :key="item.value" :label="item.label" :value="item.value"></el-option>
          </el-select>
          <el-date-picker
            v-model="syncTimeRange"
            type="daterange"
            range-separator="至"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            value-format="yyyy-MM-dd"
            class="date-range m-r-10"
          ></el-date-picker>
          <el-button type="primary" @click="searchSyncRecords">搜索</el-button>
        </div>

        <!-- Sync Records Table -->
        <el-table :data="syncRecordsTableData" stripe style="width: 100%; margin-top: 15px">
          <el-table-column prop="create_time" label="操作时间" align="center" width="180">
            <template slot-scope="scope">
              <span v-if="scope.row.create_time">
                {{ scope.row.create_time | formatToDate }}
              </span>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
          <el-table-column prop="operator_name" label="操作人" align="center" width="120"></el-table-column>
          <el-table-column prop="bus_name" label="场馆" align="center"></el-table-column>
          <el-table-column prop="status" label="状态" align="center" width="100">
            <template slot-scope="scope">
              <el-tag v-if="scope.row.status == 0" type="info" size="mini">待执行</el-tag>
              <el-tag v-else-if="scope.row.status == 1" type="warning" size="mini">执行中</el-tag>
              <el-tag v-else-if="scope.row.status == 2" type="success" size="mini">成功</el-tag>
              <el-tag v-else-if="scope.row.status == 3" type="danger" size="mini">失败</el-tag>
              <el-tag v-else type="info" size="mini">未知</el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="result" label="详情" align="center">
            <template slot-scope="scope">
              <span v-if="scope.row.result">{{ scope.row.result }}</span>
              <span v-else class="text-muted">-</span>
            </template>
          </el-table-column>
        </el-table>

        <!-- Sync Records Pagination -->
        <div class="pagination-container" v-if="syncRecordsCount > 0">
          <div class="block">
            <el-pagination
              @current-change="handleSyncRecordsCurrentChange"
              layout="prev, pager, next, sizes, jumper, ->, total"
              :page-size="syncRecordsData.page_size"
              :current-page="syncRecordsData.page_no"
              :total="syncRecordsCount"
              :page-sizes="[10, 20, 50, 100]"
              @size-change="handleSyncRecordsSizeChange"
            ></el-pagination>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import http from 'assets/js/http'
import { formatDate } from '@/utils'
import MerchantSelect from '@/components/Payment/MerchantSelect.vue'

export default {
  name: 'SyncMeituanMember',
  components: {
    BusSelect: () => import('src/components/Common/BusSelect.vue'),
    MerchantSelect,
  },
  data() {
    return {
      postData: {
        m_id: '',
        bus_id: '',
        sync_user: '',
        page_size: 10,
        page_no: 1,
      },
      statusOptions: [
        { value: 1, label: '开启' },
        { value: 0, label: '关闭' },
      ],
      tableData: [],
      dataCount: 0,
      // Sync Records Modal Data
      syncRecordsVisible: false,
      syncTimeRange: [],
      syncRecordsData: {
        bus_id: '',
        name: '',
        status: '',
        s_date: '',
        e_date: '',
        page_size: 10,
        page_no: 1,
      },
      // 状态，0待执行，1执行中，2成功，3失败
      syncStatusOptions: [
        { value: 0, label: '待执行' },
        { value: 1, label: '执行中' },
        { value: 2, label: '成功' },
        { value: 3, label: '失败' },
      ],
      syncRecordsTableData: [],
      syncRecordsCount: 0,
    }
  },
  methods: {
    // 获取会员列表
    getMemberList() {
      this.apiPost('/Web/MeituanMember/getMemberSyncBusList', this.postData).then((res) => {
        if (res.errorcode === 0) {
          this.tableData = res.data.list
          this.dataCount = res.data.count
        } else {
          this.$message.warning(res.errormsg)
        }
      })
    },
    // 搜索
    search() {
      this.postData.page_no = 1
      this.getMemberList()
    },
    // 分页
    handleCurrentChange(currentPage) {
      this.postData.page_no = currentPage
      this.getMemberList()
    },
    // 切换每页条数
    handleSizeChange(size) {
      this.postData.page_no = 1
      this.postData.page_size = size
      this.getMemberList()
    },
    // 切换同步状态
    handleSyncChange(row) {
      const status = Number(row.sync_user)
      const action = status === 0 ? '关闭' : '开启'

      this.$confirm(`确认要${action}该会员的同步状态吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.apiPost('/Web/MeituanMember/syncMember', {
            bus_id: row.bus_id,
            sync_user: status,
            meituan_id: row.meituan_id,
            dianping_id: row.dianping_id,
          }).then((res) => {
            if (res.errorcode === 0) {
              this.$message.success(res.errormsg || `${action}成功`)
              this.getMemberList()
            } else {
              row.sync_user = Math.abs(status - 1) // 反转状态
              this.$message.warning(res.errormsg)
            }
          })
        })
        .catch(() => {
          row.sync_user = Math.abs(status - 1) // 取消操作，反转状态
        })
    },
    // 手动同步
    handleSync(row) {
      this.$confirm('确认要手动同步该会员吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(() => {
        this.apiPost('/Web/MeituanMember/activeSyncMember', {
          bus_id: row.bus_id,
          meituan_id: row.meituan_id,
          dianping_id: row.dianping_id,
        }).then((res) => {
          if (res.errorcode === 0) {
            this.$message.success(res.errormsg || '同步成功')
            this.getMemberList()
          } else {
            this.$message.warning(res.errormsg)
          }
        })
      })
    },
    // 显示同步记录弹窗
    showSyncRecords() {
      this.syncRecordsVisible = true
      this.getSyncRecordsList()
    },
    // 获取同步记录列表
    getSyncRecordsList() {
      // 处理时间范围
      if (Array.isArray(this.syncTimeRange) && this.syncTimeRange.length === 2) {
        this.syncRecordsData.s_date = this.syncTimeRange[0]
        this.syncRecordsData.e_date = this.syncTimeRange[1]
      } else {
        this.syncRecordsData.s_date = ''
        this.syncRecordsData.e_date = ''
      }

      this.apiPost('/Web/MeituanMember/getMemberSyncTaskList', this.syncRecordsData).then((res) => {
        if (res.errorcode === 0) {
          this.syncRecordsTableData = res.data.list
          this.syncRecordsCount = res.data.count
        } else {
          this.$message.warning(res.errormsg)
        }
      })
    },
    // 搜索同步记录
    searchSyncRecords() {
      this.syncRecordsData.page_no = 1
      this.getSyncRecordsList()
    },
    // 同步记录分页
    handleSyncRecordsCurrentChange(currentPage) {
      this.syncRecordsData.page_no = currentPage
      this.getSyncRecordsList()
    },
    // 切换每页条数
    handleSyncRecordsSizeChange(size) {
      this.syncRecordsData.page_no = 1
      this.syncRecordsData.page_size = size
      this.getSyncRecordsList()
    },
  },
  created() {
    this.getMemberList()
  },
  mixins: [http],
  filters: {
    formatToDate(value) {
      if (!value) return ''
      return formatDate(new Date(Number(value) * 1000), 'yyyy-MM-dd HH:mm')
    },
  },
}
</script>

<style scoped>
.container {
  padding: 20px;
}

.search-header {
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.search-left {
  display: flex;
  align-items: center;
}

.search-right {
  display: flex;
  align-items: center;
}

.w-200 {
  width: 200px;
}

.m-r-10 {
  margin-right: 10px;
}

.pagination-container {
  margin-top: 20px;
  display: flex;
  justify-content: flex-end;
}

.sync-records-container {
  padding: 10px;
}

.sync-records-search {
  display: flex;
  align-items: center;
  margin-bottom: 15px;
}

.date-range {
  width: 380px;
}

.text-muted {
  color: #606266;
  text-shadow: 1px 1px 2px pink;
}
</style>
