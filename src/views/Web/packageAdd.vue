<template>
  <div class="inpanel-c-c">
    <el-form ref="form" :model="postData">
      <el-form-item label="版本名称" prop="name" :rules="{ required: 'true', type: 'string', max:20, message: '最多20个字符'}">
        <el-input class="w-320 maright-20" v-model="postData.name"></el-input>
        <el-button type="primary" style="margin-left: 15px;" @click="exportExcel" v-if="tableList && merTableList">导出权限表格</el-button>
        <Export ref="export"></Export>
      </el-form-item>
      <el-form-item label="版本类型" prop="version_type" :rules="{ required: true, message: '请选择版本类型'}">
        <el-radio-group v-model="postData.version_type" :disabled="!!$route.params.version_id" @change="handleChangeType">
          <el-radio :label="1">商家版本</el-radio>
          <el-radio :label="0">场馆版本</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="续费版本" prop="renew_version">
        <el-select v-model="postData.renew_version">
          <el-option value="">请选择</el-option>
          <el-option value="rbw">RBW</el-option>
          <el-option value="rbe">RBE</el-option>
          <el-option value="rbt">RBT</el-option>
        </el-select>
      </el-form-item>

      <el-table :data="tableData" stripe :span-method="objectSpanMethod" border class="mb-22" style="width: 95%">
        <el-table-column prop="name" label="一级菜单" width="200" align="center">
          <template scope="scope">
            <span class='buttname'>{{scope.row.name}}</span>
            <el-switch @change="leveloneSwitch(scope)" active-value='1' inactive-value='0' class="switchbutt"
                       v-model="scope.row.is_default"></el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="son" label="二级菜单" width="200" align="center">
          <template scope="scope">
            <span class='buttname'>{{scope.row.son.title}}</span>
            <el-switch :disabled="scope.row.is_default=='0'" active-value="1" inactive-value="0"
                       @change="leveltwoSwitch(scope)" class="switchbutt"
                       v-model="scope.row.son.is_default"></el-switch>
          </template>
        </el-table-column>
        <el-table-column prop="son" label="功能权限" align="center">
          <template scope="scope">
            <el-checkbox :disabled="scope.row.son.is_default=='0'" v-model="scope.row.check_all"
                         @change="handlecheckAll(scope)" v-if="scope.row.son.son&&scope.row.son.son.length>1"
                         style="display: inline-block">全选
            </el-checkbox>
            <el-checkbox-group v-model="scope.row.check_ids" @change="handlecheckedItems(scope)">
              <el-checkbox :disabled="scope.row.son.is_default=='0'" v-for="item in scope.row.son.son" :label="item.id"
                           :key="item.id">{{item.title}}
              </el-checkbox>
            </el-checkbox-group>
          </template>
        </el-table-column>
      </el-table>

      <el-form-item>
        <el-button type="primary" @click="onSubmit">保存</el-button>
        <el-button class="m-l-150" @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>

  </div>
</template>
<script>
  import http from 'assets/js/http';
  import Export from 'src/components/Export'

  export default {
    name: 'packageAdd',
    components: { Export },
    data() {
      return {
        postData: {
          name: '',
          version_type: '',
          renew_version: '',
          version_id: '',
          node_id: []
        },
        tableData: [],
        busNodeList: [],
        merNodeList: [],
        tableList: '',
        merTableList: ''
      };
    },
    methods: {
      exportExcel() {
        const columns = [
          {
            title: '一级菜单',
            key: 'one'
          },
          {
            title: '二级菜单',
            key: 'two'
          },
          {
            title: '功能权限',
            key: 'three'
          },
        ];
        const list = this.postData.version_type === 1 ? this.merTableList : this.tableList
        const data = list.map(item => {
          return {
            one: item.name,
            two: item.son.map(item => item.title),
            three: item.son.map(item => item.son && item.son.reduce((total, func) => total += `${func.title}、`, ''))
          }
        });
        this.$refs.export.export({
          filename: '权限表格',
          data,
          columns
        })
      },

      handleChangeType(radio) {
        if (radio === 1) {
          this.busNodeList = this.tableData
          this.tableData = this.merNodeList
        } else {
          this.merNodeList = this.tableData
          this.tableData = this.busNodeList
        }
      },

      leveloneSwitch(scope) {
        let index = scope.$index;
        for (let i = 0; i < this.tableData.length; i++) {
          if (this.tableData[i].id == scope.row.id) {
            if (scope.row.is_default == '1') {
              this.tableData[i].is_default = '1';
            } else {
              this.tableData[i].is_default = '0';
              this.tableData[i].son.is_default = '0';
              this.tableData[i].check_all = false;
              this.tableData[i].check_ids = [];
            }
          }
        }
      },
      leveltwoSwitch(scope) {
        let index = scope.$index;
        if (scope.row.son.is_default == '0') {
          this.tableData[index].check_all = false;
          this.tableData[index].check_ids = [];
        }
      },
      handlecheckAll(scope) {
        let index = scope.$index;
        if (!scope.row.check_all) {
          this.tableData[index].check_all = false;
          this.tableData[index].check_ids = [];
        } else {
          this.tableData[index].check_all = true;
          let ids = [];
          for (let i = 0; i < this.tableData[index].son.son.length; i++) {
            ids.push(this.tableData[index].son.son[i].id);
          }
          this.tableData[index].check_ids = ids;
        }
      },
      handlecheckedItems(scope) {
        let index = scope.$index;
        if (scope.row.check_ids && scope.row.check_ids.length != scope.row.son.son.length) {
          this.tableData[index].check_all = false;
        }
        if (scope.row.check_ids && scope.row.check_ids.length == scope.row.son.son.length) {
          this.tableData[index].check_all = true;
        }
      },
      objectSpanMethod({ row, column, rowIndex, columnIndex }) {
        if (columnIndex === 0) {
          if (rowIndex == 0 && this.tableData.length > 1) {
            if (this.tableData[rowIndex].id == this.tableData[rowIndex + 1].id) {
              let spanlen = 0;
              for (let i = rowIndex; i < this.tableData.length; i++) {
                if (this.tableData[rowIndex].id == this.tableData[i].id) {
                  spanlen = spanlen + 1;
                }
              }
              return {
                rowspan: spanlen,
                colspan: 1
              };
            }
          } else if (rowIndex > 0 && this.tableData.length > 1) {
            if (this.tableData[rowIndex].id != this.tableData[rowIndex - 1].id) {
              let spanlen = 0;
              for (let i = rowIndex; i < this.tableData.length; i++) {
                if (this.tableData[rowIndex].id == this.tableData[i].id) {
                  spanlen = spanlen + 1;
                }
              }
              return {
                rowspan: spanlen,
                colspan: 1
              };
            } else {
              return {
                rowspan: 0,
                colspan: 0
              };
            }
          }
        }
      },
      onSubmit() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.postData.node_id = [];
            for (let i = 0; i < this.tableData.length; i++) {
              if (this.tableData[i].is_default == '1' && this.tableData[i].son) {
                if (this.tableData[i].son.is_default == '1') {
                  this.postData.node_id.push(this.tableData[i].son.id);
                  if (this.tableData[i].check_ids) {
                    this.postData.node_id.push(...this.tableData[i].check_ids);
                  }
                }
              }
            }
            if (this.postData.version_id == 0) {
              this.postData.version_id = '';
              this.apiPost('/Web/Version/addVersion', this.postData).then(res => {
                if (res.errorcode == 0) {
                  _g.toastMsg('success', res.errormsg);
                  this.$router.back();
                } else {
                  _g.toastMsg('warning', res.errormsg);
                }
              });
            } else {
              this.apiPost('/Web/Version/EditVersion', this.postData).then(res => {
                if (res.errorcode == 0) {
                  _g.toastMsg('success', res.errormsg);
                  this.$router.back();
                } else {
                  _g.toastMsg('warning', res.errormsg);
                }
              });
            }
          }
        });
      },
      handleNodeList(tabletemp) {
        const list = []
        for (let i = 0; i < tabletemp.length; i++) {
          if (tabletemp[i].son) {
            //一级是否打开，只需判断二级有没有打开的
            let defaulttemp = false;

            for (let j = 0; j < tabletemp[i].son.length; j++) {
              if (tabletemp[i].son[j].is_default == '1') {
                defaulttemp = true;
              }
              let allcheck = true;
              let checkids = [];
              if (tabletemp[i].son[j].son) {
                //有三级菜单
                for (let k = 0; k < tabletemp[i].son[j].son.length; k++) {
                  if (tabletemp[i].son[j].son[k].is_default == '1') {
                    checkids.push(tabletemp[i].son[j].son[k].id);
                  } else {
                    allcheck = false;
                  }
                }
                let item = {
                  id: tabletemp[i].id,
                  name: tabletemp[i].name,
                  son: tabletemp[i].son[j],
                  is_default: '0',
                  check_all: false,
                  check_ids: checkids
                };
                if (defaulttemp) {
                  item.is_default = '1';
                }
                if (allcheck) {
                  item.check_all = true;
                }
                list.push(item);
              } else {
                //只有到二级菜单,没有check_ids
                let item = {
                  id: tabletemp[i].id,
                  name: tabletemp[i].name,
                  son: tabletemp[i].son[j],
                  is_default: '0',
                  check_all: false
                };
                if (defaulttemp) {
                  item.is_default = '1';
                }
                list.push(item);
              }
            }
          } else {

            //只有一级菜单,没有son和check_ids
            let item = {
              id: tabletemp[i].id,
              name: tabletemp[i].name,
              is_default: '0',
              check_all: false,
            };
            list.push(item);
          }
        }
        return list
      },
      getpageInfo() {
        this.apiPost('/Web/Version/getVersionAccess', { version_id: this.postData.version_id })
          .then(res => {
            _g.closeGlobalLoading();
            if (res.errorcode == 0) {
              if (res.data.name) {
                this.postData.name = res.data.name;
              }
              this.tableList = res.data.bus_node_list;
              this.merTableList = res.data.mer_node_list;

              this.busNodeList = this.handleNodeList(res.data.bus_node_list)
              this.merNodeList = this.handleNodeList(res.data.mer_node_list)
              this.postData.renew_version = res.data.renew_version
              this.tableData = this.postData.version_type === 1 ? this.merNodeList : this.busNodeList;
            } else {
              _g.toastMsg('warning', res.errormsg);
            }
          })
          .catch(error => {
            console.log(error);
          });
      }
    },
    created() {
      this.postData.version_id = this.$route.params.version_id;
      this.postData.version_type = this.$route.params.version_type !== undefined ? +this.$route.params.version_type : 0;
      this.getpageInfo();
    },
    mixins: [http]
  };
</script>
<style scoped>
  .inpanel-c-c {
    padding: 20px;
  }

  .mb-22 {
    margin-bottom: 22px;
  }

  .switchbutt {
    margin-left: 10px;
    margin-right: 10px;
  }

  .buttname {
    width: 100px;
    display: inline-block;
    text-align: right;
    padding-right: 10px;
  }
</style>
