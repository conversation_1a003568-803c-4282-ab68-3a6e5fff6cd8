
<style lang="less">
  .business {
    .el-form-item__content {
      display: flex;
    }
  }
</style>
<template>
  <div class="container business">
    <header>
      <el-input style="width: 200px" v-model="name" placeholder="名称" @keydown.enter.native="getList"></el-input>
      <el-input style="width: 200px" v-model="phone" placeholder="电话" @keydown.enter.native="getList"></el-input>
      <el-button type="success" @click="getList">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe @selection-change="selectItem">
      <!-- <el-table-column type="selection" align="center" style="width:100%" width="50"></el-table-column> -->
      <el-table-column prop="mer_name" align="center" label="名称"></el-table-column>
      <el-table-column prop="phone" align="center" label="电话"></el-table-column>
      <el-table-column prop="pcd_address" align="center" label="地址"></el-table-column>
      <el-table-column prop="status" align="center" label="状态">
        <template scope="scope">
          <el-button size="small" v-if="scope.row.status == 1" @click="handleToggle(0, scope.row.id)" type="success">正常</el-button>
          <el-button size="small" v-else @click="handleToggle(1, scope.row.id)" type="info">禁用</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="create_time" align="center" label="创建时间"></el-table-column>
      <el-table-column prop="id" align="center" label="操作">
        <template scope="scope">
          <el-button size="small" @click="clickDetail(scope.row.id)" type="primary">详情</el-button>
          <el-button size="small" type="danger" @click="deleteBusiness(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
        <el-button type="success" @click="addBusiness" size="small" icon="el-icon-plus">
          添加
        </el-button>
        <!-- <el-dropdown size="small" @command="handleDropdown" placement="top" type="info"><el-button type="info" size="small" style="margin-left: 20px">
            其他操作
            <i class="el-icon-arrow-down el-icon--right"></i></el-button><el-dropdown-menu slot="dropdown"><el-dropdown-item command="0">批量删除</el-dropdown-item></el-dropdown-menu></el-dropdown> -->
      </div>
      <el-pagination layout="total, prev, pager, next, sizes" background @size-change="sizeChange" @current-change="pageChange" :page-size="pageSize" :current-page.sync="page" :page-sizes="[10, 20, 30, 40]" :total="total"></el-pagination>
    </footer>
    <el-dialog title="详情" :visible.sync="showDetail" width="50%">
      <el-form ref="form" :model="detail" label-width="80px">
        <el-form-item label="商家名称" prop="mer_name" :rules="[{required: true, message: '请填写商家名称', trigger: 'blur'}]">
          <el-input v-model="detail.mer_name" placeholder="例如: 勤鸟运动健身馆" :disabled="!isEditDetail"></el-input>
        </el-form-item>
        <el-form-item label="场馆类型">
          <el-select v-model="detail.bus_type" :disabled="!isEditDetail" placeholder="场馆类型">
            <!-- <el-option :value="0" label="场馆类型"></el-option>
            <el-option :value="1" label="健身房"></el-option>
            <el-option :value="2" label="瑜伽馆"></el-option>
            <el-option :value="3" label="跆拳道馆"></el-option>
            <el-option :value="4" label="武道馆"></el-option>
            <el-option :value="5" label="舞蹈馆"></el-option>
            <el-option :value="6" label="其他"></el-option> -->
            <el-option v-for="(value, key) of busType" :key="key" :value="+key" :label="value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="区域">
          <el-select @change="getRegion(2, 1)" :disabled="!isEditDetail" filterable v-model="detail.province_id">
            <el-option :value="0" label="未填写"></el-option>
            <el-option v-for="(province, index) in provincesList" :value="province.region_id" :label="province.region_name" :key="province.region_id"></el-option>
          </el-select>
          <el-select @change="getRegion(3, 1)" :disabled="!isEditDetail" style="margin: 0 20px" filterable v-model="detail.city_id">
            <el-option :value="0" label="未填写"></el-option>
            <el-option v-for="(city, index) in citiesList" :value="city.region_id" :label="city.region_name" :key="city.region_id"></el-option>
          </el-select>
          <el-select filterable :disabled="!isEditDetail" @change="handleDistrict" v-model="district_id">
            <el-option :value="0" label="未填写"></el-option>
            <el-option v-for="(district, index) in districtsList" :value="district.region_id" :label="district.region_name" :key="district.region_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="联系电话" prop="phone" :rules="[{ required: true, message: '请填写手机号码', trigger: 'blur' }, { validator: phoneValidate, trigger: 'change'}]">
          <el-input v-model="detail.phone" :disabled="!isEditDetail"></el-input>
        </el-form-item>
        <el-form-item label="详细地址">
          <el-input v-model="detail.pcd_address" placeholder="请填写具体街道和门牌号" :disabled="!isEditDetail"></el-input>
        </el-form-item>
        <template v-if="isAdd">
          <el-form-item label="申请人">
            <el-input v-model="detail.apply_name" :disabled="!isEditDetail"></el-input>
          </el-form-item>
          <el-form-item label="用户名">
            <el-input v-model="detail.accounts" :disabled="!isEditDetail"></el-input>
          </el-form-item>
          <el-form-item label="密码">
            <el-input v-model="detail.password" :disabled="!isEditDetail"></el-input>
          </el-form-item>
          <el-form-item label="确认密码">
            <el-input v-model="detail.check_password" :disabled="!isEditDetail"></el-input>
          </el-form-item>
          <el-form-item label="会员规模">
            <el-select v-model="detail.member_scale" :disabled="!isEditDetail">
              <el-option :value="0" label="会员规模"></el-option>
              <el-option :value="1" label="100人以下"></el-option>
              <el-option :value="2" label="100-500人"></el-option>
              <el-option :value="3" label="500人以上"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="营业执照">
            <el-upload class="upload-demo" ref="upload" :action="uploadUrl" :before-upload="beforeUpload" :on-success="handleUploadSuccess" :show-file-list="false" :data="{savePath: './Uploads/'}" :file-list="fileList" :multiple="false">
              <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
              <a :href="licenseImg" target="_blank">
                <img :src="licenseImg" alt="" style="width: 100%">
              </a>
            </el-upload>
          </el-form-item>
        </template>
        <template v-if="!isAdd">
          <el-form-item label="状态">
            <div>
              <el-radio-group v-model="detail.status" :disabled="!isEditDetail">
                <el-radio :label="1">可用</el-radio>
                <el-radio :label="0">禁用</el-radio>
              </el-radio-group>
            </div>
          </el-form-item>
          <el-form-item label="商家简介">
            <Editor :disabled="!isEditDetail" v-model="detail.mer_description" />
          </el-form-item>
        </template>
      </el-form>
      <footer slot="footer">
        <el-button v-if="!isEditDetail" type="success" @click="isEditDetail = true">编辑</el-button>
        <el-button v-else-if="isAdd" type="success" @click="handleAddBus">保存</el-button>
        <el-button v-else type="success" @click="handleSaveEdit">保存</el-button>
        <el-button v-if="!isEditDetail" type="info" @click="showDetail = false">关闭</el-button>
        <el-button v-else type="info" @click="isEditDetail = showDetail = isAdd = false">取消</el-button>
      </footer>
    </el-dialog>
  </div>
</template>
  <script>
  import Editor from 'components/form/Editor';

  export default {
    name: 'Business',
    components: {
      Editor
    },
    data() {
      return {
        uploadUrl: _g.getRbBaseUrl() + '/Admin/Public/upload',
        fileList: [],

        form: {
          phone: ''
        },
        page: 1,
        pageSize: 10,
        total: 0,
        name: '',
        phone: '',

        showDetail: false,
        isEditDetail: false,
        isAdd: false,
        selectedItemsId: [],
        provincesList: [],
        citiesList: [],
        districtsList: [],
        district_id: '',
        licenseImg: '',
        detail: {
          mer_name: '',
          bus_type: 0,
          phone: '',
          address: '',
          content: '',
          province_id: 0,
          city_id: 0,
          district_id: 0,
          license: ''
        },
        tableData: [],
        busType: {
          '1': '健身房',
          '9': '健身工作室',
          '7': '体育馆',
          '8': '游泳馆',
          '2': '瑜伽馆',
          '3': '跆拳道馆',
          '4': '武道馆',
          '5': '舞蹈馆',
          '6': '其他',
        },
      };
    },
    created() {
      this.getList();
    },
    methods: {
      pageChange(page) {
        this.page = page;
        this.getList();
      },
      phoneValidate(rule, value, cb) {
        const reg = /^1\d{10}$/;
        if (!reg.test(value)) {
          cb(new Error('手机号码错误'));
        }
        cb();
      },
      handleDistrict(id) {
        this.detail.district_id = id;
      },
      handleUploadSuccess(res, file) {
        if (res.status === 1) {
          this.detail.license = res.info;
          this.licenseImg = res.info;
          console.log(this.detail.license, '123');
        } else {
          this.$message.error('上传失败');
        }
      },
      beforeUpload(file) {
        const fileType = ['image/jpg', 'image/png', 'image/jpeg'];
        const isJPG = fileType.includes(file.type);
        const isLt2M = file.size / 1024 / 1024 < 4;

        if (!isJPG) {
          this.$message.error('上传图片只能是 JPG/JPEG/PNG 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 4MB!');
        }
        return isJPG && isLt2M;
      },
      sizeChange(size) {
        this.pageSize = size;
        this.getList();
      },
      selectItem(item) {
        this.selectedItemsId = item.map(item => item.id);
      },
      async clickDetail(id) {
        const item = this.tableData.find(item => item.id === id);
        this.district_id = item.district_id;
        this.detail = { ...item };
        this.getRegion();
        this.getRegion(2);
        this.getRegion(3);
        this.showDetail = true;
      },
      addBusiness() {
        this.detail = {
          bus_type: 0
        };
        this.isEditDetail = true;
        this.isAdd = true;
        this.showDetail = true;
        this.getRegion();
      },
      deleteBusiness(id) {
        const url = '/Web/Merchants/delMerchants';
        this.$service
          .post(url, { id })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$message.success(res.data.errormsg);
              this.getList();
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getList() {
        const url = '/Web/Merchants/merchantsList';
        const { name, phone, page: page_no, pageSize: page_size } = this;
        this.$service
          .post(url, { name, phone, page_no, page_size })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.total = data.count;
              this.tableData = data.list.map(item => {
                return {
                  ...item,
                  ...{
                    create_time: _g.formatDate(new Date(item.create_time * 1000), 'yyyy-MM-dd HH:mm:ss')
                  }
                };
              });
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      handleToggle(status, id) {
        const url = '/Web/Merchants/ajaxStatus';
        this.$service
          .post(url, { id, status })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.tableData.find(item => item.id === id).status = status;
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      handleAddBus() {
        const url = '/Web/Merchants/addMerchants';
        const {
          mer_name: bus_name,
          province_id: province,
          city_id: city,
          district_id: district,
          pcd_address: address
        } = this.detail;
        const postData = { ...this.detail, ...{ bus_name, province, city, district, address } };
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$message.success(res.data.errormsg);
              this.isEditDetail = false;
              this.isAdd = false;
              this.showDetail = false;
              this.getList();
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      handleSaveEdit() {
        this.$refs.form.validate(valid => {
          if (valid) {
            const url = '/Web/Merchants/saveMerchants';
            const { province_id: province, city_id: city, district_id: district, pcd_address: address } = this.detail;
            const postData = { ...this.detail, ...{ province, city, district, address } };
            this.$service
              .post(url, postData)
              .then(res => {
                if (res.data.errorcode === 0) {
                  this.$message.success(res.data.errormsg);
                  this.isEditDetail = false;
                  this.showDetail = false;
                  this.getList();
                } else {
                  this.$message.error(res.data.errormsg);
                }
              })
              .catch(err => {
                console.error(err);
              });
          } else {
            this.$message.error('表单错误');
          }
        });
      },
      getRegion(type = 1, clear) {
        const url = '/Web/Merchants/ajaxGetRegion';
        const { province_id: region_id, city_id } = this.detail;
        const postData = {
          region_id: ''
        };
        if (type === 2) {
          if (clear) {
            this.detail.city_id = this.detail.district_id = this.district_id = '';
          }
          postData.region_id = region_id;
        }
        if (type === 3) {
          if (clear) {
            this.district_id = this.detail.district_id = '';
          }
          postData.region_id = city_id;
        }
        return this.$service
          .post(url, postData, { loading: false })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              if (type === 1) {
                this.provincesList = data;
              } else if (type === 2) {
                this.citiesList = data;
              } else if (type === 3) {
                this.districtsList = data;
              }
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>
