<template>
  <div class="m-l-50 m-t-30">
    <el-form ref="form" :model="form" :rules="rules" label-width="100px">
      <el-form-item label="签约场馆" prop="name">
        <el-input v-model.trim="form.name" class="h-40 w-200"></el-input>
      </el-form-item>
      <el-form-item label="使用场馆" prop="bus_id">
        <el-select v-model="form.bus_id" style="width: 186px;">
          <el-option v-for="item in use_bus" :key="item.bus_id" :label="item.bus_name" :value="item.bus_id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="场馆类型" prop="bus_type">
        <el-select v-model="form.bus_type" style="width: 186px;">
          <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.label">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否使用" prop="use_status">
        <el-radio-group v-model="form.use_status">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="绑定勤鸟" prop="follow_us">
        <el-radio-group v-model="form.follow_us">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="地址" :label-width="formLabelWidth">
        <el-select v-model="region" @change="regionChange" style="width: 156px;">
          <el-option label="省" value="">省</el-option>
          <el-option label="北京" value="2">北京</el-option>
          <el-option label="安徽" value="3">安徽</el-option>
          <el-option label="福建" value="4">福建</el-option>
          <el-option label="甘肃" value="5">甘肃</el-option>
          <el-option label="广东" value="6">广东</el-option>
          <el-option label="广西" value="7">广西</el-option>
          <el-option label="贵州" value="8">贵州</el-option>
          <el-option label="海南" value="9">海南</el-option>
          <el-option label="河北" value="10">河北</el-option>
          <el-option label="河南" value="11">河南</el-option>
          <el-option label="黑龙江" value="12">黑龙江</el-option>
          <el-option label="湖北" value="13">湖北</el-option>
          <el-option label="湖南" value="14">湖南</el-option>
          <el-option label="吉林" value="15">吉林</el-option>
          <el-option label="江苏" value="16">江苏</el-option>
          <el-option label="江西" value="17">江西</el-option>
          <el-option label="辽宁" value="18">辽宁</el-option>
          <el-option label="内蒙古" value="19">内蒙古</el-option>
          <el-option label="宁夏" value="20">宁夏</el-option>
          <el-option label="青海" value="21">青海</el-option>
          <el-option label="山东" value="22">山东</el-option>
          <el-option label="山西" value="23">山西</el-option>
          <el-option label="陕西" value="24">陕西</el-option>
          <el-option label="上海" value="25">上海</el-option>
          <el-option label="四川" value="26">四川</el-option>
          <el-option label="天津" value="27">天津</el-option>
          <el-option label="西藏" value="28">西藏</el-option>
          <el-option label="新疆" value="29">新疆</el-option>
          <el-option label="云南" value="30">云南</el-option>
          <el-option label="浙江" value="31">浙江</el-option>
          <el-option label="重庆" value="32">重庆</el-option>
          <el-option label="香港" value="33">香港</el-option>
          <el-option label="澳门" value="34">澳门</el-option>
          <el-option label="台湾" value="35">台湾</el-option>
        </el-select>
        <el-select v-model="city" @change="cityChange" style="width: 156px;">
          <el-option label="市" value="">市</el-option>
          <el-option v-for="item in citys" :key="item.region_id" :label="item.region_name" :value="item.region_id">
          </el-option>
        </el-select>
        <el-select v-model="district" style="width: 156px;">
          <el-option label="区" value="">区</el-option>
          <el-option v-for="item in districts" :key="item.region_id" :label="item.region_name" :value="item.region_id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="详细地址" prop="detailAddr">
        <el-input v-model="detailAddr" class="h-40 w-500"></el-input>
      </el-form-item>
      <el-form-item label="场馆品牌" prop="brand">
        <el-radio-group v-model.trim="form.brand">
          <el-radio :label="1">连锁品牌</el-radio>
          <el-radio :label="0">单一品牌</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="签约时间" prop="time">
        <div class="block">
          <el-date-picker v-model="form.time" type="date" size="small" popper-class="666666" placeholder="选择日期">
          </el-date-picker>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="add('form')" :loading="isLoading">提交</el-button>
        <el-button @click="goback()">返回</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<style scoped>
  .form-checkbox:first-child {
    margin-left: 15px;
  }
  .el-input--small input {
    border-radius: 2px;
  }
  .el-input--small input {
    border-radius: 2px;
  }
  .el-input__inner {
    border-radius: 2px;
  }
</style>
<script>
  import http from 'assets/js/http';
  import fomrMixin from 'assets/js/form_com';
  export default {
    name: 'bus_add',
    data() {
      return {
        isLoading: false,
        form: {
          name: '',
          bus_type: '',
          bus_id: '',
          brand: 1,
          time: '',
          follow_us: 1,
          use_status: 1
        },
        detailAddr: '',
        use_bus: [],
        regions: [
          {
            value: '',
            label: '请选择'
          },
          {
            value: '2',
            label: '北京'
          },
          {
            value: '3',
            label: '安徽'
          },
          {
            value: '4',
            label: '福建'
          },
          {
            value: '5',
            label: '甘肃'
          },
          {
            value: '6',
            label: '广东'
          },
          {
            value: '7',
            label: '广西'
          },
          {
            value: '8',
            label: '贵州'
          },
          {
            value: '9',
            label: '海南'
          },
          {
            value: '10',
            label: '河北'
          },
          {
            value: '11',
            label: '河南'
          },
          {
            value: '12',
            label: '黑龙江'
          },
          {
            value: '13',
            label: '湖北'
          },
          {
            value: '14',
            label: '湖南'
          },
          {
            value: '15',
            label: '吉林'
          },
          {
            value: '16',
            label: '江苏'
          },
          {
            value: '17',
            label: '江西'
          },
          {
            value: '18',
            label: '辽宁'
          },
          {
            value: '19',
            label: '内蒙古'
          },
          {
            value: '20',
            label: '宁夏'
          },
          {
            value: '21',
            label: '青海'
          },
          {
            value: '22',
            label: '山东'
          },
          {
            value: '23',
            label: '山西'
          },
          {
            value: '24',
            label: '陕西'
          },
          {
            value: '25',
            label: '上海'
          },
          {
            value: '26',
            label: '四川'
          },
          {
            value: '27',
            label: '天津'
          },
          {
            value: '28',
            label: '西藏'
          },
          {
            value: '29',
            label: '新疆'
          },
          {
            value: '30',
            label: '云南'
          },
          {
            value: '31',
            label: '浙江'
          },
          {
            value: '32',
            label: '重庆'
          },
          {
            value: '33',
            label: '香港'
          },
          {
            value: '34',
            label: '澳门'
          },
          {
            value: '35',
            label: '台湾'
          }
        ],
        options: [
          /* {
            value: '',
            label: '请选择'
          },
          {
            value: '1',
            label: '健身房'
          },
          {
            value: '2',
            label: '瑜伽馆'
          },
          {
            value: '3',
            label: '跆拳道馆'
          },
          {
            value: '4',
            label: '武道馆'
          },
          {
            value: '5',
            label: '舞蹈馆'
          },
          {
            value: '6',
            label: '其它'
          }, */
          { label: '请选择', value: '' },
          { label: '健身房', value: 1 },
          { label: '健身工作室', value: 9 },
          { label: '体育馆', value: 7 },
          { label: '游泳馆', value: 8 },
          { label: '瑜伽馆', value: 2 },
          { label: '跆拳道馆', value: 3 },
          { label: '武道馆', value: 4 },
          { label: '舞蹈馆', value: 5 },
          { label: '其他', value: 6 },
        ],
        region: '',
        city: '',
        district: '',
        citys: [],
        districts: [],
        rules: {
          name: [{ required: true, message: '请输入场馆名' }],
          bus_type: [{ required: true, message: '请选择场馆类型' }],
          use_status: [{ required: true, message: '请勾选是否使用' }],
          follow_us: [{ required: true, message: '请选择' }]
        }
      };
    },
    methods: {
      selectCheckbox() {
        let temp = false;
        _(this.groupOptions).forEach(res => {
          if (this.selectedGroups.toString().indexOf(res.title) > -1) {
            this.selectedIds.push(res.id);
          }
        });
        if (this.selectedIds.length) {
          this.form.groups = _.cloneDeep(this.selectedIds);
          temp = true;
        }
        this.selectedIds = [];
        return temp;
      },
      regionChange(id) {
        this.city = '';
        this.district = '';
        let url = 'https://wx.rocketbird.cn/MerchantsRegister/ajax_getRegion';
        let postData = {
          region_id: id
        };
        let _this = this;
        this.$service
          .post(url, postData)
          .then(function(response) {
            _this.citys = response.data;
          })
          .catch(function(error) {
            console.log(error);
          });
      },
      cityChange(id) {
        let url = 'https://wx.rocketbird.cn/MerchantsRegister/ajax_getRegion';
        let postData = {
          region_id: id
        };
        let _this = this;
        this.$service
          .post(url, postData)
          .then(function(response) {
            _this.districts = response.data;
          })
          .catch(function(error) {
            console.log(error);
          });
      },
      get_use_bus() {
        this.apiPost('/web/maintenance/new_qn_bus_list', this.form).then(res => {
          this.handelResponse(res, data => {
            this.use_bus = res.data;
          });
        });
      },
      add(form) {
        // console.log('res = ', _g.j2s(this.form))
        let city_name = '';
        let district_name = '';
        let region_name = '';
        for (var item in this.citys) {
          if (this.citys[item].region_id == this.city) {
            city_name = this.citys[item].region_name;
          }
        }
        for (var d in this.districts) {
          if (this.districts[d].region_id == this.district) {
            district_name = this.districts[d].region_name;
          }
        }
        for (var i in this.regions) {
          if (this.regions[i].value == this.region) {
            region_name = this.regions[i].label;
          }
        }
        this.form.time = Date.parse(new Date(this.form.time)) / 1000;
        this.$refs[form].validate(pass => {
          if (pass) {
            this.isLoading = !this.isLoading;
            this.form.address = region_name + city_name + district_name + this.detailAddr;
            this.form.action = 'add';
            this.apiPost('/web/maintenance/edit_sign_bus', this.form).then(res => {
              this.handelResponse(
                res,
                data => {
                  _g.toastMsg('success', '添加成功');
                  _g.clearVuex('setUsers');
                  setTimeout(() => {
                    this.goback();
                  }, 1500);
                },
                () => {
                  this.isLoading = !this.isLoading;
                }
              );
            });
          }
        });
      }
    },
    created() {
      this.get_use_bus();
    },
    mixins: [http, fomrMixin]
  };
</script>
