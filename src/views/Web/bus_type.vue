<template>
  <div class="main-wrap">
    <div class="main-title">
      <span>场馆统计</span>
      <i>>></i>
      <span>场馆占比</span>
    </div>
    <div class="main-nav">
      <span class="active">场馆类型</span>
      <span @click="routerChange('brand')">场馆品牌</span>
      <span @click="routerChange('region')">场馆地区</span>
      <span @click="routerChange('wx')">微信绑定</span>
    </div>
    <!--主体结构-->
    <div class="main-container">
      <!--搜索框-->
      <div class="main-container-wrap">
        <div class="search-box">
          <div class="block">
            <el-date-picker v-model="value1" type="date" size="small" placeholder="选择日期" :picker-options="pickerOptions0">
            </el-date-picker>
          </div>
          <div class="block">
            <el-date-picker v-model="value2" type="date" size="small" placeholder="选择日期" :picker-options="pickerOptions1">
            </el-date-picker>
          </div>
          <el-button size="small" v-on:click="yearquery('now')">今年</el-button>
          <el-button size="small" v-on:click="yearquery('last')">去年</el-button>
          <el-button size="small" v-on:click="yearquery('before')">前年</el-button>

          <el-select v-model="province_id" placeholder="全国" style="width: 100px;">
            <el-option v-for="item in options" :key="item.region_id" :label="item.region_name" :value="item.region_id">
            </el-option>
          </el-select>

          <el-button size="small" class="query" v-on:click="query">查询</el-button>
        </div>
        <div class="table-container">
          <el-table :data="tableData" stripe border style="width: 100%">
            <el-table-column prop="date" label="时间" min-width="120">
            </el-table-column>
            <el-table-column prop="total" label="签约场馆数" min-width="112">
            </el-table-column>
            <el-table-column prop="1" min-width="112" label="健身馆数">
            </el-table-column>
            <el-table-column prop="1_p" min-width="112" label="占比">
            </el-table-column>
            <el-table-column prop="2" min-width="112" label="瑜伽馆数">
            </el-table-column>
            <el-table-column prop="2_p" min-width="112" label="占比">
            </el-table-column>
            <el-table-column prop="3" min-width="112" label="跆拳道馆数">
            </el-table-column>
            <el-table-column prop="3_p" min-width="112" label="占比">
            </el-table-column>
            <el-table-column prop="4" min-width="112" label="武道馆数">
            </el-table-column>
            <el-table-column prop="4_p" min-width="112" label="占比">
            </el-table-column>
            <el-table-column prop="5" min-width="112" label="舞蹈室数">
            </el-table-column>
            <el-table-column prop="5_p" min-width="112" label="占比">
            </el-table-column>
            <el-table-column prop="6" min-width="112" label="其他场馆">
            </el-table-column>
            <el-table-column prop="6_p" min-width="112" label="占比">
            </el-table-column>
          </el-table>
          <div class="pos-rel p-t-20">
            <div class="block pages">
              <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="limit" :current-page="currentPage" :total="pageCount">
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
  .main-wrap {
    height: 100%;
  }
  .main-title {
    height: 40px;
    line-height: 40px;
    color: #333333;
    padding-left: 20px;
    background-color: #e4e4e4;
  }
  .main-title span {
    font-size: 14px;
    color: #333333;
  }
  .main-title i {
    margin: 10px;
  }
  .main-nav {
    padding-top: 20px;
    padding-left: 42px;
  }
  .main-nav span {
    background-color: #ffffff;
    width: 120px;
    height: 36px;
    line-height: 36px;
    border: 1px solid #d7d7d7;
    display: inline-block;
    text-align: center;
    font-size: 12px;
    margin-left: 5px;
    cursor: pointer;
  }
  .main-nav span:first-child {
    border: 1px solid #fa571c;
    font-size: 12px;
    margin-left: 5px;
  }
  .main-container {
    background-color: #ffffff;
    margin-top: 20px;
    margin-left: 46px;
    /*border: 1px solid #FA571C;*/
    border-radius: 15px;
  }
  .main-container-wrap {
    padding-left: 20px;
    padding-top: 30px;
    margin-bottom: 100px;
  }
  .search-box {
    height: 34px;
  }
  .block {
    margin-right: 20px;
    float: left;
  }
  .el-date-editor {
    width: 140px;
  }
  .el-input__inner {
    border-radius: 30px;
  }
  .search-box button {
    height: 30px;
    vertical-align: bottom;
    border-radius: 30px;
    width: 70px;
  }
  .search-box .query {
    width: 140px;
    color: #ffffff;
    font-size: 14px;
    border: 0;
    background-color: #fa571c;
  }
  .table-container {
    width: 100%;
    margin-top: 20px;
    margin-right: 60px;
    padding-bottom: 60px;
  }
</style>
<script>
  import http from 'assets/js/http';
  export default {
    name: 'bus_type',
    data() {
      return {
        options: [
          { region_id: '0', region_name: '全国' },
          { region_id: '2', region_name: '北京' },
          { region_id: '3', region_name: '安徽' },
          { region_id: '4', region_name: '福建' },
          { region_id: '5', region_name: '甘肃' },
          { region_id: '6', region_name: '广东' },
          { region_id: '7', region_name: '广西' },
          { region_id: '8', region_name: '贵州' },
          { region_id: '9', region_name: '海南' },
          { region_id: '10', region_name: '河北' },
          { region_id: '11', region_name: '河南' },
          { region_id: '12', region_name: '黑龙江' },
          { region_id: '13', region_name: '湖北' },
          { region_id: '14', region_name: '湖南' },
          { region_id: '15', region_name: '吉林' },
          { region_id: '16', region_name: '江苏' },
          { region_id: '17', region_name: '江西' },
          { region_id: '18', region_name: '辽宁' },
          { region_id: '19', region_name: '内蒙古' },
          { region_id: '20', region_name: '宁夏' },
          { region_id: '21', region_name: '青海' },
          { region_id: '22', region_name: '山东' },
          { region_id: '23', region_name: '山西' },
          { region_id: '24', region_name: '陕西' },
          { region_id: '25', region_name: '上海' },
          { region_id: '26', region_name: '四川' },
          { region_id: '27', region_name: '天津' },
          { region_id: '28', region_name: '西藏' },
          { region_id: '29', region_name: '新疆' },
          { region_id: '30', region_name: '云南' },
          { region_id: '31', region_name: '浙江' },
          { region_id: '32', region_name: '重庆' },
          { region_id: '33', region_name: '香港' },
          { region_id: '34', region_name: '澳门' },
          { region_id: '35', region_name: '台湾' }
        ],
        province_id: '',
        pickerOptions0: {},
        pickerOptions1: {},
        value1: '',
        value2: '',
        tableData: [],
        pageCount: null,
        currentPage: null,
        begin_time: '',
        end_time: ''
      };
    },
    created() {
      this.init();
    },
    methods: {
      routerChange(message) {
        // 与当前页面路由相等则刷新页面
        if (message == 'brand') {
          router.push('/web/business/bus_brand');
        }
        if (message == 'region') {
          router.push('/web/business/bus_region');
        }
        if (message == 'wx') {
          router.push('/web/business/bus_wx');
        }
      },
      handleCurrentChange(page) {
        router.push({ path: this.$route.path, query: { keywords: this.keywords, page: page } });
      },
      bus_type_statistical(begin_time = '', end_time = '') {
        let postData = {
          page_size: 10,
          page_no: this.currentPage
        };
        if (this.province_id > 0) {
          postData.province_id = this.province_id;
        }
        if (this.begin_time != '') {
          postData.begin_time = this.begin_time;
          postData.end_time = this.end_time;
        }
        this.apiPost('web/businessStatistical/bus_type_statistical', postData).then(res => {
          this.handelResponse(res, data => {
            this.tableData = res.data.list;
            this.pageCount = res.data.pageCount * 10;
          });
        });
      },
      getCurrentPage() {
        let data = this.$route.query;
        if (data) {
          if (data.page) {
            this.currentPage = parseInt(data.page);
          } else {
            this.currentPage = 1;
          }
        }
      },
      query() {
        if (this.value1 == '' || this.value2 == '') {
          _g.toastMsg('normal', '请选择时间');
        }
        _g.openGlobalLoading();
        this.begin_time = Date.parse(new Date(this.value1)) / 1000;
        this.end_time = Date.parse(new Date(this.value2)) / 1000 + 24 * 3600 - 1;
        this.bus_type_statistical();
      },
      yearquery: function(message) {
        _g.openGlobalLoading();
        let time_flag = message;
        var now = new Date();
        if (time_flag == 'now') {
          this.begin_time = Date.parse(now.getFullYear() + '-01-01 0:0:0') / 1000;
          this.end_time = Date.parse(new Date()) / 1000;
        }
        if (time_flag == 'last') {
          this.begin_time = Date.parse(now.getFullYear() - 1 + '-01-01 0:0:0') / 1000;
          this.end_time = Date.parse(now.getFullYear() - 1 + '-12-31 23:59:59') / 1000;
        }
        if (time_flag == 'before') {
          this.begin_time = Date.parse(now.getFullYear() - 2 + '-01-01 0:0:0') / 1000;
          this.end_time = Date.parse(now.getFullYear() - 2 + '-12-31 23:59:59') / 1000;
        }
        this.bus_type_statistical();
      },
      init() {
        this.getCurrentPage();
        this.bus_type_statistical();
      }
    },
    watch: {
      $route(to, from) {
        this.init();
      }
    },
    mixins: [http]
  };
</script>
