<template>
  <div class="main-wrap">
    <!--内容区头部-->
    <div class="index-nav">首页
    </div>
    <div class="main-content-wrap">
      <!--长时间内未使用系统的场馆-->
      <div class="bus-box-wrap">
        <div class="bus-box">
          <div class="bus-title">长时间内未使用系统的场馆</div>
          <div class="nouse-bus">
            <ul>
              <li>
                <div class="bus-data" v-on:click="display('boss')">{{busdata.boss_remind}}</div>
                <div class="from-port" v-on:click="display('boss')">BOSS端</div>
              </li>
              <li>
                <div class="bus-data" v-on:click="display('coach')">{{busdata.coach_remind}}</div>
                <div class="from-port" v-on:click="display('coach')">教练端</div>
              </li>
              <li>
                <div class="bus-data" v-on:click="display('membership')">{{busdata.membership_remind}}</div>
                <div class="from-port" v-on:click="display('membership')">会籍端</div>
              </li>
              <li>
                <div class="bus-data" v-on:click="display('pc')">{{busdata.pc_remind}}</div>
                <div class="from-port" v-on:click="display('pc')">PC端</div>
              </li>
            </ul>
          </div>
        </div>
      </div>
      <div>
        <div class="search-item">
          <el-input v-model="input" placeholder="场馆名称" style="width: 186px;"></el-input>
          <el-select v-model="value" placeholder="场馆类型" style="width: 186px;">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
          <el-button style="width: 128px;" v-on:click="query">查询</el-button>
        </div>
      </div>

      <div class="table-container">
        <el-table :data="tableData" @sort-change="sortchange" stripe border style="width: 100%">
          <el-table-column prop="bus_name" sortable="custom" label="场馆名称">
          </el-table-column>
          <el-table-column prop="bus_type" width="120" label="场馆类型">
          </el-table-column>
          <el-table-column prop="pc_use_time" sortable="custom" label="PC端上次使用">
            <template scope="scope">
              <span v-if="scope.row.pc_remind == 1" style="color: red;">{{ scope.row.pc_use_time }}</span>
              <span v-else>{{ scope.row.pc_use_time }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="membership_use_time" sortable="custom" label="会籍端上次使用">
            <template scope="scope">
              <span v-if="scope.row.membership_remind == 1" style="color: red;">{{ scope.row.membership_use_time }}</span>
              <span v-else>{{ scope.row.membership_use_time }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="coach_use_time" sortable="custom" label="教练端上次使用">
            <template scope="scope">
              <span v-if="scope.row.coach_remind == 1" style="color: red;">{{ scope.row.coach_use_time }}</span>
              <span v-else>{{ scope.row.coach_use_time }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="boss_use_time" sortable="custom" label="BOSS端上次使用">
            <template scope="scope">
              <span v-if="scope.row.boss_remind == 1" style="color: red;">{{ scope.row.boss_use_time }}</span>
              <span v-else>{{ scope.row.boss_use_time }}</span>
            </template>
          </el-table-column>
          <el-table-column prop="phone" width="128" label="联系电话">
          </el-table-column>
        </el-table>
        <div class="pos-rel p-t-20">
          <div class="block pages">
            <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="15" :current-page="currentPage" :total="pageCount">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  export default {
    name: 'index',
    data() {
      return {
        options: [
          /* {
            value: '',
            label: '请选择'
          },
          {
            value: '1',
            label: '健身房'
          },
          {
            value: '2',
            label: '瑜伽馆'
          },
          {
            value: '3',
            label: '跆拳道馆'
          },
          {
            value: '4',
            label: '武道馆'
          },
          {
            value: '5',
            label: '舞蹈馆'
          },
          {
            value: '6',
            label: '其它'
          }, */
          { label: '请选择', value: '' },
          { label: '健身房', value: 1 },
          { label: '健身工作室', value: 9 },
          { label: '体育馆', value: 7 },
          { label: '游泳馆', value: 8 },
          { label: '瑜伽馆', value: 2 },
          { label: '跆拳道馆', value: 3 },
          { label: '武道馆', value: 4 },
          { label: '舞蹈馆', value: 5 },
          { label: '其他', value: 6 },
        ],
        value: '',
        input: '',
        tableData: [],
        pageCount: null,
        currentPage: null,
        bus_name: '',
        bus_type: '',
        sort_field: '',
        sort_type: '',
        display_remind: '',
        busdata: []
      };
    },
    created() {
      this.init();
    },
    methods: {
      use_info() {
        let postData = {
          user_id: Lockr.get('userInfo').id,
          page_no: this.currentPage,
          page_size: 10
        };
        if (this.bus_name != '') {
          postData.bus_name = this.bus_name;
        }
        if (this.bus_type != '') {
          postData.bus_type = this.bus_type;
        }
        if (this.sort_field != '') {
          postData.sort_field = this.sort_field;
        }
        if (this.sort_type != '') {
          postData.sort_type = this.sort_type;
        }
        if (this.display_remind != '') {
          postData.display_remind = this.display_remind;
        }
        this.apiPost('web/homePage/multiterminal_used_status', postData).then(res => {
          this.handelResponse(res, data => {
            this.tableData = res.data.list;
            this.busdata = res.data.remind;
            this.pageCount = res.data.pageCount * 10;
          });
        });
      },
      query() {
        _g.openGlobalLoading();
        this.resetparam();
        this.bus_name = this.input;
        this.bus_type = parseInt(this.value);
        this.use_info();
      },
      display(m) {
        _g.openGlobalLoading();
        this.resetparam();
        this.display_remind = m;
        this.use_info();
      },
      resetparam() {
        this.begin_time = '';
        this.end_time = '';
        this.bus_name = '';
        this.sort_type = '';
        this.sort_field = '';
        this.display_remind = '';
      },
      handleCurrentChange(page) {
        router.push({ path: this.$route.path, query: { keywords: this.keywords, page: page } });
      },
      getCurrentPage() {
        let data = this.$route.query;
        if (data) {
          if (data.page) {
            this.currentPage = parseInt(data.page);
          } else {
            this.currentPage = 1;
          }
        }
      },
      getKeywords() {
        let data = this.$route.query;
        if (data) {
          if (data.bus_name) {
            this.bus_name = data.bus_name;
          } else {
            this.keywords = '';
          }
        }
      },
      sortchange(a) {
        let sortcolumn = {
          bus_name: 'name',
          pc_use_time: 'pc',
          membership_use_time: 'membership',
          coach_use_time: 'coach',
          boss_use_time: 'boss'
        };
        this.sort_type = 'asc';
        if (a.order == 'descending') {
          this.sort_type = 'desc';
        }
        _g.openGlobalLoading();
        this.sort_field = sortcolumn[a.prop];
        this.use_info();
      },
      init() {
        this.getCurrentPage();
        this.use_info();
      }
    },
    watch: {
      $route(to, from) {
        this.init();
      }
    },
    mixins: [http]
  };
</script>

<style scoped>
  .index-nav {
    height: 40px;
    width: 100%;
    line-height: 40px;
    font-size: 14px;
    padding-left: 10px;
    background-color: #e4e4e4;
  }
  .main-content-wrap {
    margin-left: 46px;
  }
  .bus-box {
    margin-top: 56px;
    margin-right: 30px;
    height: 168px;
    border: 1px solid #cccccc;
    background-color: #ffffff;
  }
  .bus-title {
    padding-left: 74px;
    line-height: 168px;
    font-size: 16px;
    float: left;
  }
  .nouse-bus {
    float: left;
    margin-left: 100px;
    margin-top: 42px;
    font-size: 18px;
  }
  .bus-data {
    margin-bottom: 40px;
    color: #ff0000;
    font-size: 20px;
    cursor: pointer;
  }
  .from-port {
    cursor: pointer;
  }
  .nouse-bus li {
    float: right;
    margin-right: 100px;
  }
  .index-search-box button {
    height: 36px;
    line-height: 36px;
    width: 140px;
    margin-left: 10px;
  }
  .search-item {
    float: left;
    margin-top: 10px;
    margin-bottom: 10px;
  }
  .search-input {
    width: 186px;
  }
  .search-select {
    width: 200px;
  }
  .search-item input {
    float: left;
  }
  .table-container {
    margin-top: 12px;
    margin-right: 20px;
  }
</style>
