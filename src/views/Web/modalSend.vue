<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="150px">
      <el-form-item>
        <el-radio-group v-model="postData.add_bus_method" @change="typeChange">
          <el-radio :label="1">全部场馆</el-radio>
          <el-radio :label="2">指定场馆</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item label="场馆" v-if="postData.add_bus_method==2">
        <el-select class="w-400" v-model="postData.remind_bus_ids" multiple filterable placeholder="请输入场馆名称" :loading="loading">
          <el-option v-for="item in busList" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="提醒时间" prop="remind_begin_time" :rules="{ required: true, message: '请选择'}">
        <el-date-picker class="w-400" v-model="dateRange" :picker-options="queryDateOption" @change="dateRangeChange" type="daterange" value-format="yyyy-MM-dd"></el-date-picker>
      </el-form-item>
       <el-form-item label="标题" prop="title" :rules="{ required: true, message: '请输入'}">
          <el-input class="w-400" v-model="postData.title" placeholder="请输入"></el-input>
      </el-form-item>
      <el-form-item label="提醒规则">
        <el-radio-group v-model="postData.remind_rule" :disabled="$route.query.id?true:false">
          <el-radio :label="1">期间仅提醒一次</el-radio>
          <el-radio :label="2">每次登录都提醒</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="内容" prop="content" :rules="{ required: true, message: '请输入发送内容'}">
         <quill-editor ref="myTextEditor" style="maxWidth:792px" v-model="postData.content" :options="editorOption"></quill-editor>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit" :loading="submitLoading">保存</el-button>
        <el-button class="m-l-150" @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import Editor from 'components/form/Editor';
import { quillEditor } from 'vue-quill-editor';
import Quill from 'quill';
const AlignStyle = Quill.import('attributors/style/align');
const BackgroundStyle = Quill.import('attributors/style/background');
const ColorStyle = Quill.import('attributors/style/color');
const DirectionStyle = Quill.import('attributors/style/direction');
const FontStyle = Quill.import('attributors/style/font');
const SizeStyle = Quill.import('attributors/style/size');
Quill.register(AlignStyle, true);
Quill.register(BackgroundStyle, true);
Quill.register(ColorStyle, true);
Quill.register(DirectionStyle, true);
Quill.register(FontStyle, true);
Quill.register(SizeStyle, true);
  export default {
    name: 'modalSend',
    data() {
      return {
        editorOption: {},
        dateRange: [],
        postData: {
          add_bus_method: 1,
          remind_bus_ids: [],
          remind_begin_time: '',
          remind_end_time: '',
          title: '',
          remind_rule: 1,
          content: ''
        },
        queryDateOption: {
          disabledDate(date) {
            return Date.now() - date > 24 * 3600 * 1000
          }
        },
        loading: false,
        submitLoading: false,
        busList: []
      };
    },
    components: { quillEditor },
    methods: {
      dateRangeChange(arr) {
        this.postData.remind_begin_time = arr[0]
        this.postData.remind_end_time = arr[1]
      },
      typeChange(val) {
        if (val == 1) {
          this.postData.bus_list = [];
        }
      },
      onSubmit() {
        if(this.submitLoading) {
          return false
        } else {
          this.submitLoading = true
        }
        this.$refs.form.validate(valid => {
          if (valid) {
            this.$service
              .post(`/web/PopupNotice/${this.$route.query.id?'edit':'add'}`, {
                ...this.postData,
                remind_bus_ids: this.postData.remind_bus_ids.join(',')
              }).then(res => {
                  this.submitLoading = false
                  if (res.data.errorcode == 0) {
                    _g.toastMsg('success', res.data.errormsg);
                    this.$router.back();
                  } else {
                    _g.toastMsg('warning', res.data.errormsg);
                  }
            }).catch(()=>{
              this.submitLoading = false
            });
          }
        });
      },
      getBusList(query) {
        if (query !== '') {
          this.loading = true;
          this.$service
          .post('/web/business/bus_list').then(res => {
            this.loading = false;
            if (res.data.errorcode == 0) {
              this.busList = res.data.data.bus_list;
            } else {
              _g.toastMsg('warning', res.data.errormsg);
            }
          });
        }
      },
      getInfo() {
        this.$service
          .post('/web/PopupNotice/detail', { id: this.$route.query.id}).then(res => {
              if (res.data.errorcode == 0) {
                const info = res.data.data.detail
                this.postData = info
                if(info.remind_bus_ids && info.remind_bus_ids.length) {
                  this.postData.remind_bus_ids = info.remind_bus_ids.split(',').map(function(data){
                      return +data;
                  })
                } else {
                  this.postData.remind_bus_ids = []
                }
                this.dateRange = [info.remind_begin_time, info.remind_end_time]
              } else {
                _g.toastMsg('warning', res.data.errormsg);
              }
          })
      }
    },
    created() {
      this.getBusList();
      if(this.$route.query.id){
        this.getInfo();
      }
    }
  };
</script>

<style scoped>
  .maright-20 {
    margin-right: 20px;
  }

  .inputlen {
    width: 180px;
  }
  .maps {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
    height: 300px;
  }
  .amap-demo {
    height: 300px;
  }
  #map_container {
    width: 100%;
    height: 300px;
  }
  #panel {
    background-color: white;
    max-height: 100%;
    overflow-y: auto;
  }
</style>
