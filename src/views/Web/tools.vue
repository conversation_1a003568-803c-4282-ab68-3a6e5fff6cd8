<template>
  <div class="download-wrap">
    <h4 class="download-h border-top">
      场馆账号解锁
    </h4>
    <div class="download-box">
      <div class="search-box">
        <el-button size="small" @click="unLock" type="primary">解锁</el-button>
      </div>
    </div>
    <el-dialog title="账号解锁" :visible.sync="showDialog" :close-on-click-modal="false" width="35%">
      <el-form :model="dialogData" ref="orgForm" label-width="120px">
        <el-form-item prop="type" label="客户端">
           <el-radio-group v-model="dialogData.type">
              <el-radio :label="0">PC端</el-radio>
              <el-radio :label="1">BOSS端</el-radio>
              <el-radio :label="2">教练端</el-radio>
              <el-radio :label="3">会籍端</el-radio>
              <el-radio :label="4">IVEP密码重置</el-radio>
            </el-radio-group>
        </el-form-item>
        <el-form-item prop="account" label="账号名称" :rules="[{ required: true, message: '请输入待解锁的账号'}]">
          <el-input v-model="dialogData.account" placeholder="请填写" class="w-300"></el-input>
        </el-form-item>
        <el-form-item>
         <el-button type="success" @click="handleSave">提交</el-button>
         <el-button type="info" @click="showDialog = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  export default {
    name: 'download',
    data() {
      return {
        showDialog: false,
        dialogData: {
          type: 0,
          account: '',
        },
      };
    },
    watch: {
      showDialog(val) {
        if(!val) {
          this.dialogData = {
            type: 0,
            account: '',
          }
        }
      }
    },
    created() {

    },
    methods: {
      unLock() {
        this.showDialog = true
      },
      handleSave(){
        this.$service.post('/web/Tools/unLockAccount', this.dialogData).then(res => {
          if (res.data.errorcode == 0) {
            this.showDialog = false
            _g.toastMsg('success', '操作成功');
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
    },
    mixins: [http]
  };
</script>

<style scoped>
  .download-wrap {
    padding: 20px 30px;
    border-radius: 5px;
    border: 1px solid #ccc;
    background: #fff;
  }
  .search-box {
    height: 34px;
    margin-top: 10px;
  }
  .block {
    float: left;
    margin-right: 10px;
  }
  .data-url {
    color: #0f1922;
    border: 1px solid #cccccc;
    padding: 5px 5px;
    line-height: 34px;
  }
  .import-box {
    margin-top: 30px;
    margin-bottom: 10px;
  }
  .border-top {
    margin-top: 15px;
    border-top: 1px dashed #ccc;
    padding-top: 15px;
  }
</style>
