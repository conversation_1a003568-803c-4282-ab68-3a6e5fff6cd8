<template>
  <div class="main-container-wrap">
    <!--内容区头部-->
    <div class="index-nav">
      运营工作 >> 模板消息推送
    </div>
    <div style="margin:20px;background: #fff;padding: 20px;border-radius: 10px;">
      <el-row :gutter="20">
        <el-col :span="3">
          <el-select v-model="postData.time_type" placeholder="请选择时间类型" @change="timeChange" style="width:150px;">
            <el-option v-for="item in options" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="5">
          <el-date-picker v-model="timeRange" type="daterange" @change="changeTimeFn" placeholder="选择日期范围">
          </el-date-picker>
        </el-col>
        <el-col :span="4" style="width:180px;">
          <el-select v-model="postData.template_type" placeholder="标题" @change="changeType" style="width:160px;">
            <el-option v-for="item in templateType" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="3" style="width:170px;">
          <el-select v-model="postData.send_object" placeholder="发送对象" @change="changeObj" style="width:150px;">
            <el-option v-for="item in sendObj" :key="item.value" :label="item.label" :value="item.value">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="3" style="width:170px;">
          <el-select v-model="postData.send_status" placeholder="发送状态" @change="changeStatus" style="width:150px;">
            <el-option label="请选择发送状态" value="">
            </el-option>
            <el-option label="已发送" value="1">
            </el-option>
            <el-option label="待发送" value="0">
            </el-option>
          </el-select>
        </el-col>
        <el-col :span="2">
          <el-button style="background-color:#fa571c;color:#fff;" @click="demand">查询</el-button>
        </el-col>
        <el-col :span="2">
          <el-button style="background-color:#fa571c;color:#fff;" @click="addInfo">添加消息</el-button>
        </el-col>

        <el-dialog :visible.sync="dialogFormVisibles">
          <!--@close="clear"-->
          <div slot="title" align="center" class="dialog-footer">模板类型</div>
          <div style="border:1px solid #e4e4e4;border-radius:8px;width: 72%;margin: auto;padding: 20px 30px;">
            <el-form :label-position="labelPosition" label-width="80px">
              <el-form-item label="标题" required>
                <el-select v-model="temPostData.template_type" placeholder="请选择标题类型" :disabled="islook ? true : false" style="width:300px" @change="templateSelect">
                  <el-option v-for="item in templateType" :key="item.value" :label="item.label" :value="item.value"></el-option>
                </el-select>
                <!-- <el-button type="text">示例</el-button> -->
              </el-form-item>
              <!-- <el-form-item label="标题">
                <div>课程安排提醒</div>
              </el-form-item> -->
              <el-form-item v-if="temPostData.data" :label="item" v-for="(item, index) in items" :key="item.index" prop="name" required>
                <el-input v-model="temPostData.data[item].value" :disabled="islook ? true : false" v-if="item=='副标题'" type="textarea" style="width:300px"></el-input>
                <el-input v-model="temPostData.data[item].value" :disabled="islook ? true : false" v-else-if="item!='副标题'" style="width:300px"></el-input>
              </el-form-item>
            </el-form>
          </div>
          <div style="border: 1px solid #e4e4e4;border-radius:8px;margin-top:20px;margin-left:auto;margin-right:auto;width: 72%;padding: 20px 30px;">
            <el-form :label-position="labelPosition" label-width="80px">
              <el-form-item label="链接跳转">
                <el-input v-model="temPostData.url_jump" :disabled="islook ? true : false" style="width:300px"></el-input>
              </el-form-item>
              <el-form-item label="发送对象" required>
                <el-select v-model="temPostData.send_object" :disabled="islook ? true : false" placeholder="请选择组别权限" style="width:300px">
                  <el-option v-for="item in sendObj" :key="item.value" :label="item.label" :value="item.value">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-form>
          </div>
          <div slot="footer" class="dialog-footer" align="center">
            <el-button @click="addSaveInfo" v-if="!islook" style="width:150px">保存</el-button>
            <el-button type="primary" v-if="saveStatus=='已发送'" @click="saveSure" style="width:150px">确定</el-button>
            <el-button type="primary" v-show="saveStatus=='待发送'" @click="sendButton" style="width:150px">发送</el-button>
            <el-button type="primary" v-show="saveStatus==''" @click="saveSend" style="width:150px">保存并发送</el-button>
          </div>
        </el-dialog>
      </el-row>
      <el-table :data="tableData" style="width: 100%;margin:20px auto;">
        <el-table-column type="index" align="center" label="序号" width="180">
        </el-table-column>
        <el-table-column prop="template_title" align="center" label="标题" width="180">
        </el-table-column>
        <el-table-column prop="send_object" align="center" label="发送对象">
        </el-table-column>
        <el-table-column prop="create_time" align="center" label="创建时间">
        </el-table-column>
        <el-table-column prop="send_time" align="center" label="发送时间">
        </el-table-column>
        <el-table-column prop="send_status" align="center" label="发送状态">

        </el-table-column>
        <el-table-column align="center" label="操作" style="padding-left:0" width="240">
          <template scope="scope">
            <el-button size="small" @click="handleLookSave(scope.$index, scope.row)" v-if="scope.row.send_status=='待发送'">发送</el-button>
            <el-button size="small" @click="handleLook(scope.$index, scope.row)">查看</el-button>
            <el-button size="small" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
          </template>

        </el-table-column>
      </el-table>
      <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange" align="right" :total="count?count:1" layout="prev, pager, next,jumper" :page-size="postData.page_size">
      </el-pagination>
    </div>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  export default {
    name: 'templateInfo',
    data() {
      return {
        postData: {
          page_no: 1, //(int, 当前页, 必填)
          page_size: 6, //(int, 每页显示条数, 必填)
          begin_date: '', //(string, 查询开始时间，格式2017-07，非必填)
          end_date: '', //(string, 查询结束时间, 格式2017-09，非必填)
          time_type: '', //(int, 时间类型查询, 1创建时间, 2发送时间，非必填)
          template_type: '', //(int, 模板类型查询)
          send_object: '', //(int, 发送对象查询, 1名权限, 2组别权限, 3场馆权限, 4所有人，非必填)
          send_status: '' //(int, 发送状态查询, 1已发送，0待发送, 非必填)
        },
        postData2: {
          template_type: 10, //(int, 模板类型查询, 1办理成功提醒,)
          data: '',
          url_jump: '', //(string, 连接跳转, 必填)
          send_object: '', //(int, 发送对象查询, 1名权限, 2组别权限, 3场馆权限, 4所有人，必填)
          send_status: '', //(int, 是否发送，1发送，0待发送)
          user_id: 15 //(int, 用户id, 必填)
        },
        saveStatus: '',
        replaceData: {},
        islook: false,
        postData3: {
          msg_id: 15
        },
        postData4: {
          msg_id: 15
        },
        postDataSave: {
          msg_id: ''
        },
        postDataLook: {
          msg_id: ''
        },
        postDataDel: {
          msg_id: ''
        },
        items: [],
        temPostData: {
          data: '',
          send_object: '',
          send_status: 0,
          template_type: '',
          url_jump: ''
          // user_id: 15
        },
        value: '',
        options: [
          {
            value: '',
            label: '请选择时间类型'
          },
          {
            value: 1,
            label: '创建时间'
          },
          {
            value: 2,
            label: '发送时间'
          }
        ], //时间选择select
        timeRange: '',
        templateType: [
          {
            value: '',
            label: '请选择标题类型'
          },
          {
            value: 1,
            label: '办理成功提醒'
          },
          {
            value: 2,
            label: '产品运行报告'
          },
          {
            value: 3,
            label: '订单进程更新通知'
          },
          {
            value: 4,
            label: '活动结果通知'
          },
          {
            value: 5,
            label: '活动开始提醒'
          },
          {
            value: 6,
            label: '活动组织通知'
          },
          {
            value: 7,
            label: '申请提交成功通知'
          },
          {
            value: 8,
            label: '审核结果通知'
          },
          {
            value: 9,
            label: '业务处理进度通知'
          },
          {
            value: 10,
            label: '服务到期提醒'
          },
          {
            value: 11,
            label: '业务生效通知'
          },
          {
            value: 12,
            label: '意向沟通提醒'
          }
        ],
        sendObj: [
          {
            value: '',
            label: '请选择发送对象'
          },
          {
            value: 1,
            label: '名下权限'
          },
          {
            value: 2,
            label: '组别权限'
          },
          {
            value: 3,
            label: '场馆权限'
          },
          {
            value: 4,
            label: '管理者'
          },
          {
            value: 5,
            label: '所有人'
          }
        ],
        // sendStatus: [
        //   {
        //     value: 1,
        //     label: '已发送'
        //   },
        //   {
        //     value: 2,
        //     label: '待发送'
        //   }
        // ],
        count: 1,
        tableData: [
          {
            template_title: '',
            send_object: '',
            create_time: '',
            send_time: '',
            send_status: '',
            msg_id: ''
          }
        ],
        dialogFormVisibles: false,
        labelPosition: 'left'
        // dialogTemplateType: [
        //   {
        //     template_title: '',
        //     template_type: '',
        //     template_data: ''
        //   }
        // ],
      };
    },
    methods: {
      limit() {
        this.$message({
          message: '没有权限',
          type: 'warning'
        });
      },
      getTemplateInfo() {
        this.apiPost('web/OperationWork/template_message_list', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.tableData = res.data.list;
            this.count = res.data.count;
            //  let timeFormat = []
            for (let i = 0; i < res.data.list.length; i++) {
              res.data.list[i].create_time = this.getDates(res.data.list[i].create_time);
              if (res.data.list[i].send_time) {
                res.data.list[i].send_time = this.getDates(res.data.list[i].send_time);
              } else {
                res.data.list[i].send_time = '-';
              }
            }
          } else {
            this.limit();
          }
        });
        _g.closeGlobalLoading();
      },
      saveSend() {
        //保存发送
        this.temPostData.send_status = 1;
        this.$confirm('是否保存并发送?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.apiPost('web/OperationWork/add_template_message', this.temPostData).then(res => {
              if (res.errorcode == 0) {
                // console.log(res)
                this.$message({
                  showClose: true,
                  message: '发送成功',
                  type: 'success'
                });
              } else {
                this.$message({
                  type: 'error',
                  message: '发送失败!'
                });
                this.dialogFormVisibles = false;
              }
            });
            this.dialogFormVisibles = false;
            this.getTemplateInfo();
          })
          .catch(() => {
            // this.dialogFormVisibles = false
          });
        _g.closeGlobalLoading();
      },
      addInfo() {
        //添加消息
        this.dialogFormVisibles = true;
        this.islook = false;
        this.saveStatus = '';
        this.getTemplateTypeList();
      },
      addTemplateMessage(status) {
        //添加消息
        // this.dialogFormVisibles = false
        this.temPostData.send_status = status;
        this.apiPost('web/OperationWork/add_template_message', this.temPostData).then(res => {
          if (res.errorcode == 0) {
            this.dialogFormVisibles = false;
            this.getTemplateInfo();
            // console.log(res)
          } else {
            this.limit();
          }
        });
        _g.closeGlobalLoading();
      },
      saveSure() {
        this.dialogFormVisibles = false;
      },
      sendButton() {
        this.postDataSave.msg_id = this.postDataSave.msg_id;
        this.$confirm('是否发送，发送后不能撤回', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.apiPost('web/OperationWork/send_template_message', this.postDataSave).then(res => {
              if (res.errorcode == 0) {
                this.$message({
                  type: 'success',
                  message: '发送成功!'
                });
                this.getTemplateInfo();
              } else {
                this.limit();
              }
            });
            this.dialogFormVisibles = false;
            _g.closeGlobalLoading();
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消发送'
            });
            // this.getTemplateInfo()
          });
      },
      addSaveInfo() {
        //保存
        // this.addTemplateMessage(0)
        this.temPostData.send_status = 0;
        this.apiPost('web/OperationWork/add_template_message', this.temPostData).then(res => {
          if (res.errorcode == 0) {
            this.$message({
              showClose: true,
              message: '保存成功',
              type: 'success'
            });
            this.dialogFormVisibles = false;
            this.getTemplateInfo();
            // console.log(res)
          } else {
            this.$message({
              showClose: true,
              message: '保存失败，请规范格式',
              type: 'error'
            });
          }
        });
        _g.closeGlobalLoading();

        // this.getTemplateInfo()
        // this.dialogFormVisibles = false
      },
      getTemplateTypeList(val, exist) {
        this.apiPost('web/OperationWork/get_template_type_list').then(res => {
          if (res.errorcode == 0) {
            if (val == undefined || val == '') {
              val = 1;
            }
            let key_obj = JSON.parse(res.data[val - 1].template_data); //转化为键对象保存起来
            // console.log(Object.keys(key_obj.data))
            this.items = Object.keys(key_obj.data); //转化键对象下面的键为一个数组键
            //  console.log(this.items)
            this.temPostData.data = key_obj.data; //对象
            this.temPostData.url_jump = '';
            this.temPostData.send_object = '';
            if (!exist) {
              this.temPostData.template_type = '';
            }
          } else {
            this.limit();
            this.dialogFormVisibles = false;
          }
        });
        _g.closeGlobalLoading();
      },

      templateSelect(val) {
        //模板切换
        // console.log(val)
        if (!this.islook) {
          this.getTemplateTypeList(val, true);
        }
        // console.log(val)
      },
      // clear(){
      //   this.temPostData.url_jump = ''
      //   this.temPostData.template_type = ''
      //   this.temPostData.send_object = ''
      // },
      demand(value) {
        //查询
        this.getTemplateInfo();
      },
      changeObj(value) {
        //发送对象
        this.postData.send_object = value;
      },
      changeStatus(value) {
        //发送状态
        this.postData.send_status = value;
      },
      changeType(value) {
        //发送类型
        this.postData.template_type = value;
      },
      changeTimeFn(value) {
        //时间改变
        let sTime = value[0];
        let eTime = value[1];
        this.postData.begin_date = _g.formatDate(sTime, 'yyyy-MM-dd');
        this.postData.end_date = _g.formatDate(eTime, 'yyyy-MM-dd');
      },
      timeChange(value) {
        this.postData.time_type = value;
      },
      getDates(tm) {
        //时间格式化
        tm = new Date(tm * 1000);
        let year = tm.getFullYear();
        let month = tm.getMonth() + 1;
        let date = tm.getDate();
        return [year, month, date].join('-');
      },
      handleSizeChange(val) {
        this.postData.page_size = val;
        this.getTemplateInfo();
        // console.log(`每页 ${val} 条`);
      },
      handleCurrentChange(val) {
        this.postData.page_no = val;
        this.getTemplateInfo();
        // console.log(`当前页: ${val}`);
      },
      handleLookSave(index, row) {
        //发送
        this.postDataSave.msg_id = this.tableData[index].msg_id;
        this.$confirm('是否发送，发送后不能撤回', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.apiPost('web/OperationWork/send_template_message', this.postDataSave).then(res => {
              if (res.errorcode == 0) {
                this.$message({
                  type: 'success',
                  message: '发送成功!'
                });
                this.getTemplateInfo();
              } else {
                this.limit();
              }
            });
            _g.closeGlobalLoading();
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消发送'
            });
            // this.getTemplateInfo()
          });
        //  console.log(this.postDataSave.msg_id)

        // console.log(index, row);
      },
      handleLook(index, row) {
        this.postDataLook.msg_id = this.tableData[index].msg_id;
        // console.log(this.postDataLook.msg_id)
        this.dialogFormVisibles = true;
        this.islook = true;
        this.saveStatus = row.send_status;
        this.apiPost('web/OperationWork/get_template_message', this.postDataLook).then(res => {
          if (res.errorcode == 0) {
            let key_obj = Object.keys(JSON.parse(res.data.send_data)); //转化为键
            this.items = key_obj;
            this.temPostData = res.data;
            this.postDataSave.msg_id = res.data.msg_id;
            this.temPostData.data = JSON.parse(res.data.send_data);
          } else {
            this.limit();
            this.dialogFormVisibles = false;
          }
        });
        _g.closeGlobalLoading();
      },
      handleDelete(index, row) {
        this.postDataDel.msg_id = this.tableData[index].msg_id;
        //  console.log(this.postDataLook.msg_id)
        this.$confirm('是否删除，删除后不能撤回', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.apiPost('web/OperationWork/del_template_message', this.postDataDel).then(res => {
              if (res.errorcode == 0) {
                this.$message({
                  type: 'success',
                  message: '删除成功!'
                });
                this.getTemplateInfo();
              } else {
                this.limit();
              }
            });
            _g.closeGlobalLoading();
          })
          .catch(() => {
            this.$message({
              type: 'info',
              message: '已取消'
            });
            // this.getTemplateInfo()
          });
      }
    },
    created() {
      this.getTemplateInfo();
    },
    mixins: [http]
  };
</script>

<style scoped>
  .index-nav {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    padding-left: 10px;
    background-color: #e4e4e4;
  }

  .addWidth {
    width: 300px;
  }
</style>
