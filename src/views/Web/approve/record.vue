<template>
  <div class="container">
    <header>
      <el-date-picker v-model="dateRange" type="daterange" range-separator="至" start-placeholder="开始日期"
        end-placeholder="结束日期" value-format="yyyy-MM-dd" class="w-300" :clearable="false" :disabled-date="disabledDate"
        @change="onDateRangeChange"></el-date-picker>
      <BusSelect class="w-200 m-r-10" v-model="postData.bus_id" placeholder="门店列表" filterable clearable />
      <el-input placeholder="会员手机号" v-model="postData.phone" class="w-200" clearable></el-input>
      <el-input placeholder="操作账号" v-model="postData.admin_name" class="w-200" clearable></el-input>
      <el-select v-model="postData.type" placeholder="操作类型" class="w-200" clearable>
        <el-option v-for="item in operationTypeOptions" :key="item.value" :label="item.label"
          :value="item.value"></el-option>
      </el-select>
      <el-button type="success" @click="search">搜索</el-button>
    </header>

    <el-table :data="tableData" stripe style="width: 100%" v-loading="loading">
      <el-table-column prop="serial_number" label="序号" width="80" align="center"></el-table-column>
      <el-table-column prop="bus_name" label="场馆名称" align="center"></el-table-column>
      <el-table-column prop="phone" label="会员手机号" align="center"></el-table-column>
      <el-table-column prop="user_id" label="会员ID" align="center"></el-table-column>
      <el-table-column prop="admin_name" label="操作人员账号" align="center"></el-table-column>
      <el-table-column prop="type_name" label="操作类型" align="center"></el-table-column>
      <el-table-column prop="create_time" label="时间" align="center"></el-table-column>
      <el-table-column prop="desc" label="描述" align="center">
        <template slot-scope="scope">
          <el-tooltip :content="scope.row.desc" placement="top" :disabled="!scope.row.desc || scope.row.desc.length <= 20">
            <span>{{ scope.row.desc && scope.row.desc.length > 20 ? scope.row.desc.substring(0, 20) + '...' : scope.row.desc }}</span>
          </el-tooltip>
        </template>
      </el-table-column>
    </el-table>

    <footer>
      <el-pagination @current-change="handleCurrentChange" @size-change="handleSizeChange"
        layout="total, sizes, prev, pager, next, jumper" :page-sizes="[10, 20, 50, 100]" :page-size="postData.page_size"
        :current-page="postData.page_no" :total="dataCount"></el-pagination>
    </footer>
  </div>
</template>

<script>
import http from 'assets/js/http'

export default {
  name: 'ApproveRecord',
  components: {
    BusSelect: () => import('src/components/Common/BusSelect.vue')
  },
  mixins: [http],
  data() {
    return {
      dateRange: [],
      postData: {
        begin_time: '',
        end_time: '',
        bus_id: '',
        phone: '',
        admin_name: '',
        type: '',
        page_size: 10,
        page_no: 1
      },
      tableData: [],
      dataCount: 0,
      loading: false,
      operationTypeOptions: [
        { value: 50, label: '人脸采集告知' },
        { value: 51, label: 'SaaS登录协议' }
      ],
    }
  },
  methods: {
    disabledDate(date) {
      const today = new Date()
      const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000)
      return date > today || date < thirtyDaysAgo
    },
    onDateRangeChange(value) {
      if (value && value.length === 2) {
        const startDate = new Date(value[0])
        const endDate = new Date(value[1])
        const diffTime = Math.abs(endDate - startDate)
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))

        if (diffDays > 30) {
          _g.toastMsg('warning', '日期范围不能超过30天')
          this.dateRange = []
        }
      }
    },
    search() {
      this.postData.page_no = 1
      this.getList()
    },
    getList() {
      this.loading = true
      // 拆分日期区间为 start_date 和 end_date
      const [start_date, end_date] = this.dateRange || []
      const params = {
        ...this.postData,
        begin_time: start_date || '',
        end_time: end_date || ''
      }
      this.apiPost('/web/Record/getHandleRecord', params)
        .then(res => {
          if (res.errorcode === 0) {
            this.tableData = res.data.list || []
            this.dataCount = res.data.count || 0
            // Add serial numbers
            this.tableData.forEach((item, index) => {
              item.serial_number = (this.postData.page_no - 1) * this.postData.page_size + index + 1
            })
          } else {
            _g.toastMsg('warning', res.errormsg)
            this.tableData = []
            this.dataCount = 0
          }
        })
        .catch(error => {
          console.error('Failed to fetch approval records:', error)
          _g.toastMsg('error', '获取数据失败')
          this.tableData = []
          this.dataCount = 0
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleCurrentChange(page) {
      this.postData.page_no = page
      this.getList()
    },
    handleSizeChange(size) {
      this.postData.page_size = size
      this.postData.page_no = 1
      this.getList()
    }
  },
  created() {
    // Set default date range to today
    this.dateRange = [
      _g.formatDate(new Date(), 'yyyy-MM-dd'),
      _g.formatDate(new Date(), 'yyyy-MM-dd')
    ]
    this.getList()
  }
}
</script>

<style scoped>
.container {
  background-color: #fff;
}

header {
  background-color: #fff;
  min-height: 80px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border-bottom: 1px solid #ececec;
  flex-wrap: wrap;
  padding: 0 20px;
}

header>.el-input,
header>.el-date-editor,
header>.el-button,
header>.el-select {
  margin-left: 15px;
  max-width: 200px;
}

header>.el-date-editor {
  max-width: 200px;
}

footer {
  background-color: #fff;
  min-height: 70px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 20px;
}

.w-200 {
  width: 200px;
}
</style>