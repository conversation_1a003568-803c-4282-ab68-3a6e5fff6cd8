<template>
  <el-card class="container">
    <header>
      <MerchantSelect v-model="postData.m_id" />
      <AppIdSelect v-model="postData.appid" />
      <el-button type="primary" @click="search">搜索</el-button>
    </header>

    <div class="action-bar">
      <el-button type="primary" @click="handleAddMerchant">新增+</el-button>
    </div>

    <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
      <el-table-column prop="index" label="序号" width="80" align="center"></el-table-column>
      <el-table-column prop="mer_name" label="商家名称" align="center"></el-table-column>
      <el-table-column prop="appid" label="AppID" align="center"></el-table-column>
      <el-table-column prop="remark" label="备注" align="center"></el-table-column>
      <el-table-column prop="operator_name" label="操作人" align="center"></el-table-column>
      <el-table-column prop="create_time" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.create_time | formatDateFilter }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="scope">
          <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <footer>
      <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next, sizes, jumper, ->, total"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
      ></el-pagination>
    </footer>
  </el-card>
</template>

<script>
import { formatDate } from '@/utils'
import MerchantSelect from '@/components/Payment/MerchantSelect.vue'
import AppIdSelect from '@/components/Payment/AppIdSelect.vue'

export default {
  name: 'MerchantList',
  components: {
    MerchantSelect,
    AppIdSelect,
  },
  data() {
    return {
      postData: {
        m_id: '',
        appid: '',
        page_size: 10,
        page_no: 1,
      },
      tableData: [],
      dataCount: 0,
      loading: false,
    }
  },
  methods: {
    search() {
      this.postData.page_no = 1
      this.getList()
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage
      this.getList()
    },
    handleSizeChange(e) {
      this.postData.page_no = 1
      this.postData.page_size = e
      this.getList()
    },
    getList() {
      this.loading = true
      this.$service
        .post('/web/payment/getMerchantPayList', this.postData)
        .then((res) => {
          if (res.data.errorcode == 0) {
            // Add index to each row
            let list = res.data.data.list || []
            list.forEach((item, index) => {
              item.index = (this.postData.page_no - 1) * this.postData.page_size + index + 1
            })
            this.tableData = list
            this.dataCount = res.data.data.count || 0
          } else {
            this.$message.error(res.data.errormsg)
          }
        })
        .catch((error) => {
          console.error(error)
          this.$message.error('获取商家列表失败')
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleAddMerchant() {
      this.$router.push({ name: 'merchantSave' })
    },
    handleDelete(row) {
      this.$confirm(`确定要删除商家 "${row.mer_name}" 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$service
            .post('/web/payment/delMerchantPay', { id: row.id })
            .then((res) => {
              if (res.data.errorcode == 0) {
                this.$message.success('删除成功')
                this.getList()
              } else {
                this.$message.error(res.data.errormsg)
              }
            })
            .catch((error) => {
              console.error(error)
              this.$message.error('删除商家失败')
            })
        })
        .catch(() => {
          // 取消删除操作
        })
    },
  },
  filters: {
    formatDateFilter(value) {
      if (!value) return '-'
      return formatDate(Number(value) * 1000, 'yyyy-MM-dd HH:mm')
    },
  },
  created() {
    this.getList()
  },
}
</script>

<style scoped>
.container {
  padding: 20px;
}

header {
  display: flex;
  margin-bottom: 20px;
}

.w-200 {
  width: 200px;
  margin-right: 10px;
}

.action-bar {
  margin-bottom: 20px;
}

footer {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
