<template>
  <div class="container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ isEdit ? '编辑商家' : '新增商家' }}</span>
      </div>
      <el-form :model="form" :rules="rules" ref="form" label-width="100px" status-icon>
        <el-form-item label="选择商家" prop="m_id">
          <MerchantSelect v-model="form.m_id" customClass="w-300" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input type="textarea" :rows="3" v-model="form.remark" placeholder="请输入备注" class="w-300"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import MerchantSelect from '@/components/Payment/MerchantSelect.vue'

export default {
  name: 'MerchantSave',
  components: {
    MerchantSelect,
  },
  props: {
    id: String,
  },
  data() {
    return {
      isEdit: false,
      form: {
        m_id: '',
        remark: '',
      },
      rules: {
        m_id: [{ required: true, message: '请选择商家', trigger: 'blur' }],
      },
    }
  },
  created() {
    // Check if we're editing an existing merchant
    if (this.id) {
      this.isEdit = true
      this.getMerchantDetail(this.id)
    }
  },
  methods: {
    getMerchantDetail(id) {
      this.$service
        .post('/web/payment/getMerchantPayDetail', { id })
        .then((res) => {
          if (res.data.errorcode == 0) {
            this.form = res.data.data
          } else {
            this.$message.error(res.data.errormsg)
          }
        })
        .catch((error) => {
          console.error(error)
          this.$message.error('获取商家详情失败')
        })
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const url = '/web/payment/addMerchantPay'
          this.$service
            .post(url, this.form)
            .then((res) => {
              if (res.data.errorcode == 0) {
                this.$message.success(this.isEdit ? '编辑成功' : '添加成功')
                this.goBack()
              } else {
                this.$message.error(res.data.errormsg)
              }
            })
            .catch((error) => {
              console.error(error)
              this.$message.error(this.isEdit ? '编辑商家失败' : '添加商家失败')
            })
        }
      })
    },
    goBack() {
      this.$router.push({ name: 'merchantList' })
    },
  },
}
</script>

<style scoped>
.container {
  padding: 20px;
}

.box-card {
  width: 600px;
}

.w-300 {
  width: 300px;
}
</style>
