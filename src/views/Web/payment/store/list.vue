<template>
  <el-card class="container">
    <header>
      <MerchantSelect v-model="postData.m_id" />
      <BusSelect class="w-200 m-r-10" v-model="postData.bus_id" placeholder="门店列表" filterable clearable />
      <AppIdSelect v-model="postData.appid" />
      <el-button type="primary" @click="search">搜索</el-button>
    </header>

    <div class="action-bar">
      <el-button type="primary" @click="handleAddStore">新增+</el-button>
    </div>

    <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
      <el-table-column prop="index" label="序号" width="80" align="center"></el-table-column>
      <el-table-column prop="mer_name" label="勤鸟商家" align="center"></el-table-column>
      <el-table-column prop="bus_name" label="门店名称" align="center"></el-table-column>
      <el-table-column prop="create_time" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.create_time | formatDateFilter }}
        </template>
      </el-table-column>
      <el-table-column prop="appid" label="AppID" align="center"></el-table-column>
      <el-table-column prop="operator_name" label="操作人" align="center"></el-table-column>
      <el-table-column label="操作" align="center" width="120">
        <template slot-scope="scope">
          <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <footer>
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        layout="prev, pager, next, sizes, jumper, ->, total"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount"
        :page-sizes="[10, 20, 50, 100]"
      ></el-pagination>
    </footer>
  </el-card>
</template>

<script>
import { formatDate } from '@/utils'
import MerchantSelect from '@/components/Payment/MerchantSelect.vue'
import AppIdSelect from '@/components/Payment/AppIdSelect.vue'

export default {
  name: 'StoreList',
  components: {
    BusSelect: () => import('src/components/Common/BusSelect.vue'),
    MerchantSelect,
    AppIdSelect,
  },
  data() {
    return {
      postData: {
        m_id: '',
        bus_id: '',
        appid: '',
        page_size: 10,
        page_no: 1,
      },
      tableData: [],
      dataCount: 0,
      loading: false,
    }
  },
  methods: {
    search() {
      this.postData.page_no = 1
      this.getList()
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage
      this.getList()
    },
    handleSizeChange(size) {
      this.postData.page_size = size
      this.postData.page_no = 1
      this.getList()
    },
    getList() {
      this.loading = true
      this.$service
        .post('/web/payment/getPayConfigList', this.postData)
        .then((res) => {
          if (res.data.errorcode == 0) {
            // Add index to each row
            let list = res.data.data.list || []
            list.forEach((item, index) => {
              item.index = (this.postData.page_no - 1) * this.postData.page_size + index + 1
            })
            this.tableData = list
            this.dataCount = res.data.data.count || 0
          } else {
            this.$message.error(res.data.errormsg)
          }
        })
        .catch((error) => {
          console.error(error)
          this.$message.error('获取门店列表失败')
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleAddStore() {
      this.$router.push({ name: 'storeSave' })
    },
    handleDelete(row) {
      this.$confirm(`删除后会影响门店支付功能，确认要删除门店 "${row.bus_name}" 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$service
            .post('/web/payment/delBusPayApply', { id: row.id })
            .then((res) => {
              if (res.data.errorcode == 0) {
                this.$message.success('删除成功')
                this.getList()
              } else {
                this.$message.error(res.data.errormsg)
              }
            })
            .catch((error) => {
              console.error(error)
              this.$message.error('删除门店失败')
            })
        })
        .catch(() => {
          // 取消删除操作
        })
    },
  },
  filters: {
    formatDateFilter(value) {
      if (!value) return '-'
      return formatDate(Number(value) * 1000, 'yyyy-MM-dd HH:mm')
    },
  },
  created() {
    this.getList()
  },
}
</script>

<style scoped>
.container {
  padding: 20px;
}

header {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
}

.w-200 {
  width: 200px;
  margin-right: 10px;
}

.action-bar {
  margin-bottom: 20px;
}

footer {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
