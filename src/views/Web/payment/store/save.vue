<template>
  <div class="container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ isEdit ? '编辑门店' : '新增门店' }}</span>
      </div>
      <el-form :model="form" :rules="rules" ref="form" label-width="120px" status-icon>
        <el-form-item label="选择商户" prop="appid">
          <AppIdSelect v-model="form.appid" customClass="w-300" emitMerchantId @merchant-id-change="form.m_id = $event" />
        </el-form-item>
        <el-form-item label="选择门店" prop="bus_id">
          <BusSelect class="w-300" v-model="form.bus_id" placeholder="门店列表" filterable clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import AppIdSelect from '@/components/Payment/AppIdSelect.vue'

export default {
  name: 'StoreSave',
  components: {
    BusSelect: () => import('src/components/Common/BusSelect.vue'),
    AppIdSelect,
  },
  data() {
    return {
      isEdit: false,
      form: {
        id: '',
        m_id: '',
        appid: '',
        bus_id: '',
      },
      rules: {
        appid: [{ required: true, message: '请选择商户', trigger: 'change' }],
        bus_id: [{ required: true, message: '请选择门店', trigger: 'change' }],
      },
    }
  },
  created() {
    // Check if we're editing an existing store
    if (this.$route.query.id) {
      this.isEdit = true
      this.getStoreDetail(this.$route.query.id)
    }
  },
  methods: {
    getStoreDetail(id) {
      this.$service
        .post('/web/payment/getBusPayDetail', { id })
        .then((res) => {
          if (res.data.errorcode == 0) {
            this.form = res.data.data
          } else {
            this.$message.error(res.data.errormsg)
          }
        })
        .catch((error) => {
          console.error(error)
          this.$message.error('获取门店详情失败')
        })
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const url = '/web/payment/addBusPayApply'
          this.$service
            .post(url, this.form)
            .then((res) => {
              if (res.data.errorcode == 0) {
                this.$message.success(this.isEdit ? '编辑成功' : '添加成功')
                this.goBack()
              } else {
                this.$message.error(res.data.errormsg)
              }
            })
            .catch((error) => {
              console.error(error)
              this.$message.error(this.isEdit ? '编辑门店失败' : '添加门店失败')
            })
        }
      })
    },
    goBack() {
      this.$router.push({ name: 'storeList' })
    },
  },
}
</script>

<style scoped>
.container {
  padding: 20px;
}

.box-card {
  width: 600px;
}

.w-300 {
  width: 300px;
}
</style>
