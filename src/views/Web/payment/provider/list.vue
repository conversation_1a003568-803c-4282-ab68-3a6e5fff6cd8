<template>
  <el-card class="container">
    <header>
      <MerchantSelect v-model="postData.m_id" />
      <el-input placeholder="支付商家名称" v-model="postData.title" class="w-200" clearable></el-input>
      <el-input placeholder="商户号" v-model="postData.cusid" class="w-200" clearable></el-input>
      <el-input placeholder="门店号" v-model="postData.store_sn" class="w-200" clearable></el-input>
      <el-input placeholder="品牌号" v-model="postData.brand_code" class="w-200" clearable></el-input>
      <AppIdSelect v-model="postData.appid" />
      <el-button type="primary" @click="search">搜索</el-button>
    </header>

    <div class="action-bar">
      <el-button type="primary" @click="handleAddProvider">服务商进件+</el-button>
    </div>

    <el-table v-loading="loading" :data="tableData" stripe style="width: 100%">
      <el-table-column prop="index" label="序号" width="80" align="center"></el-table-column>
      <el-table-column prop="mer_name" label="勤鸟商户" align="center"></el-table-column>
      <el-table-column prop="title" label="支付商家名称" align="center"></el-table-column>
      <el-table-column prop="cusid" label="商户号" align="center"></el-table-column>
      <el-table-column prop="store_sn" label="门店号" align="center"></el-table-column>
      <el-table-column prop="brand_code" label="品牌号" align="center">
        <template slot-scope="scope">
          {{ scope.row.brand_code || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="active_code" label="设备激活码" align="center">
        <template slot-scope="scope">
          {{ scope.row.active_code || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="platform_pub_key" label="平台公钥" align="center">
        <template slot-scope="scope">
          {{ scope.row.platform_pub_key || '-' }}
        </template>
      </el-table-column>
      <el-table-column prop="create_time" label="创建时间" align="center">
        <template slot-scope="scope">
          {{ scope.row.create_time | formatDateFilter }}
        </template>
      </el-table-column>
      <el-table-column prop="appid" label="AppId" align="center"></el-table-column>
      <el-table-column prop="operator_name" label="操作人" align="center"></el-table-column>
      <el-table-column prop="service" label="服务商" align="center">
        <template slot-scope="scope">
          {{ scope.row.service | serviceFilter }}
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="180">
        <template slot-scope="scope">
          <el-button :disabled="scope.row.service == 2" type="primary" size="small" @click="handleServiceAction(scope.row)">
            设备激活
          </el-button>
          <el-button type="danger" size="small" @click="handleDelete(scope.row)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <footer>
      <el-pagination
        @current-change="handleCurrentChange"
        @size-change="handleSizeChange"
        layout="prev, pager, next, sizes, jumper, ->, total"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount"
        :page-sizes="[10, 20, 50, 100]"
      ></el-pagination>
    </footer>

    <!-- 设备激活弹窗 -->
    <el-dialog title="设备激活" :visible.sync="activationDialogVisible" width="30%" :close-on-click-modal="false">
      <el-form :model="activationForm" :rules="activationRules" ref="activationFormRef" label-width="100px">
        <el-form-item label="激活码" prop="active_code">
          <el-input v-model="activationForm.active_code" placeholder="请输入设备激活码"></el-input>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelActivation">取 消</el-button>
        <el-button type="primary" @click="submitActivation" :loading="activationLoading">激 活</el-button>
      </span>
    </el-dialog>
  </el-card>
</template>

<script>
import { formatDate } from '@/utils'
import options from './options.ts'
import MerchantSelect from '@/components/Payment/MerchantSelect.vue'
import AppIdSelect from '@/components/Payment/AppIdSelect.vue'

const { serviceOptions } = options

export default {
  name: 'ProviderList',
  components: {
    MerchantSelect,
    AppIdSelect,
  },
  data() {
    return {
      postData: {
        m_id: '',
        title: '',
        cusid: '',
        store_sn: '',
        brand_code: '',
        appid: '',
        page_size: 10,
        page_no: 1,
      },
      tableData: [],
      dataCount: 0,
      loading: false,
      activationDialogVisible: false,
      activationForm: {
        active_code: '',
        id: null,
      },
      activationRules: {
        active_code: [{ required: true, message: '请输入设备激活码', trigger: 'blur' }],
      },
      activationLoading: false,
    }
  },
  methods: {
    search() {
      this.postData.page_no = 1
      this.getList()
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage
      this.getList()
    },
    handleSizeChange(size) {
      this.postData.page_size = size
      this.postData.page_no = 1
      this.getList()
    },
    getList() {
      this.loading = true
      this.$service
        .post('/web/payment/getMerchantPayApplyList', this.postData)
        .then((res) => {
          if (res.data.errorcode === 0) {
            // Add index to each row
            let list = res.data.data.list || []
            list.forEach((item, index) => {
              item.index = (this.postData.page_no - 1) * this.postData.page_size + index + 1
            })
            this.tableData = list
            this.dataCount = res.data.data.count || 0
          } else {
            this.$message.error(res.data.errormsg)
          }
        })
        .catch((error) => {
          console.error(error)
          this.$message.error('获取服务商进件列表失败')
        })
        .finally(() => {
          this.loading = false
        })
    },
    handleAddProvider() {
      this.$router.push({ name: 'providerSave' })
    },
    handleDelete(row) {
      this.$confirm(`确定要删除服务商进件 "${row.title}" 吗?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.$service
            .post('/web/payment/delMerchantPayApply', { id: row.id })
            .then((res) => {
              if (res.data.errorcode === 0) {
                this.$message.success('删除成功')
                this.getList()
              } else {
                this.$message.error(res.data.errormsg)
              }
            })
            .catch((error) => {
              console.error(error)
              this.$message.error('删除服务商进件失败')
            })
        })
        .catch(() => {
          // 取消删除操作
        })
    },
    handleServiceAction(row) {
      this.activationForm.id = row.id
      this.activationForm.active_code = row.active_code || '' // Use the row's active_code if it exists, otherwise use empty string
      this.activationDialogVisible = true
    },
    submitActivation() {
      this.$refs.activationFormRef.validate((valid) => {
        if (!valid) {
          return
        }

        this.activationLoading = true
        this.$service
          .post('/web/payment/activeSqb', {
            id: this.activationForm.id,
            active_code: this.activationForm.active_code,
          })
          .then((res) => {
            if (res.data.errorcode === 0) {
              this.$message.success('激活成功')
              this.activationDialogVisible = false
              this.getList()
            } else {
              this.$message.error(res.data.errormsg)
            }
          })
          .catch((error) => {
            console.error(error)
            this.$message.error('激活失败')
          })
          .finally(() => {
            this.activationLoading = false
          })
      })
    },
    cancelActivation() {
      this.activationDialogVisible = false
    },
  },
  filters: {
    formatDateFilter(value) {
      if (!value) return '-'
      return formatDate(Number(value) * 1000, 'yyyy-MM-dd HH:mm')
    },
    serviceFilter(value) {
      const service = serviceOptions.find((item) => item.value == value)
      return service ? service.label : '-'
    },
  },
  created() {
    this.getList()
  },
}
</script>

<style scoped>
.container {
  padding: 20px;
}

header {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20px;
  gap: 10px;
  min-height: unset !important;
  padding: 20px 0;
}

.w-200 {
  width: 200px;
}

.action-bar {
  margin-bottom: 20px;
}

footer {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
