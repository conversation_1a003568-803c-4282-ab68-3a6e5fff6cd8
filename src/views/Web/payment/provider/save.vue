<template>
  <div class="container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>{{ isEdit ? '编辑服务商进件' : '新增服务商进件' }}</span>
      </div>
      <el-form :model="form" :rules="rules" ref="form" label-width="120px" status-icon>
        <el-form-item label="选择商家" prop="appid">
          <AppIdSelect v-model="form.appid" customClass="w-300" emitMerchantId @merchant-id-change="form.m_id = $event" />
        </el-form-item>
        <el-form-item label="服务商" prop="service">
          <el-radio-group v-model="form.service">
            <el-radio v-for="item in serviceOptions" :key="item.value" :label="item.value">{{ item.label }}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="场馆/商家名称" prop="title">
          <el-input v-model="form.title" placeholder="请输入场馆/商家名称" class="w-300"></el-input>
        </el-form-item>
        <el-form-item label="商户号" prop="cusid">
          <el-input v-model="form.cusid" placeholder="请输入商户号" class="w-300"></el-input>
        </el-form-item>
        <el-form-item label="门店号" prop="store_sn">
          <el-input v-model="form.store_sn" placeholder="请输入门店号" class="w-300"></el-input>
        </el-form-item>
        <transition-group name="fade-slide" tag="div" class="service-fields" mode="out-in">
          <el-form-item v-if="form.service === 1" key="brand_code" label="品牌号" prop="brand_code" class="service-field-item">
            <el-input v-model="form.brand_code" placeholder="请输入品牌号" class="w-300"></el-input>
          </el-form-item>

          <el-form-item v-if="form.service === 1" key="active_code" label="激活码" prop="active_code" class="service-field-item">
            <el-input v-model="form.active_code" placeholder="请输入激活码" class="w-300"></el-input>
          </el-form-item>

          <el-form-item
            v-if="form.service === 2"
            key="platform_pub_key"
            label="平台公钥"
            prop="platform_pub_key"
            class="service-field-item"
          >
            <el-input v-model="form.platform_pub_key" placeholder="请输入平台公钥" class="w-300"></el-input>
          </el-form-item>
        </transition-group>
        <el-form-item>
          <el-button type="primary" @click="submitForm">保存</el-button>
          <el-button @click="goBack">取消</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import options from './options.ts'
import AppIdSelect from '@/components/Payment/AppIdSelect.vue'

const { serviceOptions } = options

export default {
  name: 'ProviderSave',
  components: {
    AppIdSelect,
  },
  data() {
    return {
      isEdit: false,
      form: {
        id: '',
        m_id: '',
        appid: '',
        title: '',
        cusid: '',
        store_sn: '',
        brand_code: '',
        active_code: '',
        service: 1,
        platform_pub_key: '',
      },
      rules: {
        appid: [{ required: true, message: '请选择商家', trigger: 'blur' }],
        title: [{ required: true, message: '请输入场馆/商家名称', trigger: 'blur' }],
        cusid: [{ required: true, message: '请输入商户号', trigger: 'blur' }],
        service: [{ required: true, message: '请选择服务商', trigger: 'blur' }],
        store_sn: [{ required: true, message: '请输入门店号', trigger: 'blur' }],
        brand_code: [{ required: true, message: '请输入品牌号', trigger: 'blur' }],
        platform_pub_key: [{ required: true, message: '请输入平台公钥', trigger: 'blur' }],
      },
      serviceOptions,
    }
  },
  created() {
    // Check if we're editing an existing provider
    if (this.$route.query.id) {
      this.isEdit = true
      this.getProviderDetail(this.$route.query.id)
    }
  },
  methods: {
    getProviderDetail(id) {
      this.$service
        .post('/web/payment/getProviderDetail', { id })
        .then((res) => {
          if (res.data.errorcode == 0) {
            this.form = res.data.data
          } else {
            this.$message.error(res.data.errormsg)
          }
        })
        .catch((error) => {
          console.error(error)
          this.$message.error('获取服务商进件详情失败')
        })
    },
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          const url = '/web/payment/addMerchantPayApply'
          this.$service
            .post(url, this.form)
            .then((res) => {
              if (res.data.errorcode == 0) {
                this.$message.success(this.isEdit ? '编辑成功' : '添加成功')
                this.goBack()
              } else {
                this.$message.error(res.data.errormsg)
              }
            })
            .catch((error) => {
              console.error(error)
              this.$message.error(this.isEdit ? '编辑服务商进件失败' : '添加服务商进件失败')
            })
        }
      })
    },
    goBack() {
      this.$router.push({ name: 'providerList' })
    },
  },
}
</script>

<style scoped>
.container {
  padding: 20px;
}

.box-card {
  width: 600px;
}

.w-300 {
  width: 300px;
}

/* Transition effects */
.service-fields {
  position: relative;
  margin-bottom: 10px;
}

.service-field-item {
  transition: all 0.4s;
}

.fade-slide-enter-active {
  transition: all 0.4s ease-out;
  transition-delay: 0.1s;
}

.fade-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.55, 0, 0.1, 1);
  position: absolute;
  width: 100%;
}

.fade-slide-enter {
  opacity: 0;
  transform: translateY(20px);
}

.fade-slide-leave-to {
  opacity: 0;
  transform: translateY(-20px);
}

.fade-slide-move {
  transition: transform 0.4s;
}
</style>
