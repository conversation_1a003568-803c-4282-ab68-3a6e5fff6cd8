
<style lang="less">
  .business {
    .el-form-item__content {
      display: flex;
    }
  }
</style>

<template>
  <div class="container business">
    <header>
      <el-input v-model="postData.worker" style="width: 190px" placeholder="操作人员" @keydown.native.enter="getList"></el-input>
      <el-select v-model="postData.is_contact" style="width: 190px" placeholder="状态" clearable>
        <el-option value="1" label="未联系"></el-option>
        <el-option value="2" label="已联系"></el-option>
      </el-select>
      <el-date-picker v-model="dateRange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="daterange" @change="dateChange" placeholder="选择日期"></el-date-picker>
      <el-button type="success" @click="getList">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column prop="create_time" align="center" label="时间"></el-table-column>
      <el-table-column prop="phone" align="center" label="电话"></el-table-column>
      <el-table-column prop="is_contact" align="center" label="联系状态">
        <template scope="scope">
          <el-button v-if="scope.row.is_contact === 1" @click="hasContacted(scope.row.id)" type="info">未联系</el-button>
          <el-button v-else @click="" type="success">已联系</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="worker" align="center" label="联系人"></el-table-column>
    </el-table>
    <footer>
      <div class="left">
        <el-button type="info" @click="exportTable" size="small">导出Excel</el-button>
        <Export ref="export" />
      </div>
      <el-pagination layout="total, prev, pager, next, sizes" background @size-change="sizeChange" @current-change="pageChange" :page-size="pageSize" :current-page.sync="page" :page-sizes="[10, 20, 30, 40]" :total="total"></el-pagination>
    </footer>
  </div>
</template>

<script>
  import Export from 'src/components/Export';
  export default {
    name: 'salesPhoneList',
    components: { Export },
    data() {
      return {
        total: 0,
        tableData: [],
        dateRange: [_g.formatDate(new Date(), 'yyyy-MM-dd'), _g.formatDate(new Date(), 'yyyy-MM-dd')],
        postData: {
          begin_time: '',
          end_time: '',
          is_contact: '',
          worker: '',
          page_no: 1,
          page_size: 10
        }
      };
    },
    created() {
      this.getList();
    },
    methods: {
      hasContacted(id) {
        const url = '/web/News/workContact';
        this.$service
          .post(url, { id })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$message.success(res.data.errormsg);
              this.getList();
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      dateChange([begin_time, end_time]) {
        this.postData.begin_time = begin_time;
        this.postData.end_time = end_time;
      },
      pageChange(page) {
        this.postData.page_no = page;
        this.getList();
      },
      sizeChange(e) {
        this.postData.page_no = 1;
        this.postData.page_size = e;
        this.getList();
      },
      getList() {
        const url = '/web/News/getPhoneLists';
        this.$service
          .post(url, this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.total = data.counts;
              this.tableData = data.lists;
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getExportData() {
        const url = '/web/News/getPhoneLists';
        return this.$service
          .post(url, { ...this.postData, ...{ page_no: 1, page_size: this.total } })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              return data.lists.map(item => {
                return {
                  ...item,
                  ...{
                    is_contact: item.is_contact === 1 ? '未联系' : '已联系'
                  }
                };
              });
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      async exportTable() {
        const data = await this.getExportData();
        const columns = [
          { title: '时间', key: 'create_time' },
          { title: '电话', key: 'phone' },
          { title: '联系状态', key: 'is_contact' },
          { title: '联系人', key: 'worker' }
        ];
        this.$refs.export.export({ data, columns, filename: '售价咨询电话' });
      }
    }
  };
</script>
