<template>
  <div class="bi-wrap">
    <div class="bi-header">
      <div class="bi-header-inner">
        <div class="r-header-box">
          <span class="bus-change" @click="busChange">{{bus.name}}
            <i class="el-icon-caret-bottom change-icon"></i>
          </span>
          <span>测试场馆(等级：{{form.customer_level}})</span>
          <span>售前：{{form.seller}}</span>
          <span>售后：{{form.support_man}}</span>
          <el-button @click="modification" type="primary">修改</el-button>
        </div>
      </div>
    </div>
    <div class="bi-body">
      <div class="bi-body-container">

      </div>
      <div class="bi-echart">
        <div id="flowStat" style="height:100%;width:100%;"></div>
      </div>
      <div class="bi-echart">
        <div id="flowStat1" style="height:100%;width:100%;"></div>
      </div>
      <div class="bi-echart">
        <div id="flowStat2" style="height:100%;width:100%;"></div>
      </div>
      <div class="bi-echart">
        <div id="flowStat3" style="height:100%;width:100%;"></div>
      </div>
      <div class="bi-echart">
        <div id="flowStat4" style="height:100%;width:100%;"></div>
      </div>
    </div>
    <el-dialog title="场馆切换" :visible.sync="dialogFormVisible">
      <div style="margin-left:5px;">
        <el-input v-model="busName" placeholder="请输入场馆名称" @change="handleSearch">
        </el-input>
      </div>
      <div class="list-bus">
        <div class="tag" @click="handleSwitchBusTag(bus)" v-for="bus in busList">
          <label class="tag-label">{{bus.name}}</label>
        </div>
      </div>
      <span slot="footer" class="dialog-footer">
        <div class="footitem"></div>
      </span>
    </el-dialog>
    <el-dialog title="修改信息" :visible.sync="busModifyVisible">
      <div class="busModifyBox">
        <el-form :label-position="labelPosition" ref="form" :model="form" :rules="rules" label-width="100px">
          <el-form-item label="场馆名称:">
            <span>{{bus.name}}</span>
          </el-form-item>
          <el-form-item label="场馆等级:" prop="customer_level">
            <el-input v-model="form.customer_level"></el-input>
          </el-form-item>
          <el-form-item label="场馆类型:" prop="bus_type">
            <el-select v-model="form.bus_type" style="width: 186px;">
              <el-option v-for="item in busType" :key="item.value" :label="item.label" :value="item.value">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="绑定勤鸟:" prop="follow_us">
            <el-radio-group v-model="form.follow_us">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-form-item>
          <el-form-item label="售前:" prop="seller">
            <el-input v-model="form.seller"></el-input>
          </el-form-item>
          <el-form-item label="售后:" prop="support_man">
            <el-input v-model="form.support_man"></el-input>
          </el-form-item>
        </el-form>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="busModifyVisible = false">取 消</el-button>
        <el-button type="primary" @click="busModify('form')">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<style scoped>
  .footitem {
    width: 100%;
    height: 30px;
  }
  .busModifyBox {
    width: 500px;
    margin: 0 auto;
  }
  .bi-header {
    height: 60px;
    width: 100%;
    border-bottom: 1px solid #c1c1c1;
  }
  .r-header-box {
    float: right;
    height: 60px;
    line-height: 60px;
    margin-right: 20px;
  }
  .bi-header-inner span {
    margin-right: 10px;
  }
  .bus-change {
    cursor: pointer;
    margin-right: 20px;
  }
  .change-icon {
    margin-left: 5px;
  }
  .bi-echart {
    width: 100%;
    height: 400px;
    background-color: #ffffff;
    margin-bottom: 40px;
  }
  .bi-body {
    margin: 40px;
  }
  .list-bus {
    display: flex;
    flex-direction: row;
    flex-wrap: wrap;
    max-height: 333px;
    overflow: auto;
  }
  .tag {
    background: #fff;
    border-radius: 4px;
    font-size: 16px;
    position: relative;
    transition: all 0.2s ease-in-out;
    transform: translate3d(0, -2px, 0);
    border: 1px solid #e9eaec;
    cursor: pointer;
    padding: 5px 10px;
    margin: 5px;
    width: 275px;
  }
  .tag:hover {
    box-shadow: -1px 5px 25px -5px rgba(0, 0, 0, 0.8);
    border-color: #eee;
    background-color: #52a4ea;
    color: #fff;
  }
  .tag-label {
    cursor: pointer;
  }
</style>
<script>
  import http from 'assets/js/http';
  import echarts from 'echarts';
  export default {
    name: 'biReports',
    data() {
      return {
        busType: [
          /* {
            value: '',
            label: '请选择'
          },
          {
            value: 1,
            label: '健身房'
          },
          {
            value: 2,
            label: '瑜伽馆'
          },
          {
            value: 3,
            label: '跆拳道馆'
          },
          {
            value: 4,
            label: '武道馆'
          },
          {
            value: 5,
            label: '舞蹈馆'
          },
          {
            value: 6,
            label: '其它'
          } */
          { label: '请选择', value: '' },
          { label: '健身房', value: 1 },
          { label: '健身工作室', value: 9 },
          { label: '体育馆', value: 7 },
          { label: '游泳馆', value: 8 },
          { label: '瑜伽馆', value: 2 },
          { label: '跆拳道馆', value: 3 },
          { label: '武道馆', value: 4 },
          { label: '舞蹈馆', value: 5 },
          { label: '其他', value: 6 },
        ],
        labelPosition: 'right',
        form: {
          seller: '',
          support_man: '',
          bus_type: '',
          customer_level: '',
          follow_us: 1
        },
        rules: {
          name: [{ required: true, message: '请输入场馆名' }],
          bus_type: [{ required: true, message: '请选择场馆类型' }],
          use_status: [{ required: true, message: '请勾选是否使用' }],
          follow_us: [{ required: true, message: '请选择' }]
        },
        bus: '',
        busInfo: '',
        busName: '',
        busList: [],
        busListStore: [],
        dialogFormVisible: false,
        busModifyVisible: false,
        option: {
          title: {
            text: '课程预约数'
          },
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['课程预约数']
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          toolbox: {
            feature: {
              saveAsImage: {}
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: []
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '课程预约数',
              type: 'line',
              stack: '总量',
              itemStyle: {
                normal: {
                  color: '#C23531',
                  lineStyle: {
                    color: '#C23531'
                  }
                }
              },
              data: []
            }
          ]
        },
        option1: {
          title: {
            text: '有效会员数'
          },
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['有效会员数']
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          toolbox: {
            feature: {
              saveAsImage: {}
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: []
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '有效会员数',
              type: 'line',
              stack: '总量',
              itemStyle: {
                normal: {
                  color: '#2F4554',
                  lineStyle: {
                    color: '#2F4554'
                  }
                }
              },
              data: [220, 182, 191, 234, 290, 330, 310]
            }
          ]
        },
        option2: {
          title: {
            text: '签到数'
          },
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['签到数']
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          toolbox: {
            feature: {
              saveAsImage: {}
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: []
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '签到数',
              type: 'line',
              stack: '总量',
              itemStyle: {
                normal: {
                  color: '#61A0A8',
                  lineStyle: {
                    color: '#61A0A8'
                  }
                }
              },
              data: [150, 232, 201, 154, 190, 330, 410]
            }
          ]
        },
        option3: {
          title: {
            text: '微信绑定数'
          },
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['微信绑定数']
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          toolbox: {
            feature: {
              saveAsImage: {}
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: []
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '微信绑定数',
              type: 'line',
              stack: '总量',
              itemStyle: {
                normal: {
                  color: '#9A32CD',
                  lineStyle: {
                    color: '#9A32CD'
                  }
                }
              },
              data: [320, 332, 301, 334, 390, 330, 320]
            }
          ]
        },
        option4: {
          title: {
            text: '微信绑定率'
          },
          tooltip: {
            trigger: 'axis'
          },
          legend: {
            data: ['微信绑定率']
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          toolbox: {
            feature: {
              saveAsImage: {}
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: []
          },
          yAxis: {
            type: 'value'
          },
          series: [
            {
              name: '微信绑定率',
              type: 'line',
              stack: '总量',
              itemStyle: {
                normal: {
                  color: '#91C7AE',
                  lineStyle: {
                    color: '#91C7AE'
                  }
                }
              },
              data: []
            }
          ]
        }
      };
    },
    created() {
      _g.closeGlobalLoading();
    },
    mounted() {
      this.init();
    },
    methods: {
      refresh() {
        window.location.reload();
      },
      init() {
        this.getAllBus();
      },
      busChange() {
        this.dialogFormVisible = true;
        // this.option.series[0].data = [0, 0, 0, 0, 0, 0, 0]
      },
      getAllBus() {
        this.apiPost('/web/Statistical/get_all_bus_list').then(res => {
          this.handelResponse(res, data => {
            this.busList = res.data;
            this.busListStore = res.data;
            if (this.$route.query.id != '' && !isNaN(this.$route.query.id)) {
              for (var i in res.data) {
                if (this.$route.query.id == res.data[i].id) {
                  this.bus = res.data[i];
                }
              }
            } else {
              this.bus = res.data[0];
            }
            this.getBusData();
          });
        });
      },
      getBusData() {
        let postData = {
          bus_id: this.bus.id,
          number: 30
        };
        this.apiPost('/web/statistical/get_statistics_log', postData).then(res => {
          this.handelResponse(res, data => {
            this.form.seller = res.data.bus_info.seller;
            this.form.support_man = res.data.bus_info.support_man;
            this.form.customer_level = res.data.bus_info.customer_level;
            this.form.bus_type = res.data.bus_info.bus_type;
            this.form.follow_us = res.data.bus_info.follow_us;
            this.option.xAxis.data = res.data.day;
            this.option1.xAxis.data = res.data.day;
            this.option2.xAxis.data = res.data.day;
            this.option3.xAxis.data = res.data.day;
            this.option4.xAxis.data = res.data.day;
            const myChart = echarts.init(document.querySelector('#flowStat'));
            const myChart1 = echarts.init(document.querySelector('#flowStat1'));
            const myChart2 = echarts.init(document.querySelector('#flowStat2'));
            const myChart3 = echarts.init(document.querySelector('#flowStat3'));
            const myChart4 = echarts.init(document.querySelector('#flowStat4'));
            this.option.series[0].data = res.data.class_mark_number;
            this.option1.series[0].data = res.data.effective_user_number;
            this.option2.series[0].data = res.data.sign_number;
            this.option3.series[0].data = res.data.wx_bind_number;
            this.option4.series[0].data = res.data.wx_bind_percent;
            myChart.setOption(this.option);
            myChart1.setOption(this.option1);
            myChart2.setOption(this.option2);
            myChart3.setOption(this.option3);
            myChart4.setOption(this.option4);
          });
        });
      },
      busModify(form) {
        this.$refs[form].validate(pass => {
          this.form.bus_id = this.bus.id;
          this.apiPost('/web/Statistical/update_bus_info', this.form).then(res => {
            this.handelResponse(res, data => {
              _g.toastMsg('success', '修改成功');
              _g.clearVuex('setUsers');
              this.busModifyVisible = false;
            });
          });
        });
      },
      modification() {
        this.busModifyVisible = true;
      },
      handleSearch() {
        console.log(this.busName);
        if (this.busListStore) {
          this.busList = this.busListStore.filter(item => item.name.indexOf(this.busName) !== -1);
        } else {
          this.busList = [];
        }
      },
      handleSwitchBusTag(bus) {
        this.bus = bus;
        router.push({ path: this.$route.path, query: { id: bus.id } });
      },
      querySearch(queryString, cb) {
        var restaurants = this.restaurants;
        var results = queryString ? restaurants.filter(this.createFilter(queryString)) : restaurants;
        // 调用 callback 返回建议列表的数据
        cb(results);
      },
      handleSelect(item) {
        console.log(item);
      }
    },
    watch: {
      $route(to, from) {
        this.refresh();
      }
    },
    mixins: [http]
  };
</script>
