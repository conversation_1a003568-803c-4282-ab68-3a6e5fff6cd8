
<style lang="less">
  .business {
    .el-form-item__content {
      display: flex;
    }
  }
  .category-dialog {
    height: 60%;
    .el-dialog__body {
      overflow-y: auto;
      padding-bottom: 20px;
      height: calc(100% - 54px);
      box-sizing: border-box;
    }
    .el-tag {
      margin-right: 15px;
      margin-bottom: 15px;
    }
    .button-new-tag {
      // margin-left: 10px;
      height: 32px;
      line-height: 30px;
      padding-top: 0;
      padding-bottom: 0;
    }
  }
</style>
<template>
  <div class="container business">
    <!-- <header>
      <el-input style="width: 200px" v-model="name" placeholder="新闻标题" @keydown.enter.native="getList"></el-input>
      <el-button type="success" @click="getList">搜索</el-button>
    </header> -->
    <el-table :data="tableData" stripe>
      <!-- <el-table-column type="selection" align="center" style="width:100%" width="50"></el-table-column> -->
      <el-table-column prop="title" align="center" label="新闻标题"></el-table-column>
      <el-table-column prop="details" align="center" label="内容预览" width="600px">
        <template scope="scope">
          <div style="overflow: hidden; height: 110px"><p v-html="scope.row.details"></p></div>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="status" align="center" label="状态">
        <template scope="scope">
          <el-button size="small" v-if="scope.row.status == 1" @click="handleToggle(0, scope.row.id)" type="success">显示</el-button>
          <el-button size="small" v-else @click="handleToggle(1, scope.row.id)" type="info">隐藏</el-button>
        </template>
      </el-table-column> -->
      <el-table-column prop="create_time" align="center" label="创建时间"></el-table-column>
      <el-table-column prop="id" align="center" label="操作">
        <template scope="scope">
          <el-button size="small" @click="toDetail(scope.row.id)" type="primary">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteNews(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
        <el-button type="success" @click="toDetail('')" size="small" icon="el-icon-plus">
          添加新闻
        </el-button>
        <el-button type="primary" @click="showCategory = true" size="small" icon="el-icon-price-tag">
          查看分类
        </el-button>
        <!-- <el-dropdown size="small" @command="handleDropdown" placement="top" type="info">
          <el-button type="info" size="small" style="margin-left: 20px">
            其他操作
            <i class="el-icon-arrow-down el-icon--right"></i>
          </el-button>
          <el-dropdown-menu slot="dropdown">
            <el-dropdown-item command="0">
              批量删除
            </el-dropdown-item>
          </el-dropdown-menu>
        </el-dropdown> -->
      </div>
      <el-pagination layout="total, prev, pager, next, sizes" background @size-change="sizeChange" @current-change="pageChange" :page-size="pageSize" :current-page.sync="page" :page-sizes="[10, 20, 30, 40]" :total="total"></el-pagination>
    </footer>

    <el-dialog
      custom-class="category-dialog"
      title="新闻分类"
      width="40%"
      v-loading="loading"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      :visible.sync="showCategory"
      :before-close="() => { showCategory = false }"
    >
      <div style="margin-bottom: 20px;">
        <el-button class="button-new-tag" size="small" @click="handleShowInput()">添加分类</el-button>
      </div>
      <el-tag
        v-for="(tag, index) in categoryList"
        :key="tag.id"
        :closable="categoryList.length > 3"
        :disable-transitions="false"
        @click="handleShowInput(tag, index)"
        @close="handleDeleteTag(tag, index)">
        {{ tag.name }}
      </el-tag>
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="showCategory = false">关闭</el-button>
      </span> -->
    </el-dialog>
  </div>
</template>
  <script>
  import Editor from 'components/form/Editor';

  export default {
    name: 'NewsPublish',
    components: {
      Editor
    },
    data() {
      return {
        uploadUrl: _g.getRbBaseUrl() + '/Admin/Public/upload',
        fileList: [],

        form: {
          phone: ''
        },
        page: 1,
        pageSize: 10,
        total: 0,
        tableData: [],
        showCategory: false,
        categoryList: [],
        loading: true,
      };
    },
    created() {
      this.getList();
      this.getCategoryList();
    },
    methods: {
      pageChange(page) {
        this.page = page;
        this.getList();
      },
      async clickDetail(id) {
        const item = this.tableData.find(item => item.id === id);
        this.district_id = item.district_id;
        this.detail = { ...item };
        this.getRegion();
        this.getRegion(2);
        this.getRegion(3);
        this.showDetail = true;
      },
      toDetail(id = '') {
        this.$router.push({ path: '/web/newsEdit', query: { id }});
      },
      deleteNews(id) {
        const url = '/web/News/delNews';
        this.$service
          .post(url, { id })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$message.success(res.data.errormsg);
              this.getList();
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      sizeChange(e) {
        this.page = 1;
        this.pageSize = e;
        this.getList();
      },
      getList() {
        const url = '/web/News/getNewsLists';
        this.$service
          .post(url, { page_no: this.page, page_size: this.pageSize })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.total = data.count;
              this.tableData = data.list;
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      getCategoryList() {
        this.$service.get('/web/NewsCategory/getLists').then(res => {
          if (res.data.errorcode === 0) {
            this.categoryList = res.data.data.reverse();
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
      },
      addCategory(name) {
        this.loading = true;
        this.$service.post('/web/NewsCategory/addCategory', { name }).then(res => {
          if (res.data.errorcode === 0) {
            this.$message.success('添加成功')
            this.getCategoryList()
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        })
        .finally(() => { this.loading = false; })
      },
      deleteCategory(id, index) {
        this.loading = true;
        this.$service.post('/web/NewsCategory/delCategory', { id }).then(res => {
          if (res.data.errorcode === 0) {
            // this.getCategoryList()
            this.categoryList.splice(index, 1);
            this.$message.success('删除成功')
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        })
        .finally(() => { this.loading = false; })
      },
      putCategory(id, name, index) {
        this.loading = true;
        this.$service.post('/web/NewsCategory/editCategory', { id, name }).then(res => {
          if (res.data.errorcode === 0) {
            this.$message.success('修改成功')
            // this.getCategoryList()
            this.$set(this.categoryList[index], 'name', name)
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        })
        .finally(() => { this.loading = false; })
      },

      handleDeleteTag(tag, index) {
        this.$confirm('此操作将删除该分类标签, 是否确定?', '删除分类', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
        tag.id && this.deleteCategory(tag.id, index)
        }).catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '已取消删除'
          // });
        });
      },
      handleShowInput(tag, index) {
        this.$prompt('请输入分类名称', tag && tag.id ? '修改' : '添加', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          inputValidator: value => !!value.trim(),
          inputErrorMessage: '名称不能为空',
          inputPlaceholder: '请输入'
        })
        .then(({ value }) => {
          const hasName = this.categoryList.some(v => v.name === value)
          if (hasName) return this.$message.error('分类名称已存在');

          tag && tag.id ?
            this.putCategory(tag.id, value, index) :
            this.addCategory(value)
        })
        .catch(() => {
          // this.$message({
          //   type: 'info',
          //   message: '取消输入'
          // });
        });
      }
    }
  };
</script>
