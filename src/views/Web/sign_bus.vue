<template>
	<div class="wrap">
		<div class="m-b-20 ovf-hd">
			<div class="fl m-l-30">
				<el-input @keyup.enter.native="search" placeholder="场馆名称" v-model="postData.key_word" class="w-150 m-r-10"></el-input>
				<el-select @change="search" clearable v-model="postData.sign_status_query" placeholder="签约状态" class="w-150 m-r-10">
					<el-option label="已签约" value=1></el-option>
					<el-option label="未签约" value=0></el-option>
				</el-select>
				<el-button type="primary" icon="search" @click="search">搜索</el-button>
			</div>
		</div>
		<el-table :data="tableData" border style="width: 100%">
			<el-table-column prop="bus_name" min-width="180" label="场馆名称">
			</el-table-column>
			<el-table-column label="类型" prop="bus_type" min-width="100">
			</el-table-column>
			<el-table-column label="品牌" prop="brand" min-width="100">
			</el-table-column>
			<el-table-column label="使用状态" prop="use_status" min-width="100">
			</el-table-column>
			<el-table-column label="绑定" prop="follow_us" min-width="100">
			</el-table-column>
			<el-table-column label="版本" prop="charge_version" min-width="140">
			</el-table-column>
			<el-table-column label="统一社会信用码" prop="social_credit_code" min-width="160">
			</el-table-column>
			<el-table-column label="签约状态" prop="sign_status" min-width="100">
			</el-table-column>
			<el-table-column label="签约时间" prop="sign_time" min-width="180">
			</el-table-column>
			<el-table-column width="100" label="操作">
				<template scope="scope">
					<div>
						<span>
							<router-link :to="{ name: 'busEdit', params: { id: scope.row.id }}" class="btn-link edit-btn">
								编辑
							</router-link>
						</span>
					</div>
				</template>
			</el-table-column>
		</el-table>
		<div class="pos-rel p-t-20">
			<div class="block pages">
				<el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="limit" :current-page="postData.page_no" :total="pageCount">
				</el-pagination>
			</div>
		</div>
	</div>
</template>

<script>
	import http from 'assets/js/http';

	export default {
	  name: 'sign_bus',
	  data() {
	    return {
	      tableData: [],
	      pageCount: null,
	      currentPage: 1,
	      limit: 10,
	      postData: {
	        key_word: '',
	        sign_status_query: '',
	        page_no: 1
	      }
	    };
	  },
	  methods: {
	    search() {
	      // router.push({ path: this.$route.path, query: { keywords: this.keywords, page: 1 }})
	      _g.openGlobalLoading();
	      this.postData.page_no = 1;
	      this.getBus();
	    },
	    handleCurrentChange(page) {
	      router.push({ path: this.$route.path, query: { page: page } });
	    },
	    confirmDelete(item) {
	      this.$confirm('确认删除该用户?', '提示', {
	        confirmButtonText: '确定',
	        cancelButtonText: '取消',
	        type: 'warning'
	      })
	        .then(() => {
	          _g.openGlobalLoading();
	          this.apiDelete('admin/users/', item.id).then(res => {
	            _g.closeGlobalLoading();
	            this.handelResponse(res, data => {
	              _g.toastMsg('success', '删除成功');
	              setTimeout(() => {
	                _g.shallowRefresh(this.$route.name);
	              }, 1500);
	            });
	          });
	        })
	        .catch(() => {
	          // catch error
	        });
	    },
	    getBus() {
	      this.apiPost('web/maintenance/sign_bus', this.postData).then(res => {
	        this.handelResponse(res, data => {
	          this.tableData = res.data.list;
	          this.pageCount = res.data.count;
	        });
	      });
	    },
	    search() {
	      this.postData.page_no = 1;
	      this.getBus();
	    },
	    getCurrentPage() {
	      let data = this.$route.query;
	      // console.log(data)
	      if (data) {
	        if (data.page) {
	          this.postData.page_no = parseInt(data.page);
	        } else {
	          this.postData.page_no = 1;
	        }
	      }
	    },
	    init() {
	      this.getCurrentPage();
	      this.getBus();
	    }
	  },
	  created() {
	    this.init();
	  },
	  watch: {
	    $route(to, from) {
	      this.init();
	    }
	  },
	  mixins: [http]
	};
</script>
<style scoped>
	.wrap {
	  padding: 20px;
	}
</style>