<template>
  <div class="main-wrap">
    <div class="main-title">
      <span>场馆统计</span>
      <i>>></i>
      <span>场馆占比</span>
    </div>
    <div class="main-nav">
      <span @click="routerChange('type')">场馆类型</span>
      <span @click="routerChange('brand')">场馆品牌</span>
      <span @click="routerChange('region')">场馆地区</span>
      <span>微信绑定</span>
    </div>
    <!--主体结构-->
    <div class="main-container">
      <!--搜索框-->
      <div class="main-container-wrap">
        <div class="search-box">
          <div class="block">
            <el-date-picker v-model="value1" type="date" size="small" placeholder="选择日期" :picker-options="pickerOptions0">
            </el-date-picker>
          </div>
          <div class="block">
            <el-date-picker v-model="value2" type="date" size="small" placeholder="选择日期" :picker-options="pickerOptions1">
            </el-date-picker>
          </div>
          <el-button size="small" v-on:click="yearquery('now')">今年</el-button>
          <el-button size="small" v-on:click="yearquery('last')">去年</el-button>
          <el-button size="small" v-on:click="yearquery('before')">前年</el-button>
          <el-button size="small" class="query" v-on:click="query">查询</el-button>
        </div>
        <div class="table-container">
          <el-table :data="tableData" stripe border style="width: 100%">
            <el-table-column prop="date" label="时间">
            </el-table-column>
            <el-table-column prop="total" label="签约场馆数">
            </el-table-column>
            <el-table-column prop="bind_qn" label="绑定勤鸟">
            </el-table-column>
            <el-table-column prop="bind_qn_p" label="占比">
            </el-table-column>
            <el-table-column prop="bind_self" label="绑定自己">
            </el-table-column>
            <el-table-column prop="bind_self_p" label="占比">
            </el-table-column>
            <el-table-column prop="not_bind" label="不绑定">
            </el-table-column>
            <el-table-column prop="not_bind_p" label="占比">
            </el-table-column>
          </el-table>
          <div class="pos-rel p-t-20">
            <div class="block pages">
              <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :current-page="currentPage" :total="pageCount">
              </el-pagination>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
<style scoped>
  .main-title {
    height: 40px;
    line-height: 40px;
    color: #333333;
    padding-left: 20px;
    background-color: #e4e4e4;
  }
  .main-title span {
    font-size: 14px;
    color: #333333;
  }
  .main-title i {
    margin: 10px;
  }
  .main-nav {
    padding-top: 20px;
    padding-left: 42px;
  }
  .main-nav span {
    background-color: #ffffff;
    width: 120px;
    height: 36px;
    line-height: 36px;
    border: 1px solid #d7d7d7;
    display: inline-block;
    text-align: center;
    font-size: 12px;
    margin-left: 5px;
    cursor: pointer;
  }
  .main-nav span:nth-child(4) {
    border: 1px solid #fa571c;
    font-size: 12px;
    margin-left: 5px;
  }
  .main-container {
    background-color: #ffffff;
    margin-top: 20px;
    margin-left: 46px;
    margin-right: 40px;
    border-radius: 15px;
  }
  .main-container-wrap {
    padding-left: 20px;
    padding-top: 30px;
    margin-bottom: 100px;
  }
  .search-box {
    height: 34px;
  }
  .block {
    margin-right: 20px;
    float: left;
  }
  .el-date-editor {
    width: 140px;
  }
  .el-input__inner {
    border-radius: 30px;
  }
  .search-box button {
    height: 30px;
    vertical-align: bottom;
    border-radius: 30px;
    width: 70px;
  }
  .search-box .query {
    width: 140px;
    color: #ffffff;
    font-size: 14px;
    border: 0;
    background-color: #fa571c;
  }
  .table-container {
    margin-top: 20px;
    margin-right: 30px;
    padding-bottom: 60px;
  }
</style>
<script>
  import http from 'assets/js/http';
  export default {
    name: 'bus_wx',
    data() {
      return {
        pickerOptions0: {},
        pickerOptions1: {},
        value1: '',
        value2: '',
        tableData: [],
        pageCount: null,
        loading: false,
        currentPage: null,
        begin_time: '',
        end_time: ''
      };
    },
    created() {
      this.init();
    },
    methods: {
      routerChange(message) {
        // 与当前页面路由相等则刷新页面
        if (message == 'type') {
          router.push('/web/business/bus_type');
        }
        if (message == 'brand') {
          router.push('/web/business/bus_brand');
        }
        if (message == 'region') {
          router.push('/web/business/bus_region');
        }
      },
      handleCurrentChange(page) {
        router.push({ path: this.$route.path, query: { keywords: this.keywords, page: page } });
      },
      bus_wx_statistical() {
        let postData = {
          page_no: this.currentPage
        };
        if (this.begin_time != '') {
          postData.begin_time = this.begin_time;
          postData.end_time = this.end_time;
        }
        this.apiPost('web/businessStatistical/bus_wechat_bind_statistical', postData).then(res => {
          this.handelResponse(res, data => {
            this.tableData = res.data.list;
            this.pageCount = res.data.pageCount * 10;
          });
        });
      },
      getCurrentPage() {
        let data = this.$route.query;
        if (data) {
          if (data.page) {
            this.currentPage = parseInt(data.page);
          } else {
            this.currentPage = 1;
          }
        }
      },
      query() {
        if (this.value1 == '' || this.value2 == '') {
          _g.toastMsg('normal', '请选择时间');
          return;
        }
        _g.openGlobalLoading();
        this.begin_time = Date.parse(new Date(this.value1)) / 1000;
        this.end_time = Date.parse(new Date(this.value2)) / 1000 + 24 * 3600 - 1;
        this.bus_wx_statistical();
      },
      yearquery: function(message) {
        _g.openGlobalLoading();
        let time_flag = message;
        var now = new Date();
        if (time_flag == 'now') {
          this.begin_time = Date.parse(now.getFullYear() + '-01-01 0:0:0') / 1000;
          this.end_time = Date.parse(new Date()) / 1000;
        }
        if (time_flag == 'last') {
          this.begin_time = Date.parse(now.getFullYear() - 1 + '-01-01 0:0:0') / 1000;
          this.end_time = Date.parse(now.getFullYear() - 1 + '-12-31 23:59:59') / 1000;
        }
        if (time_flag == 'before') {
          this.begin_time = Date.parse(now.getFullYear() - 2 + '-01-01 0:0:0') / 1000;
          this.end_time = Date.parse(now.getFullYear() - 2 + '-12-31 23:59:59') / 1000;
        }
        this.bus_wx_statistical();
      },
      init() {
        this.getCurrentPage();
        this.bus_wx_statistical();
      }
    },
    watch: {
      $route(to, from) {
        this.init();
      }
    },
    mixins: [http]
  };
</script>
