<template>
    <div class="settings-wrap">
        <div class="settings-container">
            <form>
                <div class="settings-content">
                    <div class="settings-title">PC端提醒设置</div>
                    <div class="settings-box">
                        <div class="settings-item">
                            健身房： 在
                            <input name="gym_remind" v-model="gym_remind" /> 小时内，未使用PC端需进行提醒
                        </div>
                        <div class="settings-item">
                            非健身房： 在
                            <input name="notgym_remind" v-model="notgym_remind" /> 小时内，未使用PC端需进行提醒
                        </div>
                    </div>
                </div>
                <div class="settings-content">
                    <div class="settings-title">会籍端提醒设置</div>
                    <div class="settings-box">
                        <div class="settings-item">
                            所有场馆： 在
                            <input name="membership_remind" v-model="membership_remind" /> 小时内，未使用会籍端需进行提醒
                        </div>
                    </div>
                </div>
                <div class="settings-content">
                    <div class="settings-title">教练端提醒设置</div>
                    <div class="settings-box">
                        <div class="settings-item">
                            所有场馆： 在
                            <input name="coach_remind" v-model="coach_remind" /> 小时内，未使用教练端需进行提醒
                        </div>
                    </div>
                </div>
                <div class="settings-content">
                    <div class="settings-title">BOSS端提醒设置</div>
                    <div class="settings-box">
                        <div class="settings-item">
                            所有场馆： 在
                            <input name="boss_remind" v-model="boss_remind" /> 小时内，未使用BOSS端需进行提醒
                        </div>
                    </div>
                </div>
                <el-button :plain="true" type="info" style="margin-left: 94%;margin-top:10px;" v-on:click="submit">提交</el-button>
            </form>
        </div>
    </div>
</template>
<style scoped>
    .settings-wrap {
      margin-top: 40px;
      margin-left: 20px;
    }
    .settings-container {
      background-color: #ffffff;
      border-radius: 15px;
      padding: 10px;
    }
    .settings-box {
      border: 1px solid #cccccc;
    }
    .settings-title {
      line-height: 40px;
    }

    .settings-item {
      margin-left: 20px;
      line-height: 70px;
    }
    .settings-item input {
      height: 40px;
      width: 56px;
      text-align: center;
      font-size: 16px;
    }
</style>
<script>
    import http from 'assets/js/http';
    export default {
      name: 'settings',
      data() {
        return {
          gym_remind: 10,
          notgym_remind: 10,
          membership_remind: 10,
          coach_remind: 10,
          boss_remind: 10
        };
      },
      created() {
        let postData = {
          user_id: Lockr.get('userInfo').id
        };
        this.apiPost('web/homepage/get_user_setting', postData).then(res => {
          this.handelResponse(res, data => {
            this.gym_remind = res.data.gym_remind;
            this.notgym_remind = res.data.notgym_remind;
            this.membership_remind = res.data.membership_remind;
            this.coach_remind = res.data.coach_remind;
            this.boss_remind = res.data.boss_remind;
          });
        });
      },
      methods: {
        submit() {
          _g.openGlobalLoading();
          let postData = {
            user_id: Lockr.get('userInfo').id,
            gym_remind: this.gym_remind,
            notgym_remind: this.notgym_remind,
            membership_remind: this.membership_remind,
            coach_remind: this.coach_remind,
            boss_remind: this.boss_remind
          };
          this.apiPost('web/homepage/user_setting', postData).then(res => {
            this.handelResponse(res, data => {
              _g.toastMsg('success', '操作成功');
            });
          });
        }
      },
      mixins: [http]
    };
</script>
