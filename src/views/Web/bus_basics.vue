<template>
  <div class="main-container-wrap">
    <!--内容区头部-->
    <div class="index-nav">
      场馆统计 >> 基础统计
    </div>
    <div class="main-container">
      <div class="main-content">
        <div class="search-box">
          <div class="block">
            <el-date-picker v-model="value1" type="date" size="small" placeholder="选择日期">
            </el-date-picker>
          </div>
          <div class="block">
            <el-date-picker v-model="value2" type="date" size="small" placeholder="选择日期">
            </el-date-picker>
          </div>
          <el-button size="small" v-on:click="yearquery('now')">今年</el-button>
          <el-button size="small" v-on:click="yearquery('last')">去年</el-button>
          <el-button size="small" v-on:click="yearquery('before')">前年</el-button>
          <el-button size="small" class="query" v-on:click="query">查询</el-button>
        </div>
        <div class="table-container">
          <el-table :data="tableData" stripe border style="width: 100%">
            <el-table-column prop="date" label="日期">
            </el-table-column>

            <el-table-column prop="signatory" label="签约场馆数">
            </el-table-column>

            <el-table-column prop="signatory_add" label="新增签约场馆">
            </el-table-column>
            <el-table-column prop="used" label="使用场馆数">
            </el-table-column>

            <el-table-column prop="used_p" label="使用占比">
            </el-table-column>
            <el-table-column prop="not_used" label="未使用场馆数">
            </el-table-column>
            <el-table-column prop="not_used_p" label="未使用占比">
            </el-table-column>

          </el-table>
        </div>
        <div class="pos-rel p-t-20">
          <div class="block pages">
            <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :current-page="currentPage" :total="pageCount">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  export default {
    name: 'bus_basics',
    data() {
      return {
        tableData: [],
        value1: '',
        value2: '',
        currentPage: null,
        pageCount: null,
        begin_time: '',
        end_time: ''
      };
    },
    created() {
      this.init();
    },
    methods: {
      bus_basic_statistical() {
        let postData = {
          page_no: this.currentPage,
          page_size: 10
        };
        if (this.value1 != '' && this.value2 != '') {
          postData.begin_time = Date.parse(new Date(this.value1)) / 1000;
          postData.end_time = Date.parse(new Date(this.value2)) / 1000 + 24 * 3600 - 1;
        }
        if (this.begin_time != '') {
          postData.begin_time = this.begin_time;
          postData.end_time = this.end_time;
        }
        this.apiPost('web/businessStatistical/bus_basic_statistical', postData).then(res => {
          this.handelResponse(res, data => {
            this.tableData = res.data.list;
            this.pageCount = res.data.pageCount * 10;
          });
        });
      },
      yearquery: function(message) {
        _g.openGlobalLoading();
        this.resetparam();
        let time_flag = message;
        var now = new Date();
        if (time_flag == 'now') {
          this.begin_time = Date.parse(now.getFullYear() + '-01-01 0:0:0') / 1000;
          this.end_time = Date.parse(new Date()) / 1000;
        }
        if (time_flag == 'last') {
          this.begin_time = Date.parse(now.getFullYear() - 1 + '-01-01 0:0:0') / 1000;
          this.end_time = Date.parse(now.getFullYear() - 1 + '-12-31 23:59:59') / 1000;
        }
        if (time_flag == 'before') {
          this.begin_time = Date.parse(now.getFullYear() - 2 + '-01-01 0:0:0') / 1000;
          this.end_time = Date.parse(now.getFullYear() - 2 + '-12-31 23:59:59') / 1000;
        }
        this.bus_basic_statistical();
      },
      resetparam() {
        this.value1 = '';
        this.value2 = '';
      },
      getCurrentPage() {
        let data = this.$route.query;
        if (data) {
          if (data.page) {
            this.currentPage = parseInt(data.page);
          } else {
            this.currentPage = 1;
          }
        }
      },
      handleCurrentChange(page) {
        router.push({ path: this.$route.path, query: { keywords: this.keywords, page: page } });
      },
      init() {
        this.getCurrentPage();
        this.bus_basic_statistical();
      },
      query() {
        _g.openGlobalLoading();
        this.begin_time = '';
        this.end_time = '';
        this.bus_basic_statistical();
      }
    },
    watch: {
      $route(to, from) {
        this.init();
      }
    },
    mixins: [http]
  };
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
  .index-nav {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    padding-left: 10px;
    background-color: #e4e4e4;
  }
  .main-container {
    margin-top: 40px;
    margin-left: 46px;
    background-color: #ffffff;
    border-radius: 15px;
  }
  .main-content {
    padding-top: 16px;
    padding-left: 22px;
    padding-bottom: 70px;
  }
  .search-box {
    height: 34px;
  }
  .block {
    margin-right: 20px;
    float: left;
  }
  .el-date-editor {
    width: 140px;
  }
  .el-input__inner {
    border-radius: 30px;
  }
  .search-box button {
    height: 30px;
    vertical-align: bottom;
    border-radius: 30px;
    width: 70px;
  }
  .search-box .query {
    width: 140px;
    color: #ffffff;
    font-size: 14px;
    border: 0;
    background-color: #fa571c;
  }
  .table-container {
    margin-top: 12px;
    margin-right: 20px;
  }
</style>
