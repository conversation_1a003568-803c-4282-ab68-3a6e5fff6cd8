<template>
  <div class="container">
    <el-card class="box-card">
      <!-- Header -->
      <div slot="header" class="clearfix">
        <span class="page-title">业务开通</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleAdd">
          <i class="el-icon-plus"></i>
          添加
        </el-button>
      </div>

      <!-- Search Form -->
      <div class="search-form">
        <el-form :model="searchForm" inline class="search-form-inline">
          <el-form-item label="商家">
            <MerchantSelect
              v-model="searchForm.m_id"
              placeholder="请输入商家名称，支持模糊搜索"
              custom-class="search-input"
              clearable
            />
          </el-form-item>
          <el-form-item label="场馆">
            <BusSelect
              v-model="searchForm.bus_id"
              placeholder="请输入场馆名称，支持模糊搜索"
              custom-class="search-input"
              clearable
            />
          </el-form-item>
          <el-form-item label="业务开通时间">
            <el-date-picker
              v-model="searchForm.start_time_range"
              type="daterange"
              range-separator="至"
              start-placeholder="YYYY-MM-DD"
              end-placeholder="YYYY-MM-DD"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              class="date-range-picker"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="到期时间">
            <el-date-picker
              v-model="searchForm.expire_time_range"
              type="daterange"
              range-separator="至"
              start-placeholder="YYYY-MM-DD"
              end-placeholder="YYYY-MM-DD"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              class="date-range-picker"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- Data Table -->
      <el-table v-loading="loading" :data="tableData" stripe border style="width: 100%" class="data-table">
        <el-table-column prop="index" label="序号" width="80" align="center">
          <template slot-scope="scope">
            {{ (postData.page_no - 1) * postData.page_size + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="mer_name" label="商家" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.mer_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="bus_name" label="场馆" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.bus_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="open_amount" label="业务开通金额(¥)" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.open_amount ? Number(scope.row.open_amount).toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="annual_fee" label="每年续费金额(¥)" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.annual_fee ? Number(scope.row.annual_fee).toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="meituan_rate" label="美团费率(%)" align="center" width="120">
          <template slot-scope="scope">
            {{ scope.row.meituan_rate ? Number(scope.row.meituan_rate).toFixed(2) : '2.50' }}
          </template>
        </el-table-column>
        <el-table-column prop="start_time" label="业务开通时间" align="center" min-width="160"></el-table-column>
        <el-table-column prop="expire_time" label="到期时间" align="center" min-width="160"></el-table-column>
        <el-table-column label="操作" align="center" width="100" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">查看</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <footer class="pagination-footer">
        <el-pagination
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          layout="prev, pager, next, sizes, jumper, ->, total"
          :page-size="postData.page_size"
          :current-page="postData.page_no"
          :total="dataCount"
          :page-sizes="[10, 30, 50, 100]"
        ></el-pagination>
      </footer>
    </el-card>
  </div>
</template>

<script>
import http from 'assets/js/http'
import MerchantSelect from '@/components/Payment/MerchantSelect.vue'
import BusSelect from '@/components/Common/BusSelect.vue'
// import { formatDate } from '@/utils'

export default {
  name: 'MeituanList',
  mixins: [http],
  components: {
    MerchantSelect,
    BusSelect,
  },
  data() {
    return {
      // Search form data
      searchForm: {
        m_id: '',
        bus_id: '',
        start_time_range: [],
        expire_time_range: [],
      },
      // Request parameters
      postData: {
        m_id: '',
        bus_id: '',
        start_time: '',
        end_time: '',
        expire_start_time: '',
        expire_end_time: '',
        page_size: 10,
        page_no: 1,
      },
      // Table data
      tableData: [],
      dataCount: 0,
      loading: false,
    }
  },
  // filters: {
  //   formatDateFilter(value) {
  //     if (!value) return '-'
  //     return formatDate(new Date(value), 'yyyy-MM-dd HH:mm:ss')
  //   },
  // },
  methods: {
    // Get list data
    getList() {
      this.loading = true
      this.apiPost('/Web/MeituanPaoluPei/getOpenList', this.postData)
        .then((res) => {
          if (res.errorcode === 0) {
            this.tableData = res.data.list || []
            this.dataCount = res.data.count || 0
          } else {
            this.$message.error(res.errormsg || '获取数据失败')
            this.tableData = []
            this.dataCount = 0
          }
        })
        .catch((error) => {
          console.error('Get list error:', error)
          this.$message.error('获取数据失败，请重试')
          this.tableData = []
          this.dataCount = 0
        })
        .finally(() => {
          this.loading = false
        })
    },

    // Handle search
    handleSearch() {
      // Convert search form data to request parameters
      this.postData.m_id = this.searchForm.m_id
      this.postData.bus_id = this.searchForm.bus_id

      // Handle start time range
      if (this.searchForm.start_time_range && this.searchForm.start_time_range.length === 2) {
        this.postData.start_time = this.searchForm.start_time_range[0]
        this.postData.end_time = this.searchForm.start_time_range[1]
      } else {
        this.postData.start_time = ''
        this.postData.end_time = ''
      }

      // Handle expire time range
      if (this.searchForm.expire_time_range && this.searchForm.expire_time_range.length === 2) {
        this.postData.expire_start_time = this.searchForm.expire_time_range[0]
        this.postData.expire_end_time = this.searchForm.expire_time_range[1]
      } else {
        this.postData.expire_start_time = ''
        this.postData.expire_end_time = ''
      }

      // Reset to first page
      this.postData.page_no = 1
      this.getList()
    },

    // Handle reset
    handleReset() {
      this.searchForm = {
        m_id: '',
        bus_id: '',
        start_time_range: [],
        expire_time_range: [],
      }
      this.postData = {
        m_id: '',
        bus_id: '',
        start_time: '',
        end_time: '',
        expire_start_time: '',
        expire_end_time: '',
        page_size: this.postData.page_size,
        page_no: 1,
      }
      this.getList()
    },

    // Handle add new record
    handleAdd() {
      this.$router.push({ name: 'meituan_save' })
    },

    // Handle view record
    handleView(row) {
      this.$router.push({
        name: 'meituan_save',
        params: { id: row.id },
      })
    },

    // Handle page change
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage
      this.getList()
    },

    // Handle page size change
    handleSizeChange(size) {
      this.postData.page_no = 1
      this.postData.page_size = size
      this.getList()
    },
  },

  created() {
    this.getList()
  },
}
</script>

<style scoped>
.container {
  padding: 20px;
  margin: 0 auto;
  background-color: #f5f5f5;
}

.box-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* Header styles */
.page-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.status-info {
  margin-top: 8px;
}

.status-label {
  font-size: 12px;
  color: #999;
  background-color: #f5f5f5;
  padding: 2px 8px;
  border-radius: 4px;
}

/* Search form styles */
.search-form {
  padding: 20px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
}

.search-form-inline {
  margin-bottom: 16px;
}

.search-form-inline .el-form-item {
  margin-bottom: 16px;
  margin-right: 20px;
}

.search-form-inline .el-form-item__label {
  font-weight: 500;
  color: #333;
  width: 80px !important;
}

.search-input {
  width: 200px;
}

.date-range-picker {
  width: 300px;
}

.search-results-info {
  font-size: 14px;
  color: #666;
  text-align: right;
}

/* Table styles */
.data-table {
  margin-bottom: 20px;
}

.data-table .el-table__header-wrapper th {
  background-color: #fafafa;
  color: #333;
  font-weight: 500;
}

.data-table .el-table__body-wrapper .el-table__row:hover {
  background-color: #f5f7fa;
}

/* Pagination styles */
.pagination-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
}

/* Element UI customization */
.el-card__header {
  padding: 18px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafafa;
}

.el-card__body {
  padding: 0 20px 20px;
}

.el-button--text {
  color: #409eff;
  font-size: 16px;
}

.el-button--text:hover {
  color: #66b1ff;
}

.el-form--inline .el-form-item {
  margin-right: 20px;
}

.el-table .el-button--text {
  color: #409eff;
  font-size: 14px;
}

.el-table .el-button--text:hover {
  color: #66b1ff;
}

/* Responsive design */
@media (max-width: 1200px) {
  .search-form-inline .el-form-item {
    margin-right: 10px;
  }

  .search-input {
    width: 180px;
  }

  .date-range-picker {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .search-form-inline {
    display: block;
  }

  .search-form-inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 16px;
  }

  .search-input,
  .date-range-picker {
    width: 100%;
  }

  .pagination-footer {
    justify-content: center;
  }

  .data-table {
    font-size: 12px;
  }
}
</style>
