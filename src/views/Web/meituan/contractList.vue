<template>
  <div class="container">
    <el-card class="box-card">
      <!-- Header -->
      <div slot="header" class="clearfix">
        <span class="page-title">合约管理</span>
      </div>

      <!-- Search Form -->
      <ContractSearchForm :active-tab="activeTab" :loading="loading" :initial-values="getCurrentTabSearchState()"
        @search="handleTabSearch" @reset="handleTabReset" />

      <!-- Tabs -->
      <div class="tabs-container">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick" class="contract-tabs">
          <el-tab-pane :label="`全部(${tabCounts.all || 0})`" name="all" code=""></el-tab-pane>
          <el-tab-pane :label="`履约中(${tabCounts.in_progress || 0})`" name="in_progress" code="1"></el-tab-pane>
          <el-tab-pane :label="`已解约(${tabCounts.terminated || 0})`" name="terminated" code="2"></el-tab-pane>
          <el-tab-pane :label="`履约完成(${tabCounts.fulfillment_complete || 0})`" name="fulfillment_complete"
            code="3"></el-tab-pane>
        </el-tabs>
      </div>

      <!-- Data Table -->
      <el-table v-loading="loading" :data="tableData" stripe border style="width: 100%" class="data-table">
        <el-table-column prop="index" label="序号" width="80" align="center">
          <template slot-scope="scope">
            {{ (postData.page_no - 1) * postData.page_size + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="mer_name" label="商家" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.mer_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="bus_name" label="场馆" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.bus_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="contract_no" label="合约编号" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.contract_no || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="code" label="美团券码" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.code || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="total_amount" label="签约金额(¥)" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.total_amount ? Number(scope.row.total_amount).toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="username" label="签约会员" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.username || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="mobile" label="手机号" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.mobile || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="sales_name" label="签约销售" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.sales_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column v-if="['all', 'in_progress'].includes(activeTab)" key="write_off_250" prop="write_off_date"
          label="核销时间" align="center" min-width="160">
          <template slot-scope="scope">
            {{ scope.row.write_off_date || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="deducted_periods" label="已扣款成功期数" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.deducted_periods }}/{{ scope.row.total_periods }}
          </template>
        </el-table-column>
        <el-table-column v-if="['terminated', 'fulfillment_complete'].includes(activeTab)" key="write_off_251"
          prop="write_off_date" label="核销时间" align="center" min-width="160">
          <template slot-scope="scope">
            {{ scope.row.write_off_date || '-' }}
          </template>
        </el-table-column>
        <el-table-column v-if="activeTab === 'terminated'" prop="terminate_date" label="解约时间" align="center"
          min-width="160">
          <template slot-scope="scope">
            {{ scope.row.terminate_date || '-' }}
          </template>
        </el-table-column>
        <el-table-column v-if="activeTab === 'fulfillment_complete'" prop="finish_date" label="完成时间" align="center"
          min-width="160">
          <template slot-scope="scope">
            {{ scope.row.finish_date || '-' }}
          </template>
        </el-table-column>
        <el-table-column v-if="activeTab === 'all'" prop="contract_status" label="合约状态" align="center" min-width="120">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.contract_status == 1" type="warning">履约中</el-tag>
            <el-tag v-else-if="scope.row.contract_status == 2" type="danger">已解约</el-tag>
            <el-tag v-else-if="scope.row.contract_status == 3" type="success">履约完成</el-tag>
            <el-tag v-else type="info">未知</el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <footer class="pagination-footer">
        <el-pagination @current-change="handleCurrentChange" @size-change="handleSizeChange"
          layout="prev, pager, next, sizes, jumper, ->, total" :page-size="postData.page_size"
          :current-page="postData.page_no" :total="dataCount" :page-sizes="[10, 30, 50, 100]"></el-pagination>
      </footer>
    </el-card>
  </div>
</template>

<script>
import http from 'assets/js/http'
import ContractSearchForm from '@/components/meituan/ContractSearchForm'

export default {
  name: 'ContractList',
  mixins: [http],
  components: {
    ContractSearchForm,
  },
  data() {
    return {
      // Active tab
      activeTab: 'all',
      // Tab counts
      tabCounts: {
        all: 0,
        in_progress: 0,
        terminated: 0,
        fulfillment_complete: 0,
      },
      // Per-tab search states
      tabSearchStates: {
        all: {
          contract_no: '',
          contract_status: '',
          code: '',
          username: '',
          mobile: '',
          m_id: '',
          bus_id: '',
          write_off_time_range: [],
          termination_time_range: [],
          finish_time_range: [],
        },
        in_progress: {
          contract_no: '',
          code: '',
          username: '',
          mobile: '',
          m_id: '',
          bus_id: '',
          write_off_time_range: [],
          termination_time_range: [],
          finish_time_range: [],
        },
        terminated: {
          contract_no: '',
          code: '',
          username: '',
          mobile: '',
          m_id: '',
          bus_id: '',
          write_off_time_range: [],
          termination_time_range: [],
          finish_time_range: [],
        },
        fulfillment_complete: {
          contract_no: '',
          code: '',
          username: '',
          mobile: '',
          m_id: '',
          bus_id: '',
          write_off_time_range: [],
          termination_time_range: [],
          finish_time_range: [],
        },
      },
      // Request parameters
      postData: {
        contract_no: '',
        contract_status: '',
        code: '',
        username: '',
        mobile: '',
        m_id: '',
        bus_id: '',
        write_off_s_date: '',
        write_off_e_date: '',
        terminate_s_date: '',
        terminate_e_date: '',
        finish_s_date: '',
        finish_e_date: '',
        tab_filter: '',
        page_size: 10,
        page_no: 1,
      },
      // Table data
      tableData: [],
      dataCount: 0,
      loading: false,
    }
  },
  methods: {
    // Get list data
    getList(tabFilter = null) {
      const requestData = { ...this.postData }

      // If tabFilter is provided, use it; otherwise use current postData
      if (tabFilter !== null) {
        requestData.tab_filter = tabFilter
      }

      // Show loading only when updating the active tab's table data
      const isActiveTab = requestData.tab_filter === this.postData.tab_filter
      if (isActiveTab) {
        this.loading = true
      }

      this.apiPost('/Web/MeituanPaoluPei/contractList', requestData)
        .then((res) => {
          if (res.errorcode === 0) {
            const count = res.data.count || 0

            // Update tab counts based on the tab filter
            const tabFilterMap = {
              '': 'all',
              '1': 'in_progress',
              '2': 'terminated',
              '3': 'fulfillment_complete'
            }

            const tabKey = tabFilterMap[requestData.tab_filter]
            if (tabKey) {
              this.tabCounts[tabKey] = count
            }

            // Update table data only for the active tab
            if (isActiveTab) {
              this.tableData = res.data.list || []
              this.dataCount = count
            }
          } else {
            if (isActiveTab) {
              this.$message.error(res.errormsg || '获取数据失败')
              this.tableData = []
              this.dataCount = 0
            }
          }
        })
        .catch((error) => {
          console.error('Get list error:', error)
          if (isActiveTab) {
            this.$message.error('获取数据失败，请重试')
            this.tableData = []
            this.dataCount = 0
          }
        })
        .finally(() => {
          if (isActiveTab) {
            this.loading = false
          }
        })
    },

    // Refresh single tab with its search criteria
    refreshSingleTab(tabName = null) {
      const targetTab = tabName || this.activeTab
      const tabFilterMap = {
        'all': '',
        'in_progress': '1',
        'terminated': '2',
        'fulfillment_complete': '3'
      }

      const tabFilter = tabFilterMap[targetTab]
      const searchData = this.tabSearchStates[targetTab]

      // Update postData with the tab's search criteria
      this.updatePostDataFromSearch(searchData)
      this.postData.tab_filter = tabFilter

      // Call getList to update both table data and tab count
      this.getList(tabFilter)
    },

    // Refresh all tab counts with their individual search criteria
    refreshAllTabs() {
      this.refreshSingleTab('all') // all
      this.refreshSingleTab('in_progress') // in_progress
      this.refreshSingleTab('terminated') // terminated
      this.refreshSingleTab('fulfillment_complete') // fulfillment_complete
    },

    // Get current tab's search state
    getCurrentTabSearchState() {
      return this.tabSearchStates[this.activeTab] || {}
    },

    // Update postData from search criteria
    updatePostDataFromSearch(searchData) {
      this.postData.contract_no = searchData.contract_no || ''
      this.postData.contract_status = searchData.contract_status || ''
      this.postData.code = searchData.code || ''
      this.postData.username = searchData.username || ''
      this.postData.mobile = searchData.mobile || ''
      this.postData.m_id = searchData.m_id || ''
      this.postData.bus_id = searchData.bus_id || ''

      // Handle write-off time range
      if (searchData.write_off_time_range && searchData.write_off_time_range.length === 2) {
        this.postData.write_off_s_date = searchData.write_off_time_range[0]
        this.postData.write_off_e_date = searchData.write_off_time_range[1]
      } else {
        this.postData.write_off_s_date = ''
        this.postData.write_off_e_date = ''
      }

      // Handle termination time range
      if (searchData.termination_time_range && searchData.termination_time_range.length === 2) {
        this.postData.terminate_s_date = searchData.termination_time_range[0]
        this.postData.terminate_e_date = searchData.termination_time_range[1]
      } else {
        this.postData.terminate_s_date = ''
        this.postData.terminate_e_date = ''
      }

      // Handle finish time range
      if (searchData.finish_time_range && searchData.finish_time_range.length === 2) {
        this.postData.finish_s_date = searchData.finish_time_range[0]
        this.postData.finish_e_date = searchData.finish_time_range[1]
      } else {
        this.postData.finish_s_date = ''
        this.postData.finish_e_date = ''
      }
    },

    // Handle tab click
    handleTabClick(tab) {
      this.activeTab = tab.name
      this.postData.page_no = 1

      // Only refresh the clicked tab with its search criteria
      this.refreshSingleTab(tab.name)
    },

    // Handle search from search form
    handleTabSearch(searchData) {
      // Save search criteria for current tab
      this.tabSearchStates[this.activeTab] = { ...searchData }

      // Reset to first page
      this.postData.page_no = 1

      // Only refresh current tab with new search criteria
      this.refreshSingleTab()
    },

    // Handle reset from search form
    handleTabReset() {
      // Reset search criteria for current tab
      this.tabSearchStates[this.activeTab] = {
        contract_no: '',
        contract_status: '',
        code: '',
        username: '',
        mobile: '',
        m_id: '',
        bus_id: '',
        write_off_time_range: [],
        termination_time_range: [],
        finish_time_range: [],
      }

      // Reset to first page
      this.postData.page_no = 1

      // Only refresh current tab with cleared criteria
      this.refreshSingleTab()
    },

    // Handle pagination - current page change
    handleCurrentChange(page) {
      this.postData.page_no = page
      this.getList()
    },

    // Handle pagination - page size change
    handleSizeChange(size) {
      this.postData.page_size = size
      this.postData.page_no = 1
      this.getList()
    },
  },
  created() {
    this.refreshAllTabs()
  },
}
</script>

<style scoped>
/* Container styles */
.container {
  padding: 20px;
  margin: 0 auto;
  background-color: #f5f5f5;
}

.box-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: none;
}

/* Header styles */
.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}


/* Data table styles */
.data-table {
  margin-bottom: 20px;
}

.data-table .el-table__header-wrapper {
  background-color: #f8f9fa;
}

.data-table .el-table__header th {
  background-color: #f8f9fa;
  color: #333;
  font-weight: 600;
  border-bottom: 2px solid #ebeef5;
}

.data-table .el-table__body tr:hover>td {
  background-color: #f5f7fa;
}

.data-table .el-button--text {
  color: #409eff;
  padding: 0;
  margin-right: 10px;
}

.data-table .el-button--text:hover {
  color: #66b1ff;
}

/* Tabs styles */
.tabs-container {
  margin-bottom: 20px;
}

.contract-tabs {
  border-bottom: 1px solid #ebeef5;
}

.contract-tabs .el-tabs__header {
  margin: 0 0 15px;
}

.contract-tabs .el-tabs__nav-wrap::after {
  height: 1px;
}

.contract-tabs .el-tabs__item {
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #666;
  position: relative;
  white-space: nowrap;
}

.contract-tabs .el-tabs__item.is-active {
  color: #409eff;
  font-weight: 500;
}

.contract-tabs .el-tabs__item:hover {
  color: #409eff;
}

.contract-tabs .el-tabs__active-bar {
  background-color: #409eff;
}


/* Pagination styles */
.pagination-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }


  .pagination-footer {
    justify-content: center;
  }

  .data-table {
    font-size: 12px;
  }

  .contract-tabs .el-tabs__item {
    padding: 0 10px;
    font-size: 12px;
  }
}
</style>
