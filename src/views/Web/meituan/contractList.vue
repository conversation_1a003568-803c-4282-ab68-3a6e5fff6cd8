<template>
  <div class="container">
    <el-card class="box-card">
      <!-- Header -->
      <div slot="header" class="clearfix">
        <span class="page-title">合约管理</span>
      </div>

      <!-- Search Form -->
      <div class="search-form">
        <el-form :model="searchForm" inline class="search-form-inline">
          <el-form-item label="合约编号">
            <el-input v-model="searchForm.contract_no" placeholder="请输入合约编号" class="search-input" clearable></el-input>
          </el-form-item>
          <el-form-item v-if="activeTab === 'all'" label="合约状态">
            <el-select v-model="searchForm.contract_status" placeholder="请选择合约状态" class="search-input" clearable>
              <el-option
                v-for="item in contractStatusOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              ></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="美团券码">
            <el-input v-model="searchForm.meituan_code" placeholder="请输入美团券码" class="search-input" clearable></el-input>
          </el-form-item>
          <el-form-item label="签约会员">
            <el-input v-model="searchForm.member_name" placeholder="请输入会员名称" class="search-input" clearable></el-input>
          </el-form-item>
          <el-form-item label="手机号">
            <el-input v-model="searchForm.phone" placeholder="请输入会员手机号" class="search-input" clearable></el-input>
          </el-form-item>
          <el-form-item label="商家">
            <MerchantSelect v-model="searchForm.m_id" placeholder="请输入商家名称" custom-class="search-input" clearable />
          </el-form-item>
          <el-form-item label="场馆">
            <BusSelect v-model="searchForm.bus_id" placeholder="请输入场馆名称" custom-class="search-input" clearable />
          </el-form-item>
          <el-form-item label="核销时间">
            <el-date-picker
              v-model="searchForm.verification_time_range"
              type="daterange"
              range-separator="至"
              start-placeholder="YYYY-MM-DD"
              end-placeholder="YYYY-MM-DD"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              class="date-range-picker"
            ></el-date-picker>
          </el-form-item>
          <el-form-item v-if="activeTab === 'terminated'" label="解约时间">
            <el-date-picker
              v-model="searchForm.verification_time_range"
              type="daterange"
              range-separator="至"
              start-placeholder="YYYY-MM-DD"
              end-placeholder="YYYY-MM-DD"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              class="date-range-picker"
            ></el-date-picker>
          </el-form-item>
          <el-form-item v-if="activeTab === 'fulfillment_complete'" label="完成时间">
            <el-date-picker
              v-model="searchForm.verification_time_range"
              type="daterange"
              range-separator="至"
              start-placeholder="YYYY-MM-DD"
              end-placeholder="YYYY-MM-DD"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              class="date-range-picker"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- Tabs -->
      <div class="tabs-container">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick" :class="['contract-tabs', { loading: countLoading }]">
          <el-tab-pane :label="`全部(${tabCounts.all || 0})`" name="all" code=""></el-tab-pane>
          <el-tab-pane :label="`履约中(${tabCounts.in_progress || 0})`" name="in_progress" code="1"></el-tab-pane>
          <el-tab-pane :label="`已解约(${tabCounts.terminated || 0})`" name="terminated" code="2"></el-tab-pane>
          <el-tab-pane
            :label="`履约完成(${tabCounts.fulfillment_complete || 0})`"
            name="fulfillment_complete"
            code="3"
          ></el-tab-pane>
        </el-tabs>
      </div>

      <!-- Data Table -->
      <el-table v-loading="loading" :data="tableData" stripe border style="width: 100%" class="data-table">
        <el-table-column prop="index" label="序号" width="80" align="center">
          <template slot-scope="scope">
            {{ (postData.page_no - 1) * postData.page_size + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="mer_name" label="商家" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.mer_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="bus_name" label="场馆" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.bus_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="contract_no" label="合约编号" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.contract_no || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="meituan_code" label="美团券码" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.meituan_code || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="contract_amount" label="签约金额(¥)" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.contract_amount ? Number(scope.row.contract_amount).toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="member_name" label="签约会员" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.member_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="phone" label="手机号" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.phone || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="contract_sales" label="签约销售" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.contract_sales || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="['all', 'in_progress'].includes(activeTab)"
          key="verification_250"
          prop="verification_time"
          label="核销时间（签约）"
          align="center"
          min-width="160"
        >
          <template slot-scope="scope">
            {{ scope.row.verification_time || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="deduction_count" label="已扣款成功期数" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.deduction_count || 0 }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="['terminated', 'fulfillment_complete'].includes(activeTab)"
          key="verification_251"
          prop="verification_time"
          label="核销时间（签约）"
          align="center"
          min-width="160"
        >
          <template slot-scope="scope">
            {{ scope.row.verification_time || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="activeTab === 'terminated'"
          prop="termination_time"
          label="解约时间"
          align="center"
          min-width="160"
        >
          <template slot-scope="scope">
            {{ scope.row.termination_time || '-' }}
          </template>
        </el-table-column>
        <el-table-column
          v-if="activeTab === 'fulfillment_complete'"
          prop="fulfillment_time"
          label="完成时间"
          align="center"
          min-width="160"
        >
          <template slot-scope="scope">
            {{ scope.row.fulfillment_time || '-' }}
          </template>
        </el-table-column>
        <el-table-column v-if="activeTab === 'all'" prop="contract_status" label="合约状态" align="center" min-width="120">
          <template slot-scope="scope">
            <el-tag v-if="scope.row.contract_status === 1" type="warning">履约中</el-tag>
            <el-tag v-if="scope.row.contract_status === 2" type="danger">已解约</el-tag>
            <el-tag v-if="scope.row.contract_status === 3" type="success">履约完成</el-tag>
            <el-tag v-else type="info">未知</el-tag>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <footer class="pagination-footer">
        <el-pagination
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          layout="prev, pager, next, sizes, jumper, ->, total"
          :page-size="postData.page_size"
          :current-page="postData.page_no"
          :total="dataCount"
          :page-sizes="[10, 30, 50, 100]"
        ></el-pagination>
      </footer>
    </el-card>
  </div>
</template>

<script>
import http from 'assets/js/http'
import MerchantSelect from '@/components/Payment/MerchantSelect'
import BusSelect from '@/components/Common/BusSelect'

export default {
  name: 'ContractList',
  mixins: [http],
  components: {
    MerchantSelect,
    BusSelect,
  },
  data() {
    return {
      // Active tab
      activeTab: 'all',
      // Tab counts
      tabCounts: {
        all: 0,
        in_progress: 0,
        terminated: 0,
        fulfillment_complete: 0,
      },
      // Search form data
      searchForm: {
        contract_no: '',
        meituan_code: '',
        member_name: '',
        phone: '',
        m_id: '',
        bus_id: '',
        verification_time_range: [],
        contract_status: '',
      },
      // Request parameters
      postData: {
        contract_no: '',
        meituan_code: '',
        member_name: '',
        phone: '',
        m_id: '',
        bus_id: '',
        verification_start_time: '',
        verification_end_time: '',
        contract_status: '',
        tab_filter: 'all',
        page_size: 10,
        page_no: 1,
      },
      // Table data
      tableData: [],
      dataCount: 0,
      loading: false,
      countLoading: false,
      // Contract status options
      contractStatusOptions: [
        { value: 1, label: '履约中' },
        { value: 2, label: '已解约' },
        { value: 3, label: '履约完成' },
      ],
    }
  },
  methods: {
    // Get list data
    getList() {
      this.loading = true
      this.apiPost('/Web/MeituanPaoluPei/getOpenList', this.postData)
        .then((res) => {
          if (res.errorcode === 0) {
            this.tableData = res.data.list || []
            this.dataCount = res.data.count || 0
          } else {
            this.$message.error(res.errormsg || '获取数据失败')
            this.tableData = []
            this.dataCount = 0
          }
        })
        .catch((error) => {
          console.error('Get list error:', error)
          this.$message.error('获取数据失败，请重试')
          this.tableData = []
          this.dataCount = 0
        })
        .finally(() => {
          this.loading = false
        })
    },

    // Get tab counts
    getTabCounts() {
      this.countLoading = true

      this.apiGet('/Web/MeituanPaoluPei/getTabCount')
        .then((res) => {
          if (res.errorcode === 0) {
            this.tabCounts = {
              all: res.data.all || 0,
              in_progress: res.data.in_progress || 0,
              terminated: res.data.terminated || 0,
              fulfillment_complete: res.data.fulfillment_complete || 0,
            }
          } else {
            console.error('Get tab counts error:', res.errormsg)
            // Reset counts on error
            this.tabCounts = {
              all: 0,
              in_progress: 0,
              terminated: 0,
              fulfillment_complete: 0,
            }
          }
        })
        .catch((error) => {
          console.error('Get tab counts error:', error)
          // Reset counts on error
          this.tabCounts = {
            all: 0,
            in_progress: 0,
            terminated: 0,
            fulfillment_complete: 0,
          }
        })
        .finally(() => {
          this.countLoading = false
        })
    },

    // Handle tab click
    handleTabClick(tab) {
      this.activeTab = tab.name
      this.postData.tab_filter = tab.$attrs.code
      this.postData.page_no = 1
      this.getList()
    },

    // Handle search
    handleSearch() {
      // Convert search form data to request parameters
      this.postData.contract_no = this.searchForm.contract_no
      this.postData.meituan_code = this.searchForm.meituan_code
      this.postData.member_name = this.searchForm.member_name
      this.postData.phone = this.searchForm.phone
      this.postData.m_id = this.searchForm.m_id
      this.postData.bus_id = this.searchForm.bus_id
      this.postData.contract_status = this.searchForm.contract_status

      // Handle verification time range
      if (this.searchForm.verification_time_range && this.searchForm.verification_time_range.length === 2) {
        this.postData.verification_start_time = this.searchForm.verification_time_range[0]
        this.postData.verification_end_time = this.searchForm.verification_time_range[1]
      } else {
        this.postData.verification_start_time = ''
        this.postData.verification_end_time = ''
      }

      // Reset to first page
      this.postData.page_no = 1
      this.getList()
      this.getTabCounts()
    },

    // Handle reset
    handleReset() {
      this.searchForm = {
        contract_no: '',
        meituan_code: '',
        member_name: '',
        phone: '',
        m_id: '',
        bus_id: '',
        verification_time_range: [],
        contract_status: '',
      }
      this.postData = {
        contract_no: '',
        meituan_code: '',
        member_name: '',
        phone: '',
        m_id: '',
        bus_id: '',
        verification_start_time: '',
        verification_end_time: '',
        contract_status: '',
        tab_filter: this.activeTab,
        page_size: this.postData.page_size,
        page_no: 1,
      }
      this.getList()
      this.getTabCounts()
    },

    // Handle pagination - current page change
    handleCurrentChange(page) {
      this.postData.page_no = page
      this.getList()
    },

    // Handle pagination - page size change
    handleSizeChange(size) {
      this.postData.page_size = size
      this.postData.page_no = 1
      this.getList()
    },
  },
  created() {
    this.getList()
    this.getTabCounts()
  },
}
</script>

<style scoped>
/* Container styles */
.container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.box-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: none;
}

/* Header styles */
.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* Search form styles */
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #ebeef5;
}

.search-form-inline {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.search-form-inline .el-form-item {
  margin-right: 20px;
  margin-bottom: 16px;
}

.search-input {
  width: 200px;
}

.date-range-picker {
  width: 240px;
}

/* Data table styles */
.data-table {
  margin-bottom: 20px;
}

.data-table .el-table__header-wrapper {
  background-color: #f8f9fa;
}

.data-table .el-table__header th {
  background-color: #f8f9fa;
  color: #333;
  font-weight: 600;
  border-bottom: 2px solid #ebeef5;
}

.data-table .el-table__body tr:hover > td {
  background-color: #f5f7fa;
}

.data-table .el-button--text {
  color: #409eff;
  padding: 0;
  margin-right: 10px;
}

.data-table .el-button--text:hover {
  color: #66b1ff;
}

/* Tabs styles */
.tabs-container {
  margin-bottom: 20px;
}

.contract-tabs {
  border-bottom: 1px solid #ebeef5;
}

.contract-tabs .el-tabs__header {
  margin: 0 0 15px;
}

.contract-tabs .el-tabs__nav-wrap::after {
  height: 1px;
}

.contract-tabs .el-tabs__item {
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #666;
  position: relative;
  white-space: nowrap;
}

.contract-tabs .el-tabs__item.is-active {
  color: #409eff;
  font-weight: 500;
}

.contract-tabs .el-tabs__item:hover {
  color: #409eff;
}

.contract-tabs .el-tabs__active-bar {
  background-color: #409eff;
}

/* Loading state for tabs */
.contract-tabs.loading .el-tabs__item {
  opacity: 0.6;
}

/* Pagination styles */
.pagination-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .search-form-inline {
    display: block;
  }

  .search-form-inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 16px;
  }

  .search-input,
  .date-range-picker {
    width: 100%;
  }

  .pagination-footer {
    justify-content: center;
  }

  .data-table {
    font-size: 12px;
  }

  .contract-tabs .el-tabs__item {
    padding: 0 10px;
    font-size: 12px;
  }
}
</style>
