<template>
  <div class="container">
    <el-card class="box-card">
      <!-- Header -->
      <div slot="header" class="clearfix">
        <span class="page-title">签约数据统计</span>
      </div>

      <!-- Search Form -->
      <div class="search-form">
        <el-form :model="searchForm" inline class="search-form-inline">
          <el-form-item>
            <el-select v-model="searchForm.year" placeholder="请选择年份" class="search-input" clearable>
              <el-option label="2024年" value="2024"></el-option>
              <el-option label="2023年" value="2023"></el-option>
              <el-option label="2022年" value="2022"></el-option>
              <el-option label="2021年" value="2021"></el-option>
              <el-option label="2020年" value="2020"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item>
            <BusSelect v-model="searchForm.bus_id" placeholder="请输入场馆名称" custom-class="search-input" clearable />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- Statistics Table -->
      <el-table v-loading="loading" :data="tableData" stripe border style="width: 100%" class="statistics-table">
        <el-table-column prop="month" label="月份" width="100" align="center">
          <template slot-scope="scope">
            {{ scope.row.month }}
          </template>
        </el-table-column>
        <el-table-column prop="new_merchant_count" label="新增跑路赔合约总数量" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.new_merchant_count || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="new_merchant_amount" label="新增跑路赔合约总金额(¥)" align="center" min-width="160">
          <template slot-scope="scope">
            {{ scope.row.new_merchant_amount ? Number(scope.row.new_merchant_amount).toFixed(2) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="active_contract_count" label="累计履约中合约总数量" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.active_contract_count || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="active_contract_amount" label="扣款成功合约总数量" align="center" min-width="160">
          <template slot-scope="scope">
            {{ scope.row.active_contract_amount ? Number(scope.row.active_contract_amount).toFixed(2) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="completed_contract_count" label="扣款成功合约总金额(¥)" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.completed_contract_count || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="completed_contract_amount" label="扣款失败合约总数量" align="center" min-width="160">
          <template slot-scope="scope">
            {{ scope.row.completed_contract_amount ? Number(scope.row.completed_contract_amount).toFixed(2) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="total_contract_count" label="扣款失败合约总金额(¥)" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.total_contract_count || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="total_contract_amount" label="解约合约总数量" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.total_contract_amount ? Number(scope.row.total_contract_amount).toFixed(2) : '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="completion_rate" label="解约合约总金额(¥)" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.completion_rate ? scope.row.completion_rate + '%' : '-' }}
          </template>
        </el-table-column>
        <!-- <el-table-column prop="completion_rate" label="跑路赔合约解约率" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.completion_rate ? scope.row.completion_rate + '%' : '-' }}
          </template>
        </el-table-column> -->
      </el-table>
    </el-card>
  </div>
</template>

<script>
import http from 'assets/js/http'
import BusSelect from '@/components/Common/BusSelect'

export default {
  name: 'StatisticList',
  components: {
    BusSelect,
  },
  mixins: [http],
  data() {
    return {
      // Search form data
      searchForm: {
        year: '2022',
        bus_id: '',
      },
      // Request parameters
      postData: {
        year: '2022',
        bus_id: '',
      },
      // Table data
      tableData: [],
      loading: false,
    }
  },
  methods: {
    // Get statistics data
    getStatistics() {
      this.loading = true
      this.apiPost('/Web/MeituanPaoluPei/getContractStatistics', this.postData)
        .then((res) => {
          if (res.errorcode === 0) {
            this.tableData = res.data.list || []
            // Add summary row if not present
            this.addSummaryRow()
          } else {
            this.$message.error(res.errormsg || '获取数据失败')
            this.tableData = []
          }
        })
        .catch((error) => {
          console.error('Get statistics error:', error)
          this.$message.error('获取数据失败，请重试')
          this.tableData = []
        })
        .finally(() => {
          this.loading = false
        })
    },

    // Add summary row to table data
    addSummaryRow() {
      if (this.tableData.length === 0) return

      const summary = {
        month: '合计',
        new_merchant_count: 0,
        new_merchant_amount: 0,
        active_contract_count: 0,
        active_contract_amount: 0,
        completed_contract_count: 0,
        completed_contract_amount: 0,
        total_contract_count: 0,
        total_contract_amount: 0,
        completion_rate: 0,
      }

      // Calculate totals
      this.tableData.forEach(row => {
        if (row.month !== '合计') {
          summary.new_merchant_count += Number(row.new_merchant_count) || 0
          summary.new_merchant_amount += Number(row.new_merchant_amount) || 0
          summary.active_contract_count += Number(row.active_contract_count) || 0
          summary.active_contract_amount += Number(row.active_contract_amount) || 0
          summary.completed_contract_count += Number(row.completed_contract_count) || 0
          summary.completed_contract_amount += Number(row.completed_contract_amount) || 0
          summary.total_contract_count += Number(row.total_contract_count) || 0
          summary.total_contract_amount += Number(row.total_contract_amount) || 0
        }
      })

      // Calculate completion rate
      if (summary.total_contract_count > 0) {
        summary.completion_rate = ((summary.completed_contract_count / summary.total_contract_count) * 100).toFixed(1)
      }

      // Remove existing summary row and add new one
      this.tableData = this.tableData.filter(row => row.month !== '合计')
      this.tableData.push(summary)
    },

    // Handle search
    handleSearch() {
      this.postData.year = this.searchForm.year
      this.postData.bus_id = this.searchForm.bus_id
      this.getStatistics()
    },

    // Handle reset
    handleReset() {
      this.searchForm = {
        year: '2022',
        bus_id: '',
      }
      this.postData = {
        year: '2022',
        bus_id: '',
      }
      this.getStatistics()
    },
  },
  created() {
    this.getStatistics()
  },
}
</script>

<style scoped>
/* Container styles */
.container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.box-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: none;
}

/* Header styles */
.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* Search form styles */
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #ebeef5;
}

.search-form-inline {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.search-form-inline .el-form-item {
  margin-right: 20px;
  margin-bottom: 16px;
}

.search-input {
  width: 200px;
}

/* Statistics table styles */
.statistics-table {
  margin-bottom: 20px;
}

.statistics-table .el-table__header-wrapper {
  background-color: #f8f9fa;
}

.statistics-table .el-table__header th {
  background-color: #f8f9fa;
  color: #333;
  font-weight: 600;
  border-bottom: 2px solid #ebeef5;
}

.statistics-table .el-table__body tr:hover>td {
  background-color: #f5f7fa;
}

/* Summary row styling */
.statistics-table .el-table__body tr:last-child {
  background-color: #f0f9ff;
  font-weight: 600;
}

.statistics-table .el-table__body tr:last-child td {
  background-color: #f0f9ff !important;
}

/* Numeric columns styling */
.statistics-table .el-table__body .cell {
  word-break: break-word;
  line-height: 1.4;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .search-form-inline {
    display: block;
  }

  .search-form-inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 16px;
  }

  .search-input {
    width: 100%;
  }

  .statistics-table {
    font-size: 12px;
  }

  .statistics-table .el-table__header th,
  .statistics-table .el-table__body td {
    padding: 8px 4px;
  }
}

/* Table column specific styles */
.statistics-table .el-table-column--selection .cell,
.statistics-table .el-table-column--index .cell {
  text-align: center;
}

/* Highlight percentage column */
.statistics-table .el-table__body td:last-child {
  color: #409eff;
  font-weight: 500;
}
</style>