<template>
  <div class="container">
    <el-card class="box-card">
      <!-- Header -->
      <div slot="header" class="clearfix">
        <span class="page-title">业务续费</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="handleAdd">
          <i class="el-icon-plus"></i>
          业务开通
        </el-button>
      </div>

      <!-- Search Form -->
      <div class="search-form">
        <el-form :model="searchForm" inline class="search-form-inline">
          <el-form-item label="商家">
            <MerchantSelect
              v-model="searchForm.m_id"
              placeholder="请输入商家名称，支持模糊搜索"
              custom-class="search-input"
              clearable
            />
          </el-form-item>
          <el-form-item label="场馆">
            <BusSelect
              v-model="searchForm.bus_id"
              placeholder="请输入场馆名称，支持模糊搜索"
              custom-class="search-input"
              clearable
            />
          </el-form-item>
          <el-form-item label="业务开通时间">
            <el-date-picker
              v-model="searchForm.start_time_range"
              type="daterange"
              range-separator="至"
              start-placeholder="YYYY-MM-DD"
              end-placeholder="YYYY-MM-DD"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              class="date-range-picker"
            ></el-date-picker>
          </el-form-item>
          <el-form-item label="到期时间">
            <el-date-picker
              v-model="searchForm.expire_time_range"
              type="daterange"
              range-separator="至"
              start-placeholder="YYYY-MM-DD"
              end-placeholder="YYYY-MM-DD"
              format="yyyy-MM-dd"
              value-format="yyyy-MM-dd"
              class="date-range-picker"
            ></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- Tabs -->
      <div class="tabs-container">
        <el-tabs v-model="activeTab" @tab-click="handleTabClick" :class="['renewal-tabs', { loading: countLoading }]">
          <el-tab-pane :label="`全部(${tabCounts.all || 0})`" name="all" code=""></el-tab-pane>
          <el-tab-pane :label="`本年度待续费(${tabCounts.pending_renewal || 0})`" name="pending_renewal" code="0"></el-tab-pane>
          <el-tab-pane :label="`到期提醒(${tabCounts.expiring_soon || 0})`" name="expiring_soon" code="1"></el-tab-pane>
          <el-tab-pane :label="`已续费(${tabCounts.renewed || 0})`" name="renewed" code="2"></el-tab-pane>
          <el-tab-pane
            :label="`到期未续费(${tabCounts.expired_not_renewed || 0})`"
            name="expired_not_renewed"
            code="3"
          ></el-tab-pane>
        </el-tabs>
      </div>

      <!-- Data Table -->
      <el-table v-loading="loading" :data="tableData" stripe border style="width: 100%" class="data-table">
        <el-table-column prop="index" label="序号" width="80" align="center">
          <template slot-scope="scope">
            {{ (postData.page_no - 1) * postData.page_size + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="mer_name" label="商家" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.mer_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="bus_name" label="场馆" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.bus_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="open_amount" label="业务开通金额(¥)" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.open_amount ? Number(scope.row.open_amount).toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="annual_fee" label="每年续费金额(¥)" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.annual_fee ? Number(scope.row.annual_fee).toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="start_time" label="业务开通时间" align="center" min-width="160">
          <template slot-scope="scope">
            {{ scope.row.start_time || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="expire_time" label="到期时间" align="center" min-width="160">
          <template slot-scope="scope">
            {{ scope.row.expire_time || '-' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" width="150" fixed="right">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="handleView(scope.row)">查看</el-button>
            <el-button type="text" size="small" @click="handleRenew(scope.row)">续费</el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <footer class="pagination-footer">
        <el-pagination
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
          layout="prev, pager, next, sizes, jumper, ->, total"
          :page-size="postData.page_size"
          :current-page="postData.page_no"
          :total="dataCount"
          :page-sizes="[10, 30, 50, 100]"
        ></el-pagination>
      </footer>
    </el-card>

    <!-- Renewal Modal Dialog -->
    <el-dialog
      title="续费"
      :visible.sync="renewModalVisible"
      width="480px"
      :close-on-click-modal="false"
      :close-on-press-escape="false"
      custom-class="renewal-modal"
      center
    >
      <el-form :model="renewForm" :rules="renewRules" ref="renewForm" label-width="120px" class="renewal-form">
        <el-form-item label="续费金额(¥)" prop="renewal_amount">
          <el-input v-model.number="renewForm.renewal_amount" placeholder="请输入金额" class="renewal-input"></el-input>
        </el-form-item>

        <el-form-item label="续费到期时间" prop="renewal_expiry_date">
          <el-date-picker
            v-model="renewForm.renewal_expiry_date"
            type="date"
            placeholder="YY-MM-DD"
            format="yyyy-MM-dd"
            value-format="yyyy-MM-dd"
            class="renewal-date-picker"
            :picker-options="renewDatePickerOptions"
          ></el-date-picker>
        </el-form-item>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="handleRenewalCancel" class="cancel-button">取消</el-button>
        <el-button type="primary" @click="handleRenewalSubmit" :loading="renewSubmitLoading" class="confirm-button">
          确定
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import http from 'assets/js/http'
import MerchantSelect from '@/components/Payment/MerchantSelect.vue'
import BusSelect from '@/components/Common/BusSelect.vue'

export default {
  name: 'MeituanRenewList',
  mixins: [http],
  components: {
    MerchantSelect,
    BusSelect,
  },
  data() {
    return {
      // Active tab
      activeTab: 'all',
      // Tab counts
      tabCounts: {
        all: 0,
        pending_renewal: 0,
        expiring_soon: 0,
        renewed: 0,
        expired_not_renewed: 0,
      },
      // Search form data
      searchForm: {
        m_id: '',
        bus_id: '',
        start_time_range: [],
        expire_time_range: [],
      },
      // Request parameters
      postData: {
        m_id: '',
        bus_id: '',
        start_time: '',
        end_time: '',
        expire_start_time: '',
        expire_end_time: '',
        status: '',
        page_size: 10,
        page_no: 1,
      },
      // Table data
      tableData: [],
      dataCount: 0,
      loading: false,
      countLoading: false,
      // Renewal modal data
      renewModalVisible: false,
      renewSubmitLoading: false,
      currentRenewalRow: null,
      renewForm: {
        renewal_amount: '',
        renewal_expiry_date: '',
      },
      renewRules: {
        renewal_amount: [
          { required: true, message: '请输入每年续费金额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' },
        ],
        renewal_expiry_date: [
          { required: true, message: '请选择续费到期时间', trigger: 'change' },
          { validator: this.validateExpiryDate, trigger: 'change' },
        ],
      },
      renewDatePickerOptions: {
        disabledDate: (time) => {
          // Disable dates before today
          return time.getTime() < Date.now() - 8.64e7
        },
      },
    }
  },
  methods: {
    // Get list data
    getList() {
      this.loading = true
      this.apiPost('/Web/MeituanPaoluPei/getOpenList', this.postData)
        .then((res) => {
          if (res.errorcode === 0) {
            this.tableData = res.data.list || []
            this.dataCount = res.data.count || 0
          } else {
            this.$message.error(res.errormsg || '获取数据失败')
            this.tableData = []
            this.dataCount = 0
          }
        })
        .catch((error) => {
          console.error('Get list error:', error)
          this.$message.error('获取数据失败，请重试')
          this.tableData = []
          this.dataCount = 0
        })
        .finally(() => {
          this.loading = false
        })
    },

    // Get tab counts
    getTabCounts() {
      this.countLoading = true

      this.apiGet('/Web/MeituanPaoluPei/getTabCount')
        .then((res) => {
          if (res.errorcode === 0) {
            this.tabCounts = {
              all: res.data.all_count || 0,
              pending_renewal: res.data.pending_count || 0,
              expiring_soon: res.data.reminder_count || 0,
              renewed: res.data.renewed_count || 0,
              expired_not_renewed: res.data.expired_count || 0,
            }
          } else {
            console.error('Get tab counts error:', res.errormsg)
            // Reset counts on error
            this.tabCounts = {
              all: 0,
              pending_renewal: 0,
              expiring_soon: 0,
              renewed: 0,
              expired_not_renewed: 0,
            }
          }
        })
        .catch((error) => {
          console.error('Get tab counts error:', error)
          // Reset counts on error
          this.tabCounts = {
            all: 0,
            pending_renewal: 0,
            expiring_soon: 0,
            renewed: 0,
            expired_not_renewed: 0,
          }
        })
        .finally(() => {
          this.countLoading = false
        })
    },

    // Handle tab click
    handleTabClick(tab) {
      this.activeTab = tab.name
      this.postData.status = tab.$attrs.code
      this.postData.page_no = 1
      this.getList()
    },

    // Handle search
    handleSearch() {
      // Convert search form data to request parameters
      this.postData.m_id = this.searchForm.m_id
      this.postData.bus_id = this.searchForm.bus_id

      // Handle start time range
      if (this.searchForm.start_time_range && this.searchForm.start_time_range.length === 2) {
        this.postData.start_time = this.searchForm.start_time_range[0]
        this.postData.end_time = this.searchForm.start_time_range[1]
      } else {
        this.postData.start_time = ''
        this.postData.end_time = ''
      }

      // Handle expire time range
      if (this.searchForm.expire_time_range && this.searchForm.expire_time_range.length === 2) {
        this.postData.expire_start_time = this.searchForm.expire_time_range[0]
        this.postData.expire_end_time = this.searchForm.expire_time_range[1]
      } else {
        this.postData.expire_start_time = ''
        this.postData.expire_end_time = ''
      }

      // Reset to first page
      this.postData.page_no = 1
      this.getList()
      this.getTabCounts()
    },

    // Handle reset
    handleReset() {
      this.searchForm = {
        m_id: '',
        bus_id: '',
        start_time_range: [],
        expire_time_range: [],
      }
      this.postData = {
        m_id: '',
        bus_id: '',
        start_time: '',
        end_time: '',
        expire_start_time: '',
        expire_end_time: '',
        status: this.postData.status,
        page_size: this.postData.page_size,
        page_no: 1,
      }
      this.getList()
      this.getTabCounts()
    },

    // Handle add new renewal
    handleAdd() {
      this.$router.push({ name: 'meituan_save' })
    },

    // Handle renew action
    handleRenew(row) {
      this.openRenewalModal(row)
    },

    // Open renewal modal
    openRenewalModal(row) {
      this.currentRenewalRow = row
      // Pre-fill the renewal amount with annual fee from the record
      this.renewForm = {
        renewal_amount: row.annual_fee || '',
        renewal_expiry_date: '',
      }
      // Reset form validation
      if (this.$refs.renewForm) {
        this.$refs.renewForm.resetFields()
      }
      this.renewModalVisible = true
    },

    // Validate expiry date
    validateExpiryDate(rule, value, callback) {
      if (!value) {
        callback(new Error('请选择续费到期时间'))
      } else if (new Date(value) <= new Date()) {
        callback(new Error('到期时间不能小于或等于当前日期'))
      } else {
        callback()
      }
    },

    // Handle renewal form submission
    handleRenewalSubmit() {
      // Validate form
      this.$refs.renewForm.validate((valid) => {
        if (valid) {
          this.performRenew(this.currentRenewalRow, this.renewForm)
        }
      })
    },

    // Handle renewal cancel
    handleRenewalCancel() {
      this.renewModalVisible = false
      this.renewForm = {
        renewal_amount: '',
        renewal_expiry_date: '',
      }
      this.currentRenewalRow = null
    },

    // Perform renewal operation
    performRenew(row, formData) {
      this.renewSubmitLoading = true

      // Prepare the API data
      const postData = {
        id: row.id,
        renewal_fee: formData.renewal_amount,
        expire_time: formData.renewal_expiry_date,
      }

      this.apiPost('/Web/MeituanPaoluPei/renew', postData)
        .then((res) => {
          if (res.errorcode === 0) {
            this.$message.success('续费成功')
            this.renewModalVisible = false
            this.handleRenewalCancel() // Reset form
            this.getList()
            this.getTabCounts() // Refresh tab counts after renewal
          } else {
            this.$message.error(res.errormsg || '续费失败')
          }
        })
        .catch((error) => {
          console.error('Renew error:', error)
          this.$message.error('续费失败，请重试')
        })
        .finally(() => {
          this.renewSubmitLoading = false
        })
    },

    // Handle view record
    handleView(row) {
      this.$router.push({
        name: 'meituan_save',
        params: { id: row.id },
      })
    },

    // Handle page change
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage
      this.getList()
    },

    // Handle page size change
    handleSizeChange(size) {
      this.postData.page_no = 1
      this.postData.page_size = size
      this.getList()
    },
  },

  created() {
    this.getList()
    this.getTabCounts()
  },
}
</script>

<style scoped>
.container {
  padding: 20px;
  margin: 0 auto;
  background-color: #f5f5f5;
}

.box-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

/* Header styles */
.page-title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

/* Search form styles */
.search-form {
  padding: 20px 0;
  border-bottom: 1px solid #ebeef5;
  margin-bottom: 20px;
}

.search-form-inline {
  margin-bottom: 16px;
}

.search-form-inline .el-form-item {
  margin-bottom: 16px;
  margin-right: 20px;
}

.search-form-inline .el-form-item__label {
  font-weight: 500;
  color: #333;
  width: 80px !important;
}

.search-input {
  width: 200px;
}

.date-range-picker {
  width: 300px;
}

/* Table styles */
.data-table {
  margin-bottom: 20px;
}

.data-table .el-table__header-wrapper th {
  background-color: #fafafa;
  color: #333;
  font-weight: 500;
}

.data-table .el-table__body-wrapper .el-table__row:hover {
  background-color: #f5f7fa;
}

/* Tabs styles */
.tabs-container {
  margin-bottom: 20px;
}

.renewal-tabs {
  border-bottom: 1px solid #ebeef5;
}

.renewal-tabs .el-tabs__header {
  margin: 0 0 15px;
}

.renewal-tabs .el-tabs__nav-wrap::after {
  height: 1px;
}

.renewal-tabs .el-tabs__item {
  padding: 0 20px;
  height: 40px;
  line-height: 40px;
  font-size: 14px;
  color: #666;
  position: relative;
}

.renewal-tabs .el-tabs__item.is-active {
  color: #409eff;
  font-weight: 500;
}

.renewal-tabs .el-tabs__item:hover {
  color: #409eff;
}

.renewal-tabs .el-tabs__active-bar {
  background-color: #409eff;
}

/* Tab count styling */
.renewal-tabs .el-tabs__item {
  white-space: nowrap;
}

/* Loading state for tabs */
.renewal-tabs.loading .el-tabs__item {
  opacity: 0.6;
}

/* Pagination styles */
.pagination-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
}

/* Element UI customization */
.el-card__header {
  padding: 18px 20px;
  border-bottom: 1px solid #ebeef5;
  background-color: #fafafa;
}

.el-card__body {
  padding: 20px;
}

.el-button--text {
  color: #409eff;
  font-size: 14px;
}

.el-button--text:hover {
  color: #66b1ff;
}

.el-form--inline .el-form-item {
  margin-right: 20px;
}

.el-table .el-button--text {
  color: #409eff;
  font-size: 14px;
  margin-right: 8px;
}

.el-table .el-button--text:hover {
  color: #66b1ff;
}

.el-table .el-button--text:last-child {
  margin-right: 0;
}

/* Responsive design */
@media (max-width: 1200px) {
  .search-form-inline .el-form-item {
    margin-right: 10px;
  }

  .search-input {
    width: 180px;
  }

  .date-range-picker {
    width: 280px;
  }
}

@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .search-form-inline {
    display: block;
  }

  .search-form-inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 16px;
  }

  .search-input,
  .date-range-picker {
    width: 100%;
  }

  .pagination-footer {
    justify-content: center;
  }

  .data-table {
    font-size: 12px;
  }

  .renewal-tabs .el-tabs__item {
    padding: 0 10px;
    font-size: 12px;
  }
}

/* Renewal Modal Styles */
.renewal-modal {
  border-radius: 8px;
}

.renewal-modal .el-dialog__header {
  background-color: #fafafa;
  padding: 18px 24px;
  border-bottom: 1px solid #ebeef5;
}

.renewal-modal .el-dialog__title {
  font-size: 16px;
  font-weight: 500;
  color: #333;
}

.renewal-modal .el-dialog__body {
  padding: 24px;
}

.renewal-form {
  margin-bottom: 0;
}

.renewal-form .el-form-item {
  margin-bottom: 24px;
}

.renewal-form .el-form-item__label {
  font-weight: 500;
  color: #333;
  line-height: 40px;
}

.renewal-input,
.renewal-date-picker {
  width: 300px;
}

.renewal-input .el-input__inner,
.renewal-date-picker .el-input__inner {
  height: 40px;
  line-height: 40px;
  border: 1px solid #dcdfe6;
  border-radius: 4px;
  font-size: 14px;
}

.renewal-input.input-error .el-input__inner,
.renewal-date-picker.input-error .el-input__inner {
  border-color: #f56c6c;
}

.renewal-input .el-input__inner:focus,
.renewal-date-picker .el-input__inner:focus {
  border-color: #409eff;
  outline: none;
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.error-message {
  color: #f56c6c;
  font-size: 12px;
  margin-top: 4px;
  line-height: 1;
  padding-left: 2px;
}

.dialog-footer {
  text-align: center;
  padding: 0 24px 24px;
}

.dialog-footer .cancel-button {
  padding: 10px 24px;
  font-size: 14px;
  border-radius: 4px;
  color: #666;
  border-color: #dcdfe6;
  background-color: #fff;
  margin-right: 12px;
  min-width: 80px;
}

.dialog-footer .cancel-button:hover {
  color: #409eff;
  border-color: #409eff;
  background-color: #ecf5ff;
}

.dialog-footer .confirm-button {
  padding: 10px 24px;
  font-size: 14px;
  border-radius: 4px;
  background-color: #409eff;
  border-color: #409eff;
  color: #fff;
  min-width: 80px;
}

.dialog-footer .confirm-button:hover {
  background-color: #66b1ff;
  border-color: #66b1ff;
}

.dialog-footer .confirm-button.is-loading {
  pointer-events: none;
}

/* Modal overlay styling */
.renewal-modal .el-dialog__wrapper {
  background-color: rgba(0, 0, 0, 0.5);
}

/* Form validation styling override */
.renewal-form .el-form-item.is-error .el-input__inner,
.renewal-form .el-form-item.is-error .el-textarea__inner {
  border-color: #f56c6c;
}

.renewal-form .el-form-item__error {
  display: none; /* Hide default Element UI error messages */
}
</style>
