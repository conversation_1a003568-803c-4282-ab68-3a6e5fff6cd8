<template>
  <div class="container">
    <el-card class="box-card">
      <!-- Header -->
      <div slot="header" class="clearfix">
        <span class="page-title">履约扣款记录</span>
      </div>

      <!-- Search Form -->
      <div class="search-form">
        <el-form :model="searchForm" inline class="search-form-inline">
          <el-form-item label="美团订单编号">
            <el-input v-model="searchForm.meituan_order_no" placeholder="请输入美团订单编号" class="search-input"
              clearable></el-input>
          </el-form-item>
          <el-form-item label="合同编号">
            <el-input v-model="searchForm.contract_no" placeholder="请输入合同编号" class="search-input" clearable></el-input>
          </el-form-item>
          <el-form-item label="商品名称">
            <el-input v-model="searchForm.product_name" placeholder="请输入商品名称" class="search-input" clearable></el-input>
          </el-form-item>
          <el-form-item label="商品名称">
            <el-input v-model="searchForm.product_name_2" placeholder="请输入商品名称" class="search-input"
              clearable></el-input>
          </el-form-item>
          <el-form-item label="签约会员">
            <el-input v-model="searchForm.contract_member" placeholder="请输入签约会员" class="search-input"
              clearable></el-input>
          </el-form-item>
          <el-form-item label="会员联系方式">
            <el-input v-model="searchForm.member_contact" placeholder="请输入会员联系方式" class="search-input"
              clearable></el-input>
          </el-form-item>
          <el-form-item label="业绩归属">
            <el-select v-model="searchForm.sale_id" placeholder="请选择" class="w-300">
              <el-option v-for="item in saleList" :key="item.id" :label="item.sale_name" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="扣款状态">
            <el-select v-model="searchForm.deduction_status" placeholder="请选择扣款状态" class="search-input" clearable>
              <el-option label="全部" value=""></el-option>
              <el-option label="成功" value="success"></el-option>
              <el-option label="失败" value="failed"></el-option>
              <el-option label="处理中" value="processing"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="商家">
            <MerchantSelect v-model="searchForm.m_id" placeholder="请输入商家名称" custom-class="search-input" clearable />
          </el-form-item>
          <el-form-item label="场馆">
            <BusSelect v-model="searchForm.bus_id" placeholder="请输入场馆名称" custom-class="search-input" clearable />
          </el-form-item>
          <el-form-item label="扣款时间">
            <el-date-picker v-model="searchForm.deduction_time_range" type="daterange" range-separator="至"
              start-placeholder="YYYY-MM-DD" end-placeholder="YYYY-MM-DD" format="yyyy-MM-dd" value-format="yyyy-MM-dd"
              class="date-range-picker"></el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="handleSearch" :loading="loading">查询</el-button>
            <el-button @click="handleReset">重置</el-button>
          </el-form-item>
        </el-form>
      </div>

      <!-- Data Table -->
      <el-table v-loading="loading" :data="tableData" stripe border style="width: 100%" class="data-table">
        <el-table-column prop="index" label="序号" width="80" align="center">
          <template slot-scope="scope">
            {{ (postData.page_no - 1) * postData.page_size + scope.$index + 1 }}
          </template>
        </el-table-column>
        <el-table-column prop="merchant_name" label="商家" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.merchant_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="venue_name" label="场馆" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.venue_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="meituan_store_no" label="美团门店编号" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.meituan_store_no || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="contract_no" label="合同编号" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.contract_no || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="product_name" label="商品名称" align="center" min-width="150">
          <template slot-scope="scope">
            {{ scope.row.product_name || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="meituan_product_code" label="美团商品编号或编码" align="center" min-width="160">
          <template slot-scope="scope">
            {{ scope.row.meituan_product_code || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="contract_member" label="签约会员" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.contract_member || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="member_contact_method" label="会员联系方式" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.member_contact_method || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="meituan_deduction_percent" label="美团扣款(%)" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.meituan_deduction_percent ? Number(scope.row.meituan_deduction_percent).toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="business_deduction" label="业务扣款" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.business_deduction || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="deduction_amount" label="扣款金额(¥)" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.deduction_amount ? Number(scope.row.deduction_amount).toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="deduction_time" label="扣款时间" align="center" min-width="160">
          <template slot-scope="scope">
            {{ scope.row.deduction_time || '-' }}
          </template>
        </el-table-column>
        <el-table-column prop="meituan_revenue_amount" label="美团收入金额(¥)" align="center" min-width="140">
          <template slot-scope="scope">
            {{ scope.row.meituan_revenue_amount ? Number(scope.row.meituan_revenue_amount).toFixed(2) : '0.00' }}
          </template>
        </el-table-column>
        <el-table-column prop="deduction_status" label="扣款状态" align="center" min-width="120">
          <template slot-scope="scope">
            {{ scope.row.deduction_status || '-' }}
          </template>
        </el-table-column>
      </el-table>

      <!-- Pagination -->
      <footer class="pagination-footer">
        <el-pagination @current-change="handleCurrentChange" @size-change="handleSizeChange"
          layout="prev, pager, next, sizes, jumper, ->, total" :page-size="postData.page_size"
          :current-page="postData.page_no" :total="dataCount" :page-sizes="[10, 30, 50, 100]"></el-pagination>
      </footer>
    </el-card>
  </div>
</template>

<script>
import http from 'assets/js/http'
import MerchantSelect from '@/components/Payment/MerchantSelect'
import BusSelect from '@/components/Common/BusSelect'

export default {
  name: 'RecordList',
  mixins: [http],
  components: {
    MerchantSelect,
    BusSelect,
  },
  data() {
    return {
      // Search form data
      searchForm: {
        condition_type: '',
        contract_no: '',
        product_name: '',
        product_name_2: '',
        contract_member: '',
        member_contact: '',
        business_deduction: '',
        deduction_status: '',
        merchant_name: '',
        venue_name: '',
        deduction_time_range: [],
      },
      // Request parameters
      postData: {
        condition_type: '',
        contract_no: '',
        product_name: '',
        product_name_2: '',
        contract_member: '',
        member_contact: '',
        business_deduction: '',
        deduction_status: '',
        merchant_name: '',
        venue_name: '',
        deduction_start_time: '',
        deduction_end_time: '',
        page_size: 10,
        page_no: 1,
      },
      // Table data
      tableData: [],
      dataCount: 0,
      loading: false,
      saleList: [],
    }
  },
  methods: {
    // Get list data
    getList() {
      this.loading = true
      this.apiPost('/Web/MeituanPaoluPei/getDeductionRecordList', this.postData)
        .then((res) => {
          if (res.errorcode === 0) {
            this.tableData = res.data.list || []
            this.dataCount = res.data.count || 0
          } else {
            this.$message.error(res.errormsg || '获取数据失败')
            this.tableData = []
            this.dataCount = 0
          }
        })
        .catch((error) => {
          console.error('Get list error:', error)
          this.$message.error('获取数据失败，请重试')
          this.tableData = []
          this.dataCount = 0
        })
        .finally(() => {
          this.loading = false
        })
    },
    getSaleList() {
      this.$service.post('/Web/ZhimaFitPay/sales', {
        _no_paginate: true,
        status: 0
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.saleList = res.data.data;
        } else {
          this.$message.error(res.data.errormsg);
        }
      }).catch(e => {
        throw new Error(e)
      })
    },

    // Handle search
    handleSearch() {
      // Convert search form data to request parameters
      this.postData.condition_type = this.searchForm.condition_type
      this.postData.contract_no = this.searchForm.contract_no
      this.postData.product_name = this.searchForm.product_name
      this.postData.product_name_2 = this.searchForm.product_name_2
      this.postData.contract_member = this.searchForm.contract_member
      this.postData.member_contact = this.searchForm.member_contact
      this.postData.business_deduction = this.searchForm.business_deduction
      this.postData.deduction_status = this.searchForm.deduction_status
      this.postData.merchant_name = this.searchForm.merchant_name
      this.postData.venue_name = this.searchForm.venue_name

      // Handle deduction time range
      if (this.searchForm.deduction_time_range && this.searchForm.deduction_time_range.length === 2) {
        this.postData.deduction_start_time = this.searchForm.deduction_time_range[0]
        this.postData.deduction_end_time = this.searchForm.deduction_time_range[1]
      } else {
        this.postData.deduction_start_time = ''
        this.postData.deduction_end_time = ''
      }

      // Reset to first page
      this.postData.page_no = 1
      this.getList()
    },

    // Handle reset
    handleReset() {
      this.searchForm = {
        condition_type: '',
        contract_no: '',
        product_name: '',
        product_name_2: '',
        contract_member: '',
        member_contact: '',
        business_deduction: '',
        deduction_status: '',
        merchant_name: '',
        venue_name: '',
        deduction_time_range: [],
      }
      this.postData = {
        condition_type: '',
        contract_no: '',
        product_name: '',
        product_name_2: '',
        contract_member: '',
        member_contact: '',
        business_deduction: '',
        deduction_status: '',
        merchant_name: '',
        venue_name: '',
        deduction_start_time: '',
        deduction_end_time: '',
        page_size: this.postData.page_size,
        page_no: 1,
      }
      this.getList()
    },

    // Handle pagination - current page change
    handleCurrentChange(page) {
      this.postData.page_no = page
      this.getList()
    },

    // Handle pagination - page size change
    handleSizeChange(size) {
      this.postData.page_size = size
      this.postData.page_no = 1
      this.getList()
    },
  },
  created() {
    this.getList()
    this.getSaleList()
  },
}
</script>

<style scoped>
/* Container styles */
.container {
  padding: 20px;
  margin: 0 auto;
  background-color: #f5f5f5;
}

.box-card {
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  border: none;
}

/* Header styles */
.page-title {
  font-size: 18px;
  font-weight: 600;
  color: #333;
}

/* Search form styles */
.search-form {
  margin-bottom: 20px;
  padding: 20px;
  background-color: #fafafa;
  border-radius: 6px;
  border: 1px solid #ebeef5;
}

.search-form-inline {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.search-form-inline .el-form-item {
  margin-right: 20px;
  margin-bottom: 16px;
}

.search-input {
  width: 200px;
}

.date-range-picker {
  width: 240px;
}

/* Form row break for two-row layout */
.form-row-break {
  width: 100%;
  height: 0;
  flex-basis: 100%;
}

/* Data table styles */
.data-table {
  margin-bottom: 20px;
}

.data-table .el-table__header-wrapper {
  background-color: #f8f9fa;
}

.data-table .el-table__header th {
  background-color: #f8f9fa;
  color: #333;
  font-weight: 600;
  border-bottom: 2px solid #ebeef5;
}

.data-table .el-table__body tr:hover>td {
  background-color: #f5f7fa;
}

.data-table .el-button--text {
  color: #409eff;
  padding: 0;
  margin-right: 10px;
}

.data-table .el-button--text:hover {
  color: #66b1ff;
}

/* Status tag styles */
.data-table .el-tag {
  border-radius: 4px;
  font-size: 12px;
  padding: 0 8px;
  height: 24px;
  line-height: 22px;
}

.data-table .el-tag--success {
  background-color: #f0f9ff;
  border-color: #67c23a;
  color: #67c23a;
}

.data-table .el-tag--warning {
  background-color: #fdf6ec;
  border-color: #e6a23c;
  color: #e6a23c;
}

.data-table .el-tag--danger {
  background-color: #fef0f0;
  border-color: #f56c6c;
  color: #f56c6c;
}

.data-table .el-tag--info {
  background-color: #f4f4f5;
  border-color: #909399;
  color: #909399;
}

/* Pagination styles */
.pagination-footer {
  display: flex;
  justify-content: flex-end;
  padding: 20px 0;
  border-top: 1px solid #ebeef5;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .search-form-inline {
    display: block;
  }

  .search-form-inline .el-form-item {
    display: block;
    margin-right: 0;
    margin-bottom: 16px;
  }

  .search-input,
  .date-range-picker {
    width: 100%;
  }

  .pagination-footer {
    justify-content: center;
  }

  .data-table {
    font-size: 12px;
  }

  .data-table .el-table__header th,
  .data-table .el-table__body td {
    padding: 8px 4px;
  }
}

/* Table column specific styles */
.data-table .el-table__body .cell {
  word-break: break-word;
  line-height: 1.4;
}

/* Numeric columns alignment */
.data-table .el-table-column--selection .cell,
.data-table .el-table-column--index .cell {
  text-align: center;
}
</style>
