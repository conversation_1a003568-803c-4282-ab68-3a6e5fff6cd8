<template>
  <div class="container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span v-if="reviewMode">查看</span>
        <span v-else>业务开通</span>
        <el-button style="float: right; padding: 3px 0" type="text" @click="goBack">返回</el-button>
      </div>

      <!-- 提示信息 -->
      <div v-if="!reviewMode" class="notice-banner">
        <i class="el-icon-warning"></i>
        <span>请注意：美团跑路赔功能仅面向已经入驻了美团的商家开通，如果商家未入驻/开通美团，将无法与美团的商品团购关联。</span>
      </div>

      <el-form :model="form" :rules="rules" ref="form" label-width="140px" status-icon class="meituan-form">
        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="商家名称" prop="m_id">
              <MerchantSelect v-model="form.m_id" custom-class="w-300" :disabled="reviewMode" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="请选择场馆" prop="bus_id">
              <BusSelect
                v-model="form.bus_id"
                placeholder="门店列表"
                custom-class="w-300"
                filterable
                clearable
                :disabled="reviewMode"
              />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="业务开通金额(¥)" prop="open_amount">
              <el-input
                v-model.number="form.open_amount"
                placeholder="请输入金额"
                class="form-input"
                :disabled="reviewMode"
              ></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="每年续费金额(¥)" prop="annual_fee">
              <el-input
                v-model.number="form.annual_fee"
                placeholder="请输入金额"
                class="form-input"
                :disabled="reviewMode"
              ></el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="40">
          <el-col :span="24">
            <el-form-item label="美团跑路赔费率(%)" prop="business_description">
              <div>2.50%</div>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="40">
          <el-col :span="12">
            <el-form-item label="业务开通时间" prop="start_time">
              <el-date-picker
                v-model="form.start_time"
                type="datetime"
                placeholder="YYYY-MM-DD HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                :picker-options="startDatePickerOptions"
                :disabled="reviewMode"
                style="width: 300px"
              ></el-date-picker>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="到期时间" prop="expire_time">
              <el-date-picker
                v-model="form.expire_time"
                type="datetime"
                placeholder="YYYY-MM-DD HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                value-format="yyyy-MM-dd HH:mm:ss"
                :picker-options="endDatePickerOptions"
                :disabled="reviewMode"
                style="width: 300px"
              ></el-date-picker>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row v-if="form.status" :gutter="40">
          <el-col :span="24">
            <el-form-item label="续费状态" prop="status">
              <!-- 0=未续费, 1=到期提醒, 2=已续费, 3=到期未续费(已过期) -->
              <el-tag v-if="form.status === 0" type="info">未续费</el-tag>
              <el-tag v-if="form.status === 1" type="warning">到期提醒</el-tag>
              <el-tag v-if="form.status === 2" type="success">已续费</el-tag>
              <el-tag v-if="form.status === 3" type="danger">到期未续费(已过期)</el-tag>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item v-if="!reviewMode" class="form-buttons" label-width="0">
          <el-button type="primary" @click="submitForm" :loading="submitLoading" class="confirm-btn">确定</el-button>
          <el-button @click="cancelForm" class="cancel-btn">取消</el-button>
        </el-form-item>
        <el-form-item v-else class="form-buttons" label-width="0">
          <el-button @click="goBack" class="cancel-btn">返回</el-button>
        </el-form-item>
      </el-form>
    </el-card>
  </div>
</template>

<script>
import http from 'assets/js/http'
import MerchantSelect from '@/components/Payment/MerchantSelect.vue'
import BusSelect from '@/components/Common/BusSelect.vue'

export default {
  name: 'MeituanSave',
  mixins: [http],
  components: {
    MerchantSelect,
    BusSelect,
  },
  props: {
    id: String,
  },
  data() {
    return {
      reviewMode: false,
      form: {
        m_id: '',
        bus_id: '',
        open_amount: '',
        annual_fee: '',
        start_time: '',
        expire_time: '',
        status: '',
      },
      rules: {
        m_id: [{ required: true, message: '请输入商家名称，支持模糊搜索', trigger: 'change' }],
        bus_id: [{ required: true, message: '请选择场馆', trigger: 'change' }],
        open_amount: [
          { required: true, message: '请输入业务开通金额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' },
        ],
        annual_fee: [
          { required: true, message: '请输入每年续费金额', trigger: 'blur' },
          { pattern: /^\d+(\.\d{1,2})?$/, message: '请输入正确的金额格式', trigger: 'blur' },
        ],
        start_time: [{ required: true, message: '请选择业务开通时间', trigger: 'change' }],
        expire_time: [
          { required: true, message: '请输入到期时间，到期时间不能小于业务开通时间', trigger: 'change' },
          { validator: this.validateExpiryDate, trigger: 'change' },
        ],
      },
      submitLoading: false,
      startDatePickerOptions: {
        disabledDate: (time) => {
          return time.getTime() < Date.now() - 8.64e7 // 不能选择今天之前的日期
        },
      },
      endDatePickerOptions: {
        disabledDate: (time) => {
          if (this.form.start_time) {
            return time.getTime() < new Date(this.form.start_time).getTime()
          }
          return time.getTime() < Date.now() - 8.64e7
        },
      },
    }
  },
  methods: {
    // 验证到期时间
    validateExpiryDate(rule, value, callback) {
      if (!value) {
        callback(new Error('请选择到期时间'))
      } else if (this.form.start_time && new Date(value) <= new Date(this.form.start_time)) {
        callback(new Error('到期时间不能小于或等于业务开通时间'))
      } else {
        callback()
      }
    },

    // 获取详情
    getDetail() {
      this.apiGet('/Web/MeituanPaoluPei/getInfo', { id: this.id }).then((res) => {
        if (res.errorcode === 0) {
          this.form = {
            ...res.data.info,
            m_id: res.data.info.mer_id,
            bus_id: Number(res.data.info.bus_id),
            status: Number(res.data.info.status),
          }
        } else {
          this.$message.error(res.errormsg)
        }
      })
    },

    // 提交表单
    submitForm() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.submitLoading = true

          // 构建提交数据
          const postData = {
            m_id: this.form.m_id,
            bus_id: this.form.bus_id,
            open_amount: this.form.open_amount,
            annual_fee: this.form.annual_fee,
            start_time: this.form.start_time,
            expire_time: this.form.expire_time,
            meituan_rate: 2.5,
          }

          // 调用API保存数据
          this.apiPost('/Web/MeituanPaoluPei/open', postData)
            .then((res) => {
              if (res.errorcode === 0) {
                this.$message.success('开通成功，立即跳转到对应页面的功能菜单')
                setTimeout(() => {
                  this.goBack()
                }, 2000)
              } else {
                this.$message.error(res.errormsg || '保存失败')
              }
            })
            .catch((error) => {
              console.error('Save error:', error)
              this.$message.error('保存失败，请重试')
            })
            .finally(() => {
              this.submitLoading = false
            })
        } else {
          this.$message.error('请检查表单信息')
          return false
        }
      })
    },

    // 取消表单
    cancelForm() {
      this.$confirm('确认要取消吗？未保存的数据将丢失', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(() => {
          this.goBack()
        })
        .catch(() => {
          // 用户取消
        })
    },

    // 返回上一页
    goBack() {
      this.$router.go(-1)
    },

    // 重置错误状态
    resetErrors() {
      Object.keys(this.errors).forEach((key) => {
        this.errors[key] = false
      })
    },
  },

  created() {
    if (this.id) {
      this.reviewMode = true
      this.getDetail()
    }
  },

  watch: {
    // 监听业务开通时间变化，更新到期时间的可选范围
    'form.start_time'(newVal) {
      if (newVal && this.form.expire_time && new Date(this.form.expire_time) <= new Date(newVal)) {
        this.form.expire_time = ''
      }
    },
  },
}
</script>

<style scoped>
.container {
  padding: 20px;
  margin: 0 auto;
}

.box-card {
  max-width: 996px;
  border-radius: 8px;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.notice-banner {
  background-color: #fff7e6;
  border: 1px solid #ffd591;
  border-radius: 4px;
  padding: 12px 16px;
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  color: #d48806;
}

.notice-banner i {
  margin-right: 8px;
  font-size: 16px;
}

.meituan-form {
  padding: 20px 0;
}

.form-input {
  width: 300px;
}

.form-buttons {
  margin-top: 40px;
  text-align: center;
}

.confirm-btn {
  background-color: #1890ff;
  border-color: #1890ff;
  padding: 10px 32px;
  font-size: 14px;
  border-radius: 4px;
  margin-right: 16px;
}

.confirm-btn:hover {
  background-color: #40a9ff;
  border-color: #40a9ff;
}

.cancel-btn {
  padding: 10px 32px;
  font-size: 14px;
  border-radius: 4px;
  color: #666;
  border-color: #d9d9d9;
}

.cancel-btn:hover {
  color: #1890ff;
  border-color: #1890ff;
}

/* Element UI form item label styling */
.meituan-form .el-form-item__label {
  font-weight: 500;
  color: #333;
}

/* Element UI input styling */
.meituan-form .el-input__inner {
  border-radius: 4px;
  border: 1px solid #d9d9d9;
  height: 40px;
  line-height: 40px;
}

.meituan-form .el-input__inner:focus {
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* Element UI select styling */
.meituan-form .el-select .el-input__inner {
  cursor: pointer;
}

/* Element UI date picker styling */
.meituan-form .el-date-editor.el-input {
  width: 100%;
}

.meituan-form .el-date-editor .el-input__inner {
  padding-left: 30px;
}

/* Responsive design */
@media (max-width: 768px) {
  .container {
    padding: 10px;
  }

  .meituan-form .el-col {
    margin-bottom: 0;
  }

  .form-buttons {
    margin-top: 20px;
  }

  .confirm-btn,
  .cancel-btn {
    width: 100%;
    margin: 5px 0;
  }
}
</style>
