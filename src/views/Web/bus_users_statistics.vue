<template>
  <div class="main-container-wrap">
    <!--内容区头部-->
    <div class="index-nav">
      场馆统计 >> 场馆会员统计
    </div>
    <div class="main-container">
      <div class="main-content">
        <div class="search-box">
          <div class="block">
            <el-date-picker v-model="value1" type="date" size="small" placeholder="选择日期">
            </el-date-picker>
          </div>
          <el-button size="small" v-on:click="timequery('yesterday')">昨天</el-button>
          <el-button size="small" v-on:click="timequery('before')">前天</el-button>
          <el-button size="small" v-on:click="timequery('thismonth')">本月</el-button>
          <el-input v-model="input" placeholder="场馆名称" size="small" class="search-input"></el-input>
          <el-button size="small" class="query" v-on:click="query">查询</el-button>
        </div>
        <div class="table-container">
          <el-table :data="tableData" stripe border>
            <el-table-column prop="date" min-width="108" label="时间">
            </el-table-column>
            <el-table-column prop="bus_name" min-width="148" label="场馆名称">
            </el-table-column>
            <el-table-column prop="23" min-width="108" label="客户总数">
            </el-table-column>
            <el-table-column prop="24" min-width="108" label="新增客户">
            </el-table-column>
            <el-table-column prop="25" min-width="108" label="过期会员">
            </el-table-column>
            <el-table-column prop="26" min-width="108" label="体验卡会员">
            </el-table-column>
            <el-table-column prop="27" min-width="108" label="有效会员">
            </el-table-column>
            <el-table-column prop="28" min-width="108" label="体验未购卡">
            </el-table-column>
            <el-table-column prop="29" min-width="108" label="线上购卡数">
            </el-table-column>
            <el-table-column prop="30" min-width="108" label="线下购卡数">
            </el-table-column>
          </el-table>
        </div>
        <div class="pos-rel p-t-20">
          <div class="block pages">
            <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="limit" :current-page="currentPage" :total="pageCount">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  export default {
    name: 'bus_user_statistics',
    data() {
      return {
        tableData: [],
        value1: '',
        input: '',
        pageCount: null,
        currentPage: 1,
        begin_time: '',
        end_time: ''
      };
    },
    created() {
      this.init();
    },
    methods: {
      get_users_statistical() {
        let postData = {
          page_size: 10,
          page_no: this.currentPage
        };
        if (this.value1 != '') {
          postData.begin_time = Date.parse(new Date(this.value1)) / 1000;
          postData.end_time = postData.begin_time + 24 * 3600 - 1;
        }
        if (this.input != '') {
          postData.param = this.input;
        }
        if (this.begin_time != '') {
          postData.begin_time = this.begin_time;
          postData.end_time = this.end_time;
        }
        this.apiPost('web/businessStatistical/bus_user_statistical', postData).then(res => {
          this.handelResponse(res, data => {
            this.tableData = res.data.list;
            this.pageCount = res.data.pageCount * 10;
          });
        });
      },
      query() {
        this.begin_time = '';
        this.end_time = '';
        _g.openGlobalLoading();
        this.get_users_statistical();
      },
      handleCurrentChange(page) {
        _g.openGlobalLoading();
        this.currentPage = page;
        this.get_users_statistical();
      },
      timequery(flag) {
        _g.openGlobalLoading();
        this.value1 = '';
        this.input = '';
        let now = new Date();
        if (flag == 'yesterday') {
          this.begin_time = Date.parse(now.getFullYear() + '-' + (now.getMonth() + 1) + '-' + (now.getDate() - 1)) / 1000;
          this.end_time = this.begin_time + 24 * 3600 - 1;
        }
        if (flag == 'before') {
          this.begin_time = Date.parse(now.getFullYear() + '-' + (now.getMonth() + 1) + '-' + (now.getDate() - 2)) / 1000;
          this.end_time = this.begin_time + 24 * 3600 - 1;
        }
        if (flag == 'thismonth') {
          this.begin_time = Date.parse(now.getFullYear() + '-' + (now.getMonth() + 1) + '-01 0:0:0') / 1000;
          this.end_time =
            Date.parse(now.getFullYear() + '-' + (now.getMonth() + 1) + '-' + now.getDate() + ' 23:59:59') / 1000;
        }
        this.get_users_statistical();
      },
      init() {
        this.get_users_statistical();
      }
    },
    watch: {
      $route(to, from) {
        this.init();
      }
    },
    mixins: [http]
  };
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
  .index-nav {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    padding-left: 10px;
    background-color: #e4e4e4;
  }
  .main-container {
    margin-top: 40px;
    margin-left: 46px;
    margin-right: 60px;
    background-color: #ffffff;
    border-radius: 15px;
  }
  .main-content {
    padding-top: 16px;
    padding-left: 22px;
    padding-bottom: 70px;
  }
  .search-box {
    height: 34px;
  }
  .block {
    margin-right: 20px;
    float: left;
  }
  .el-date-editor {
    width: 140px;
  }
  .el-input--small input {
    border-radius: 30px;
  }
  .search-box button {
    height: 30px;
    vertical-align: bottom;
    border-radius: 30px;
    width: 60px;
  }
  .el-input {
    width: 140px;
  }
  input {
    border-radius: 10px;
  }
  .el-input__inner {
    border-radius: 30px;
  }
  .search-box .query {
    width: 140px;
    color: #ffffff;
    font-size: 14px;
    border: 0;
    background-color: #fa571c;
  }
  .table-container {
    margin-top: 12px;
  }
</style>
