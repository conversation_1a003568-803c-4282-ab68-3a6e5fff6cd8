<template>

  <div class="container">
    <header>
      <div class="fl">
        <el-button type="success" size="small" icon="el-icon-plus" @click="$router.push({ name: 'noticeSend' })">
          通知发布
        </el-button>
      </div>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column align="center" prop="bus_names" label="场馆名称"></el-table-column>
      <el-table-column align="center" prop="create_time" label="发布时间"></el-table-column>
      <el-table-column align="center" label="操作" width="100">
        <template scope="scope">
          <el-button size="small" type="primary" @click="showDetail(scope.row)">内容</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pos-rel p-t-20 ovf-hd flexend">
      <div class="block">
        <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
        </el-pagination>
      </div>
    </div>
    <el-dialog title="内容详情" :visible.sync="showModal" :close-on-click-modal="false">
      <Editor v-model="detail.content" disabled></Editor>
    </el-dialog>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  import Editor from 'components/form/Editor';
  export default {
    name: 'noticeList',
    data() {
      return {
        tableData: [],
        detail: '',
        showModal: false,
        postData: {
          page_size: 10,
          page_no: 1
        },
        dataCount: 0
      };
    },
    components: { Editor },
    methods: {
      showDetail(detail) {
        this.detail = detail;
        this.showModal = true;
      },
      getList() {
        this.apiPost('/web/msg/msg_list', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.dataCount = res.data.count;
            this.tableData = res.data.list;
          } else {
            _g.toastMsg('success', res.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.getList();
      },
      search() {
        this.postData.page_no = 1;
        this.getList();
      }
    },
    created() {
      this.getList();
    },
    mixins: [http]
  };
</script>
