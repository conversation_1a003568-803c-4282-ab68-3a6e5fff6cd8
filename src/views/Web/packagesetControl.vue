<template>
  <div class="inpanel-c-c">
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column prop="name" label="版本管理" align="center"></el-table-column>
      <el-table-column prop="version_type" label="类型" min-width="80" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.version_type === 1 ? '商家版本' : '场馆版本' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template slot-scope="{ row }">
          <router-link class='editfont' :to="{name: 'packageAdd',params: {version_id: row.id, version_type: row.version_type}}">
            <el-button size="small" type="primary">编辑</el-button>
          </router-link>
          <el-button size="small" type="primary" class="eidtfont" @click="versionOper(row.name,row.id)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pos-rel p-t-20 ovf-hd">
      <div class="fl">
        <router-link class="btn-link-large add-btn" :to="{name:'packageAdd',params: {version_id: 0}}">
          新增版本
        </router-link>
      </div>
      <div class="block pages" v-if="dataCount>0">
        <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
        </el-pagination>
      </div>
    </div>

    <!-- 删除弹窗 -->
    <el-dialog width="40%" title="版本删除" :visible.sync="deleteDialog">
      <span>确认删除{{deletePost.version_name}}?</span>
      <div slot="footer" class="dialog-footer">
        <el-button @click="deleteDialog = false">取 消</el-button>
        <el-button type="primary" @click="versionDelete">确 定</el-button>
      </div>
    </el-dialog>

  </div>
</template>

<script>
  import http from 'assets/js/http';
  export default {
    name: 'packageSetControl',
    data() {
      return {
        postData: {
          page_size: 10,
          page_no: 1
        },
        tableData: [],
        dataCount: 0,
        deleteDialog: false,
        deletePost: {
          version_id: '',
          version_name: ''
        }
      };
    },
    methods: {
      gettableList() {
        this.apiPost('/Web/Version/getVersionList', this.postData).then(res => {
          _g.closeGlobalLoading();
          if (res.errorcode == 0) {
            this.tableData = res.data.list;
            this.dataCount = res.data.count;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.gettableList();
      },
      versionOper(name, id) {
        this.deletePost.version_id = id;
        this.deletePost.version_name = name;
        this.deleteDialog = true;
      },
      versionDelete() {
        this.apiPost('/Web/Version/delVersion', this.deletePost).then(res => {
          if (res.errorcode == 0) {
            _g.toastMsg('success', res.errormsg);
            this.deleteDialog = false;
            this.gettableList();
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      }
    },
    created() {
      this.gettableList();
    },
    mixins: [http]
  };
</script>

<style scoped>
  .inpanel-c-c {
    padding: 20px;
  }
  .editfont {
    color: #fff;
  }
</style>








