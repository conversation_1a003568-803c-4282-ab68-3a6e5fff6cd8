<template>
    <div class="download-wrap">
        <div class="download-h">
            场馆版本数据下载
        </div>
        <div class="download-box">
            <div class="search-box">
                <div class="block">
                    <el-date-picker v-model="value1" type="date" size="small" format placeholder="选择注册日期" :picker-options="pickerOptions0">
                    </el-date-picker>
                </div>
                <div class="block">
                    <el-date-picker v-model="value2" type="date" size="small" placeholder="选择注册日期" :picker-options="pickerOptions1">
                    </el-date-picker>
                </div>
                <a v-bind:href="downloadUrl" class="data-url">下载</a>
            </div>
        </div>
        <div class="import-box">
            场馆版本数据导入
        </div>
        <el-upload class="upload-demo" ref="upload" :action="this.action" :on-preview="handlePreview" :on-success="handleSuccess" :on-change="handleChange" :file-list="fileList" :auto-upload="false">
            <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
            <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">导入</el-button>
            <div slot="tip" class="el-upload__tip">只能上传xls/xlsx文件</div>
        </el-upload>
        <el-dialog title="数据导入结果" :visible.sync="dialogVisible" width="40%">
            <div>
                <span>总共数据：{{count}}条</span>
            </div>
            <div>
                <span>导入成功：{{success}}条</span>
            </div>
            <div>
                <span>导入失败：
                    <span style="color:red;">{{fail}}</span>条</span>
            </div>
            <div>
                <a v-bind:href="errorUrl" style="color:#20A0FF;">下载失败列表</a>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="dialogVisible = false">取 消</el-button>
                <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
            </span>
        </el-dialog>

    </div>
</template>
<style scoped>
    .download-wrap {
      margin-top: 40px;
      margin-left: 80px;
    }

    .search-box {
      height: 34px;
      margin-top: 10px;
    }

    .block {
      float: left;
      margin-right: 10px;
    }

    .data-url {
      color: #0f1922;
      border: 1px solid #cccccc;
      padding: 5px 5px;
      line-height: 34px;
    }

    .import-box {
      margin-top: 30px;
      margin-bottom: 10px;
    }
</style>
<script>
    import http from 'assets/js/http';
    export default {
      name: 'busVersionImport',
      data() {
        return {
          pickerOptions0: {},
          pickerOptions1: {},
          value1: '',
          value2: '',
          fileList: [],
          action: '',
          status: true,
          dialogVisible: false,
          count: 0,
          fail: 0,
          success: 0,
          url: ''
        };
      },
      computed: {
        downloadUrl: function() {
          // console.log(this.value1)
          let authKey = Lockr.get('authKey');
          let sessionId = Lockr.get('sessionId');
          let now = new Date();
          let s_day = '';
          let e_day = '';
          let url =
            window.HOST +
            '/web/Initialization/busListExcel' +
            '?' +
            's_day=' +
            s_day +
            '&e_day=' +
            e_day +
            '&sessionid=' +
            sessionId +
            '&authkey=' +
            authKey;
          if (this.value1 != '' && this.value2 != '') {
            let s = new Date(this.value1);
            let e = new Date(this.value2);
            s_day = s.getFullYear() + '-' + (s.getMonth() + 1) + '-' + s.getDate();
            e_day = e.getFullYear() + '-' + (e.getMonth() + 1) + '-' + e.getDate();
            url =
              window.HOST +
              '/web/Initialization/busListExcel' +
              '?' +
              's_day=' +
              s_day +
              '&e_day=' +
              e_day +
              '&sessionid=' +
              sessionId +
              '&authkey=' +
              authKey;
          }
          if (this.value1 != '' && this.value2 == '') {
            let s = new Date(this.value1);
            let now = new Date();
            s_day = s.getFullYear() + '-' + (s.getMonth() + 1) + '-' + s.getDate();
            e_day = now.getFullYear() + '-' + (now.getMonth() + 1) + '-' + (now.getDate() - 1);
            url =
              window.HOST +
              '/web/Initialization/busListExcel' +
              '?' +
              's_day=' +
              s_day +
              '&e_day=' +
              e_day +
              '&sessionid=' +
              sessionId +
              '&authkey=' +
              authKey;
          }
          return url;
        },
        errorUrl: function() {
          let authKey = Lockr.get('authKey');
          let sessionId = Lockr.get('sessionId');
          let url = this.url + '&' + 'sessionid=' + sessionId + '&authkey=' + authKey;
          return url;
        }
      },
      created() {
        let authKey = Lockr.get('authKey');
        let sessionId = Lockr.get('sessionId');
        this.action =
          window.HOST + '/web/Initialization/busVersionImport' + '?' + 'sessionid=' + sessionId + '&authkey=' + authKey;
        _g.closeGlobalLoading();
      },
      methods: {
        submitUpload() {
          if (this.status) {
            _g.toastMsg('error', '请选择文件');
          }
          this.$refs.upload.submit();
          this.status = true;
        },
        handlePreview(file) {
          console.log(file);
        },
        handleChange(file) {
          this.status = false;
        },
        handleSuccess(response, file, fileList) {
          // console.log(response);
          if (response.errorcode === 0) {
            this.$refs.upload.clearFiles();
            this.status = true;
            console.log(response);
            if (response.data.fail === 0) {
              _g.toastMsg('success', '导入成功');
            } else if (response.data.fail > 0) {
              this.success = response.data.success;
              this.fail = response.data.fail;
              this.count = response.data.count;
              this.url = response.data.url;
              this.dialogVisible = true;
            }
          } else {
            this.$refs.upload.clearFiles();
            _g.toastMsg('error', '导入失败');
          }
        }
      },
      mixins: [http]
    };
</script>
