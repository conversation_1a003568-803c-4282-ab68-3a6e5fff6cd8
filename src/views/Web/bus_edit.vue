<template>
  <div class="m-l-50 m-t-30">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-form-item label="场馆名称" prop="bus_name">
        <el-input v-model.trim="form.bus_name" class="h-40 w-200" :disabled="true"></el-input>
      </el-form-item>
      <el-form-item label="场馆类型" prop="bus_type">
        <el-select v-model="form.bus_type" style="width: 186px;">
          <el-option v-for="item in options" :label="item.label" :value="item.value">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="是否使用" prop="use_status">
        <el-radio-group v-model.trim="form.use_status">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否签约" prop="sign_status">
        <el-radio-group v-model.trim="form.sign_status">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="绑定勤鸟" prop="follow_us">
        <el-radio-group v-model="form.follow_us">
          <el-radio :label="1">是</el-radio>
          <el-radio :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="场馆品牌" prop="brand">
        <el-radio-group v-model.trim="form.brand">
          <el-radio :label="2">连锁品牌</el-radio>
          <el-radio :label="1">单一品牌</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="版本" prop="charge_version">
        <el-radio-group v-model.trim="form.charge_version">
          <el-radio :label="0">免费版</el-radio>
          <el-radio :label="1">收费版</el-radio>
          <el-radio :label="2">高级版</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="统一社会信用码" prop="social_credit_code">
        <el-input v-model.trim="form.social_credit_code" class="h-40 w-200"></el-input>
      </el-form-item>
      <el-form-item label="地址" prop="address">
        <el-input v-model.trim="form.address" class="h-40 w-500"></el-input>
      </el-form-item>
      <el-form-item label="签约时间" prop="sign_time">
        <div class="block">
          <el-date-picker :disabled="form.status" v-model="form.sign_time" type="date" size="small" format placeholder="选择日期">
          </el-date-picker>
        </div>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="edit('form')" :loading="isLoading">提交</el-button>
        <el-button @click="goback()">返回</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
<script>
  import http from 'assets/js/http';
  import fomrMixin from 'assets/js/form_com';

  export default {
    name: 'bus_edit',
    data() {
      return {
        isLoading: false,
        vshow: true,
        form: {
          name: '',
          bus_type: '',
          bus_id: '',
          brand: '',
          sign_time: '',
          follow_us: '',
          use_status: '',
          address: '',
          charge_version: '',
          social_credit_code: '',
          sign_status: '',
          status: false,
          mer_id: ''
        },
        use_bus: [],
        options: [
          // { label: '请选择', value: '' },
          // { label: '健身房', value: 1 },
          // { label: '瑜伽馆', value: 2 },
          // { label: '跆拳道馆', value: 3 },
          // { label: '武道馆', value: 4 },
          // { label: '舞蹈馆', value: 5 },
          // { label: '其它', value: 6 },
          { label: '请选择', value: '' },
          { label: '健身房', value: 1 },
          { label: '健身工作室', value: 9 },
          { label: '体育馆', value: 7 },
          { label: '游泳馆', value: 8 },
          { label: '瑜伽馆', value: 2 },
          { label: '跆拳道馆', value: 3 },
          { label: '武道馆', value: 4 },
          { label: '舞蹈馆', value: 5 },
          { label: '其他', value: 6 },
        ],
        rules: {
          charge_version: [{ required: true, message: '请选择' }],
          bus_id: [{ required: true, message: '请选择' }],
          follow_us: [{ required: true, message: '请选择' }],
          use_status: [{ required: true, message: '请选择' }],
          // social_credit_code: [
          //   { required: true, message: '请选择' }
          // ],
          address: [{ required: true, message: '请选择' }],
          brand: [{ required: true, message: '请选择' }],
          sign_status: [{ required: true, message: '请选择' }]
        }
      };
    },
    methods: {
      edit(form) {
        this.$refs[form].validate(valid => {
          if (valid) {
            this.isLoading = !this.isLoading;
            this.form.action = 'edit';
            if (this.form.sign_time) {
              let d = new Date(this.form.sign_time);
              delete this.form.time;
              this.form.sign_time = d.getFullYear() + '-' + (d.getMonth() + 1) + '-' + d.getDate();
            } else {
              this.form.sign_time = '';
            }
            this.apiPost('web/maintenance/edit_sign_bus', this.form).then(res => {
              this.handelResponse(
                res,
                data => {
                  _g.toastMsg('success', '编辑成功');
                  setTimeout(() => {
                    this.goback();
                  }, 1500);
                },
                () => {
                  this.isLoading = !this.isLoading;
                }
              );
            });
          }
        });
      },
      getBusInfo() {
        this.form.id = this.$route.params.id;
        this.apiPost('/web/maintenance/sign_bus_info', { id: this.form.id }).then(res => {
          this.handelResponse(res, data => {
            this.form.name = res.data.name;
            this.form.bus_name = res.data.bus_name;
            if (res.data.bus_id > 0) {
              this.form.bus_id = res.data.bus_id;
              this.vshow = false;
            }
            this.form.use_status = res.data.use_status;
            this.form.follow_us = res.data.follow_us;
            this.form.status = res.data.status;
            this.form.sign_time = res.data.sign_time;
            if (this.form.sign_time != '') {
              this.form.status = true;
            }
            this.form.brand = res.data.brand;
            this.form.address = res.data.address;
            this.form.bus_type = res.data.bus_type;
            this.form.charge_version = res.data.charge_version;
            this.form.sign_status = res.data.sign_status;
            this.form.social_credit_code = res.data.social_credit_code;
            this.form.mer_id = res.data.mer_id;
          });
        });
      }
    },
    created() {
      this.getBusInfo();
    },
    mixins: [http, fomrMixin]
  };
</script>
