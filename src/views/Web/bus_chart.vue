<template>
  <div class="main-container-wrap">
    <!--内容区头部-->
    <div class="index-nav">
      场馆统计 >> 场馆运营排行
    </div>
    <div class="main-container">
      <div class="main-content">
        <div class="search-box">
          <div class="block">
            <el-date-picker v-model="value1" type="date" size="small" placeholder="选择日期" :picker-options="pickerOptions0">
            </el-date-picker>
          </div>
          <el-button size="small" v-on:click="dayquery('yes')">昨天</el-button>
          <el-button size="small" v-on:click="dayquery('last')">前天</el-button>
          <el-input v-model="input" placeholder="场馆名称" size="small" class="search-input"></el-input>
          <el-button size="small" class="query" v-on:click="query">查询</el-button>
        </div>
        <div class="table-container">
          <el-table :data="tableData" @sort-change="sortchange" stripe border style="width: 100%">
            <el-table-column prop="rank" label="排名" min-width="99">
            </el-table-column>
            <el-table-column prop="bus_name" label="场馆名称" sortable="custom" min-width="168">
            </el-table-column>
            <el-table-column prop="14" label="客户总数" sortable="custom" min-width="128">
            </el-table-column>
            <el-table-column prop="15" label="有效会员" sortable="custom" min-width="128">
            </el-table-column>
            <el-table-column prop="16" label="微信绑定会员" sortable="custom" min-width="148">
            </el-table-column>
            <el-table-column prop="17" label="微信绑定率" sortable="custom" min-width="148">
            </el-table-column>
            <el-table-column prop="18" label="打卡数" sortable="custom" min-width="138">
            </el-table-column>
            <el-table-column prop="19" label="开卡金额" sortable="custom" min-width="148">
            </el-table-column>
            <el-table-column prop="20" label="人均开卡金额" sortable="custom" min-width="148">
            </el-table-column>
            <el-table-column prop="21" label="私教开卡金额" sortable="custom" min-width="148">
            </el-table-column>
            <el-table-column prop="22" label="人均私教开卡金额" min-width="170" sortable="custom">
            </el-table-column>
          </el-table>
        </div>
        <div class="pos-rel p-t-20">
          <div class="block pages">
            <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :current-page="currentPage" :total="pageCount">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  export default {
    name: 'bus_chart',
    data() {
      return {
        tableData: [],
        value1: '',
        input: '',
        pageCount: null,
        currentPage: 1,
        begin_time: '',
        end_time: '',
        sort_field: 'user_total',
        sort_type: 'desc'
      };
    },
    created() {
      this.init();
    },
    methods: {
      get_bus_chart() {
        let now = new Date();
        if (this.begin_time == '' && this.param == '') {
          this.begin_time = Date.parse(now.getFullYear() + '-' + (now.getMonth() + 1) + '-' + (now.getDate() - 1)) / 1000;
          this.end_time = this.begin_time + 24 * 3600 - 1;
        }
        let postData = {
          page_no: this.currentPage,
          begin_time: this.begin_time,
          end_time: this.end_time
        };
        if (this.sort_type != '') {
          postData.sort_type = this.sort_type;
        }
        if (this.sort_field != '') {
          postData.sort_field = this.sort_field;
        }
        if (this.input != '' && isNaN(this.input)) {
          postData.param = this.input;
        }
        this.apiPost('web/businessStatistical/bus_chart_statistical', postData).then(res => {
          this.tableData = res.data.list;
          this.pageCount = res.data.pageCount * 10;
          _g.closeGlobalLoading();
        });
      },
      query() {
        _g.openGlobalLoading();
        this.begin_time = '';
        this.end_time = '';
        this.currentPage = 1;
        if (this.value1 != '' && !isNaN(this.value1)) {
          this.begin_time = Date.parse(new Date(this.value1)) / 1000;
          this.end_time = this.begin_time + 24 * 3600 - 1;
        }
        this.get_bus_chart();
      },
      dayquery(flag) {
        let now = new Date();
        _g.openGlobalLoading();
        if (flag == 'yes') {
          this.begin_time = Date.parse(now.getFullYear() + '-' + (now.getMonth() + 1) + '-' + (now.getDate() - 1)) / 1000;
          this.end_time = this.begin_time + 24 * 3600 - 1;
        }
        if (flag == 'last') {
          this.begin_time = Date.parse(now.getFullYear() + '-' + (now.getMonth() + 1) + '-' + (now.getDate() - 2)) / 1000;
          this.end_time = this.begin_time + 24 * 3600 - 1;
        }
        this.get_bus_chart();
      },
      sortchange(a) {
        console.log(a);
        _g.openGlobalLoading();
        let sortcolumn = {
          bus_name: 'bus_name',
          14: 'user_total',
          15: 'user_valid',
          16: 'wechat_bind',
          17: 'wechat_bind_p',
          18: 'sign_in',
          19: 'open_card_m',
          20: 'p_open_card_m',
          21: 'pt_open_card_m',
          22: 'P_pt_open_card_m'
        };
        this.sort_type = 'desc';
        if (a.order == 'ascending') {
          this.sort_type = 'asc';
        }
        this.sort_field = sortcolumn[a.prop];
        this.get_bus_chart();
      },
      getCurrentPage() {
        let data = this.$route.query;
        if (data) {
          if (data.page) {
            this.currentPage = parseInt(data.page);
          } else {
            this.currentPage = 1;
          }
        }
      },
      handleCurrentChange(page) {
        _g.openGlobalLoading();
        this.currentPage = page;
        this.get_bus_chart();
      },
      init() {
        this.getCurrentPage();
        this.get_bus_chart();
      }
    },
    mixins: [http]
  };
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
  .index-nav {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    padding-left: 10px;
    background-color: #e4e4e4;
  }
  .main-container {
    margin-top: 40px;
    margin-left: 46px;
    background-color: #ffffff;
    border-radius: 15px;
  }
  .main-content {
    padding-top: 16px;
    padding-left: 22px;
    padding-bottom: 70px;
  }
  .search-box {
    height: 34px;
    width: 704px;
  }
  .block {
    margin-right: 20px;
    float: left;
  }
  .el-date-editor {
    width: 140px;
  }
  .el-input--small input {
    border-radius: 30px;
  }
  .search-box button {
    height: 30px;
    vertical-align: bottom;
    border-radius: 30px;
    width: 60px;
  }
  .el-input {
    width: 140px;
  }
  input {
    border-radius: 10px;
  }
  .el-input__inner {
    border-radius: 30px;
  }
  .search-box .query {
    width: 140px;
    color: #ffffff;
    font-size: 14px;
    border: 0;
    background-color: #fa571c;
  }
  .table-container {
    margin-top: 12px;
  }
</style>
