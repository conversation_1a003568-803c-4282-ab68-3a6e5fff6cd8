<template>

  <div class="container">
    <el-table :data="tableData" stripe>
      <el-table-column align="center" prop="bus_names" label="提醒标题"></el-table-column>
      <el-table-column align="center" prop="bus_names" label="提醒有效期"></el-table-column>
      <el-table-column align="center" prop="bus_names" label="提醒规则"></el-table-column>
      <el-table-column align="center" prop="bus_names" label="提醒场馆"></el-table-column>
      <el-table-column align="center" label="操作" width="100">
        <template scope="scope">
          <el-button size="small" type="primary" @click="showDetail(scope.row)">内容</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
        <el-button type="success" size="small" icon="el-icon-plus" @click="addRenzheng">
          通知发布
        </el-button>
      </div>
       <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
        </el-pagination>
    </footer>
  </div>
</template>

<script>
  export default {
    name: 'DialogNoticeList',
    data() {
      return {
        tableData: [],
        detail: '',
        showModal: false,
        postData: {
          page_size: 10,
          page_no: 1
        },
        dataCount: 0
      };
    },
    methods: {
      addRenzheng() {
        this.$router.push({ name: 'dialogNoticeDetail' })
      },
      showDetail(detail) {
        this.detail = detail;
        this.showModal = true;
      },
      getList() {
        this.$service.post('/web/msg/msg_list', this.postData).then(res => {
          if (res.data.errorcode == 0) {
            const resData = res.data.data
            this.dataCount = resData.count;
            this.tableData = resData.list;
          } else {
            _g.toastMsg('success', res.data.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.getList();
      },
      search() {
        this.postData.page_no = 1;
        this.getList();
      }
    },
    created() {
      this.getList();
    }
  };
</script>
