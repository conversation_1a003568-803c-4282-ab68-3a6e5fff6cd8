<template>
  <div class="download-wrap">
    <h4 class="download-h">
      场馆数据导出
    </h4>
    <div class="download-box">
      <div class="search-box">
        <el-date-picker v-model="busExportDateRange" type="daterange" value-format="yyyy-MM-dd" size="small" start-placeholder="开始日期" end-placeholder="结束日期" :clearable="false">
        </el-date-picker>
        <a v-bind:href="downloadUrl" class="data-url">下载</a>
      </div>
    </div>
    <h4 class="import-box">
      场馆数据导入
    </h4>
    <el-upload class="upload-demo" ref="upload" :action="this.action" :on-preview="handlePreview" :on-success="handleSuccess" :on-change="handleChange" :file-list="fileList" :auto-upload="false">
      <el-button slot="trigger" size="small" type="primary">选取文件</el-button>
      <el-button style="margin-left: 10px;" size="small" type="success" @click="submitUpload">导入</el-button>
      <el-button type="primary" size="small" style="margin-left:10px;">
        <a style="color:#ffffff" v-bind:href="templateUrl">模版下载</a>
      </el-button>
      <div slot="tip" class="el-upload__tip">只能上传xls/xlsx文件</div>
    </el-upload>

    <h4 class="download-h border-top">
      注册商家导出
    </h4>
    <div class="download-box">
      <div class="search-box">
        <div class="block">
          <el-date-picker v-model="registerMerchants.s_day" type="date" size="small" placeholder="选择日期" value-format="yyyy-MM-dd"></el-date-picker>
        </div>
        <div class="block">
          <el-date-picker v-model="registerMerchants.e_day" type="date" size="small" placeholder="选择日期" value-format="yyyy-MM-dd"></el-date-picker>
        </div>
        <el-button size="small" @click="exportMerchants" type="primary">导出</el-button>
      </div>
    </div>

    <h4 class="download-h border-top">
      所有场馆导出
    </h4>
    <div class="download-box">
      <div class="search-box">
        <!-- <div class="block">
          <el-date-picker v-model="registerMerchants.s_day" type="date" size="small" format placeholder="选择日期" value-format="yyyy-MM-dd"></el-date-picker>
        </div>
        <div class="block">
          <el-date-picker v-model="registerMerchants.e_day" type="date" size="small" format placeholder="选择日期" value-format="yyyy-MM-dd"></el-date-picker>
        </div> -->
        <el-button size="small" @click="exportAllBus" type="primary">导出</el-button>
      </div>
    </div>

    <h4 class="download-h border-top">
      教练数量导出
    </h4>
    <div class="download-box">
      <div class="search-box">
        <!-- <div class="block">
          <el-date-picker v-model="registerMerchants.s_day" type="date" size="small" format placeholder="选择日期" value-format="yyyy-MM-dd"></el-date-picker>
        </div>
        <div class="block">
          <el-date-picker v-model="registerMerchants.e_day" type="date" size="small" format placeholder="选择日期" value-format="yyyy-MM-dd"></el-date-picker>
        </div> -->
        <el-button size="small" @click="exportCoachNumber" type="primary">导出</el-button>
      </div>
    </div>

    <el-dialog title="数据导入结果" :visible.sync="dialogVisible" width="40%">
      <div>
        <span>总共数据：{{count}}条</span>
      </div>
      <div>
        <span>导入成功：{{success}}条</span>
      </div>
      <div>
        <span>导入失败：
          <span style="color:red;">{{fail}}</span>条</span>
      </div>
      <div>
        <a v-bind:href="errorUrl" style="color:#20A0FF;">下载失败列表</a>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="dialogVisible = false">确 定</el-button>
      </span>
    </el-dialog>
    <Export ref="export" />
  </div>
</template>

<script>
  const YESTERDAY = new Date(Date.now() - 24 * 3600 * 1000);
  import http from 'assets/js/http';
  import Export from 'src/components/Export';
  export default {
    name: 'download',
    data() {
      return {
        pickerOptions0: {},
        pickerOptions1: {},
        busExportDateRange: [_g.formatDate(YESTERDAY, 'yyyy-MM-dd'), _g.formatDate(YESTERDAY, 'yyyy-MM-dd')],
        fileList: [],
        action: '',
        status: true,
        dialogVisible: false,
        count: 0,
        fail: 0,
        success: 0,
        registerMerchants: {
          s_day: '',
          e_day: ''
        },
        exportMerchantsList: [],
        url: ''
      };
    },
    components: {
      Export
    },
    computed: {
      downloadUrl() {
        const authKey = Lockr.get('authKey');
        const sessionId = Lockr.get('sessionId');

        return `${
          window.HOST
        }/web/DataTable/getBusiStat?s_day=${this.busExportDateRange[0]}&e_day=${this.busExportDateRange[1]}&sessionid=${sessionId}&authkey=${authKey}`;
      },
      templateUrl: function() {
        let authKey = Lockr.get('authKey');
        let sessionId = Lockr.get('sessionId');
        let url =
          window.HOST + '/web/excel/bus_templet_download' + '?' + 'sessionid=' + sessionId + '&authkey=' + authKey;
        return url;
      },
      errorUrl: function() {
        let authKey = Lockr.get('authKey');
        let sessionId = Lockr.get('sessionId');
        let url = this.url + '&' + 'sessionid=' + sessionId + '&authkey=' + authKey;
        return url;
      }
    },
    created() {
      let authKey = Lockr.get('authKey');
      let sessionId = Lockr.get('sessionId');
      this.action = window.HOST + '/web/excel/bus_import' + '?' + 'sessionid=' + sessionId + '&authkey=' + authKey;
      _g.closeGlobalLoading();
    },
    methods: {
      exportAllBus() {
        const url = '/web/DataTable/getAllBus';
        this.$service.get(url).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data.bus_list;
            const columns = [
              { title: '场馆id', key: 'bus_id' },
              { title: '场馆名称', key: 'bus_name' },
              { title: '省 (直辖市)', key: 'province' },
              { title: '市', key: 'city' },
              { title: '区', key: 'district' },
              { title: '电话', key: 'phone' },
              { title: '版本', key: 'version' },
              { title: '注册时间', key: 'create_time' },
              { title: '到期时间', key: 'expire_time' }
            ];
            this.$refs.export.export({ columns, data, filename: '场馆列表' });
          } else {
            this.$message.error(res.data.errormsg);
          }
        });
      },
      exportCoachNumber() {
        const url = '/web/statistical/getChannelCoaches';
        this.$service
          .post(url)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              const columns = [
                { title: '省份', key: 'region_name' },
                { title: '男教练数量', key: 'male' },
                { title: '女教练数量', key: 'female' }
              ];
              this.$refs.export.export({ data, columns, filename: '场馆教练数量' });
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      exportMerchants() {
        if (!this.registerMerchants.s_day || !this.registerMerchants.e_day) {
          _g.toastMsg('warning', '请先选择时间');
          return false;
        }
        this.apiGet('/Web/DataTable/getRegisterMerchants', this.registerMerchants).then(res => {
          if (res.errorcode == 0) {
            const excelColumns = [
              { title: '商家名称', key: 'bus_name' },
              { title: '申请人', key: 'apply_name' },
              { title: '申请账号', key: 'accounts' },
              { title: '申请时间', key: 'apply_time' },
              { title: '手机', key: 'phone' },
              { title: '省市区', key: 'district' },
              { title: '地址', key: 'address' },
              { title: '场馆类型', key: 'type' },
              { title: '营业执照', key: 'license' },
              { title: '状态', key: 'status' }
            ];
            const resData = res.data;
            if (resData && resData.bus_list && resData.bus_list.length > 0) {
              this.$refs.export.export({
                columns: excelColumns,
                data: resData.bus_list,
                filename: '注册商家（' + this.registerMerchants.s_day + '至' + this.registerMerchants.e_day + '）'
              });
            } else {
              _g.toastMsg('warning', '该时段无注册商家');
            }
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      submitUpload() {
        if (this.status) {
          _g.toastMsg('error', '请选择文件');
        }
        this.$refs.upload.submit();
        this.status = true;
      },
      handlePreview(file) {
        console.log(file);
      },
      handleChange(file) {
        this.status = false;
      },
      handleSuccess(response, file, fileList) {
        if (response.errorcode === 0) {
          this.$refs.upload.clearFiles();
          this.status = true;
          if (response.data.fail === 0) {
            _g.toastMsg('success', '导入成功');
          } else if (response.data.fail > 0) {
            this.success = response.data.success;
            this.fail = response.data.fail;
            this.count = response.data.count;
            this.url = response.data.url;
            this.dialogVisible = true;
          }
        } else {
          this.$refs.upload.clearFiles();
          _g.toastMsg('error', '导入失败');
        }
      }
    },
    mixins: [http]
  };
</script>

<style scoped>
  .download-wrap {
    padding: 20px 30px;
    border-radius: 5px;
    border: 1px solid #ccc;
    background: #fff;
  }
  .search-box {
    height: 34px;
    margin-top: 10px;
  }
  .block {
    float: left;
    margin-right: 10px;
  }
  .data-url {
    color: #0f1922;
    border: 1px solid #cccccc;
    padding: 5px 5px;
    line-height: 34px;
  }
  .import-box {
    margin-top: 30px;
    margin-bottom: 10px;
  }
  .border-top {
    margin-top: 15px;
    border-top: 1px dashed #ccc;
    padding-top: 15px;
  }
</style>
