<template>

  <div class="container">
    <header>
      <div class="fl">
        <el-button type="success" size="small" icon="el-icon-plus" @click="$router.push({ name: 'modalSend' })">
          通知发布
        </el-button>
      </div>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column align="center" prop="title" label="提醒标题"></el-table-column>
      <el-table-column align="center" prop="bus_names" label="提醒有效期">
        <template scope="scope">
          {{scope.row.remind_begin_time}}~{{scope.row.remind_end_time}}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="remind_rule" label="提醒规则">
        <template scope="scope">
          {{scope.row.remind_rule==1?'期间仅提醒一次':'每次登录都提醒'}}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="bus_list" label="提醒场馆" show-overflow-tooltip></el-table-column>
      <el-table-column align="center" label="操作" width="150">
        <template scope="scope">
          <el-button size="small" type="primary" @click="showDetail(scope.row)">编辑</el-button>
          <el-button size="small" type="danger" @click="delModal(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pos-rel p-t-20 ovf-hd flexend">
      <div class="block">
        <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
  export default {
    name: 'ModalNotice',
    data() {
      return {
        tableData: [],
        detail: '',
        showModal: false,
        postData: {
          page_size: 10,
          page_no: 1
        },
        dataCount: 0
      };
    },
    methods: {
      showDetail(detail) {
        this.$router.push({ name: 'modalSend', query: {id:detail.id} })
      },
      delModal(id) {
        this.$confirm('确认删除?', '提示', {
	        confirmButtonText: '确定',
	        cancelButtonText: '取消',
	        type: 'warning'
	      })
	        .then(() => {
	          this.$service
              .post('web/PopupNotice/delete', { id }).then(res => {
                if (res.data.errorcode == 0) {
                  this.getList()
                  _g.toastMsg('success', res.data.errormsg);
                } else {
                   _g.toastMsg('warning', res.data.errormsg);
                }
	          });
	        })
      },
      getList() {
        this.$service
          .post('/web/PopupNotice/index', this.postData).then(res => {
          if (res.data.errorcode == 0) {
            this.dataCount = res.data.data.count;
            this.tableData = res.data.data.list;
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.getList();
      },
      search() {
        this.postData.page_no = 1;
        this.getList();
      }
    },
    created() {
      this.getList();
    }
  };
</script>
