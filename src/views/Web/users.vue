<template>
  <div class="main-container-wrap">
    <!--内容区头部-->
    <div class="index-nav">
      C端用户分析 >> 用户群体分析
    </div>
    <div class="main-container">
      <div class="main-content">
        <div class="search-box">
          <div class="block">
            <el-date-picker v-model="value1" type="date" size="small" placeholder="选择日期">
            </el-date-picker>
          </div>
          <div class="block">
            <el-date-picker v-model="value2" type="date" size="small" placeholder="选择日期">
            </el-date-picker>
          </div>
          <el-button size="small" v-on:click="yearquery('now')">今年</el-button>
          <el-button size="small" v-on:click="yearquery('last')">去年</el-button>
          <el-button size="small" v-on:click="yearquery('before')">前年</el-button>
          <el-button size="small" class="query" v-on:click="query">查询</el-button>
        </div>
        <div class="table-container">
          <el-table :data="tableData" stripe border style="width: 100%">
            <el-table-column prop="date" label="时间" min-width="112">
            </el-table-column>
            <el-table-column prop="sex" label="性别" min-width="112">
            </el-table-column>
            <el-table-column prop="3" label="客户数" min-width="112">
            </el-table-column>
            <el-table-column prop="4" label="14岁下" min-width="112">
            </el-table-column>
            <el-table-column prop="5" label="15-20" min-width="112">
            </el-table-column>
            <el-table-column prop="6" label="21-30" min-width="112">
            </el-table-column>
            <el-table-column prop="7" label="31-40" min-width="112">
            </el-table-column>
            <el-table-column prop="8" label="41-50" min-width="112">
            </el-table-column>
            <el-table-column prop="9" label="51-60" min-width="112">
            </el-table-column>
            <el-table-column prop="10" label="61-70" min-width="112">
            </el-table-column>
            <el-table-column prop="11" min-width="112" label="70岁上">
            </el-table-column>
            <el-table-column prop="12" min-width="112" label="未知">
            </el-table-column>
          </el-table>
        </div>
        <div class="pos-rel p-t-20">
          <div class="block pages">
            <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :current-page="currentPage" :total="pageCount">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  let qs = require('qs');
  export default {
    name: 'users',
    data() {
      return {
        tableData: [],
        value1: '',
        value2: '',
        begin_time: '',
        end_time: '',
        currentPage: null,
        pageCount: null
      };
    },
    created() {
      this.init();
    },
    methods: {
      getCurrentPage() {
        let data = this.$route.query;
        console.log(qs);
        if (data) {
          if (data.page) {
            this.currentPage = parseInt(data.page);
          } else {
            this.currentPage = 1;
          }
        }
      },
      query: function(evevt) {
        if (this.value1 == '' || this.value2 == '') {
          _g.toastMsg('normal', '请选择时间');
          return;
        }
        this.begin_time = Date.parse(new Date(this.value1)) / 1000;
        this.end_time = Date.parse(new Date(this.value2)) / 1000 + 24 * 3600 - 1;
        _g.openGlobalLoading();
        this.users_statistical_list();
      },
      yearquery: function(message) {
        this.value1 = '';
        this.value2 = '';
        _g.openGlobalLoading();
        let time_flag = message;
        let postData = {};
        var now = new Date();
        if (time_flag == 'now') {
          this.begin_time = Date.parse(now.getFullYear() + '-01-01 0:0:0') / 1000;
          this.end_time = Date.parse(new Date()) / 1000;
        }
        if (time_flag == 'last') {
          this.begin_time = Date.parse(now.getFullYear() - 1 + '-01-01 0:0:0') / 1000;
          this.end_time = Date.parse(now.getFullYear() - 1 + '-12-31 23:59:59') / 1000;
        }
        if (time_flag == 'before') {
          this.begin_time = Date.parse(now.getFullYear() - 2 + '-01-01 0:0:0') / 1000;
          this.end_time = Date.parse(now.getFullYear() - 2 + '-12-31 23:59:59') / 1000;
        }
        this.users_statistical_list();
      },
      users_statistical_list() {
        let postData = {};
        let now = new Date();
        postData.page_size = 6;
        postData.page_no = this.currentPage;
        if (this.begin_time != '') {
          postData.begin_time = this.begin_time;
          postData.end_time = this.end_time;
        } else {
          postData.begin_time = Date.parse(now.getFullYear() + '-01-01 0:0:0') / 1000;
          postData.end_time = Date.parse(new Date()) / 1000;
        }
        this.apiPost('web/usersStatistical/users_statistical', postData).then(res => {
          this.handelResponse(res, data => {
            this.tableData = res.data.list;
            this.pageCount = res.data.pageCount * 10;
          });
        });
      },
      handleCurrentChange(page) {
        // router.push({ path: this.$route.path, query: { keywords: this.keywords, page: page }})
        _g.openGlobalLoading();
        this.currentPage = page;
        this.users_statistical_list();
      },
      getKeywords() {
        let data = this.$route.query;
        if (data) {
          if (data.begin_time) {
            this.begin_time = data.begin_time;
            this.end_time = data.end_time;
            this.value1 = data.begin_time;
            this.value2 = data.end_time;
          } else {
            this.keywords = '';
          }
        }
      },
      init() {
        this.getKeywords();
        this.getCurrentPage();
        this.users_statistical_list();
      }
    },
    watch: {
      $route(to, from) {
        this.init();
      }
    },
    mixins: [http]
  };
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
  .index-nav {
    height: 40px;
    line-height: 40px;
    font-size: 14px;
    padding-left: 10px;
    background-color: #e4e4e4;
  }
  .main-container {
    margin-top: 40px;
    margin-left: 46px;
    margin-bottom: 60px;
    background-color: #ffffff;
    border-radius: 15px;
  }
  .main-content {
    padding-top: 16px;
    padding-left: 22px;
    padding-bottom: 70px;
  }
  .search-box {
    height: 34px;
    width: 704px;
  }
  .block {
    margin-right: 20px;
    float: left;
  }
  .el-date-editor {
    width: 140px;
  }
  .el-input__inner {
    border-radius: 30px;
  }
  .search-box button {
    height: 30px;
    vertical-align: bottom;
    border-radius: 30px;
    width: 60px;
  }
  .search-box .query {
    width: 140px;
    color: #ffffff;
    font-size: 14px;
    border: 0;
    background-color: #fa571c;
  }
  .table-container {
    margin-top: 12px;
    margin-right: 20px;
  }
</style>
