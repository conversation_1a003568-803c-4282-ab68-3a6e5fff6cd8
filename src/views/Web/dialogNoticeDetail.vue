<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="150px">
      <el-form-item>
        <el-radio-group v-model="postData.type" @change="typeChange">
          <el-radio :label="1">全部场馆</el-radio>
          <el-radio :label="2">指定场馆</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="场馆" v-if="postData.type==2">
        <el-select v-model="postData.bus_list" multiple filterable placeholder="请输入场馆名称" :loading="loading">
          <el-option v-for="item in busList" :key="item.id" :label="item.name" :value="item.id">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="提醒时间" prop="register_time" :rules="{ required: true, message: '请选择时间'}">
        <el-date-picker
          v-model="postData.register_time"
          type="daterange"
          range-separator="至"
          start-placeholder="开始时间"
          end-placeholder="结束时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="标题" prop="title" :rules="{ required: true, message: '请输入发送内容'}">
        <el-input v-model="postData.title" placeholder="请填写" class="w-300"></el-input>
      </el-form-item>
      <el-form-item label="提醒规则">
        <el-radio-group v-model="postData.rule">
          <el-radio :label="1">期间仅提醒一次</el-radio>
          <el-radio :label="2">每次登录都提醒</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="内容" prop="content" :rules="{ required: true, message: '请输入发送内容'}">
        <Editor v-model="postData.content"></Editor>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">保存</el-button>
        <el-button class="m-l-150" @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  import Editor from 'components/form/Editor';
  export default {
    name: 'DialogNoticeDetail',
    data() {
      return {
        postData: {
          type: 1,
          rule: 1,
          bus_list: [],
          title: '',
          register_time: '',
          begin_time: '',
          end_time: '',
          content: ''
        },
        loading: false,
        busList: []
      };
    },
    components: { Editor },
    methods: {
      typeChange(val) {
        if (val == 1) {
          this.postData.bus_list = [];
        }
      },
      onSubmit() {
        this.$refs.form.validate(valid => {
          if (valid) {
            this.postData.begin_time = formatDate(this.postData.register_time[0],'yyyy-MM-dd');
            this.postData.end_time = formatDate(this.postData.register_time[1],'yyyy-MM-dd');
            this.$service.post('/web/msg/send', this.postData).then(res => {
              if (res.data.errorcode == 0) {
                _g.toastMsg('success', res.data.errormsg);
                this.$router.back();
              } else {
                _g.toastMsg('warning', res.data.errormsg);
              }
            });
          }
        });
      },
      getBusList(query) {
        if (query !== '') {
          this.loading = true;
          this.$service.post('/web/business/bus_list').then(res => {
            this.loading = false;
            if (res.data.errorcode == 0) {
              this.busList = res.data.data.bus_list;
            } else {
              _g.toastMsg('warning', res.data.errormsg);
            }
          });
        }
      }
    },
    created() {
      this.getBusList();
    },
    mixins: [http]
  };
</script>

<style scoped>
  .maright-20 {
    margin-right: 20px;
  }

  .inputlen {
    width: 180px;
  }
  .maps {
    display: flex;
    flex-direction: row;
    flex-wrap: nowrap;
    justify-content: flex-start;
    align-items: flex-start;
    width: 100%;
    height: 300px;
  }
  .amap-demo {
    height: 300px;
  }
  #map_container {
    width: 100%;
    height: 300px;
  }
  #panel {
    background-color: white;
    max-height: 100%;
    overflow-y: auto;
  }
</style>
