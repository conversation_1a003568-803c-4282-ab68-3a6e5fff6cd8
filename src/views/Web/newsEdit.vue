
<style lang="less">
  .business {
    .el-form-item__content {
      display: flex;
    }
  }
</style>
<template>
  <div class="container">
    <el-form class="form-frame" ref="form" :model="postData" label-width="150px">
      <el-form-item label="新闻标题" prop="title" :rules="[{required: true, message: '请填写新闻标题', trigger: 'blur'}]">
        <el-input v-model="postData.title" placeholder="新闻标题"></el-input>
      </el-form-item>
      <el-form-item label="新闻分类" prop="news_category_id" :rules="[{required: true, message: '请选择新闻分类', trigger: 'blur'}]">
        <el-select v-model="postData.news_category_id" placeholder="新闻分类" @change="handleChangeCategory">
          <el-option v-for="item in categoryList" :value="item.id" :label="item.name" :key="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="新闻封面" prop="images" :rules="[{required: true, message: '请添加新闻封面', trigger: 'blur'}]">
        <Cropper v-model="postData.images" :options="{aspectRatio: 560 / 400}" toDataURLType="image/jpeg" :toDataURLEncoderOptions="0.7"></Cropper>
        <!-- <img :src="postData.images" style="height: 100px" alt=""> -->
        <!-- <ImgUploader v-model="postData.images"></ImgUploader> -->
      </el-form-item>
      <el-form-item prop="details" label="新闻内容" :rules="[{required: true, message: '请填写新闻内容', trigger: 'blur'}]">
        <Editor v-model="postData.details" />
      </el-form-item>
      <el-form-item style="padding-top: 50px">
        <el-button type="success" @click="clickSave">保存</el-button>
        <el-button type="info" @click="$router.back()" style="margin-left: 100px;">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
  <script>
  import Editor from 'components/form/Editor';
  import Cropper from 'components/form/cropper';
  import ImgUploader from 'components/form/imgUploader';

  export default {
    name: 'NewsPublish',
    components: {
      Editor,
      Cropper,
      ImgUploader
    },
    data() {
      return {
        uploadUrl: _g.getRbBaseUrl() + '/Admin/Public/upload',
        fileList: [],
        categoryList: [{ name: '默认', id: 0 }], // 新闻分类选项列表
        postData: {
          title: '',
          news_category_id: 0, // 新闻分类ID 不分类或者没有分类的旧新闻则为0
          news_category_name: '', // 新闻分类名称
          images: '',
          details: ''
        }
      };
    },
    created() {
      this.getCategoryList()

      this.id = this.$route.query.id;
      if (this.id) {
        this.getInfo();
      }
    },
    methods: {
      getCategoryList() {
        this.$service.get('/web/NewsCategory/getLists').then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data.reverse();
            this.categoryList = [...data, { name: '默认', id: 0 }];
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
      },
      getInfo() {
        const url = '/web/News/getNewsInfo';
        this.$service
          .post(url, { id: this.id })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.postData = data;
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      handleChangeCategory(id) {
        this.postData.news_category_name =
          id ? this.categoryList.find(v => v.id == id).name : '';
      },
      clickSave() {
        this.$refs.form.validate(valid => {
          if (valid) {
            // this.postData.details = this.postData.details.replace(/<p><br><\/p>/g, '');
            if (this.id) {
              this.saveEdit();
            } else {
              this.addNews();
            }
          } else {
            this.$message.error('表单错误');
          }
        });
      },
      addNews() {
        const url = '/web/News/addNews';
        this.$service
          .post(url, this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$router.back();
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      handleUploadSuccess(res, file) {
        if (res.status === 1) {
          this.detail.license = res.info;
          this.licenseImg = res.info;
          console.log(this.detail.license, '123');
        } else {
          this.$message.error('上传失败');
        }
      },
      beforeUpload(file) {
        const fileType = ['image/jpg', 'image/png', 'image/jpeg'];
        const isJPG = fileType.includes(file.type);
        const isLt2M = file.size / 1024 / 1024 < 4;

        if (!isJPG) {
          this.$message.error('上传图片只能是 JPG/JPEG/PNG 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 4MB!');
        }
        return isJPG && isLt2M;
      },
      saveEdit() {
        const url = '/web/News/editNews';
        this.$service
          .post(url, this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$router.back();
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>
