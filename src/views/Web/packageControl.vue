<template>
  <div class="inpanel-c-c">
    <div class="m-b-20 ovf-hd">
      <div class="fl">
        <el-input placeholder="商家名" @keyup.enter.native="search" v-model="postData.merchants_name"
                  class="w-200 m-r-10"></el-input>
        <el-input placeholder="场馆名" @keyup.enter.native="search" v-model="postData.business_name"
                  class="w-150 m-r-10"></el-input>
        <el-input placeholder="场馆电话" @keyup.enter.native="search" v-model="postData.phone"
                  class="w-150 m-r-10"></el-input>
        <el-select clearable placeholder="版本" @change="search" v-model="postData.version_id" class="w-150 m-r-10">
          <el-option v-for="(item, index) in versionList" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
        <el-date-picker v-model="dateRange" format="yyyy-MM-dd" value-format="yyyy-MM-dd" type="daterange"
                        @change="dateChange" start-placeholder="到期时间" end-placeholder="到期时间"
                        ></el-date-picker>
        <el-button type="primary" icon="search" @click="search">搜索</el-button>
      </div>
    </div>

    <div class="static-wrap" v-if="statics">
      <div class="static-item" v-for="item in Object.keys(statics)">
        <div class="static-title">{{statics[item].name}}</div>
        <div class="static-num"><span>场馆数量</span>  {{statics[item].count}}</div>
        <div class="static-num"><span>应续金额</span>  {{statics[item].annual_fee}}</div>
      </div>
    </div>

    <el-table ref="tableRef" :data="tableData" row-key="bus_id" stripe style="width: 100%" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" reserve-selection></el-table-column>
      <el-table-column prop="merchants_name" label="商家名" width="180" align="center"></el-table-column>
      <el-table-column prop="business_name" label="场馆" width="200" align="center"></el-table-column>
      <el-table-column prop="phone" label="场馆电话" width="180" align="center"></el-table-column>
      <el-table-column prop="version_name" label="使用版本" width="180" align="center"></el-table-column>
      <el-table-column prop="sms_number" label="短信剩余量" width="120" align="center"></el-table-column>
      <el-table-column prop="version_expire_time" label="到期时间" width="100" align="center"></el-table-column>
      <el-table-column prop="renew_status" label="使用状态" width="120" align="center"></el-table-column>
      <el-table-column prop="mer_version_status" label="商家端" width="100" align="center">
        <template scope="{ row }">{{ row.mer_edition !== 0 ? '已开通' : '未开通' }}</template>
      </el-table-column>
      <el-table-column prop="annual_fee" label="年费金额" width="120" align="center" />
      <el-table-column label="操作" align="center">
        <template scope="scope">
          <router-link class='editfont' :to="{name: 'cuspackageEdit',params: {bus_id: scope.row.bus_id}}">
            编辑
          </router-link>
        </template>
      </el-table-column>
    </el-table>

    <div class="pos-rel p-t-20 ovf-hd">
      <div class="fl">
        <router-link class="btn-link add-btn" style="padding:9px 15px;" :to="{name:'packagesetControl'}">
          版本管理
        </router-link>
        <el-button size="small" @click="handleShowBatched('1')">批量延期</el-button>
        <el-button size="small" @click="handleShowBatched('2')">批量赋权</el-button>
      </div>
      <div class="block pages" v-if="dataCount>0">
        <el-pagination @current-change="handleCurrentChange" layout="total, prev, pager, next" :page-size="postData.page_size"
                       :current-page.sync="postData.page_no" :total="dataCount">
        </el-pagination>
      </div>
    </div>

    <PackageBatchedDialog
      :show.sync="showBatched"
      :type="batchedType"
      :selected="multipleSelection"
      @on-success="handleClearSelection"
    />

  </div>
</template>

<script>
  import http from 'assets/js/http';
  import PackageBatchedDialog from './components/packageBatchedDialog';

  export default {
    name: 'packageControl',
    components: {
      PackageBatchedDialog,
    },
    data() {
      return {
        dateRange: [
          _g.formatDate(new Date(new Date().setDate(1)), 'yyyy-MM-dd'),
          _g.formatDate(new Date(new Date(new Date().setMonth(new Date().getMonth() + 1)).setDate(0)), 'yyyy-MM-dd')
        ],
        postData: {
          merchants_name: '',
          business_name: '',
          version_id: '',
          page_size: 10,
          page_no: 1,
          start_date: _g.formatDate(new Date(new Date().setDate(1)), 'yyyy-MM-dd'),
          end_date: _g.formatDate(
            new Date(new Date(new Date().setMonth(new Date().getMonth() + 1)).setDate(0)),
            'yyyy-MM-dd'
          )
        },
        statics: null,
        tableData: [],
        dataCount: 0,
        versionList: [],

        multipleSelection: [],
        showBatched: false, // 批量操作弹窗
        batchedType: '1' // '1'延期, '2'赋权

      };
    },
    methods: {
      dateChange(dateRange) {
        dateRange = dateRange || [];
        const [start_date, end_date] = dateRange;
        this.postData.start_date = start_date;
        this.postData.end_date = end_date;
      },
      gettableList() {
        this.apiPost('/Web/Version/busList', this.postData).then(res => {
          _g.closeGlobalLoading();
          if (res.errorcode == 0) {
            this.tableData = res.data.list;
            this.dataCount = res.data.count;
            this.statics = res.data.statics;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.gettableList();
      },
      search() {
        this.postData.page_no = 1;
        this.gettableList();
      },
      getversionList() {
        let versionPost = {
          page_no: '',
          page_size: ''
        };
        this.apiPost('/Web/Version/getVersionList', versionPost).then(res => {
          if (res.errorcode == 0) {
            this.versionList = res.data.list;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      handleSelectionChange(val) {
        this.multipleSelection = val
      },
      handleShowBatched(type) {
        if (this.multipleSelection.length) {
          this.batchedType = type
          this.showBatched = true
        } else {
          this.$message.warning(`请勾选需要${ type === '1' ? '延期' : '赋权' }的商家`);
        }
      },
      handleClearSelection() {
        this.$refs.tableRef.clearSelection();
        this.gettableList()
      }
    },
    created() {
      this.getversionList();
      this.gettableList();
    },
    mixins: [http]
  };
</script>

<style scoped>
  .inpanel-c-c {
    padding: 20px;
  }

  .editfont {
    color: #fff;
    background-color: #409EFF;
    border-radius: 3px;
    padding: 9px 15px;
    font-size: 12px;
    display: inline-block;
    line-height: 1;
  }
  .static-wrap {
    display: flex;
    align-items: center;
    width: 100%;
    overflow-x: auto;
    margin-bottom: 16px;
  }
  .static-item {
    display: flex;
    flex-direction: column;
    width: auto;
    min-width: 145px;
    min-height: 82px;
    padding: 10px 16px;
    background: #F5F8FE;
    border-radius: 4px;
    margin-right: 16px;

    .static-title {
      white-space: nowrap;
      line-height: 20px;
      font-weight: bold;
      margin-bottom: 10px;
      font-size: 16px;
    }

    .static-num {
      line-height: 34px;
      font-size: 14px;
      .name {
        margin-right: 4px;
        font-size: 16px;
      }
    }

  }
</style>








