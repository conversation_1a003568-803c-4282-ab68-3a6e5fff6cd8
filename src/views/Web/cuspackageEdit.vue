<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="150px">
      <el-form-item label="请选择场馆" prop="bus_id" :rules="{ required: true, message: '请选择场馆' }">
        <el-select placeholder="请选择" @change="busChange" v-model="postData.bus_id" class="w-320" filterable>
          <el-option v-for="item in busList" :label="item.business_name" :key="item.bus_id" :value="item.bus_id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="签到方式" :rules="{ required: true, message: '请选择签到方式' }">
        <el-radio-group v-model="postData.sign_mode">
          <el-radio class="radio" :label="0">普通</el-radio>
          <el-radio class="radio" :label="1">指纹</el-radio>
          <el-radio class="radio" :label="2">指静脉</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- <el-form-item label="会员端私教签到" :rules="{required: true, message: '请选择会员端私教签到开启状态'}">
        <el-radio-group v-model="postData.sign_personal_trainer">
          <el-radio class="radio" :label="0">开启</el-radio>
          <el-radio class="radio" :label="1">关闭</el-radio>
        </el-radio-group>
      </el-form-item> -->
      <el-form-item label="系统版本" :rules="{ required: true, message: '请选择系统版本' }" prop="version_id">
        <el-radio-group v-model="postData.version_id" class="radiogroup">
          <el-radio v-for="item in versionList" class="radio radioitem" :key="item.id" :label="item.id">{{ item.name }}</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        label="商家端版本"
        :rules="{ required: postData.is_open_mer, message: '请选择商家端版本' }"
        prop="mer_version_id"
      >
        <el-switch v-model="postData.is_open_mer" />
        <span class="m-l-10 c-warning">注意商家端一但开通，商家下的所有门店都能进入商家端</span>
        <div v-if="postData.is_open_mer">
          <el-select v-model="postData.mer_version_id" class="w-320" filterable placeholder="请选择">
            <el-option v-for="item in merVersionList" :label="item.name" :key="item.id" :value="item.id"></el-option>
          </el-select>
        </div>
      </el-form-item>
      <el-form-item
        label="年费金额"
        prop="annual_fee"
        :rules="{ required: true, type: 'string', pattern: /^-?[0-9]+(.[0-9]{0,2})?$/, message: '必须为数字且只能保留两位小数' }"
      >
        <el-input class="w-320" v-model="postData.annual_fee" controls-position="right" placeholder="请填写"></el-input>
        <div class="c-warning">当前版本的年订阅费用</div>
      </el-form-item>
      <!-- FIXME: procrastination days -->
      <!-- 延期天数不可超过15天 -->
      <!-- <el-form-item label="延期天数" :rules="{required: true, validator: validateProcrastination }" prop="procrastination">
        <el-input-number :min="0" :max="365" v-model="postData.procrastination" controls-position="right" placeholder="请填写延期天数"></el-input-number>
      </el-form-item> -->
      <el-form-item label="到期时间" :rules="{ required: true, message: '请选择日期' }" prop="expire_date">
        <el-date-picker
          v-model="postData.expire_date"
          :editable="false"
          :clearable="false"
          class="m-r-10 w-320"
          type="date"
          @change="changeDate"
          placeholder="选择日期"
          :picker-options="datePickerOption"
        ></el-date-picker>
      </el-form-item>
      <el-form-item
        v-if="showStatus"
        label="当前状态"
        prop="renew_status"
        :rules="{ required: true, message: '请选择场馆付费状态' }"
      >
        <el-radio-group v-model="postData.renew_status">
          <template v-if="alreadyPaid">
            <el-radio class="radio" :label="5">临时延期</el-radio>
            <el-radio class="radio" :label="4">续费</el-radio>
          </template>
          <template v-else>
            <el-radio class="radio" :label="1">试用</el-radio>
            <el-radio class="radio" :label="3">首次付费</el-radio>
          </template>
        </el-radio-group>
      </el-form-item>
      <el-form-item
        v-if="showStatus && (postData.renew_status == 4 || postData.renew_status == 3)"
        label="合同编号"
        prop="renewal_contract"
        :rules="{ required: true, message: '请输入合同编号' }"
      >
        <el-input class="w-320" v-model="postData.renewal_contract" controls-position="right" placeholder="合同编号"></el-input>
      </el-form-item>
      <el-form-item
        v-if="showStatus && (postData.renew_status == 4 || postData.renew_status == 3)"
        label="合同金额"
        prop="renewal_fee"
        :rules="{ required: true, type: 'string', pattern: /^-?[0-9]+(.[0-9]{0,2})?$/, message: '必须为数字且只能保留两位小数' }"
      >
        <el-input class="w-320" v-model="postData.renewal_fee" controls-position="right" placeholder="合同金额"></el-input>
      </el-form-item>
      <el-form-item
        label="当前短信条数"
        prop="sms_number"
        :rules="{ required: true, type: 'string', pattern: /^[0-9]\d*$/, message: '必填项，零或正整数' }"
      >
        <el-input class="w-320" v-model="postData.sms_number"></el-input>
      </el-form-item>
      <el-form-item label="当前合同券">
        {{ contractNumber }}
        <el-button type="text" @click="showAddContract(1)">充值</el-button>
        <el-button type="text" style="color: red" @click="showAddContract(0)">扣除</el-button>
      </el-form-item>
      <AddContract
        ref="addContract"
        @on-success="reGetContractNumber"
        :plus="plus"
        :contractNumber="contractNumber"
        :bus-id="postData.bus_id"
        v-model="showAddContractDialog"
      />
      <el-form-item
        label="欠费金额"
        prop="bus_no_money"
        :rules="{ required: true, type: 'string', pattern: /^-?[0-9]+(.[0-9]{0,2})?$/, message: '必须为数字且只能保留两位小数' }"
      >
        <el-input class="w-320" v-model="postData.bus_no_money"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input v-model="postData.remark" class="w-320" :rows="4" type="textarea" placeholder="请输入备注..." />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" :loading="isLoading" @click="onSubmit">保存</el-button>
        <el-button class="m-l-150" @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>

    <div style="padding: 20px 30px">
      <p>到期时间操作记录</p>
      <div class="container">
        <el-table :data="tableData">
          <el-table-column type="index" label="#" width="60px"></el-table-column>
          <el-table-column prop="create_time" label="操作时间" width="180px"></el-table-column>
          <el-table-column prop="username" label="操作人" width="180px"></el-table-column>
          <el-table-column prop="bus_version_name" label="版本" width="180px"></el-table-column>
          <el-table-column prop="mer_version_name" label="商家版本" width="180px">
            <template scope="{ row }">{{ row.mer_version_id === 0 ? '未开通' : row.mer_version_name }}</template>
          </el-table-column>
          <el-table-column prop="after_status" label="场馆状态" width="180px"></el-table-column>
          <el-table-column prop="after_expire_time" label="更改后的到期时间" width="180px"></el-table-column>
          <el-table-column prop="after_renewal_contract" label="合同编号" width="120px"></el-table-column>
          <el-table-column prop="after_renewal_fee" label="合同金额" width="120px"></el-table-column>
        </el-table>
        <footer>
          <span></span>
          <Pager :total="total" @on-change="pageChange"></Pager>
        </footer>
      </div>
    </div>
  </div>
</template>

<script>
import http from 'assets/js/http'
import Pager from 'src/components/pager'
import AddContract from './components/AddContract.vue'

export default {
  name: 'cuspackageEdit',
  components: { Pager, AddContract },
  data() {
    return {
      showStatus: false,
      isLoading: false,
      tableData: [],
      total: 0,
      busList: [],
      versionList: [],
      merVersionList: [],
      alreadyPaid: true,
      showAddContractDialog: false,
      contractNumber: 0,
      plus: 1,
      postData: {
        bus_id: '',
        sign_mode: '',
        // sign_personal_trainer: '',
        expire_date: '',
        version_id: '',
        is_open_mer: false,
        mer_version_id: null,
        // mer_version_name: '',
        sms_number: '',
        bus_no_money: '',
        renew_status: '',
        annual_fee: '',
        renewal_fee: '',
        renewal_contract: '',
        user_id: '',
        page_no: 1,
        page_size: 10,
        remark: '',
      },
      datePickerOption: {
        shortcuts: [
          {
            text: '今天',
            onClick(picker) {
              picker.$emit('pick', new Date())
            },
          },
          {
            text: '30天后',
            onClick(picker) {
              const date = new Date(Date.now() + 30 * 24 * 3600 * 1000)
              picker.$emit('pick', date)
            },
          },
          {
            text: '60天后',
            onClick(picker) {
              const date = new Date(Date.now() + 60 * 24 * 3600 * 1000)
              picker.$emit('pick', date)
            },
          },
          {
            text: '90天后',
            onClick: (picker) => {
              const date = new Date(Date.now() + 90 * 24 * 3600 * 1000)
              picker.$emit('pick', date)
            },
          },
          {
            text: '增加30天',
            onClick: (picker) => {
              const old = new Date(this.postData.expire_date || Date.now())
              const date = new Date(old.getTime() + 30 * 24 * 3600 * 1000)
              picker.$emit('pick', date)
            },
          },
          {
            text: '增加60天',
            onClick: (picker) => {
              const old = new Date(this.postData.expire_date || Date.now())
              const date = new Date(old.getTime() + 60 * 24 * 3600 * 1000)
              picker.$emit('pick', date)
            },
          },
          {
            text: '增加90天',
            onClick: (picker) => {
              const old = new Date(this.postData.expire_date || Date.now())
              const date = new Date(old.getTime() + 90 * 24 * 3600 * 1000)
              picker.$emit('pick', date)
            },
          },
        ],
      },
    }
  },
  methods: {
    // FIXME: procrastination days validator
    // 延期天数不可超过15天
    // validateProcrastination(rule, value, callback) {
    //   if (!value) {
    //     callback(new Error('请填写延期天数'))
    //   } else if (value > 15) {
    //     callback(new Error('延期天数不可超过15天'))
    //   } else {
    //     callback()
    //   }
    // },
    showAddContract(plus) {
      this.plus = plus
      this.showAddContractDialog = true
    },
    pageChange({ page_no, page_size }) {
      this.postData.page_no = page_no
      this.postData.page_size = page_size
      this.getpageInfo()
    },
    busChange() {
      this.getpageInfo()
    },
    changeDate(value) {
      if (this.postData.renew_status === 3) this.postData.renew_status = 5
      this.showStatus = true
      this.postData.expire_date = _g.formatDate(value, 'yyyy-MM-dd')
    },
    onSubmit() {
      if(this.isLoading) return
      
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.isLoading = true
          if (new Date(this.postData.expire_date) - Date.now() > 90 * 24 * 3600 * 1000 && this.postData.renew_status === 1) {
            return this.$confirm('试用时间大于三个月(90天)，是否继续？', '提示').then(() => this.doSubmit())
          }
          this.doSubmit()
        }
      })
    },
    doSubmit() {
      const postData = { ...this.postData }
      // 如果没有开启商家端则商家端版本为0
      if (!postData.is_open_mer || postData.mer_version_id === null) {
        postData.mer_version_id = 0
      }
      delete postData.is_open_mer

      postData.user_id = this.$store.state.userInfo.id
      this.apiPost('/Web/version/editBusVersion', postData).then((res) => {
        if (res.errorcode == 0) {
          _g.toastMsg('success', res.errormsg)
          this.$router.back()
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
        this.isLoading = false
      })
    },
    reGetContractNumber() {
      this.apiPost('/Web/version/busVersionInfo', {
        bus_id: this.postData.bus_id,
        page_no: this.postData.page_no,
        page_size: this.postData.page_size,
      }).then((res) => {
        _g.closeGlobalLoading()
        if (res.errorcode == 0) {
          this.contractNumber = res.data.contract_number
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    getpageInfo() {
      this.apiPost('/Web/version/busVersionInfo', {
        bus_id: this.postData.bus_id,
        page_no: this.postData.page_no,
        page_size: this.postData.page_size,
      })
        .then((res) => {
          _g.closeGlobalLoading()
          if (res.errorcode == 0) {
            this.postData = res.data
            this.postData.bus_id = res.data.id
            this.postData.sign_mode = Number(res.data.sign_mode)
            // this.postData.sign_personal_trainer = Number(res.data.sign_personal_trainer);
            this.postData.version_id = Number(res.data.version_id)
            this.$set(this.postData, 'is_open_mer', !!Number(res.data.mer_version_id))
            this.postData.mer_version_id = Number(res.data.mer_version_id) || null
            // this.postData.mer_version_name = res.data.mer_version_name || '';
            this.postData.expire_date = String(res.data.expire_date)
            this.postData.sms_number = String(res.data.sms_number)
            this.postData.bus_no_money = String(res.data.bus_no_money || 0)

            this.tableData = res.data.operation.list
            this.total = res.data.operation.count
            this.contractNumber = res.data.contract_number
            if (res.data.renew_status === 2) res.data.renew_status = 1
            if (res.data.renew_status === 1) {
              this.alreadyPaid = false
            }
          } else {
            _g.toastMsg('warning', res.errormsg)
          }
        })
        .catch((error) => {
          console.log(error)
        })
    },
    // 获取系统版本列表 versionType 1 系统版本 2 商家权限的版本
    async getversionList(versionType) {
      let versionPost = {
        type: versionType,
        page_no: '',
        page_size: '',
      }
      return new Promise((resolve, reject) => {
        this.apiPost('/Web/Version/getVersionList', versionPost)
          .then((res) => {
            if (res.errorcode == 0) {
              this[versionType === 2 ? 'merVersionList' : 'versionList'] = res.data.list
              resolve()
            } else {
              _g.toastMsg('warning', res.errormsg)
              reject(err)
            }
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
    async getbusList() {
      let busPost = {
        page_no: '',
        page_size: '',
      }
      return new Promise((resolve, reject) => {
        this.apiPost('/Web/Version/busList', busPost)
          .then((res) => {
            if (res.errorcode == 0) {
              this.busList = res.data.list
              resolve()
            } else {
              _g.toastMsg('warning', res.errormsg)
              reject(err)
            }
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
  },
  created() {
    this.postData.bus_id = Number(this.$route.params.bus_id)
    this.getpageInfo()
    this.getversionList(1)
    this.getversionList(2)
    this.getbusList()
  },
  mixins: [http],
}
</script>

<style scoped>
.maright-20 {
  margin-right: 20px;
}

.unbind {
  background: url(../../assets/images/unbind.png);
  background-size: contain;
  width: 32px;
  height: 32px;
  background-repeat: no-repeat;
  border-color: #fff;
}

.profilepicbg {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: center;
  align-items: center;
  width: 200px;
  height: 180px;
  padding: 10px;
  background: #fff;
}

.profilepic {
  width: 180px;
  height: 160px;
}

.radiogroup {
  width: 80%;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: flex-start;
  flex-wrap: wrap;
}

.radioitem {
  margin-left: 0;
  margin-right: 30px;
  height: 40px;
  line-height: 40px;
}
</style>
