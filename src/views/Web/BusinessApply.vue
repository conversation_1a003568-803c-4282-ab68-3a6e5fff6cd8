
<style lang="less" scoped>
</style>
<template>
  <div class="container">
    <header>
      <el-input style="width: 200px" placeholder="场馆名称或电话" v-model="name" @keydown.enter.native="getList"></el-input>
      <el-select v-model="status" placeholder="审核状态" clearable>
        <el-option :value="1" label="待审核">待审核</el-option>
        <el-option :value="2" label="重新审核">重新审核</el-option>
        <el-option :value="3" label="未通过">未通过</el-option>
        <el-option :value="4" label="已通过">已通过</el-option>
      </el-select>
      <el-button type="success" @click="getList">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe @select-all="selectAll" @selection-change="selectItem">
      <el-table-column type="selection" label="选择" align="center" width="50"></el-table-column>
      <el-table-column prop="bus_name" align="center" label="场馆名称"></el-table-column>
      <el-table-column prop="apply_name" align="center" width="80" label="姓名"></el-table-column>
      <el-table-column prop="phone" align="center" label="手机号"></el-table-column>
      <el-table-column prop="pcd_address" align="center" label="省市区"></el-table-column>
      <!-- <el-table-column prop="address" align="center" label="地址"></el-table-column> -->
      <el-table-column prop="bus_type" width="80" align="center" label="场馆类型"></el-table-column>
      <!-- <el-table-column prop="member_scale" width="80" align="center" label="场馆规模"></el-table-column> -->
      <el-table-column prop="license" width="60" align="center" label="营业执照">
        <template scope="scope">
          <a :href="scope.row.license" target="_blank" style="color: #20a0ff">查看</a>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="accounts" width="80" align="center" label="申请账号"></el-table-column> -->
      <el-table-column prop="create_time" align="center" label="申请时间"></el-table-column>
      <el-table-column prop="init_msg" align="center" label="系统初始执行状态(如显示授权按钮，需先授权再审核)"></el-table-column>
      <el-table-column prop="recommender" align="center" label="推荐人"></el-table-column>
      <el-table-column prop="status" width="80" align="center" label="审核状态">
        <template scope="scope">
          <p>{{statusText[scope.row.status - 1]}}</p>
        </template>
      </el-table-column>
      <el-table-column prop="id" width="240" align="center" label="操作">
        <template scope="scope">
          <template v-if="scope.row.status == 1 || scope.row.status == 2">
            <el-button size="mini" type="success" @click="handleOperate(scope.row.id, 4)">通过</el-button>
            <el-button size="mini" type="info" @click="handleOperate(scope.row.id, 3)">不通过</el-button>
          </template>
          <el-button size="mini" type="info" v-if="scope.row.status == 3" @click="handleOperate(scope.row.id, 2)">重新审核</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <el-upload class="upload-demo" ref="upload" :action="uploadUrl" :before-upload="beforeUpload" :on-success="handleUploadSuccess" :show-file-list="false" :data="{savePath: './Uploads/'}" :file-list="fileList" :multiple="false">
        <el-button slot="trigger" size="small" type="primary">重新上传营业执照</el-button>
      </el-upload>
      <el-pagination layout="total, prev, pager, next, sizes" background @size-change="sizeChange" @current-change="pageChange" :page-size="pageSize" :current-page.sync="page" :page-sizes="[10, 20, 30, 40]" :total="total"></el-pagination>
    </footer>
  </div>
</template>

<script>
  // const BUS_TYPE = ['健身房', '瑜伽馆', '跆拳道馆', '武道馆', '舞蹈馆', '健身工作室'];
  const BUS_TYPE = {
    '1': '健身房',
    '2': '瑜伽馆',
    '3': '跆拳道馆',
    '4': '武道馆',
    '5': '舞蹈馆',
    '6': '其他',
    '7': '体育馆',
    '8': '游泳馆',
    '9': '健身工作室',
  };
  const SCALE = ['100人以下', '100-500人', '500人以上'];

  export default {
    name: 'BusinessApply',
    data() {
      return {
        uploadUrl: _g.getRbBaseUrl() + '/Admin/Public/upload',
        id: '',

        page: 1,
        pageSize: 10,
        total: 0,

        status: '',
        statusText: ['待审核', '重新审核', '未通过', '已通过'],
        name: '',
        tableData: []
      };
    },
    created() {
      this.getList();
    },
    methods: {
      handleUploadSuccess(res, file) {
        if (res.status === 1) {
          this.changeLicense(res.info);
        } else {
          this.$message.error('上传失败');
        }
      },
      beforeUpload(file) {
        if (!this.id) {
          this.$message.error('请选择上传商家!');
          return false;
        }
        const fileType = ['image/jpg', 'image/png', 'image/jpeg'];
        const isJPG = fileType.includes(file.type);
        const isLt2M = file.size / 1024 / 1024 < 4;

        if (!isJPG) {
          this.$message.error('上传图片只能是 JPG/JPEG/PNG 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 4MB!');
        }
        return isJPG && isLt2M;
      },
      handleOperate(id, status) {
        const url = '/Web/Merchants/ajaxCheckApply';
        this.$service
          .post(url, { id, status })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$message.success(res.data.errormsg);
              this.getList();
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      changeLicense(image) {
        const url = '/Web/Merchants/ajaxUpdateImage';
        const postData = { image, id: this.id };
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$message.success(res.data.errormsg);
              this.id = '';
              this.getList();
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      pageChange(e) {
        this.page = e;
        this.getList();
      },
      sizeChange(size) {
        this.page = 1;
        this.pageSize = size;
        this.getList();
      },
      selectAll(items) {},
      selectItem(item) {
        if (!item.length) {
          this.id = '';
          return false;
        }
        if (item.length > 2) {
          item.splice(0, item.length);
          return false;
        }
        if (item.length > 1) {
          item.shift();
          this.id = item[0].id;
          return false;
        }
        this.id = item[0].id;
      },
      getList() {
        const url = '/Web/Merchants/applyList';
        const { page: page_no, pageSize: page_size, name, status } = this;
        this.$service
          .post(url, { page_no, page_size, name, status })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.total = data.count;
              this.tableData = data.list.map(item => {
                return {
                  ...item,
                  ...{
                    bus_type: BUS_TYPE[item.bus_type],
                    member_scale: SCALE[item.member_scale - 1],
                    create_time: _g.formatDate(new Date(item.create_time * 1000), 'yyyy-MM-dd HH:mm:ss')
                  }
                };
              });
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>
