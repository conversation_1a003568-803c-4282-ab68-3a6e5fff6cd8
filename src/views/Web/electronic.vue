<template>
  <div class="container">
    <div class="ovf-hd searchbar">
      <div class="fl m-l-30">
        <el-input
          placeholder="公司"
          @keyup.enter.native="search"
          v-model="postData.org_name"
          class="w-150 m-r-10 p-t-20"
        ></el-input>
        <el-select v-model="postData.auth_status" class="w-150 m-r-10" placeholder="开通状态" clearable>
          <el-option :value="1" label="待认证"></el-option>
          <el-option :value="2" label="已开通"></el-option>
          <el-option :value="3" label="认证失败"></el-option>
        </el-select>
        <el-select v-model="postData.authorization_status" class="w-150 m-r-10" placeholder="授权状态" clearable>
          <el-option :value="0" label="未授权"></el-option>
          <el-option :value="3" label="授权中"></el-option>
          <el-option :value="1" label="已授权"></el-option>
          <el-option :value="2" label="授权失败"></el-option>
        </el-select>
        <el-button type="primary" icon="search" @click="search" class="m-t-20">搜索</el-button>
      </div>
    </div>

    <el-table :data="tableData" id="infotable" stripe>
      <el-table-column prop="org_name" label="认证企业" align="center"></el-table-column>
      <el-table-column prop="bus_names" label="场馆" align="center">
        <template slot-scope="{ row }">
          <span>{{ Array.isArray(row.bus_names) ? row.bus_names.join('、') : '—' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="auth_status" label="开通状态" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.auth_status === 1 ? '待认证' : row.auth_status === 2 ? '已开通' : '认证失败' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="authorization_status" label="授权状态" align="center">
        <template slot-scope="{ row }">
          <span>{{ row.authorization_status === 1 ? '已授权' : row.authorization_status === 2 ? '授权失败' : row.authorization_status === 3 ? '授权中' : '未授权' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="org_number" label="社会统一信用代码" align="center"></el-table-column>
      <el-table-column prop="org_legal_name" label="企业法人" align="center"></el-table-column>
      <el-table-column prop="url_time" label="发起时间" align="center"></el-table-column>
      <el-table-column prop="authorization_url" label="授权任务链接" align="center"></el-table-column>
      <el-table-column label="操作" align="center" width="200">
        <template scope="{row}">
          <div>
            <el-button size="small" @click="editEle(row)" type="primary">{{row.authorization_status !== 1 ? '编辑':'编辑门店'}}</el-button>
            <el-button size="small" v-if="row.auth_status !== 2 && row.authorization_status === 1" @click="cancelCur(row)" type="primary">
              放弃
            </el-button>
            <el-button size="small" v-if="(!row.authorization_url && row.authorization_status === 0) || row.authorization_status === 2" type="primary" @click="authCur(row)">
              企业授权
            </el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <footer>
      <div class="left">
        <el-button type="success" size="small" icon="el-icon-plus" @click="addRenzheng">新增企业授权</el-button>
      </div>
      <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next, sizes"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
      ></el-pagination>
    </footer>
    <el-dialog :title="isEdit?'编辑企业授权':'新增企业授权'" :visible.sync="showDialog" :close-on-click-modal="false" custom-class="w-500">
      <el-form :model="dialogData" ref="orgForm" label-width="140px">
        <el-form-item prop="org_name" label="组织机构名称" :rules="[{ required: true, message: '请输入组织机构名称' }]">
          <el-input
            v-model="dialogData.org_name"
            placeholder="请填写"
            class="w-300"
            :disabled="!!dialogData.account_encryption"
          ></el-input>
        </el-form-item>
        <el-form-item prop="org_id" label="社会统一信用代码" :rules="[{ required: true, message: '请输入社会统一信用代码' }]">
          <el-input
            v-model="dialogData.org_id"
            placeholder="请填写"
            class="w-300"
            :disabled="!!dialogData.account_encryption"
          ></el-input>
        </el-form-item>
        <el-form-item prop="org_legal_name" label="法定代表人姓名" :rules="[{ required: true, message: '请输入法定代表人姓名' }]">
          <el-input
            v-model="dialogData.org_legal_name"
            placeholder="请填写"
            class="w-300"
            :disabled="isEdit && curEditInfo.authorization_status === 1"
          ></el-input>
        </el-form-item>
        <el-form-item
          prop="mobile"
          label="法人手机号"
          :rules="[
            { required: true, message: '请填写手机号' },
            { message: '请输入正确的手机号码', pattern: /^1\d{10}$/ },
          ]"
        >
          <el-input
            v-model="dialogData.mobile"
            placeholder="请填写"
            class="w-300"
            :disabled="isEdit && curEditInfo.authorization_status === 1"
          ></el-input>
        </el-form-item>
        <el-form-item prop="bus_ids" label="场馆">
          <el-select
            v-if="busList && busList.length"
            v-model="dialogData.bus_ids"
            multiple
            filterable
            placeholder="请输入场馆名称"
            class="w-300"
          >
            <el-option v-for="item in busList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="handleSave">保存</el-button>
          <el-button type="info" @click="showDialog = false">取消</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'electronic',

  data() {
    return {
      showDialog: false,
      isEdit: false,
      curEditInfo: {},
      busList: [],
      dialogData: {
        account_encryption: '',
        bus_ids: [],
        org_name: '',
        org_id: '',
        org_legal_name: '',
        mobile: '',
        
        org_legal_id_number: '',
      },
      postData: {
        auth_status: '',
        authorization_status: '',
        bus_name: '',
        org_name: '',
        page_size: 10,
        page_no: 1,
      },
      tableData: [],
      dataCount: 0,
    }
  },
  watch: {
    showDialog(val) {
      if (!val) {
        this.isEdit = false
        this.curEditInfo = {}
        this.dialogData = {
          account_encryption: '',
          bus_ids: [],
          org_name: '',
          org_id: '',
          mobile: '',
          org_legal_name: '',
          org_legal_id_number: '',
        }
      }
    },
  },
  methods: {
    cancelCur(info) {
      this.$confirm('确认放弃吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
      })
        .then(() => {
          this.$service.post('/web/Esign/unbind_bus_org', { account_encryption: info.account_encryption }).then((res) => {
            if (res.data.errorcode == 0) {
              this.showDialog = false
              _g.toastMsg('success', '操作成功')
              this.gettableList()
            } else {
              _g.toastMsg('warning', res.data.errormsg)
            }
          })
        })
        .catch(() => {})
    },
    authCur(info) {
      this.$service.post('/web/Esign/orgAuthorize', { account_encryption: info.account_encryption }).then((res) => {
        if (res.data.errorcode == 0) {
          this.showDialog = false
          _g.toastMsg('success', '操作成功')
          this.gettableList()
        } else {
          _g.toastMsg('warning', res.data.errormsg)
        }
      })
    },
    editEle(info) {
      this.showDialog = true
      this.isEdit = true
      this.curEditInfo = info
      this.dialogData.account_encryption = info.account_encryption
      this.dialogData.bus_ids = info.bus_ids
      this.dialogData.org_name = info.org_name
      this.dialogData.org_id = info.org_id
      this.dialogData.mobile = info.mobile
      this.dialogData.org_legal_name = info.org_legal_name
      this.dialogData.org_legal_id_number = info.org_legal_id_number
    },
    handleSave() {
      this.$refs.orgForm.validate((val) => {
        if (val) {
          const url = this.dialogData.account_encryption ? '/web/Esign/updateAuthInfo' : '/web/Esign/addOrgAccountAuth'
          this.$service.post(url, this.dialogData).then((res) => {
            if (res.data.errorcode == 0) {
              this.showDialog = false
              _g.toastMsg('success', '操作成功')
              this.gettableList()
            } else {
              _g.toastMsg('warning', res.data.errormsg)
            }
          })
        }
      })
    },
    handleSizeChange(val) {
      this.postData.page_size = val
      this.postData.page_no = 1
      this.gettableList()
    },
    addRenzheng() {
      this.showDialog = true
    },
    getBusList() {
      this.$service.post('/web/business/bus_list').then((res) => {
        this.loading = false
        if (res.data.errorcode == 0) {
          const reData = res.data.data
          this.busList = reData.bus_list
        } else {
          _g.toastMsg('warning', res.data.errormsg)
        }
      })
    },
    gettableList() {
      this.$service.post('/web/Esign/getOrgAuthList', this.postData).then((res) => {
        if (res.data.errorcode == 0) {
          const reData = res.data.data
          this.tableData = reData.list
          this.dataCount = reData.count
        } else {
          _g.toastMsg('warning', res.data.errormsg)
        }
      })
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage
      this.gettableList()
    },
    search() {
      this.postData.page_no = 1
      this.gettableList()
    },
  },
  created() {
    this.gettableList()
    this.getBusList()
  },
}
</script>

<style scoped>
.m-r-5 {
  margin-right: 5px;
}
.editfont {
  color: #fff;
}
.searchbar {
  background-color: #fff;
  min-height: 80px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  border-bottom: 1px solid #ececec;
  padding-bottom: 20px;
}
.el-dialog__footer {
  padding: 0;
}
.centeritem {
  display: flex;
  align-items: center;
  justify-content: center;
}
.qricon {
  border: none;
  width: 32px;
  height: 32px;
  background: url(../../assets/images/qrcode.png) no-repeat center;
  background-size: 32px 32px;
}
</style>
