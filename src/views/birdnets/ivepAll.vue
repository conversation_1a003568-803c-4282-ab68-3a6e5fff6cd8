<template>
  <div class="container">
    <header>
      <el-date-picker v-model="createTimeRange"
                      @change="createTimeRangeChange"
                      type="daterange"
                      align="right"
                      unlink-panels
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      value-format="yyyy-MM-dd"
                      :picker-options="pickerOptions">
      </el-date-picker>
    </header>
    <div class="data-wrap">
      <div class="data-box box1"
           v-if="accountsStatistics">
        <h3 class="graphtitle">
          账号数据
        </h3>
        <ul class="compdata">
          <li class="compdata-item">
            <h3>总场馆数</h3>
            <p class="num">
              {{accountsStatistics.bus_count}}
            </p>

          </li>
          <li class="compdata-item">
            <h3>近期开通</h3>
            <p class="num">
              {{accountsStatistics.bus_recently_opened_count.value}}
            </p>
            <p class="colorwords"
               :class="{'redup':accountsStatistics.bus_recently_opened_count.ring_ratio_data.ring_ratio_type === '+','greendown':accountsStatistics.bus_recently_opened_count.ring_ratio_data.ring_ratio_type === '-','black': !accountsStatistics.bus_recently_opened_count.ring_ratio_data.ring_ratio_type}">环比 {{accountsStatistics.bus_recently_opened_count.ring_ratio_data.ring_ratio}}</p>
          </li>
          <li class="compdata-item">
            <h3>近期活跃<Info value="期间上课数>=登录过的教练×天数×2" /></h3>
            <p class="num">
              {{accountsStatistics.recently_active_number.value}}
            </p>
            <p class="colorwords"
               :class="{'redup':accountsStatistics.recently_active_number.ring_ratio_data.ring_ratio_type === '+','greendown':accountsStatistics.recently_active_number.ring_ratio_data.ring_ratio_type === '-','black': !accountsStatistics.recently_active_number.ring_ratio_data.ring_ratio_type}">环比 {{accountsStatistics.recently_active_number.ring_ratio_data.ring_ratio}}</p>
          </li>
          <li class="compdata-item">
            <h3>总开通账号</h3>
            <p class="num">
              {{accountsStatistics.open_accounts_count}}
            </p>
          </li>
          <li class="compdata-item">
            <h3>总账号绑定</h3>
            <p class="num">
              {{accountsStatistics.coach_acounts_count}}
            </p>
          </li>
          <li class="compdata-item">
            <h3>总账号登录</h3>
            <p class="num">
              {{accountsStatistics.coach_login_count}}
            </p>
          </li>
          <li class="compdata-item">
            <h3>近期开通账号</h3>
            <p class="num">
              {{accountsStatistics.recently_open_accounts_count.value}}
            </p>
            <p class="colorwords"
               :class="{'redup':accountsStatistics.recently_open_accounts_count.ring_ratio_data.ring_ratio_type === '+','greendown':accountsStatistics.recently_open_accounts_count.ring_ratio_data.ring_ratio_type === '-','black': !accountsStatistics.recently_open_accounts_count.ring_ratio_data.ring_ratio_type}">环比 {{accountsStatistics.recently_open_accounts_count.ring_ratio_data.ring_ratio}}</p>
          </li>
          <li class="compdata-item">
            <h3>近期绑定</h3>
            <p class="num">
              {{accountsStatistics.recently_coach_acounts_count.value}}
            </p>
            <p class="colorwords"
               :class="{'redup':accountsStatistics.recently_coach_acounts_count.ring_ratio_data.ring_ratio_type === '+','greendown':accountsStatistics.recently_coach_acounts_count.ring_ratio_data.ring_ratio_type === '-','black': !accountsStatistics.recently_coach_acounts_count.ring_ratio_data.ring_ratio_type}">环比 {{accountsStatistics.recently_coach_acounts_count.ring_ratio_data.ring_ratio}}</p>
          </li>
          <li class="compdata-item">
            <h3>近期登录</h3>
            <p class="num">
              {{accountsStatistics.recently_coach_login_count.value}}
            </p>
            <p class="colorwords"
               :class="{'redup':accountsStatistics.recently_coach_login_count.ring_ratio_data.ring_ratio_type === '+','greendown':accountsStatistics.recently_coach_login_count.ring_ratio_data.ring_ratio_type === '-','black': !accountsStatistics.recently_coach_login_count.ring_ratio_data.ring_ratio_type}">环比 {{accountsStatistics.recently_coach_login_count.ring_ratio_data.ring_ratio}}</p>
          </li>
        </ul>
      </div>
      <div class="data-box box2"
           v-if="memberStatistics">
        <h3 class="graphtitle">
          会员数据
        </h3>
        <ul class="compdata">
          <li class="compdata-item">
            <h3>总上课会员数（去重)</h3>
            <p class="num">
              {{memberStatistics.attend_course_member_count}}
            </p>

          </li>
          <li class="compdata-item">
            <h3>近期上课会员数（去重）</h3>
            <p class="num">
              {{memberStatistics.recently_attend_course_member_count.value}}
            </p>
            <p class="colorwords"
               :class="{'redup':memberStatistics.recently_attend_course_member_count.ring_ratio_data.ring_ratio_type === '+','greendown':memberStatistics.recently_attend_course_member_count.ring_ratio_data.ring_ratio_type === '-','black': !memberStatistics.recently_attend_course_member_count.ring_ratio_data.ring_ratio_type}">环比 {{memberStatistics.recently_attend_course_member_count.ring_ratio_data.ring_ratio}}</p>
          </li>
          <li class="compdata-item">
            <h3>IVEP服务会员总数<Info value="指会员在ivep上产生过接待、场开、上课等数据" /></h3>
            <p class="num">
              {{memberStatistics.ivep_service_member_count}}
            </p>

          </li>
          <li class="compdata-item">
            <h3>近期服务会员数（去重）</h3>
            <p class="num">
              {{memberStatistics.ivep_recently_service_member_count.number}}
            </p>
            <p class="colorwords"
               :class="{'redup':memberStatistics.ivep_recently_service_member_count.ring_ratio_data.ring_ratio_type === '+','greendown':memberStatistics.ivep_recently_service_member_count.ring_ratio_data.ring_ratio_type === '-','black': !memberStatistics.ivep_recently_service_member_count.ring_ratio_data.ring_ratio_type}">环比 {{memberStatistics.ivep_recently_service_member_count.ring_ratio_data.ring_ratio}}</p>
          </li>
        </ul>
      </div>
    </div>
    <div class="data-wrap">
      <div class="data-box box3"
           v-if="useStatus">
        <h3 class="graphtitle">
          使用情况
        </h3>
        <ul class="compdata">
          <li class="compdata-item">
            <h3>总上课数</h3>
            <p class="num">
              {{useStatus.attend_count}}
            </p>

          </li>
          <li class="compdata-item"
              v-if="typeof useStatus.recently_attend_count!=='undefined'">
            <h3>近期上课数</h3>
            <p class="num">
              {{useStatus.recently_attend_count.value}}
            </p>
            <p class="colorwords"
               :class="{'redup':useStatus.recently_attend_count.ring_ratio_data.ring_ratio_type === '+','greendown':useStatus.recently_attend_count.ring_ratio_data.ring_ratio_type === '-','black': !useStatus.recently_attend_count.ring_ratio_data.ring_ratio_type}">环比 {{useStatus.recently_attend_count.ring_ratio_data.ring_ratio}}</p>
          </li>
          <li class="compdata-item">
            <h3>备课总数</h3>
            <p class="num">
              {{useStatus.pre_course_count}}
            </p>
          </li>
          <li class="compdata-item"
              v-if="typeof useStatus.recently_pre_course_count!=='undefined'">
            <h3>近期备课数</h3>
            <p class="num">
              {{useStatus.recently_pre_course_count.value}}
            </p>
            <p class="colorwords"
               :class="{'redup':useStatus.recently_pre_course_count.ring_ratio_data.ring_ratio_type === '+','greendown':useStatus.recently_pre_course_count.ring_ratio_data.ring_ratio_type === '-','black': !useStatus.recently_pre_course_count.ring_ratio_data.ring_ratio_type}">环比 {{useStatus.recently_pre_course_count.ring_ratio_data.ring_ratio}}</p>
          </li>
          <li class="compdata-item">
            <h3>备课率</h3>
            <p class="num">
              {{useStatus.pre_course_rate}}
            </p>
          </li>
          <li class="compdata-item"
              v-if="typeof useStatus.recently_pre_course_rate!=='undefined'">
            <h3>近期备课率</h3>
            <p class="num">
              {{useStatus.recently_pre_course_rate.value}}
            </p>
            <p class="colorwords"
               :class="{'redup':useStatus.recently_pre_course_rate.ring_ratio_data.ring_ratio_type === '+','greendown':useStatus.recently_pre_course_rate.ring_ratio_data.ring_ratio_type === '-','black': !useStatus.recently_pre_course_rate.ring_ratio_data.ring_ratio_type}">环比 {{useStatus.recently_pre_course_rate.ring_ratio_data.ring_ratio}}</p>
          </li>
          <li class="compdata-item">
            <h3>总接待数</h3>
            <p class="num">
              {{useStatus.pos_info_count}}
            </p>
          </li>
          <li class="compdata-item"
              v-if="typeof useStatus.recently_pos_info_count!=='undefined'">
            <h3>近期接待数</h3>
            <p class="num">
              {{useStatus.recently_pos_info_count.value}}
            </p>
            <p class="colorwords"
               :class="{'redup':useStatus.recently_pos_info_count.ring_ratio_data.ring_ratio_type === '+','greendown':useStatus.recently_pos_info_count.ring_ratio_data.ring_ratio_type === '-','black': !useStatus.recently_pos_info_count.ring_ratio_data.ring_ratio_type}">环比 {{useStatus.recently_pos_info_count.ring_ratio_data.ring_ratio}}</p>
          </li>
          <li class="compdata-item"
              v-if="typeof useStatus.recently_pos_info_success_rate!=='undefined'">
            <h3>近期接待成功率</h3>
            <p class="num">
              {{useStatus.recently_pos_info_success_rate.value}}
            </p>
            <p class="colorwords"
               :class="{'redup':useStatus.recently_pos_info_success_rate.ring_ratio_data.ring_ratio_type === '+','greendown':useStatus.recently_pos_info_success_rate.ring_ratio_data.ring_ratio_type === '-','black': !useStatus.recently_pos_info_success_rate.ring_ratio_data.ring_ratio_type}">环比 {{useStatus.recently_pos_info_success_rate.ring_ratio_data.ring_ratio}}</p>
          </li>
          <li class="compdata-item">
            <h3>场开总数</h3>
            <p class="num">
              {{useStatus.bus_open_count}}
            </p>

          </li>
          <li class="compdata-item"
              v-if="typeof useStatus.recently_bus_open_count!=='undefined'">
            <h3>近期场开数</h3>
            <p class="num">
              {{useStatus.recently_bus_open_count.value}}
            </p>
            <p class="colorwords"
               :class="{'redup':useStatus.recently_bus_open_count.ring_ratio_data.ring_ratio_type === '+','greendown':useStatus.recently_bus_open_count.ring_ratio_data.ring_ratio_type === '-','black': !useStatus.recently_bus_open_count.ring_ratio_data.ring_ratio_type}">环比 {{useStatus.recently_bus_open_count.ring_ratio_data.ring_ratio}}</p>

          </li>
          <li class="compdata-item"
              v-if="typeof useStatus.recently_bus_open_success_rate!=='undefined'">
            <h3>近期场开成功率</h3>
            <p class="num">
              {{useStatus.recently_bus_open_success_rate.value}}
            </p>
            <p class="colorwords"
               :class="{'redup':useStatus.recently_bus_open_success_rate.ring_ratio_data.ring_ratio_type === '+','greendown':useStatus.recently_bus_open_success_rate.ring_ratio_data.ring_ratio_type === '-','black': !useStatus.recently_bus_open_success_rate.ring_ratio_data.ring_ratio_type}">环比 {{useStatus.recently_bus_open_success_rate.ring_ratio_data.ring_ratio}}</p>
          </li>
          <li class="compdata-item"
              v-if="typeof useStatus.experience_attend_course_count!=='undefined'">
            <h3>体验课总数</h3>
            <p class="num">
              {{useStatus.experience_attend_course_count}}
            </p>
          </li>
          <li class="compdata-item"
              v-if="typeof useStatus.recently_experience_attend_course_count!=='undefined'">
            <h3>近期体验课</h3>
            <p class="num">
              {{useStatus.recently_experience_attend_course_count.value}}
            </p>
            <p class="colorwords"
               :class="{'redup':useStatus.recently_experience_attend_course_count.ring_ratio_data.ring_ratio_type === '+','greendown':useStatus.recently_experience_attend_course_count.ring_ratio_data.ring_ratio_type === '-','black': !useStatus.recently_experience_attend_course_count.ring_ratio_data.ring_ratio_type}">环比 {{useStatus.recently_experience_attend_course_count.ring_ratio_data.ring_ratio}}</p>
          </li>
          <li class="compdata-item"
              v-if="typeof useStatus.experience_attend_clinch_rate!=='undefined'">
            <h3>体验课成交率</h3>
            <p class="num">
              {{useStatus.experience_attend_clinch_rate}}
            </p>
          </li>
        </ul>
      </div>
    </div>

  </div>
</template>

<script>
import { formatDate } from 'src/assets/js/utils'
import Info from 'src/components/info'
export default {
  name: 'ivepAll',
  components: {Info},
  data () {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '近7天',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近30天',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近90天',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      createTimeRange: [formatDate(new Date(Date.now() - 7 * 3600 * 1000 * 24), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd')],
      postData: {
        query_type: 1,
        start_time: formatDate(new Date(Date.now() - 7 * 3600 * 1000 * 24), 'yyyy-MM-dd'),
        end_time: formatDate(new Date(), 'yyyy-MM-dd')
      },
      accountsStatistics: '',
      memberStatistics: '',
      useStatus: ''
    }
  },
  created () {
    this.getData();
  },
  mounted () {
  },
  methods: {
    createTimeRangeChange (d) {
      const [s, e] = d || [];
      this.postData.start_time = s;
      this.postData.end_time = e;
      this.getData()
    },
    getData () {
      this.$service.post('/ivep/IvepStatistics/getAccountsStatistics', this.postData).then(res => {
        if (res.data.errorcode === 0) {
          this.accountsStatistics = res.data.data;
        } else {
          this.$message.error(res.data.errormsg);
        }
      }).catch(e => {
        throw new Error(e)
      })
      this.$service.post('/ivep/IvepStatistics/ivepUseStatus', this.postData).then(res => {
        if (res.data.errorcode === 0) {
          this.useStatus = res.data.data;
        } else {
          this.$message.error(res.data.errormsg);
        }
      }).catch(e => {
        throw new Error(e)
      })
      this.$service.post('/ivep/IvepStatistics/memberStatistics', this.postData).then(res => {
        if (res.data.errorcode === 0) {
          this.memberStatistics = res.data.data;
        } else {
          this.$message.error(res.data.errormsg);
        }
      }).catch(e => {
        throw new Error(e)
      })
    }
  }
}
</script>

<style scoped lang="less">
.data-wrap {
  width: 100%;
  display: flex;
  padding: 10px;
  box-sizing: border-box;
}
.data-box {
  border: 1px solid #dcdcdc;
  margin-bottom: 20px;
  ul {
    padding: 0 30px;
    margin: 0;
    box-sizing: border-box;
  }
  .graphtitle {
    font-size: 16px;
    padding-left: 15px;
  }
  .compdata-item {
    h3 {
      color: #333;
      font-size: 14px;
    }
    .colorwords {
      font-size: 12px;
    }
  }
}

.compdata {
  width: 100%;
  padding: 25px;
  margin: 0;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: flex-start;
  .compdata-item {
    width: 33.3%;
    margin-bottom: 20px;
  }
}
.box1 {
  flex: 1;
}
.box2 {
  flex: 1;
  margin-left: 20px;
  .compdata {
    .compdata-item {
      width: 50%;
    }
  }
}
.box3 {
  width: 100%;
  .compdata {
    .compdata-item {
      width: 16.65%;
    }
  }
}
.greendown {
  color: #5cb85c;
  position: relative;
  padding-left: 30px;
}
.greendown:before {
  content: '';
  position: absolute;
  width: 16px;
  height: 11px;
  background: url(~assets/images/greendown.gif) no-repeat;
  left: 4px;
  top: 3px;
}
.black {
  color: #666;
  position: relative;
}
.redup {
  color: #d9544f;
  position: relative;
  padding-left: 25px;
}
.redup:before {
  content: '';
  position: absolute;
  width: 16px;
  height: 11px;
  background: url(~assets/images/redup.gif) no-repeat;
  left: 4px;
  top: 3px;
}
</style>
