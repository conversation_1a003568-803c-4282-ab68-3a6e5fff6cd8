
<style lang="less">
  .business {
    .el-form-item__content {
      display: flex;
    }
  }
</style>
<template>
  <div class="container">
    <el-form class="form-frame" ref="form" :model="postData" label-width="150px">
      <el-form-item label="帮助标题" prop="title" :rules="[{required: true, message: '请填写帮助标题', trigger: 'blur'}]">
        <el-input v-model="postData.title" placeholder="帮助标题"></el-input>
      </el-form-item>
      <el-form-item prop="content" label="帮助内容">
        <Editor v-model="postData.content" :compress="false" />
      </el-form-item>
      <el-form-item style="padding-top: 50px">
        <el-button type="success" @click="defineStatus">保存</el-button>
        <el-button type="info" @click="$router.back()" style="margin-left: 100px;">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
  <script>
  import Editor from 'components/form/Editor';
  import ImgUploader from 'components/form/imgUploader';

  export default {
    name: 'faqEdit',
    components: {
      Editor,
      ImgUploader
    },
    data() {
      return {
        uploadUrl: _g.getRbBaseUrl() + '/Admin/Public/upload',
        fileList: [],
        postData: {
          title: '',
          content: ''
        }
      };
    },
    created() {
      this.getHelpList();
      this.id = this.$route.query.id;
      if (this.id) {
        this.getInfo();
      }
    },
    methods: {
      getHelpList() {
        const url = '/bird/Faq/getFaqList';
        this.$service.get(url).then(res => {
          if(res.status === 200) {
            if(res.data.errorcode == 0) {
            } else {
              this.$Message.error(res.data.errormsg)
            }
          }
        }).catch(err => {
          console.error(err)
        })
      },
      defineStatus() {
        this.$confirm("您已编辑成功！是否要同步显示在前台？", "编辑成功", {
          confirmButtonText: "保存并显示在前台",
          cancelButtonText: "仅保存",
          type: 'success',
          showClose: false,
        }).then(async () => {
          this.postData.status = 0;
          await this.clickSave();
        }).catch(async () => {
          this.postData.status = 1;
          await this.clickSave();
        });      
      },
      getInfo() {
        this.$service
          .get(`/bird/Faq/getFaqList?id=${this.id}`)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.postData.title = data.data.title;
              this.postData.content = data.data.content;
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      clickSave() {
        this.$refs.form.validate(valid => {
          if (valid) {
            if (this.id) {
              this.postData.id = this.id;
              return this.saveEdit();
            } else {
              return this.addHelp();
            }
          } else {
            this.$message.error('表单错误');
          }
        });
      },
      addHelp() {
        const url = 'bird/Faq/postFaqCreate';
        return this.$service
          .post(url, this.postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$router.back();
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      handleUploadSuccess(res, file) {
        if (res.status === 1) {
          this.detail.license = res.info;
          this.licenseImg = res.info;
          console.log(this.detail.license, '123');
        } else {
          this.$message.error('上传失败');
        }
      },
      beforeUpload(file) {
        const fileType = ['image/jpg', 'image/png', 'image/jpeg'];
        const isJPG = fileType.includes(file.type);
        const isLt2M = file.size / 1024 / 1024 < 4;

        if (!isJPG) {
          this.$message.error('上传图片只能是 JPG/JPEG/PNG 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 4MB!');
        }
        return isJPG && isLt2M;
      },
      saveEdit() {
        const url = 'bird/Faq/postFaqUpdate';
        return this.$service
          .post(url, { ...this.postData, id: this.id })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$router.back();
            } else {
              // this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>
