<template>
  <div>
    <el-card header="筛选条件">
      <el-form inline>

        <h3>按基础信息</h3>
        <el-form-item>
          <el-input v-model="postData.bus_name" placeholder="场馆"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="postData.mer_name" placeholder="所属商家"></el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="postData.brand" clearable filterable placeholder="是否连锁">
            <el-option label="是" value="2"></el-option>
            <el-option label="否" value="1"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="postData.bus_type" clearable filterable placeholder="场馆类型">
            <el-option v-for="(value, key) of busType" :key="key" :value="key" :label="value"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="postData.custom_level" clearable filterable placeholder="客户等级">
            <el-option v-for="(item, index) in customLevel" :value="index + 1" :key="item" :label="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="postData.charge_version" clearable filterable placeholder="使用版本">
            <el-option v-for="version in versionList" :key="version.id" :value="version.id"
                       :label="version.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="postData.renew_status" clearable filterable placeholder="当前状态">
            <el-option v-for="(item, index) in chargeVersion" :value="index + 1" :key="item" :label="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="postData.is_hardware" clearable filterable placeholder="智能硬件">
            <el-option label="是" value="2"></el-option>
            <el-option label="否" value="1"></el-option>
          </el-select>
        </el-form-item>
        <!--<el-form-item>-->
        <!--<el-select v-model="postData.renew_status" clearable filterable placeholder="IVEP">-->
        <!--<el-option label="是" value="1"></el-option>-->
        <!--<el-option label="否" value="2"></el-option>-->
        <!--</el-select>-->
        <!--</el-form-item>-->
        <el-form-item>
          <el-select placeholder="省份" @change="getRegion(2)" clearable filterable v-model="postData.province_id">
            <el-option v-for="(province, index) in provincesList" :value="province.region_id"
                       :label="province.region_name" :key="province.region_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select placeholder="城市" @change="getRegion(3)" clearable filterable v-model="postData.city_id">
            <el-option v-for="(city, index) in citiesList" :value="city.region_id" :label="city.region_name"
                       :key="city.region_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select placeholder="区县" clearable filterable v-model="postData.district_id">
            <el-option v-for="(district, index) in districtsList" :value="district.region_id"
                       :label="district.region_name" :key="district.region_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select placeholder="我司销售" v-model="postData.sales_id" clearable filterable>
            <el-option v-for="item in saleList" :key="item.id" :value="item.id" :label="item.username"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select placeholder="硬件售后工程师" v-model="postData.hardware_sale_after" clearable filterable>
            <el-option v-for="item in hardwareSaleList" :key="item.id" :value="item.id" :label="item.username"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select placeholder="软件售后工程师" v-model="postData.software_sale_after" clearable filterable>
            <el-option v-for="item in softwareSaleList" :key="item.id" :value="item.id" :label="item.username"></el-option>
          </el-select>
        </el-form-item>

        <div :class="{ 'panel': true, 'expand': expand }">
          <h3>按时间维度</h3>
          <el-form-item label="入驻时间">
            <el-date-picker v-model="createTimeRange" @change="createTimeRangeChange" type="daterange"
                            value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>
          <el-form-item v-model="endTimeRange" label="到期时间">
            <el-date-picker v-model="endTimeRange" @change="endTimeRangeChange" type="daterange"
                            value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>

          <h3>按活跃度</h3>
          <el-form-item label="最近活跃">
            <el-select v-model="postData.activity_dates" clearable filterable>
              <el-option value="1" label="≥ 22天"></el-option>
              <el-option value="2" label="< 22天"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="最后活跃日">
            <el-date-picker v-model="activeRange" @change="activeRangeChange" type="daterange"
                            value-format="yyyy-MM-dd"></el-date-picker>
          </el-form-item>

          <h3>按场馆运营</h3>
          <el-form-item label="本月会籍售卡">
            <el-select v-model="postData.all_card_level" clearable filterable>
              <el-option value="1" label="0"></el-option>
              <el-option value="2" label="1 ~ 20 张"></el-option>
              <el-option value="3" label="21 ~ 50 张"></el-option>
              <el-option value="4" label="51 ~ 100 张"></el-option>
              <el-option value="5" label="101 ~ 300张"></el-option>
              <el-option value="6" label="301 ~ 1000张"></el-option>
              <el-option value="7" label="1001张以上"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="本月私教售卡">
            <el-select v-model="postData.private_card_level" clearable filterable>
              <el-option value="1" label="30 人次以下"></el-option>
              <el-option value="2" label="31~70 人次"></el-option>
              <el-option value="3" label="71~160 人次"></el-option>
              <el-option value="4" label="161~300 人次"></el-option>
              <el-option value="5" label="300 人次以上"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="本月售课课时">
            <el-select v-model="postData.private_class_level" clearable filterable>
              <el-option value="1" label="400 课时以下"></el-option>
              <el-option value="2" label="401~1000 课时"></el-option>
              <el-option value="3" label="1001~2000 课时"></el-option>
              <el-option value="4" label="2001~4000 课时"></el-option>
              <el-option value="5" label="4000 课时以上"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="本月消课课时">
            <el-select v-model="postData.eliminate_private_level" clearable filterable>
              <el-option value="1" label="400 课时以下"></el-option>
              <el-option value="2" label="401~1000 课时"></el-option>
              <el-option value="3" label="1001~2000 课时"></el-option>
              <el-option value="4" label="2001~4000 课时"></el-option>
              <el-option value="5" label="4000 课时以上"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="本月流水">
            <el-select v-model="postData.sales_flow_level" clearable filterable>
              <el-option value="1" label="30,000 元以下"></el-option>
              <el-option value="2" label="30,001~83,000 元(年化100万)"></el-option>
              <el-option value="3" label="83,001~166,000 元(年化200万)"></el-option>
              <el-option value="4" label="166,001~416,000 元(年化500万)"></el-option>
              <el-option value="5" label="416,001~832,000 元(年化1000万)"></el-option>
              <el-option value="6" label="832,000 元以上"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="有效会员">
            <el-select v-model="postData.member_level" clearable filterable>
              <el-option value="1" label="300 人以下"></el-option>
              <el-option value="2" label="301~800 人"></el-option>
              <el-option value="3" label="801~2000 人"></el-option>
              <el-option value="4" label="2001~5000 人"></el-option>
              <el-option value="5" label="5000 人以上"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="店面会籍">
            <el-select v-model="postData.work_level" clearable filterable>
              <el-option value="1" label="5 人以下"></el-option>
              <el-option value="2" label="6~10 人"></el-option>
              <el-option value="3" label="11~25 人"></el-option>
              <el-option value="4" label="26~50 人"></el-option>
              <el-option value="5" label="50 人以上"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="店面教练">
            <el-select v-model="postData.coach_level" clearable filterable>
              <el-option value="1" label="5 人以下"></el-option>
              <el-option value="2" label="6~10 人"></el-option>
              <el-option value="3" label="11~25 人"></el-option>
              <el-option value="4" label="26~50 人"></el-option>
              <el-option value="5" label="50 人以上"></el-option>
            </el-select>
          </el-form-item>

          <h3>查询时间</h3>
          <el-form-item>
            <el-date-picker v-model="postData.query_date" value-format="yyyy-MM-dd"
                            :picker-options="queryDateOption" :clearable="false" :editable="false"></el-date-picker>
          </el-form-item>
        </div>

        <div>
          <el-button @click="expand = !expand"
                     :icon="expand ? 'el-icon-caret-top' : 'el-icon-caret-bottom'"
                     style="width: 100%; border: 0; margin-bottom: 20px;">{{ expand
            ? '收起'
            : '展开' }}
          </el-button>
        </div>

        <div>
          <el-form-item>
            <el-button type="info" @click="handleReset">重置</el-button>
            <el-button type="primary" @click="doSearch">查询</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-card>
    <el-card style="margin-top: 20px" header="场馆列表">
      <div class="container">
        <el-table :data="tableData" stripe ref="table">
          <el-table-column fixed align="center" prop="bus_name" label="场馆名称">
            <template scope="scope">
              <router-link :to="{ path: '/birdnets/detail', query: { id: scope.row.bus_id } }" target="_blank">{{
                scope.row.bus_name
                }}
              </router-link>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="mer_name" label="所属商家">
            <template scope="scope">
              <router-link :to="{ path: '/birdnets/merchantDetail', query: { id: scope.row.mer_id } }" target="_blank">
                {{ scope.row.mer_name
                }}
              </router-link>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="brand" label="是否连锁"></el-table-column>
          <el-table-column align="center" prop="bus_type" label="场馆类型"></el-table-column>
          <el-table-column align="center" prop="custom_level" label="客户等级"></el-table-column>
          <el-table-column align="center" prop="version_name" label="使用版本"></el-table-column>
          <el-table-column align="center" prop="renew_status" label="当前状态"></el-table-column>
          <el-table-column align="center" prop="isHardware" label="智能硬件">
            <template scope="scope">
              <el-button type="text" @click="showHardware(scope.row.bus_id)">{{scope.row.is_hardware === 2 ? '是' :
                '否'}}
              </el-button>
            </template>
          </el-table-column>
          <!--<el-table-column align="center" prop="renew_status" label="IVEP">-->
          <!--<template scope="scope">-->
          <!--<el-button type="text" @click="showIvep(scope.row.bus_id)">是</el-button>-->
          <!--</template>-->
          <!--</el-table-column>-->
          <el-table-column align="center" prop="create_time" label="入驻时间"></el-table-column>
          <el-table-column align="center" prop="version_expire_time" label="到期时间"></el-table-column>
          <el-table-column align="center" prop="activity_dates" label="最近30日活跃"></el-table-column>
          <el-table-column align="center" prop="last_activity_time" label="最后活跃日"></el-table-column>
          <el-table-column align="center" prop="all_cards" label="本月会籍售卡"></el-table-column>
          <el-table-column align="center" prop="private_cards" label="本月私教售卡"></el-table-column>
          <el-table-column align="center" prop="private_class" label="本月售课课时"></el-table-column>
          <el-table-column align="center" prop="eliminate_private" label="本月消课课时"></el-table-column>
          <el-table-column align="center" prop="sales_flow" label="本月流水"></el-table-column>
          <el-table-column align="center" prop="member" label="会员数量"></el-table-column>
          <el-table-column align="center" prop="work" label="会籍数量"></el-table-column>
          <el-table-column align="center" prop="coach" label="教练数量"></el-table-column>
          <el-table-column align="center" prop="province_name" label="省份"></el-table-column>
          <el-table-column align="center" prop="city_name" label="城市"></el-table-column>
          <el-table-column align="center" prop="district_name" label="区县"></el-table-column>
          <el-table-column align="center" prop="address" label="地址"></el-table-column>
          <el-table-column align="center" prop="sale_name" label="我方销售"></el-table-column>
          <el-table-column align="center" prop="hardware_name" label="硬件售后工程师"></el-table-column>
          <el-table-column align="center" prop="software_name" label="软件售后工程师"></el-table-column>
        </el-table>
        <footer>
          <el-button type="success" @click="exportTable">导出Excel</el-button>
          <Export ref="export"></Export>
          <Pager :total="total" @on-change="onPageChange" :postData="postData"></Pager>
        </footer>
      </div>
    </el-card>
    <el-dialog title="智能硬件" :visible.sync="showHardwareModal">
      <el-table :data="hardwareTableData">
        <el-table-column type="index" width="80"></el-table-column>
        <el-table-column prop="type" label="智能硬件"></el-table-column>
        <el-table-column prop="num" label="数量"></el-table-column>
        <el-table-column prop="first_time" label="首次启用时间"></el-table-column>
      </el-table>
    </el-dialog>
    <el-dialog title="IVEP" :visible.sync="showIvepModal">
      <el-table :data="ivepTableData">
        <el-table-column type="index" width="80"></el-table-column>
        <el-table-column label="私教平板"></el-table-column>
        <el-table-column label="授权数量"></el-table-column>
        <el-table-column label="首次使用时间"></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
  import Pager from 'components/pager';
  import { formatDate } from 'src/assets/js/utils'
  import Export from 'src/components/Export'

  export default {
    name: 'dataExport',
    components: { Pager, Export },
    data() {
      return {
        showHardwareModal: false,
        showIvepModal: false,
        hardwareTableData: [],
        ivepTableData: [],
        saleList: [],
        hardwareSaleList: [],
        softwareSaleList: [],
        expand: false,
        queryDateOption: {
          disabledDate(date) {
            return Date.now() - date < 24 * 3600 * 1000
          }
        },
        createTimeRange: [],
        endTimeRange: [],
        activeRange: [],
        total: 0,
        tableData: [],
        busType: {
          '1': '健身房',
          '9': '健身工作室',
          '7': '体育馆',
          '8': '游泳馆',
          '2': '瑜伽馆',
          '3': '跆拳道馆',
          '4': '武道馆',
          '5': '舞蹈馆',
          '6': '其他',
        },
        customLevel: ['A', 'AA', 'AAA', 'AAAA', 'AAAAA'],
        chargeVersion: ['试用期', '试用结束', '初次购买', '续费', '临时延期', '到期终结'],
        provincesList: [],
        citiesList: [],
        districtsList: [],
        versionList: [],
        postData: {
          province_id: '',
          city_id: '',
          district_id: '',
          page_no: 1,
          page_size: 10,
          bus_name: '',
          mer_name: '',
          brand: '',
          bus_type: '',
          custom_level: '',
          is_hardware: '',
          charge_version: '',
          renew_status: '',
          begin_bus_create_time: '',
          end_bus_create_time: '',
          begin_expire_time: '',
          end_expire_time: '',
          activity_dates: '',
          begin_last_activity_date: '',
          end_last_activity_date: '',
          all_card_level: '',
          private_card_level: '',
          private_class_level: '',
          eliminate_private_level: '',
          sales_flow_level: '',
          member_level: '',
          work_level: '',
          coach_level: '',
          sales_id: '',
          hardware_sale_after: '',
          software_sale_after: '',
          query_date: formatDate(new Date(Date.now() - 24 * 3600 * 1000), 'yyyy-MM-dd')
        },
        initPostData: {
          province_id: '',
          city_id: '',
          page_no: 1,
          page_size: 10,
          bus_name: '',
          mer_name: '',
          brand: '',
          bus_type: '',
          custom_level: '',
          charge_version: '',
          renew_status: '',
          begin_bus_create_time: '',
          end_bus_create_time: '',
          begin_expire_time: '',
          end_expire_time: '',
          activity_dates: '',
          begin_last_activity_date: '',
          end_last_activity_date: '',
          all_card_level: '',
          private_card_level: '',
          private_class_level: '',
          eliminate_private_level: '',
          sales_flow_level: '',
          member_level: '',
          work_level: '',
          coach_level: '',
          sales_id: '',
          hardware_sale_after: '',
          software_sale_after: '',
          query_date: formatDate(new Date(Date.now() - 24 * 3600 * 1000), 'yyyy-MM-dd')
        },
      }
    },
    created() {
      this.getSales();
      this.getVersionList();
      this.getRegion()
      this.getHardwareSale()
      this.getSoftwareSale()
    },
    methods: {
      getSales() {
        const url = '/bird/User/getUserList'
        this.$service.post(url).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.saleList = data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      getHardwareSale() {
        const url = '/bird/User/hardwareUserList'
        this.$service.post(url).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.hardwareSaleList = data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      getSoftwareSale() {
        const url = '/bird/User/softwareUserList'
        this.$service.post(url).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.softwareSaleList = data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      showHardware(bus_id) {
        this.getHardware(bus_id)
        this.showHardwareModal = true;
      },
      getHardware(bus_id) {
        const url = '/bird/Business/getEquipmentInfo'
        this.$service.post(url, { bus_id }).then(res => {
          if (res.data.errorcode === 0) {
            this.hardwareTableData = res.data.data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      showIvep(bus_id) {
        this.getIvep(bus_id);
        this.showIvepModal = true;
      },
      getIvep(bus_id) {
        const url = ''
        this.$service.post(url, { bus_id }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.ivepTableData = data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      handleReset() {
        this.postData = { ...this.initPostData };
        this.createTimeRange = this.endTimeRange = this.activeRange = []
      },
      createTimeRangeChange(d) {
        const [s, e] = d || [];
        this.postData.begin_bus_create_time = s;
        this.postData.end_bus_create_time = e;
      },
      endTimeRangeChange(d) {
        const [s, e] = d || [];
        this.postData.begin_expire_time = s;
        this.postData.end_expire_time = e;
      },
      activeRangeChange(d) {
        const [s, e] = d || [];
        this.postData.begin_last_activity_date = s;
        this.postData.end_last_activity_date = e;
      },
      getVersionList() {
        const url = '/bird/Business/getVersionList'
        this.$service.get(url).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.versionList = data.list;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      onPageChange({ page_no, page_size }) {
        this.postData = { ...this.postData, page_no, page_size };
        this.getList();
      },
      getList(e, isExport = false) {
        const url = isExport ? '/bird/Business/getExcelList' : '/bird/Business/getStatisticsLists'
        return this.$service.post(url, {
          ...this.postData,
          page_no: isExport ? 1 : this.postData.page_no,
          page_size: isExport ? this.total : this.postData.page_size
        }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            if (!data.list.length) this.$message.error('未查询到数据');
            const list = data.list.map(item => {
              return {
                ...item,
                brand: item.brand == 2 ? '是' : '否',
                bus_type: this.busType[item.bus_type],
                custom_level: this.customLevel[item.custom_level - 1],
                renew_status: this.chargeVersion[item.renew_status - 1],
                isHardware: item.is_hardware === 2 ? '是' : '否'
              }
            });
            if (isExport) {
              return list;
            } else {
              this.total = data.count;
              this.tableData = list;
            }
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      getRegion(type = 1) {
        const url = '/Web/Merchants/ajaxGetRegion';
        const { province_id: region_id, city_id } = this.postData;
        const postData = {
          region_id: ''
        };
        if (type === 2) {
          postData.region_id = region_id;
        }
        if (type === 3) {
          postData.region_id = city_id;
        }
        return this.$service
          .post(url, postData, { loading: false })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              if (type === 1) {
                this.provincesList = data;
              } else if (type === 2) {
                this.citiesList = data;
              } else if (type === 3) {
                this.districtsList = data;
              }
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      async exportTable() {
        if (!this.tableData.length) return this.$message.error('请先查询要导出的数据');
        const data = await this.getList('', true);
        const columns = this.$refs.table.$children.filter(t => t.prop != null).map(item => ({
          title: item.label,
          key: item.prop
        }));
        const device = {
          device_type_1: '一体机',
          device_type_2: '门禁',
          device_type_3: '闸机',
          device_type_4: '中控',
          device_type_5: '人脸',
        }
        const deviceColumns = [{
          title: '一体机',
          key: 'device_type_1'
        }, {
          title: '门禁',
          key: 'device_type_2'
        }, {
          title: '闸机',
          key: 'device_type_3'
        }, {
          title: '中控',
          key: 'device_type_4'
        }, {
          title: '人脸',
          key: 'device_type_5'
        },];
        const index = columns.findIndex(item => item.key === 'isHardware');
        columns.splice(index + 1, 0, ...deviceColumns);
        columns.splice(1, 0, ...[{ title: '场馆ID', key: 'bus_id' },{ title: '场馆电话', key: 'phone' }])
        for (let item of data) {
          if (!item.equipment) break;
          for (let device of item.equipment) {
            item[`device_type_${device.type}`] = device.num;
          }
        }
        this.$refs.export.export({
          filename: '场馆列表',
          data,
          columns
        })
      }
    },
  }
</script>

<style scoped lang="less">
  .panel {
    height: 0;
    overflow: hidden;
  }

  .expand {
    height: auto;
  }

</style>
