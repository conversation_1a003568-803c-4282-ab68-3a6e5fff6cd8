<script>
import http from 'assets/js/http'

export default {
  name: 'AccessReportSettings',
  mixins: [http],
  data() {
    return {
      // search
      searchPost: {
        page_no: 1,
        page_size: 10,
        merchantsName: '',
        busName: '',
      },

      // table
      tableData: [],
      total: 0,
      controlNodeList: [
        {
          value: 1,
          label: '出入闸机',
        },
        {
          value: 2,
          label: '存取退柜',
        },
      ],

      // save dialog
      saveDialogFlag: false,
      savePost: {
        id: '',
        control_node: [],
        bus_ids: [],
        bus_name: '',
      },
      saveRules: {
        control_node: [{ required: true, message: '请选择监控节点', trigger: 'blur' }],
        bus_ids: [{ required: true, message: '请选择场馆', trigger: 'blur' }],
      },
      storeList: [],

      // delete dialog
      deleteDialogFlag: false,
      deletePost: {
        id: '',
        name: '',
      },
    }
  },
  methods: {
    // request
    getStoreList() {
      this.apiGet('/bird/DataAnalysis/getBusId').then((res) => {
        if (res.errorcode == 0) {
          this.storeList = res.data
        }
      })
    },
    async getList(params = this.searchPost) {
      return await this.apiPost('/bird/DataAnalysis/getDataAnalysisNodeList', params).then((res) => {
        if (res.errorcode == 0) {
          this.tableData = res.data.list
          this.total = res.data.count
        }
      })
    },
    // event
    handleSearch() {
      this.searchPost.page_no = 1
      this.getList()
    },
    handleSizeChange(val) {
      this.searchPost.page_size = val
      this.searchPost.page_no = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.searchPost.page_no = val
      this.getList()
    },
    handleShowSave(row) {
      if (row) {
        this.savePost = {
          id: row.id,
          control_node: row.control_node.map(Number),
          bus_name: row.bus_name,
        }
      } else {
        this.savePost = {
          control_node: [],
          bus_ids: [],
        }
      }
      this.saveDialogFlag = true
    },
    handleHideSave() {
      this.savePost = {
        id: '',
        control_node: [],
        bus_ids: [],
        bus_name: '',
      }
      this.$refs.saveRef.clearValidate()
      this.saveDialogFlag = false
    },
    handleSave() {
      this.$refs.saveRef.validate((valid) => {
        if (valid) {
          let url = '/bird/DataAnalysis/addDataAnalysisNode'
          if (this.savePost.id) {
            url = '/bird/DataAnalysis/upDataAnalysisNode'
            delete this.savePost.bus_ids
            delete this.savePost.bus_name
          } else {
            delete this.savePost.id
            delete this.savePost.bus_name
          }
          this.apiPost(url, this.savePost).then((res) => {
            if (res.errorcode == 0) {
              this.$message.success(res.errormsg)
              this.handleHideSave()
              this.getStoreList()
              this.getList()
            } else {
              this.$message.error(res.errormsg)
            }
          })
        }
      })
    },
    handleShowDelete(row) {
      this.deletePost = {
        id: row.id,
        name: row.bus_name,
      }
      this.deleteDialogFlag = true
    },
    handleDelete() {
      this.apiPost('/bird/DataAnalysis/delDataAnalysisNode', this.deletePost).then((res) => {
        if (res.errorcode == 0) {
          this.$message.success(res.errormsg)
          this.deleteDialogFlag = false
          this.getStoreList()
          this.getList()
        } else {
          this.$message.error(res.errormsg)
        }
      })
    },
  },
  created() {
    this.getStoreList()
    this.getList()
  },
}
</script>

<template>
  <div class="box">
    <div class="search-box">
      <el-button type="success" style="margin-right: 20px" icon="el-icon-plus" @click="saveDialogFlag = true">添加场馆</el-button>
      <div class="search-item">
        <el-input
          placeholder="商家名称"
          v-model="searchPost.merchantsName"
          @keyup.enter.native="handleSearch"
          clearable
        ></el-input>
      </div>
      <div class="search-item">
        <el-input placeholder="场馆名称" v-model="searchPost.busName" @keyup.enter.native="handleSearch" clearable></el-input>
      </div>
      <el-button type="success" icon="el-icon-search" @click="handleSearch">搜索</el-button>
      <div style="width: 100%">
        <el-link type="success" style="float: right; margin-right: 20px" href="/birdnets/accessOperationLog">操作记录</el-link>
      </div>
    </div>

    <div class="table-box">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="mer_name" label="商家名称" align="center"></el-table-column>
        <el-table-column prop="bus_name" label="场馆名称" align="center"></el-table-column>
        <el-table-column prop="control_node" label="监控节点" align="center">
          <template slot-scope="scope">
            <span v-for="item in scope.row.control_node" :key="item">
              {{ controlNodeList.find((t) => t.value == item).label }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="100" align="center">
          <template slot-scope="scope">
            <el-button type="text" @click="handleShowSave(scope.row)">编辑</el-button>
            <el-button type="text" style="color: red" @click="handleShowDelete(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div class="page-box">
      <div></div>
      <el-pagination
        background
        layout="prev, pager, next, sizes, jumper, ->, total"
        :total="total"
        prev-text="上一页"
        next-text="下一页"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="searchPost.page_size"
        @size-change="handleSizeChange"
        :current-page="searchPost.page_no"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>

    <el-dialog :title="!savePost.id ? '添加场馆' : '编辑监控节点'" :visible.sync="saveDialogFlag" width="30%">
      <el-form ref="saveRef" :model="savePost" :rules="saveRules" label-width="120px">
        <el-form-item label="监控节点" prop="control_node">
          <el-checkbox-group v-model="savePost.control_node">
            <el-checkbox v-for="item in controlNodeList" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="场馆名称" prop="bus_ids" v-if="!savePost.id">
          <el-select v-model="savePost.bus_ids" placeholder="请选择场馆名称" style="width: 100%" filterable clearable multiple>
            <el-option v-for="item in storeList" :key="item.id" :label="item.name" :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="场馆名称" v-else>
          <div>{{ savePost.bus_name }}</div>
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleSave">确定</el-button>
        <el-button @click="handleHideSave" style="margin-left: 120px">取消</el-button>
      </span>
    </el-dialog>

    <el-dialog title="提示" :visible.sync="deleteDialogFlag" width="20%">
      <span>确认是否删除 "{{ deletePost.name }}" 场馆？</span>
      <span slot="footer" class="dialog-footer" style="justify-content: flex-end">
        <el-button @click="deleteDialogFlag = false">取 消</el-button>
        <el-button type="primary" @click="handleDelete">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<style lang="less" scoped>
.box {
  background-color: white;
  padding: 20px;

  .search-box {
    display: flex;
    flex-direction: row;
    align-items: center;

    .search-item {
      min-width: 200px;
      margin-right: 20px;
    }
  }

  .table-box {
    margin-top: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .page-box {
    margin-top: 20px;
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }

  .dialog-footer {
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
}
</style>
