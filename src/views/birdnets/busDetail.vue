<template>
  <div>
    <el-card header="基础信息">
      <el-form inline label-width="150px">
        <el-form-item label="场馆名称">{{form.bus_name}}</el-form-item>
        <el-form-item label="所属商家">{{form.mer_name}}</el-form-item>
        <el-form-item label="是否连锁">{{form.brand == 2 ? '是' : '否'}}</el-form-item>
        <el-form-item label="场馆类型">{{busType[form.bus_type]}}</el-form-item>
        <el-form-item label="使用版本">{{form.version_name}}</el-form-item>
        <el-form-item label="场馆地址">{{form.address}}</el-form-item>
        <el-form-item label="入驻时间">{{form.create_time}}</el-form-item>
        <el-form-item label="到期时间">{{form.version_expire_time}}</el-form-item>
        <el-form-item label="最后活跃日">{{form.last_activity_time}}</el-form-item>
        <div>
          <el-form-item label="重点客户">
            <el-select v-model="form.custom_level" :disabled="!isEdit">
              <el-option v-for="(item, index) in customLevel" :value="index + 1" :key="item" :label="item"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="销售">
            <el-select v-model="form.sales_id" :disabled="!isEdit">
              <el-option v-for="item in saleList" :key="item.id" :value="item.id" :label="item.username"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="硬件售后">
            <el-select v-model="form.hardware_sale_after" :disabled="!isEdit">
              <el-option v-for="item in hardwareSaleList" :key="item.id" :value="item.id" :label="item.username"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="软件售后">
            <el-select v-model="form.software_sale_after" :disabled="!isEdit">
              <el-option v-for="item in softwareSaleList" :key="item.id" :value="item.id" :label="item.username"></el-option>
            </el-select>
          </el-form-item>
        </div>
        <div>
          <el-form-item label=" ">
            <el-button type="primary" v-if="!isEdit" @click="isEdit = true">编辑</el-button>
            <el-button type="primary" v-else @click="editInfo">完成</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-card>
    <el-card header="场馆运营数据" style="margin-top: 20px">
      <span slot="header" style="display: flex; justify-content: space-between; align-items: center">
        <span>场馆运营数据</span>
        <el-radio-group v-model="chartType" @change="chartTypeChange">
          <el-radio-button :label="1">汇总数据</el-radio-button>
          <el-radio-button :label="2">私教消耗</el-radio-button>
          <el-radio-button :label="3">周营业额</el-radio-button>
        </el-radio-group>
      </span>
      <template v-if="chartType === 1">
        <h3>场馆运营</h3>
        <div class="cards">
          <el-card class="card">
            <h3>{{stat.all_cards}}笔</h3>
            <p>本月会籍售卡
              <Info value="本月会籍销售期限卡、次卡、储值卡数量。现逻辑：新购卡+续卡+升卡。"/>
            </p>
          </el-card>
          <el-card class="card">
            <h3>{{stat.private_cards}}笔</h3>
            <p>本月私教售卡
              <Info value="本月私教课程销售次数。现逻辑：新购卡+续卡+升卡"/>
            </p>
          </el-card>
          <el-card class="card">
            <h3>{{stat.private_class}}课时</h3>
            <p>本月售课数
              <Info value="本月私教课售卖课时总量。现逻辑：新购卡+续卡+升卡"/>
            </p>
          </el-card>
          <el-card class="card">
            <h3>{{stat.eliminate_private}}课时</h3>
            <p>本月消课数
              <Info value="本月私教课消课课时总量"/>
            </p>
          </el-card>
          <el-card class="card">
            <h3>{{stat.sales_flow}}元</h3>
            <p>本月营业额
              <Info value="本月场馆营业额数据。现逻辑：本月所有流水(总额）-升卡(跨店出)。"/>
            </p>
          </el-card>
          <el-card class="card">
            <h3>{{stat.activity_dates}}天</h3>
            <p>最近活跃
              <Info value="最近30天，场馆的累计活跃天数，以22日为分界岭，低于22日需引起注意。"/>
            </p>
          </el-card>
        </div>
        <h3>人员运营</h3>
        <div class="cards">
          <el-card class="card">
            <h3>{{stat.member}}人</h3>
            <p>会员数量
              <Info value="场馆的有效会员数(普通会员+私教会员)。"/>
            </p>
          </el-card>
          <el-card class="card">
            <h3>{{stat.work}}人</h3>
            <p>会籍数量
              <Info value="系统记录的场馆会籍人数。"/>
            </p>
          </el-card>
          <el-card class="card">
            <h3>{{stat.coach}}人</h3>
            <p>教练数量
              <Info value="系统记录的场馆教练人数。"/>
            </p>
          </el-card>
        </div>
      </template>
      <template v-if="chartType === 2">
        <el-form inline style="display: flex; justify-content: flex-end">
          <el-form-item style="margin-left: 30px;" label="私教剩余课时数:">{{ptTotal.surplusPrivate}}节</el-form-item>
          <el-form-item style="margin-left: 30px;" label="6周消耗均值:">{{ptTotal.averagePrivate}}节/周</el-form-item>
          <el-form-item style="margin-left: 30px;" label="私教消耗周期:"><span style="font-size: 18px; font-weight: bold;">{{ptTotal.eliminateCycle}}周</span>
          </el-form-item>
        </el-form>
        <div ref="ptChart" key="ptChart" class="chart" style="height: 400px"></div>
      </template>
      <template v-if="chartType === 3">
        <div ref="weekChart" key="weekChart" class="chart" style="height: 400px"></div>
      </template>
    </el-card>
    <el-card header="智能硬件" style="margin-top: 20px">
      <div class="container">
        <el-table :data="hardwareTableData">
          <el-table-column type="index" width="80"></el-table-column>
          <el-table-column prop="type" label="智能硬件"></el-table-column>
          <el-table-column prop="num" label="数量"></el-table-column>
          <el-table-column prop="first_time" label="首次启用时间"></el-table-column>
        </el-table>
      </div>
    </el-card>
    <!--<el-card header="IVEP" style="margin-top: 20px">-->
      <!--<div class="container">-->
        <!--<el-table :data="ivepTableData">-->
          <!--<el-table-column type="index" width="80"></el-table-column>-->
          <!--<el-table-column label="授权账号"></el-table-column>-->
          <!--<el-table-column label="首次启用时间"></el-table-column>-->
          <!--<el-table-column label="最后登录时间"></el-table-column>-->
        <!--</el-table>-->
      <!--</div>-->
    <!--</el-card>-->
    <el-card header="备注信息" style="margin-top: 20px">
      <div class="container">
        <el-table :data="tableData">
          <el-table-column type="index" width="100"></el-table-column>
          <el-table-column prop="create_time" label="时间"></el-table-column>
          <el-table-column prop="username" label="操作人"></el-table-column>
          <el-table-column prop="message" label="备注信息"></el-table-column>
        </el-table>
        <footer>
          <el-button size="small" type="success" @click="showDialog = true">新建备注</el-button>
          <Pager :total="total" @on-change="onPageChange"></Pager>
        </footer>
      </div>
    </el-card>
    <el-dialog title="添加备注" :visible.sync="showDialog" center>
      <el-input v-model="remark" type="textarea"
                :autosize="{ minRows: 6, maxRows: 8}"></el-input>
      <div slot="footer">
        <el-button type="success" @click="addRemark">确定</el-button>
        <el-button @click="showDialog = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import Echarts from 'echarts'
  import Pager from 'components/pager'
  import Info from 'src/components/info'

  export default {
    name: 'busDetail',
    components: { Pager, Info },
    data() {
      return {
        chartType: 1,
        hardwareTableData: [],
        ivepTableData: [],
        isEdit: false,
        showDialog: false,
        remark: '',
        saleList: [],
        hardwareSaleList: [],
        softwareSaleList: [],
        busType: {
          '1': '健身房',
          '2': '瑜伽馆',
          '3': '跆拳道馆',
          '4': '武道馆',
          '5': '舞蹈馆',
          '6': '其他',
          '7': '体育馆',
          '8': '游泳馆',
          '9': '健身工作室',
        },
        customLevel: ['A', 'AA', 'AAA', 'AAAA', 'AAAAA'],
        chargeVersion: ['试用期', '试用结束', '初次购买', '续费', '临时延期', '到期终结'],
        form: {
          address: "",
          brand: '',
          bus_name: "",
          bus_type: '',
          charge_version: '',
          city_name: '',
          create_time: "",
          last_activity_time: '',
          custom_level: '',
          district_name: '',
          mer_name: "",
          pc_use_time: "",
          province_name: "",
          renew_status: '',
          username: '',
          sales_id: '',
          hardware_sale_after: '',
          software_sale_after: '',
        },
        postData: {
          bus_id: '',
          page_no: 1,
          page_size: 10
        },
        total: 0,
        tableData: [],
        stat: {
          activity_dates: 0,
          all_cards: 0,
          bus_id: 0,
          coach: 0,
          create_time: 0,
          eliminate_private: 0,
          id: 0,
          last_activity_time: 0,
          member: 0,
          private_cards: 0,
          private_class: 0,
          sales_flow: 0,
          work: 0,
        },
        ptTotal: {
          surplusPrivate: 0,
          averagePrivate: 0,
          eliminateCycle: 0
        },
        ptChart: null,
        ptChartOption: {
          color: ['#52a4ea', '#1bd4c9', '#ff696a', '#a76de8', '#ff9c28'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              }
            }
          },
          legend: { data: ['售课', '消课'] },
          xAxis: [
            {
              type: 'category',
              data: [],
              axisPointer: {
                type: 'shadow'
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '消课课时数',
              min: 0,
              // max: 400,
              axisLabel: {
                formatter: '{value}'
              }
            },
          ],
          series: [
            {
              name: '售课',
              type: 'bar',
              label: { normal: { show: true, position: 'top' } },
              data: []
            },
            {
              name: '消课',
              type: 'bar',
              label: { normal: { show: true, position: 'top' } },
              data: []
            },
          ]
        },
        weekChart: null,
        weekChartOption: {
          color: ['#52a4ea', '#1bd4c9', '#ff696a', '#a76de8', '#ff9c28'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              }
            }
          },
          legend: { data: ['会员卡', '私教课'] },
          xAxis: [
            {
              type: 'category',
              data: [],
              axisPointer: {
                type: 'shadow'
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '周营业额(万元)',
              min: 0,
              max: 0,
              axisLabel: {
                formatter: '{value}'
              }
            },
            {
              type: 'value',
              name: '成交量(笔)',
              min: -100,
              max: 100,
              splitLine: { show: false },
              axisLabel: {
                formatter: '{value}'
              }
            }
          ],
          series: [
            {
              name: '会员卡',
              type: 'bar',
              label: { normal: { show: true, position: 'top' } },
              data: []
            },
            {
              name: '私教课',
              type: 'bar',
              label: { normal: { show: true, position: 'top' } },
              data: []
            },
            {
              name: '成交量',
              type: 'line',
              yAxisIndex: 1,
              label: {
                normal: {
                  show: true,
                  // formatter: '{c} 家'
                }
              },
              data: []
            },
          ]
        },
      }
    },
    created() {
      this.postData.bus_id = this.$route.query.id;
      this.getSales();
      this.getList();
      this.getHardwareSale()
      this.getSoftwareSale()
    },
    mounted() {
      this.getHardware()
      // this.repayChart = Echarts.init(this.$refs.repayChart);
    },
    methods: {
      getHardwareSale() {
        const url = '/bird/User/hardwareUserList'
        this.$service.post(url).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.hardwareSaleList = data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      getSoftwareSale() {
        const url = '/bird/User/softwareUserList'
        this.$service.post(url).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.softwareSaleList = data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      chartTypeChange(val) {
        if (val === 2) {
          this.ptChart = Echarts.init(this.$refs.ptChart);
          this.getPtChartData();
        } else if (val === 3) {
          this.weekChart = Echarts.init(this.$refs.weekChart)
          this.getWeekChartData()
        }
      },
      getWeekChartData() {
        const url = '/bird/Business/getTurnover'
        this.$service.post(url, { bus_id: this.postData.bus_id }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.dealWeekChartData(data);
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      getPtChartData() {
        const url = '/bird/Business/eliminatingPrivateClass'
        this.$service.post(url, { bus_id: this.postData.bus_id }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            const {
              surplusPrivate,
              averagePrivate,
              eliminateCycle
            } = data;
            this.ptTotal = {
              surplusPrivate,
              averagePrivate,
              eliminateCycle
            };
            this.dealPtChartData(data.list)
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      dealWeekChartData(data) {
        const keys = Object.keys(data);
        const values = Object.values(data);
        const memberCardValue = values.map(item => (item.ordinary_card_turnover/10000).toFixed(2));
        const ptCardValue = values.map(item => (item.private_card_turnover/10000).toFixed(2));
        const dealCount = values.map(item => item.private_order_number);
        const maxValue = Math.max(...[...memberCardValue, ...ptCardValue]);
        const maxCount = Math.max(...dealCount);
        const max = maxValue * 2;
        this.weekChartOption.xAxis[0].data = keys;
        this.weekChartOption.yAxis[0].max = max;
        this.weekChartOption.yAxis[1].min = -Math.max(maxCount, 100);
        this.weekChartOption.yAxis[1].max = Math.max(maxCount, 100);
        this.weekChartOption.series[0].data = memberCardValue;
        this.weekChartOption.series[1].data = ptCardValue;
        this.weekChartOption.series[2].data = dealCount;
        this.weekChart.setOption(this.weekChartOption);
      },
      dealPtChartData(data) {
        const keys = Object.keys(data);
        const values = Object.values(data);
        this.ptChartOption.xAxis[0].data = keys;
        this.ptChartOption.series[0].data = values.map(item => item.sale_class)
        this.ptChartOption.series[1].data = values.map(item => item.eliminate_class)
        this.ptChart.setOption(this.ptChartOption);
      },
      addRemark() {
        const url = '/bird/Business/addRemarks'
        this.$service.post(url, {
          message: this.remark,
          bus_id: this.postData.bus_id,
          user_id: this.$store.state.userInfo.id
        }).then(res => {
          if (res.data.errorcode === 0) {
            this.$message.success(res.data.errormsg);
            this.getList();
            this.remark = ''
            this.showDialog = false
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      editInfo() {
        const url = '/bird/Business/updateBusiness'
        const { custom_level, sales_id, hardware_sale_after, software_sale_after } = this.form;
        const { bus_id } = this.postData;
        this.$service.post(url, { bus_id, custom_level, sales_id, hardware_sale_after, software_sale_after }).then(res => {
          if (res.data.errorcode === 0) {
            this.$message.success(res.data.errormsg);
            this.isEdit = false
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      getSales() {
        const url = '/bird/User/getUserList'
        this.$service.post(url).then(res => {
          if (res.data.errorcode === 0) {
            this.saleList = res.data.data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      onPageChange({ page_no, page_size }) {
        this.postData.page_no = page_no;
        this.postData.page_size = page_size;
        this.getList()
      },
      getHardware(bus_id = this.$route.query.id) {
        const url = '/bird/Business/getEquipmentInfo'
        this.$service.post(url, { bus_id }).then(res => {
          if (res.data.errorcode === 0) {
            this.hardwareTableData = res.data.data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      getList() {
        const url = '/bird/Business/getBusinessInfo'
        this.$service.post(url, this.postData).then(res => {
          if (res.data.errorcode === 0) {
            const { info, statistics, remarks } = res.data.data;
            this.form = info;
            this.stat = statistics;
            this.tableData = remarks.list;
            this.total = remarks.count;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
    },
  }
</script>

<style scoped lang="less">
  .cards {
    display: flex;
    flex-wrap: wrap;

    .card {
      width: 160px;
      text-align: center;
      margin: 10px;
    }
  }
</style>
