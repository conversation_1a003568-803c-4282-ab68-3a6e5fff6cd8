<script>
import { formatDate } from '@/utils/index'
import ExportCsv from 'src/components/form/csvExport'
import http from 'assets/js/http'

export default {
  name: 'SkyPalmReport',
  mixins: [http],
  data() {
    return {
      searchPost: {
        page_no: 1,
        page_size: 10,
        merchantsName: '',
        begin_time: '',
        end_time: '',
      },
      daterange: [],
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [
          {
            text: '近7天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '近30天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '近90天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },
      tableData: [],
      total: 0,
    }
  },
  methods: {
    async getList(params = this.searchPost) {
      return await this.apiPost('/bird/DataAnalysis/getFirmamentUserOpenList', params).then((res) => {
        if (res.errorcode == 0) {
          if (params.is_export === 1) {
            const list = res.data.list
            return list
          } else {
            this.tableData = res.data.list
            this.total = res.data.count
          }
        }
      })
    },
    async handleExport() {
      const params = {
        ...this.searchPost,
        page_no: 1,
        page_size: this.total,
        is_export: 1,
      }
      const list = await this.getList(params)
      const columns = this.getColumns()
      ExportCsv(list, columns, '空中开掌数据监控')
    },
    handleSearch() {
      this.searchPost.page_no = 1
      this.getList()
    },    
    handleSizeChange(val) {
      this.searchPost.page_size = val
      this.searchPost.page_no = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.searchPost.page_no = val
      this.getList()
    },
    pickChange(val) {
      this.searchPost.begin_time = val ? _g.formatDate(val[0], 'yyyy-MM-dd') : ''
      this.searchPost.end_time = val ? _g.formatDate(val[1], 'yyyy-MM-dd') : ''
    },
    getColumns() {
      let columns = [
        { prop: 'mer_name', label: '商家名称' },
        { prop: 'statistics_date', label: '日期' },
        { prop: 'my', label: '我的页面-点击人数' },
        { prop: 'my_palmservice', label: '刷掌服务-点击人数' },
        { prop: 'home_popup', label: '小程序首页弹窗-弹窗展示次数' },
        { prop: 'home_popup_click', label: '小程序首页弹窗-点击人数' },
        { prop: 'firmament_popup_num', label: '小程序首页弹窗-开通人数' },
        { prop: 'palmservice_service', label: '刷掌服务-本人开掌-点击人数' },
        { prop: 'firmament_service_num', label: '刷掌服务-本人开掌-开通人数' },
        { prop: 'card_done', label: '小程序购票/卡完成页-显示该页面人数' },
        { prop: 'card_done_click', label: '小程序购票/卡完成页-点击人数' },
        { prop: 'firmament_card_num', label: '小程序购票/卡完成页-开通人数' },
        { prop: 'store_guide', label: '进店指引入口-显示该页面人数' },
        { prop: 'store_guide_click', label: '进店指引入口-点击人数' },
        { prop: 'firmament_guide_num', label: '进店指引入口-开通人数' },
        { prop: 'assist', label: '刷掌服务-亲友开掌-点击人数' },
        { prop: 'assist_open_num', label: '刷掌服务-亲友开掌-开通人数' },
      ];

      return columns;
    },
  },
  created() {
    const begin = new Date()
    const end = new Date()
    begin.setTime(begin.getTime() - 3600 * 1000 * 24 * 30)
    this.daterange = [begin, end]
    this.getList()
  },
}
</script>

<template>
  <div class="box">
    <div class="search-box">
      <div class="search-item">
        <el-input
          placeholder="商家名称"
          v-model="searchPost.merchantsName"
          @keyup.enter.native="handleSearch"
          clearable
        ></el-input>
      </div>
      <div class="search-item">
        <el-date-picker
          v-model="daterange"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          :clearable="false"
          @change="pickChange"
        ></el-date-picker>
      </div>
      <el-button type="success" icon="el-icon-search" @click="handleSearch">搜索</el-button>
    </div>
    <div class="table-box">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="mer_name" label="商家名称" width="150" align="center"></el-table-column>
        <el-table-column prop="statistics_date" label="日期" width="150" align="center"></el-table-column>
        <el-table-column label="小程序" align="center">
          <el-table-column label="我的页面" align="center">
            <el-table-column prop="my" label="点击人数" align="center"></el-table-column>
          </el-table-column>
          <el-table-column label="刷掌服务" align="center">
            <el-table-column prop="my_palmservice" label="点击人数" align="center"></el-table-column>
          </el-table-column>
        </el-table-column>
        <el-table-column label="空中开掌" align="center">
          <el-table-column label="小程序首页弹窗" align="center">
            <el-table-column prop="home_popup" label="弹窗展示次数" align="center"></el-table-column>
            <el-table-column prop="home_popup_click" label="点击人数" align="center"></el-table-column>
            <el-table-column prop="firmament_popup_num" label="开通人数" align="center"></el-table-column>
          </el-table-column>
          <el-table-column label="刷掌服务-本人开掌" align="center">
            <el-table-column prop="palmservice_service" label="点击人数" align="center"></el-table-column>
            <el-table-column prop="firmament_service_num" label="开通人数" align="center"></el-table-column>
          </el-table-column>
          <el-table-column label="小程序购票/卡完成页" align="center">
            <el-table-column prop="card_done" label="显示该页面人数" align="center"></el-table-column>
            <el-table-column prop="card_done_click" label="点击人数" align="center"></el-table-column>
            <el-table-column prop="firmament_card_num" label="开通人数" align="center"></el-table-column>
          </el-table-column>
          <el-table-column label="进店指引入口" align="center">
            <el-table-column prop="store_guide" label="显示该页面人数" align="center"></el-table-column>
            <el-table-column prop="store_guide_click" label="点击人数" align="center"></el-table-column>
            <el-table-column prop="firmament_guide_num" label="开通人数" align="center"></el-table-column>
          </el-table-column>
        </el-table-column>
        <el-table-column label="协助绑掌" align="center">
          <el-table-column label="刷掌服务-亲友开掌" align="center">
            <el-table-column prop="assist" label="点击人数" align="center"></el-table-column>
            <el-table-column prop="assist_open_num" label="开通人数" align="center"></el-table-column>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
    <div class="page-box">
      <el-button @click="handleExport">导出</el-button>
      <el-pagination
        background
        layout="prev, pager, next, sizes, jumper, ->, total"
        :total="total"
        prev-text="上一页"
        next-text="下一页"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="searchPost.page_size"
        @size-change="handleSizeChange"
        :current-page="searchPost.page_no"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<style lang="less" scoped>
.box {
  background-color: white;
  padding: 20px;

  .search-box {
    display: flex;
    flex-direction: row;
    align-items: center;

    .search-item {
      min-width: 200px;
      margin-right: 20px;
    }
  }

  .filter-box {
    height: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .table-box {
    margin-top: 20px;
  }

  .page-box {
    margin-top: 20px;
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
