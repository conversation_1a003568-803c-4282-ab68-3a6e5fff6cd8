<script>
import { formatDate } from '@/utils/index'
import ExportCsv from 'src/components/form/csvExport'
import http from 'assets/js/http'

export default {
  name: 'AccessGateReport',
  mixins: [http],
  data() {
    return {
      // search
      daterange: [],
      searchPost: {
        page_no: 1,
        page_size: 10,
        merchantsName: '',
        busName: '',
        begin_time: '',
        end_time: '',
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [
          {
            text: '近7天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '近30天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '近90天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },

      // filter
      checkList: [1, 2, 3, 4, 5, 6],
      isIndeterminate: false,
      checkAll: true,

      // table
      tableData: [],
      total: 0,
    }
  },
  computed: {
    checkOne() {
      return this.checkList.includes(1)
    },
    checkTwo() {
      return this.checkList.includes(2)
    },
    checkThree() {
      return this.checkList.includes(3)
    },
    checkFour() {
      return this.checkList.includes(4)
    },
    checkFive() {
      return this.checkList.includes(5)
    },
    checkSix() {
      return this.checkList.includes(6)
    },
  },
  methods: {
    // request
    async getList(params = this.searchPost) {
      this.setDateParams()
      return await this.apiPost('/bird/DataAnalysis/getGateDataAnalysisList', params).then((res) => {
        if (res.errorcode == 0) {
          if (params.is_export === 1) {
            const list = res.data.list
            list.forEach((item) => {
              // enter
              item.scan_enter_rate_str = item.scan_enter_rate + '%'
              item.qrcode_enter_rate_str = item.qrcode_enter_rate + '%'
              item.face_enter_rate_str = item.face_enter_rate + '%'
              item.nfc_enter_rate_str = item.nfc_enter_rate + '%'
              item.vein_enter_rate_str = item.vein_enter_rate + '%'
              item.palmservice_enter_rate_str = item.palmservice_enter_rate + '%'
              item.assist_palmservice_enter_rate_str = item.assist_palmservice_enter_rate + '%'
              item.palmservice_available_enter_rate_str = item.palmservice_available_enter_rate + '%'
              item.firmament_palmservice_enter_rate_str = item.firmament_palmservice_enter_rate + '%'
              item.keygen_palmservice_enter_rate_str = item.keygen_palmservice_enter_rate + '%'
              // leave
              item.scan_leave_rate_str = item.scan_leave_rate + '%'
              item.qrcode_leave_rate_str = item.qrcode_leave_rate + '%'
              item.face_leave_rate_str = item.face_leave_rate + '%'
              item.nfc_leave_rate_str = item.nfc_leave_rate + '%'
              item.vein_leave_rate_str = item.vein_leave_rate + '%'
              item.palmservice_leave_rate_str = item.palmservice_leave_rate + '%'
              item.palmservice_available_leave_rate_str = item.palmservice_available_leave_rate + '%'
              item.assist_palmservice_leave_rate_str = item.assist_palmservice_leave_rate + '%'
              item.firmament_palmservice_leave_rate_str = item.firmament_palmservice_leave_rate + '%'
              item.keygen_palmservice_leave_rate_str = item.keygen_palmservice_leave_rate + '%'
            })
            return list
          } else {
            this.tableData = res.data.list
            this.total = res.data.count
          }
        }
      })
    },
    // event
    async handleExport() {
      this.setDateParams()
      const params = {
        ...this.searchPost,
        page_no: 1,
        page_size: this.total,
        is_export: 1,
      }
      const list = await this.getList(params)
      const columns = this.getColumns()
      ExportCsv(list, columns, '出入闸机数据分析')
    },
    handleSearch() {
      this.searchPost.page_no = 1
      this.getList()
    },
    handleFilter() {
      const checkedCount = this.checkList.length
      this.checkAll = checkedCount === 6
      this.isIndeterminate = checkedCount > 0 && checkedCount < 6
    },
    handleCheckAllChange() {
      this.checkList = this.checkAll ? [1, 2, 3, 4, 5, 6] : []
      this.isIndeterminate = false
    },
    handleSizeChange(val) {
      this.searchPost.page_size = val
      this.searchPost.page_no = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.searchPost.page_no = val
      this.getList()
    },
    // util
    setDateParams() {
      if (Array.isArray(this.daterange) && this.daterange.length === 2) {
        this.searchPost.begin_time = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchPost.end_time = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchPost.begin_time = ''
        this.searchPost.end_time = ''
      }
    },
    getColumns() {
      let columns = [
        { prop: 'mer_name', label: '商家名称' },
        { prop: 'bus_name', label: '场馆名称' },
        { prop: 'statistics_date', label: '日期' },
        { prop: 'enter_num', label: '入闸次数' },
      ]
      // enter
      if (this.checkOne) {
        columns.push({ prop: 'scan_enter_num', label: '扫码入闸次数' })
        columns.push({ prop: 'scan_enter_rate_str', label: '扫码入闸渗透率' })
      }
      if (this.checkTwo) {
        columns.push({ prop: 'qrcode_enter_num', label: '二维码入闸次数' })
        columns.push({ prop: 'qrcode_enter_rate_str', label: '二维码入闸渗透率' })
      }
      if (this.checkThree) {
        columns.push({ prop: 'face_enter_num', label: '人脸入闸次数' })
        columns.push({ prop: 'face_enter_rate_str', label: '人脸入闸渗透率' })
      }
      if (this.checkFour) {
        columns.push({ prop: 'nfc_enter_num', label: 'NFC入闸次数' })
        columns.push({ prop: 'nfc_enter_rate_str', label: 'NFC入闸渗透率' })
      }
      if (this.checkFive) {
        columns.push({ prop: 'vein_enter_num', label: '指静脉入闸次数' })
        columns.push({ prop: 'vein_enter_rate_str', label: '指静脉入闸渗透率' })
      }
      if (this.checkSix) {
        columns.push({ prop: 'open_palmservice_user_enter_num', label: '已开通刷掌的入闸用户数' })
        columns.push({ prop: 'open_palmservice_user_overall_enter_num', label: '已开通刷掌用户的入闸总次数' })
        columns.push({ prop: 'open_firmament_palmservice_user_overall_enter_num', label: '已开通空中绑掌用户的入闸总次数' })
        columns.push({ prop: 'open_keygen_palmservice_user_overall_enter_num', label: '已注册机开通刷掌用户的入闸总次数' })
        columns.push({ prop: 'open_assist_palmservice_user_overall_enter_num', label: '已开通协助绑掌用户的入闸总次数' })
        columns.push({ prop: 'palmservice_enter_num', label: '刷掌入闸次数' })
        columns.push({ prop: 'user_palmservice_enter_num', label: '会员刷掌入闸次数' })
        columns.push({ prop: 'whitelist_palmservice_enter_num', label: '白名单刷掌入闸次数' })
        columns.push({ prop: 'palmservice_enter_rate_str', label: '刷掌入闸渗透率' })
        columns.push({ prop: 'palmservice_available_enter_rate_str', label: '刷掌入闸可用就用率' })
        columns.push({ prop: 'firmament_palmservice_enter_rate_str', label: '（空中绑掌）刷掌入闸的可用就用率' })
        columns.push({ prop: 'keygen_palmservice_enter_rate_str', label: '（注册机）绑掌入闸的可用就用率' })
        columns.push({ prop: 'assist_palmservice_enter_rate_str', label: '（协助绑掌）刷掌入闸的可用就用率' })
        columns.push({ prop: 'palmservice_enter_user_num', label: '刷掌入闸用户数' })
      }
      // leave
      columns.push({ prop: 'leave_num', label: '出闸次数' })
      if (this.checkOne) {
        columns.push({ prop: 'scan_leave_num', label: '扫码出闸次数' })
        columns.push({ prop: 'scan_leave_rate_str', label: '扫码出闸渗透率' })
      }
      if (this.checkTwo) {
        columns.push({ prop: 'qrcode_leave_num', label: '二维码出闸次数' })
        columns.push({ prop: 'qrcode_leave_rate_str', label: '二维码出闸渗透率' })
      }
      if (this.checkThree) {
        columns.push({ prop: 'face_leave_num', label: '人脸出闸次数' })
        columns.push({ prop: 'face_leave_rate_str', label: '人脸出闸渗透率' })
      }
      if (this.checkFour) {
        columns.push({ prop: 'nfc_leave_num', label: 'NFC出闸次数' })
        columns.push({ prop: 'nfc_leave_rate_str', label: 'NFC出闸渗透率' })
      }
      if (this.checkFive) {
        columns.push({ prop: 'vein_leave_num', label: '指静脉出闸次数' })
        columns.push({ prop: 'vein_leave_rate_str', label: '指静脉出闸渗透率' })
      }
      if (this.checkSix) {
        columns.push({ prop: 'open_palmservice_user_leave_num', label: '已开通刷掌的出闸用户数' })
        columns.push({ prop: 'open_palmservice_user_overall_leave_num', label: '已开通刷掌用户的出闸总次数' })
        columns.push({ prop: 'open_firmament_palmservice_user_overall_leave_num', label: '已开通空中绑掌用户的出闸总次数' })
        columns.push({ prop: 'open_keygen_palmservice_user_overall_leave_num', label: '已注册机开通刷掌用户的出闸总次数' })
        columns.push({ prop: 'open_assist_palmservice_user_overall_leave_num', label: '已开通协助绑掌用户的出闸总次数' })
        columns.push({ prop: 'palmservice_leave_num', label: '刷掌出闸次数' })
        columns.push({ prop: 'user_palmservice_leave_num', label: '会员刷掌出闸次数' })
        columns.push({ prop: 'whitelist_palmservice_leave_num', label: '白名单刷掌出闸次数' })
        columns.push({ prop: 'palmservice_leave_rate_str', label: '刷掌出闸渗透率' })
        columns.push({ prop: 'palmservice_available_leave_rate_str', label: '刷掌出闸可用就用率' })
        columns.push({ prop: 'firmament_palmservice_leave_rate_str', label: '（空中绑掌）刷掌出闸的可用就用率' })
        columns.push({ prop: 'keygen_palmservice_leave_rate_str', label: '（注册机）刷掌出闸的可用就用率' })
        columns.push({ prop: 'assist_palmservice_leave_rate_str', label: '（协助绑掌）刷掌出闸的可用就用率' })
         

        columns.push({ prop: 'palmservice_leave_user_num', label: '刷掌出闸用户数' })
      }
      return columns
    },
  },
  created() {
    const begin = new Date()
    const end = new Date()
    begin.setTime(begin.getTime() - 3600 * 1000 * 24 * 30)
    this.daterange = [begin, end]
    this.getList()
  },
}
</script>

<template>
  <div class="box">
    <div class="search-box">
      <div class="search-item">
        <el-input
          placeholder="商家名称"
          v-model="searchPost.merchantsName"
          @keyup.enter.native="handleSearch"
          clearable
        ></el-input>
      </div>
      <div class="search-item">
        <el-input placeholder="场馆名称" v-model="searchPost.busName" @keyup.enter.native="handleSearch" clearable></el-input>
      </div>
      <div class="search-item">
        <el-date-picker
          v-model="daterange"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          :clearable="false"
        ></el-date-picker>
      </div>
      <el-button type="success" icon="el-icon-search" @click="handleSearch">搜索</el-button>
    </div>
    <div class="filter-box">
      <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange" style="margin-right: 20px">
        全选
      </el-checkbox>
      <el-checkbox-group v-model="checkList" @change="handleFilter">
        <el-checkbox :label="1">扫码出/入闸</el-checkbox>
        <el-checkbox :label="2">二维码出/入闸</el-checkbox>
        <el-checkbox :label="3">人脸出/入闸</el-checkbox>
        <el-checkbox :label="4">NFC出/入闸</el-checkbox>
        <el-checkbox :label="5">指静脉出/入闸</el-checkbox>
        <el-checkbox :label="6">刷掌出/入闸</el-checkbox>
      </el-checkbox-group>
    </div>
    <div class="table-box">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="mer_name" label="商家名称" width="150" align="center"></el-table-column>
        <el-table-column prop="bus_name" label="场馆名称" width="150" align="center"></el-table-column>
        <el-table-column prop="statistics_date" label="日期" width="150" align="center"></el-table-column>
        <el-table-column label="入闸" width="150" align="center">
          <el-table-column prop="enter_num" label="入闸次数" align="center"></el-table-column>
          <el-table-column prop="scan_enter_num" v-if="checkOne" label="扫码入闸次数" align="center"></el-table-column>
          <el-table-column prop="scan_enter_rate" v-if="checkOne" label="扫码入闸渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.scan_enter_rate }}%</template>
          </el-table-column>
          <el-table-column prop="qrcode_enter_num" v-if="checkTwo" label="二维码入闸次数" align="center"></el-table-column>
          <el-table-column prop="qrcode_enter_rate" v-if="checkTwo" label="二维码入闸渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.qrcode_enter_rate }}%</template>
          </el-table-column>
          <el-table-column prop="face_enter_num" v-if="checkThree" label="人脸入闸次数" align="center"></el-table-column>
          <el-table-column prop="face_enter_rate" v-if="checkThree" label="人脸入闸渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.face_enter_rate }}%</template>
          </el-table-column>
          <el-table-column prop="nfc_enter_num" v-if="checkFour" label="NFC入闸次数" align="center"></el-table-column>
          <el-table-column prop="nfc_enter_rate" v-if="checkFour" label="NFC入闸渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.nfc_enter_rate }}%</template>
          </el-table-column>
          <el-table-column prop="vein_enter_num" v-if="checkFive" label="指静脉入闸次数" align="center"></el-table-column>
          <el-table-column prop="vein_enter_rate" v-if="checkFive" label="指静脉入闸渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.vein_enter_rate }}%</template>
          </el-table-column>
          <el-table-column
            prop="open_palmservice_user_enter_num"
            v-if="checkSix"
            label="已开通刷掌的入闸用户数"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="open_palmservice_user_overall_enter_num"
            v-if="checkSix"
            label="已开通刷掌用户的入闸总次数"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="open_firmament_palmservice_user_overall_enter_num"
            v-if="checkSix"
            label="已开通空中绑掌用户的入闸总次数"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="open_keygen_palmservice_user_overall_enter_num"
            v-if="checkSix"
            label="已注册机开通刷掌用户的入闸总次数"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="open_assist_palmservice_user_overall_enter_num"
            v-if="checkSix"
            label="已开通协助绑掌用户的入闸总次数"
            align="center"
          ></el-table-column>
          <el-table-column prop="palmservice_enter_num" v-if="checkSix" label="刷掌入闸次数" align="center"></el-table-column>
          <el-table-column
            prop="user_palmservice_enter_num"
            v-if="checkSix"
            label="会员刷掌入闸次数"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="whitelist_palmservice_enter_num"
            v-if="checkSix"
            label="白名单刷掌入闸次数"
            align="center"
          ></el-table-column>
          <el-table-column prop="palmservice_enter_rate" v-if="checkSix" label="刷掌入闸渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.palmservice_enter_rate }}%</template>
          </el-table-column>
          <el-table-column prop="palmservice_available_enter_rate" v-if="checkSix" label="刷掌入闸可用就用率" align="center">
            <template slot-scope="scope">{{ scope.row.palmservice_available_enter_rate }}%</template>
          </el-table-column>
          <el-table-column prop="firmament_palmservice_enter_rate" v-if="checkSix" label="（空中绑掌）刷掌入闸的可用就用率" align="center">
            <template slot-scope="scope">{{ scope.row.firmament_palmservice_enter_rate }}%</template>
          </el-table-column>
          <el-table-column prop="keygen_palmservice_enter_rate" v-if="checkSix" label="（注册机）绑掌入闸的可用就用率" align="center">
            <template slot-scope="scope">{{ scope.row.keygen_palmservice_enter_rate }}%</template>
          </el-table-column>

          <el-table-column
            prop="assist_palmservice_enter_rate"
            v-if="checkSix"
            label="（协助绑掌）刷掌入闸的可用就用率"
            align="center"
          >
            <template slot-scope="scope">{{ scope.row.assist_palmservice_enter_rate }}%</template>
          </el-table-column>
          <el-table-column
            prop="palmservice_enter_user_num"
            v-if="checkSix"
            label="刷掌入闸用户数"
            align="center"
          ></el-table-column>
        </el-table-column>
        <el-table-column label="出闸" align="center">
          <el-table-column prop="leave_num" label="出闸次数" align="center"></el-table-column>
          <el-table-column prop="scan_leave_num" v-if="checkOne" label="扫码出闸次数" align="center"></el-table-column>
          <el-table-column prop="scan_leave_rate" v-if="checkOne" label="扫码出闸渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.scan_leave_rate }}%</template>
          </el-table-column>
          <el-table-column prop="qrcode_leave_num" v-if="checkTwo" label="二维码出闸次数" align="center"></el-table-column>
          <el-table-column prop="qrcode_leave_rate" v-if="checkTwo" label="二维码出闸渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.qrcode_leave_rate }}%</template>
          </el-table-column>
          <el-table-column prop="face_leave_num" v-if="checkThree" label="人脸出闸次数" align="center"></el-table-column>
          <el-table-column prop="face_leave_rate" v-if="checkThree" label="人脸出闸渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.face_leave_rate }}%</template>
          </el-table-column>
          <el-table-column prop="nfc_leave_num" v-if="checkFour" label="NFC出闸次数" align="center"></el-table-column>
          <el-table-column prop="nfc_leave_rate" v-if="checkFour" label="NFC出闸渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.nfc_leave_rate }}%</template>
          </el-table-column>
          <el-table-column prop="vein_leave_num" v-if="checkFive" label="指静脉出闸次数" align="center"></el-table-column>
          <el-table-column prop="vein_leave_rate" v-if="checkFive" label="指静脉出闸渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.vein_leave_rate }}%</template>
          </el-table-column>
          <el-table-column
            prop="open_palmservice_user_leave_num"
            v-if="checkSix"
            label="已开通刷掌的出闸用户数"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="open_palmservice_user_overall_leave_num"
            v-if="checkSix"
            label="已开通刷掌用户的出闸总次数"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="open_firmament_palmservice_user_overall_leave_num"
            v-if="checkSix"
            label="已开通空中绑掌用户的出闸总次数"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="open_keygen_palmservice_user_overall_leave_num"
            v-if="checkSix"
            label="已注册机开通刷掌用户的出闸总次数"
            align="center"
          ></el-table-column>

          <el-table-column
            prop="open_assist_palmservice_user_overall_leave_num"
            v-if="checkSix"
            label="已开通协助绑掌用户的出闸总次数"
            align="center"
          ></el-table-column>
          <el-table-column prop="palmservice_leave_num" v-if="checkSix" label="刷掌出闸次数" align="center"></el-table-column>
          <el-table-column
            prop="user_palmservice_leave_num"
            v-if="checkSix"
            label="会员刷掌出闸次数"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="whitelist_palmservice_leave_num"
            v-if="checkSix"
            label="白名单刷掌出闸次数"
            align="center"
          ></el-table-column>
          <el-table-column prop="palmservice_leave_rate" v-if="checkSix" label="刷掌出闸渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.palmservice_leave_rate }}%</template>
          </el-table-column>
          <el-table-column prop="palmservice_available_leave_rate" v-if="checkSix" label="刷掌出闸可用就用率" align="center">
            <template slot-scope="scope">{{ scope.row.palmservice_available_leave_rate }}%</template>
          </el-table-column>
          <el-table-column prop="firmament_palmservice_leave_rate" v-if="checkSix" label="（空中绑掌）刷掌出闸的可用就用率" align="center">
            <template slot-scope="scope">{{ scope.row.firmament_palmservice_leave_rate }}%</template>
          </el-table-column>
          <el-table-column prop="keygen_palmservice_leave_rate" v-if="checkSix" label="（注册机）刷掌出闸的可用就用率" align="center">
            <template slot-scope="scope">{{ scope.row.keygen_palmservice_leave_rate }}%</template>
          </el-table-column>
          <el-table-column
            prop="assist_palmservice_leave_rate"
            v-if="checkSix"
            label="（协助绑掌）刷掌出闸的可用就用率"
            align="center"
          >
            <template slot-scope="scope">{{ scope.row.assist_palmservice_leave_rate }}%</template>
          </el-table-column>
          <el-table-column
            prop="palmservice_leave_user_num"
            v-if="checkSix"
            label="刷掌出闸用户数"
            align="center"
          ></el-table-column>
        </el-table-column>
      </el-table>
    </div>
    <div class="page-box">
      <el-button @click="handleExport">导出</el-button>
      <el-pagination
        background
        layout="prev, pager, next, sizes, jumper, ->, total"
        :total="total"
        prev-text="上一页"
        next-text="下一页"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="searchPost.page_size"
        @size-change="handleSizeChange"
        :current-page="searchPost.page_no"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<style lang="less" scoped>
.box {
  background-color: white;
  padding: 20px;

  .search-box {
    display: flex;
    flex-direction: row;
    align-items: center;

    .search-item {
      min-width: 200px;
      margin-right: 20px;
    }
  }

  .filter-box {
    height: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .table-box {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .page-box {
    margin-top: 20px;
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
