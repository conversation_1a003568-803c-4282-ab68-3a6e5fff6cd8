
<style lang="less">
  .business {
    .el-form-item__content {
      display: flex;
    }
  }
  .brief {
    height: 500px;
    padding: 10px;
    overflow-y: scroll;
    > * {
      max-width: 100%;
      overflow: hidden;
      text-overflow: ellipsis;
      white-space: no-wrap;
    }
    img {
      display: block;
      max-width: 100%;
    }
  }
</style>
<template>
  <div class="container business">
    <el-table :data="tableData" stripe>
      <el-table-column prop="title" align="center" label="帮助标题"></el-table-column>
      <el-table-column prop="content" align="center" label="内容预览" width="600px">
        <template scope="scope">
          <el-button size="small" @click="showBrief(scope.row.id)" type="text">预览</el-button>
        </template>
      </el-table-column>
      <el-table-column prop="create_time" align="center" label="创建时间"></el-table-column>
      <el-table-column prop="update_time" align="center" label="更新时间"></el-table-column>
      <el-table-column prop="id" align="center" label="操作">
        <template scope="scope">
          <el-button size="small" @click="toDetail(scope.row.id)" type="primary">编辑</el-button>
          <el-button size="small" type="danger" @click="deleteNews(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
        <el-button type="success" @click="toDetail('')" size="small" icon="el-icon-plus">
          添加
        </el-button>
      </div>
      <el-pagination layout="total, prev, pager, next, sizes" background @size-change="sizeChange" @current-change="pageChange" :page-size="pageSize" :current-page.sync="page" :page-sizes="[10, 20, 30]" :total="total"></el-pagination>
    </footer>
    <el-dialog :visible.sync="brief">
      <div slot="title" align="center" class="dialog-footer">内容预览</div>
      <div class="brief">
        <p v-html="briefContent"></p>
      </div>
    </el-dialog>
  </div>
</template>
  <script>
  import Editor from 'components/form/Editor';
  import { formatDate } from '../../utils/index.js'
  export default {
    name: 'faqPublish',
    components: {
      Editor
    },
    data() {
      return {
        uploadUrl: _g.getRbBaseUrl() + '/Admin/Public/upload',
        fileList: [],
        briefContent: '',
        brief: false,
        form: {
          phone: ''
        },
        page: 1,
        pageSize: 10,
        total: 0,
        tableData: []
      };
    },
    created() {
      this.getList();
    },
    methods: {
      pageChange(page) {
        this.page = page;
        this.getList();
      },
      async showBrief(id) {
        await this.$service
          .get(`/bird/Faq/getFaqList?id=${id}`)
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.briefContent = data.data.content;
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          })
          this.brief = true;
      },
      async clickDetail(id) {
        const item = this.tableData.find(item => item.id === id);
        this.district_id = item.district_id;
        this.detail = { ...item };
        this.getRegion();
        this.getRegion(2);
        this.getRegion(3);
        this.showDetail = true;
      },
      toDetail(id = '') {
        this.$router.push({ path: '/birdnets/faqContent', query: { id }});
      },
      deleteNews(id) {
        const url = '/bird/Faq/postFaqDelete';
        this.$service
          .post(url, { id })
          .then(res => {
            if (res.data.errorcode === 0) {
              this.$message.success(res.data.errormsg);
              this.getList();
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      },
      sizeChange(e) {
        this.page = 1;
        this.pageSize = e;
        this.getList();
      },
      getList() {
        const url = '/bird/Faq/getFaqList';
        this.$service
          .get(url, {
            params: { page_no: this.page, page_size: this.pageSize }
          })
          .then(res => {
            if (res.data.errorcode === 0) {
              const data = res.data.data;
              this.total = data.count;
              let exp = /<p><img/;
              data.list.map(item => {
                
              })
              this.tableData = data.list;
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      }
    }
  };
</script>
