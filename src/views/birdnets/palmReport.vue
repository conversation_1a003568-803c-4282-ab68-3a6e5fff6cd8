<script>
import { formatDate } from '@/utils/index'
import ExportCsv from 'src/components/form/csvExport'
import http from 'assets/js/http'

export default {
  name: 'PalmReport',
  mixins: [http],
  data() {
    return {
      // search
      daterange: [],
      searchPost: {
        page_no: 1,
        page_size: 10,
        merchantsName: '',
        begin_time: '',
        end_time: '',
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [
          {
            text: '近7天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '近30天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '近90天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },

      // filter
      checkList: [1, 2, 3, 4, 5, 6, 7],
      isIndeterminate: false,
      checkAll: true,

      // table
      tableData: [],
      total: 0,
    }
  },
  methods: {
    // request
    async getList(params = this.searchPost) {
      this.setDateParams()
      return await this.apiPost('/bird/DataAnalysis/getPalmserviceUserOpenList', params).then((res) => {
        if (res.errorcode == 0) {
          if (params.is_export === 1) {
            const list = res.data.list
            return list
          } else {
            this.tableData = res.data.list
            this.total = res.data.count
          }
        }
      })
    },
    // event
    async handleExport() {
      this.setDateParams()
      const params = {
        ...this.searchPost,
        page_no: 1,
        page_size: this.total,
        is_export: 1,
      }
      const list = await this.getList(params)
      const columns = this.getColumns()
      ExportCsv(list, columns, '刷掌用户开通数据分析')
    },
    handleSearch() {
      this.searchPost.page_no = 1
      this.getList()
    },
    handleSizeChange(val) {
      this.searchPost.page_size = val
      this.searchPost.page_no = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.searchPost.page_no = val
      this.getList()
    },
    // util
    setDateParams() {
      if (Array.isArray(this.daterange) && this.daterange.length === 2) {
        this.searchPost.begin_time = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchPost.end_time = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchPost.begin_time = ''
        this.searchPost.end_time = ''
      }
    },
    getColumns() {
      let columns = [
        { prop: 'mer_name', label: '商家名称' },
        { prop: 'statistics_date', label: '日期' },
        { prop: 'all_num', label: '开通总人数' },
        { prop: 'register_num', label: '注册机开通总人数' },
        { prop: 'firmament_num', label: '空中绑掌开通总人数' },
        { prop: 'assist_num', label: '协助绑掌开通总人数' },
      ]
      return columns
    },
  },
  created() {
    const begin = new Date()
    const end = new Date()
    begin.setTime(begin.getTime() - 3600 * 1000 * 24 * 30)
    this.daterange = [begin, end]
    this.getList()
  },
}
</script>

<template>
  <div class="box">
    <div class="search-box">
      <div class="search-item">
        <el-input
          placeholder="商家名称"
          v-model="searchPost.merchantsName"
          @keyup.enter.native="handleSearch"
          clearable
        ></el-input>
      </div>
      <div class="search-item">
        <el-date-picker
          v-model="daterange"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          :clearable="false"
        ></el-date-picker>
      </div>
      <el-button type="success" icon="el-icon-search" @click="handleSearch">搜索</el-button>
    </div>
    <div class="filter-box"></div>
    <div class="table-box">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="mer_name" label="商家名称" align="center"></el-table-column>
        <el-table-column prop="statistics_date" label="日期" align="center"></el-table-column>
        <el-table-column prop="all_num" label="开通总人数" align="center"></el-table-column>
        <el-table-column prop="register_num" label="注册机开通总人数" align="center"></el-table-column>
        <el-table-column prop="firmament_num" label="空中绑掌开通总人数" align="center"></el-table-column>
        <el-table-column prop="assist_num" label="协助绑掌开通总人数" align="center"></el-table-column>
      </el-table>
    </div>
    <div class="page-box">
      <el-button @click="handleExport">导出</el-button>
      <el-pagination
        background
        layout="prev, pager, next, sizes, jumper, ->, total"
        :total="total"
        prev-text="上一页"
        next-text="下一页"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="searchPost.page_size"
        @size-change="handleSizeChange"
        :current-page="searchPost.page_no"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<style lang="less" scoped>
.box {
  background-color: white;
  padding: 20px;

  .search-box {
    display: flex;
    flex-direction: row;
    align-items: center;

    .search-item {
      min-width: 200px;
      margin-right: 20px;
    }
  }

  .filter-box {
    height: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .table-box {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .page-box {
    margin-top: 20px;
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
