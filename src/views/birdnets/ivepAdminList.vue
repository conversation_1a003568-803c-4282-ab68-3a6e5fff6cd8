<template>
  <div class="container">
    <header>
      <el-date-picker v-model="createTimeRange"
                      @change="createTimeRangeChange"
                      type="daterange"
                      align="right"
                      unlink-panels
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      value-format="yyyy-MM-dd"
                      :picker-options="pickerOptions">
      </el-date-picker>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column prop="name" label="场馆" align="center"></el-table-column>
      <el-table-column prop="open_accounts_count" label="账号开通数量" align="center"></el-table-column>
      <el-table-column prop="coach_acounts_count" label="账号绑定数" align="center"></el-table-column>
      <el-table-column prop="coach_login_count" label="登录过教练数" align="center"></el-table-column>
      <el-table-column prop="recently_coach_login_count" label="近期登录数" align="center"></el-table-column>
      <el-table-column prop="service_member_count" label="服务会员数" align="center"></el-table-column>
      <el-table-column prop="recently_service_member_count" label="近期服务会员数" align="center"></el-table-column>
      <el-table-column prop="attend_course_member_count" label="上课总会员数(去重)" align="center"></el-table-column>
      <el-table-column prop="recently_attend_course_member_count" label="近期上课会员数(去重)" align="center"></el-table-column>
    </el-table>
     <footer>
      <div class="left">
        <el-button type="success" @click="exportCsv" size="small">
          导出Excel
        </el-button>
      </div>
      <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
      </el-pagination>
    </footer>
  </div>
</template>
<script>
import { formatDate } from 'src/assets/js/utils'
import ExportCsv from 'src/components/form/csvExport';
export default {
  name: 'ivepAdminList',
  data() {
    return {
      pickerOptions: {
        shortcuts: [{
          text: '近7天',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 7);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近30天',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
            picker.$emit('pick', [start, end]);
          }
        }, {
          text: '近90天',
          onClick (picker) {
            const end = new Date();
            const start = new Date();
            start.setTime(start.getTime() - 3600 * 1000 * 24 * 90);
            picker.$emit('pick', [start, end]);
          }
        }]
      },
      createTimeRange: [formatDate(new Date(Date.now() - 7 * 3600 * 1000 * 24), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd')],
      postData: {
        query_type: 1,
        start_time: formatDate(new Date(Date.now() - 7 * 3600 * 1000 * 24), 'yyyy-MM-dd'),
        end_time: formatDate(new Date(), 'yyyy-MM-dd'),
        page_size: 10,
        page_no: 1
      },
      tableData: [],
      dataCount: 0
    };
  },
  methods: {
     exportCsv() {
        const columns = [
          { prop: 'name', label: '场馆' },
          { prop: 'open_accounts_count', label: '账号开通数量' },
          { prop: 'coach_login_count', label: '账号绑定数' },
          { prop: 'coach_login_count', label: '登录过教练数' },
          { prop: 'recently_coach_login_count', label: '近期登录数' },
          { prop: 'service_member_count', label: '服务会员数' },
          { prop: 'recently_service_member_count', label: '近期服务会员数' },
          { prop: 'attend_course_member_count', label: '上课总会员数(去重)' },
          { prop: 'recently_attend_course_member_count', label: '近期上课会员数(去重)' }
        ];
        this.exportDataList(columns);
      },
      exportDataList(columns) {
        let postd = { is_export: 1 };
        postd = Object.assign(postd, this.postData);
        postd.page_size = this.dataCount;
        this.$service.post('ivep/IvepStatistics/AccountsInfoList', postd).then(res => {
          if (res.data.errorcode == 0) {
            let exportData = res.data.data.list;
            ExportCsv(exportData, columns);
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
    createTimeRangeChange (d) {
      const [s, e] = d || [];
      this.postData.start_time = s;
      this.postData.end_time = e;
      this.getList()
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.getList();
    },
    search() {
      this.postData.page_no = 1;
      this.getList();
    },
    getList() {
      this.$service.post('ivep/IvepStatistics/AccountsInfoList', this.postData).then(res => {
        if (res.data.errorcode == 0) {
          this.tableData = res.data.data.list;
          this.dataCount = res.data.data.count;
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    }
  },
  created() {
    this.getList();
  }
};
</script>
