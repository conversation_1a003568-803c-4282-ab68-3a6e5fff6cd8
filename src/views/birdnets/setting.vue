<template>
  <el-card header="参数设置">
    <el-form label-width="140px">
      <el-form-item label="我司销售">
        将
        <el-select v-model="before_user_id" clearable>
          <el-option value="" label="未分配"></el-option>
          <el-option v-for="item in saleList" :key="item.id" :value="item.id" :label="item.username"></el-option>
        </el-select>
        名下数据调整到
        <el-select v-model="after_user_id" clearable>
          <el-option value="" label="未分配"></el-option>
          <el-option v-for="item in saleList" :key="item.id" :value="item.id" :label="item.username"></el-option>
        </el-select>
        名下
      </el-form-item>
      <el-form-item label=" ">
        <el-button type="primary" @click="submit">变更</el-button>
      </el-form-item>
    </el-form>
  </el-card>
</template>

<script>
  export default {
    name: 'setting',
    data() {
      return {
        saleList: [],
        before_user_id: '',
        after_user_id: ''
      }
    },
    created() {
      this.getSales()
    },
    methods: {
      getSales() {
        const url = '/bird/User/getUserList'
        this.$service.post(url).then(res => {
          if (res.data.errorcode === 0) {
            this.saleList = res.data.data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      submit() {
        const url = '/bird/User/changeSales'
        this.$service.post(url, {
          before_user_id: this.before_user_id,
          after_user_id: this.after_user_id
        }).then(res => {
          if (res.data.errorcode === 0) {
            this.$message.success(res.data.errormsg);
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      }
    },
  }
</script>

<style scoped>

</style>
