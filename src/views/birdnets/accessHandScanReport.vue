<script>
import { formatDate } from '@/utils/index'
import ExportCsv from 'src/components/form/csvExport'
import http from 'assets/js/http'

export default {
  name: 'AccessHandScanReport',
  mixins: [http],
  data() {
    return {
      // search
      daterange: [],
      searchPost: {
        page_no: 1,
        page_size: 10,
        merchandiser_name: '',
        store_name: '',
        begin_time: '',
        end_time: '',
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [
          {
            text: '近7天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '近30天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '近90天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },

      // table
      tableData: [],
      total: 0,
    }
  },
  methods: {
    // request
    async getList(params = this.searchPost) {
      this.setDateParams()
      return await this.apiPost('/bird/DataAnalysis/getPalmserviceDataAnalysisList', params).then((res) => {
        if (res.errorcode == 0) {
          if (params.is_export === 1) {
            const list = res.data.list
            list.forEach((item) => {
              item.open_palmservice_enter_rate_str = item.open_palmservice_enter_rate + '%'
              item.utilization_rate_str = item.utilization_rate + '%'
              item.palmservice_pay_rate_str = item.palmservice_pay_rate + '%'
            })
            return list
          } else {
            this.tableData = res.data.list
            this.total = res.data.count
          }
        }
      })
    },
    // event
    async handleExport() {
      this.setDateParams()
      const params = {
        ...this.searchPost,
        page_no: 1,
        page_size: this.total,
        is_export: 1,
      }
      const list = await this.getList(params)
      const columns = this.getColumns()
      ExportCsv(list, columns, '刷掌开通及购票分析')
    },
    handleSearch() {
      this.searchPost.page_no = 1
      this.getList()
    },
    handleSizeChange(val) {
      this.searchPost.page_size = val
      this.searchPost.page_no = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.searchPost.page_no = val
      this.getList()
    },
    // util
    setDateParams() {
      if (Array.isArray(this.daterange) && this.daterange.length === 2) {
        this.searchPost.begin_time = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchPost.end_time = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchPost.begin_time = ''
        this.searchPost.end_time = ''
      }
    },
    getColumns() {
      let columns = [
        { prop: 'mer_name', label: '商家名称' },
        { prop: 'statistics_date', label: '日期' },
        { prop: 'open_num', label: '已开通总人数' },
        { prop: 'enter_num', label: '入闸用户数' },
        { prop: 'open_user_num', label: '已开通刷掌入闸用户数' },
        { prop: 'open_palmservice_enter_rate_str', label: '刷掌开通渗透率' },
        { prop: 'order_num', label: '订单总数' },
        { prop: 'open_order_num', label: '开通刷掌用户的总订单数' },
        { prop: 'palmservice_order_num', label: '使用刷掌支付的订单数' },
        { prop: 'utilization_rate_str', label: '可用就用率' },
        { prop: 'palmservice_pay_rate_str', label: '刷掌支付渗透率' },
      ]
      return columns
    },
  },
  created() {
    const begin = new Date()
    const end = new Date()
    begin.setTime(begin.getTime() - 3600 * 1000 * 24 * 30)
    this.daterange = [begin, end]
    this.getList()
  },
}
</script>

<template>
  <div class="box">
    <div class="search-box">
      <div class="search-item">
        <el-input
          placeholder="商家名称"
          v-model="searchPost.merchandiser_name"
          @keyup.enter.native="handleSearch"
          clearable
        ></el-input>
      </div>
      <div class="search-item">
        <el-input placeholder="场馆名称" v-model="searchPost.store_name" @keyup.enter.native="handleSearch" clearable></el-input>
      </div>
      <div class="search-item">
        <el-date-picker
          v-model="daterange"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          :clearable="false"
        ></el-date-picker>
      </div>
      <el-button type="success" icon="el-icon-search" @click="handleSearch">搜索</el-button>
    </div>
    <div class="table-box">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="mer_name" label="商家名称" width="150" align="center"></el-table-column>
        <!-- <el-table-column prop="bus_name" label="场馆名称" width="150"></el-table-column> -->
        <el-table-column prop="statistics_date" label="日期" width="150" align="center"></el-table-column>
        <el-table-column label="开通" align="center">
          <el-table-column prop="open_num" label="已开通总人数" align="center"></el-table-column>
          <el-table-column prop="enter_num" label="入闸用户数" align="center"></el-table-column>
          <el-table-column prop="open_user_num" label="已开通刷掌入闸用户数" align="center"></el-table-column>
          <el-table-column prop="open_palmservice_enter_rate" label="刷掌开通渗透率">
            <template slot-scope="scope">
              {{ scope.row.open_palmservice_enter_rate + '%' }}
            </template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="购票" align="center">
          <el-table-column prop="order_num" label="订单总数" align="center"></el-table-column>
          <el-table-column prop="open_order_num" label="开通刷掌用户的总订单数" align="center"></el-table-column>
          <el-table-column prop="palmservice_order_num" label="使用刷掌支付的订单数" align="center"></el-table-column>
          <el-table-column prop="utilization_rate" label="可用就用率" align="center">
            <template slot-scope="scope">
              {{ scope.row.utilization_rate + '%' }}
            </template>
          </el-table-column>
          <el-table-column prop="palmservice_pay_rate" label="刷掌支付渗透率" align="center">
            <template slot-scope="scope">
              {{ scope.row.palmservice_pay_rate + '%' }}
            </template>
          </el-table-column>
        </el-table-column>
      </el-table>
    </div>
    <div class="page-box">
      <el-button @click="handleExport">导出</el-button>
      <el-pagination
        background
        layout="prev, pager, next, sizes, jumper, ->, total"
        :total="total"
        prev-text="上一页"
        next-text="下一页"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="searchPost.page_size"
        @size-change="handleSizeChange"
        :current-page="searchPost.page_no"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<style lang="less" scoped>
.box {
  background-color: white;
  padding: 20px;

  .search-box {
    display: flex;
    flex-direction: row;
    align-items: center;

    .search-item {
      min-width: 200px;
      margin-right: 20px;
    }
  }

  .table-box {
    margin-top: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .page-box {
    margin-top: 20px;
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
