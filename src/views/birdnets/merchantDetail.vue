<template>
  <div>
    <el-card header="基础信息">
      <el-form inline>
        <el-form-item label="商家名称">
          <el-input v-model="form.mer_name" disabled></el-input>
        </el-form-item>
        <el-form-item label="影响力">
          <el-select v-model="form.mer_effect" disabled>
            <el-option :value="1" label="跨区域"></el-option>
            <el-option :value="2" label="省内"></el-option>
            <el-option :value="3" label="市内"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select :disabled="!isEdit" v-model="form.mer_level" clearable filterable placeholder="客户等级">
            <el-option v-for="(item, index) in customLevel" :value="index + 1" :key="item" :label="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" v-if="!isEdit" @click="isEdit = true">编辑</el-button>
          <el-button type="primary" v-else @click="editInfo">保存</el-button>
        </el-form-item>
      </el-form>
    </el-card>
    <el-card style="margin-top: 20px;" header="商家名下场馆">
      <div class="container">
        <el-table :data="merBusData">
          <el-table-column type="index" width="60"></el-table-column>
          <el-table-column prop="bus_name" label="场馆名"></el-table-column>
          <el-table-column prop="bus_type" label="类型"></el-table-column>
          <el-table-column prop="renew_status" label="当前状态"></el-table-column>
          <el-table-column prop="province_name" label="省"></el-table-column>
          <el-table-column prop="city_name" label="市"></el-table-column>
          <el-table-column prop="district_name" label="区县"></el-table-column>
          <el-table-column prop="address" label="详细地址"></el-table-column>
        </el-table>
        <footer>
          <Pager :post-data="postData" :total="postData.total" @on-change="busPageChange"></Pager>
        </footer>
      </div>
    </el-card>
    <el-card style="margin-top: 20px;" header="备注信息">
      <div class="container">
        <el-table :data="remarkList">
          <el-table-column type="index" width="60"></el-table-column>
          <el-table-column prop="create_time" label="时间"></el-table-column>
          <el-table-column prop="username" label="操作人"></el-table-column>
          <el-table-column prop="message" label="备注信息"></el-table-column>
        </el-table>
        <footer>
          <el-button size="small" type="primary" @click="showDialog = true">新建备注</el-button>
          <Pager :total="remarkTotal" :post-data="remarkPostData" @on-change="onRemarkPageChange"></Pager>
        </footer>
      </div>
    </el-card>
    <el-dialog title="添加备注" :visible.sync="showDialog" center>
      <el-input v-model="remark" type="textarea"
                :autosize="{ minRows: 6, maxRows: 8}"></el-input>
      <div slot="footer">
        <el-button type="success" @click="addRemark">确定</el-button>
        <el-button @click="showDialog = false">取消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
  import Pager from 'src/components/pager'

  export default {
    name: 'merchantDetail',
    components: { Pager },
    data() {
      return {
        merBusData: [],
        remarkList: [],
        remark: '',
        showDialog: false,
        postData: {
          mer_id: '',
          page_no: 1,
          page_size: 10,
          total: 0
        },
        form: {
          mer_id: '',
          mer_name: '',
          mer_effect: '',
          mer_level: ''
        },
        isEdit: false,
        customLevel: ['A', 'AA', 'AAA', 'AAAA', 'AAAAA'],
        remarkTotal: 0,
        remarkPostData: { page_no: 1, page_size: 10, mer_id: '' }
      }
    },
    created() {
      this.postData.mer_id = this.remarkPostData.mer_id = this.$route.query.id;
      this.getInfo()
      this.getMerBusData()
      this.getRemarkList()
    },
    methods: {
      getInfo() {
        const url = '/bird/Merchants/getMerchantsInfo'
        this.$service.post(url, { mer_id: this.$route.query.id }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.form = data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      busPageChange({ page_no, page_size }) {
        this.postData.page_no = page_no;
        this.postData.page_size = page_size;
        this.getMerBusData()
      },
      getMerBusData() {
        const url = '/bird/Merchants/getBusList'
        this.$service.post(url, this.postData).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.merBusData = data.list;
            this.postData.total = data.count;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      addRemark() {
        const url = '/bird/Merchants/addRemarks'
        this.$service.post(url, {
          message: this.remark,
          mer_id: this.postData.mer_id,
          user_id: this.$store.state.userInfo.id
        }).then(res => {
          if (res.data.errorcode === 0) {
            this.$message.success(res.data.errormsg);
            this.getRemarkList();
            this.remark = ''
            this.showDialog = false
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      editInfo() {
        const url = '/bird/Merchants/updateMerchants'
        this.$service.post(url, this.form).then(res => {
          if (res.data.errorcode === 0) {
            this.$message.success(res.data.errormsg);
            this.isEdit = false
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      onRemarkPageChange({ page_no, page_size }) {
        this.remarkPostData = { ...this.remarkPostData, page_no, page_size }
        this.getRemarkList();
      },
      getRemarkList() {
        const url = '/bird/Merchants/getMerRemarkList'
        this.$service.post(url, this.remarkPostData).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.remarkList = data.list;
            this.remarkTotal = data.count;
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      }
    },
  }
</script>

<style scoped>

</style>
