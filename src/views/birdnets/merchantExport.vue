<template>
  <div>
    <el-card header="商家列表">
      <el-form inline>

        <h3>按基础信息</h3>
        <el-form-item>
          <el-input v-model="postData.mer_name" placeholder="商家名称"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input v-model="postData.mer_region" placeholder="布局城市"></el-input>
        </el-form-item>
        <el-form-item>
          <el-select v-model="postData.mer_effect" clearable filterable placeholder="影响力">
            <el-option label="跨区域" value="1"></el-option>
            <el-option label="省内" value="2"></el-option>
            <el-option label="市内" value="3"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="postData.mer_level" clearable filterable placeholder="客户等级">
            <el-option v-for="(item, index) in customLevel" :value="index + 1" :key="item" :label="item"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select v-model="postData.bus_num" clearable filterable placeholder="场馆数量">
            <el-option label="3家以下" value="1"></el-option>
            <el-option label="4~10家" value="2"></el-option>
            <el-option label="11~20家" value="3"></el-option>
            <el-option label="21~50家" value="4"></el-option>
            <el-option label="50家以上" value="5"></el-option>
          </el-select>
        </el-form-item>

        <div>
          <el-form-item>
            <el-button type="info" @click="handleReset">重置</el-button>
            <el-button type="primary" @click="doSearch">查询</el-button>
          </el-form-item>
        </div>
      </el-form>
    </el-card>
    <el-card style="margin-top: 20px" header="场馆列表">
      <div class="container">
        <el-table :data="tableData" stripe ref="table">
          <el-table-column fixed align="center" prop="mer_name" label="商家名称">
            <template scope="scope">
              <router-link :to="{ path: '/birdnets/merchantDetail', query: { id: scope.row.mer_id } }" target="_blank">{{ scope.row.mer_name
                }}
              </router-link>
            </template>
          </el-table-column>
          <el-table-column align="center" prop="bus_num" label="场馆数量"></el-table-column>
          <el-table-column align="center" prop="mer_effect" label="影响力"></el-table-column>
          <el-table-column align="center" prop="mer_level" label="客户等级"></el-table-column>
          <el-table-column align="center" prop="mer_region" label="布局城市"></el-table-column>
        </el-table>
        <footer>
          <el-button type="success" @click="exportTable">导出Excel</el-button>
          <Export ref="export"></Export>
          <Pager :total="total" @on-change="onPageChange" :postData="postData"></Pager>
        </footer>
      </div>
    </el-card>
  </div>
</template>

<script>
  import Pager from 'components/pager';
  import { formatDate } from 'src/assets/js/utils'
  import Export from 'src/components/Export'

  export default {
    name: 'dataExport',
    components: { Pager, Export },
    data() {
      return {
        saleList: [],
        expand: false,
        queryDateOption: {
          disabledDate(date) {
            return Date.now() - date < 24 * 3600 * 1000
          }
        },
        createTimeRange: [],
        endTimeRange: [],
        activeRange: [],
        total: 0,
        tableData: [],
        customLevel: ['A', 'AA', 'AAA', 'AAAA', 'AAAAA'],
        chargeVersion: ['试用期', '试用结束', '初次购买', '续费', '临时延期', '到期终结'],
        provincesList: [],
        citiesList: [],
        versionList: [],
        postData: {
          page_no: 1,
          page_size: 10,
          mer_name: '',
          mer_region: '',
          mer_level: '',
          mer_effect: '',
          bus_num: '',
        },
        initPostData: {
          page_no: 1,
          page_size: 10,
          mer_name: '',
          mer_region: '',
          mer_level: '',
          mer_effect: '',
          bus_num: '',
        }
      }
    },
    created() {
    },
    methods: {
      doSearch() {
        this.postData.page_no = 1;
        this.getList();
      },
      handleReset() {
        this.postData = { ...this.initPostData };
        this.createTimeRange = this.endTimeRange = this.activeRange = []
      },
      onPageChange({ page_no, page_size }) {
        this.postData = { ...this.postData, page_no, page_size };
        this.getList();
      },
      getList(e, isExport = false) {
        const url = '/bird/Merchants/getMerchantsList'
        return this.$service.post(url, {
          ...this.postData,
          page_no: isExport ? 1 : this.postData.page_no,
          page_size: isExport ? this.total : this.postData.page_size
        }).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            if (!data.list.length) this.$message.error('未查询到数据');
            const list = data.list.map(item => {
              return {
                ...item,
                mer_effect: item.mer_effect == 1 ? '跨区域' : item.mer_effect == 2 ? '省内' : '市内',
              }
            });
            if (isExport) {
              return list;
            } else {
              this.total = data.count;
              this.tableData = list;
            }
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      async exportTable() {
        if (!this.tableData.length) return this.$message.error('请先查询要导出的数据');
        const data = await this.getList('', true);
        const columns = this.$refs.table.$children.filter(t => t.prop != null).map(item => ({
          title: item.label,
          key: item.prop
        }));
        this.$refs.export.export({
          filename: '商家列表',
          data,
          columns
        })
      }
    },
  }
</script>

<style scoped lang="less">
  .panel {
    height: 0;
    overflow: hidden;
  }

  .expand {
    height: auto;
  }

</style>
