<script>
import { formatDate } from '@/utils/index'
import http from 'assets/js/http'

export default {
  name: 'AccessOperationLog',
  mixins: [http],
  data() {
    return {
      // search
      daterange: [],
      searchPost: {
        page_no: 1,
        page_size: 10,
        merchantsName: '',
        busName: '',
        operation_type: '',
        begin_time: '',
        end_time: '',
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [
          {
            text: '近7天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '近30天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '近90天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },

      // table
      tableData: [],
      total: 0,
    }
  },
  methods: {
    // request
    getList(params = this.searchPost) {
      this.setDateParams()
      return this.apiPost('/bird/DataAnalysis/getDataAnalysisNodeOperateList', params).then((res) => {
        if (res.errorcode == 0) {
          this.tableData = res.data.list
          this.total = res.data.count
        }
      })
    },
    // event
    handleSearch() {
      this.searchPost.page_no = 1
      this.getList()
    },
    handleSizeChange(val) {
      this.searchPost.page_size = val
      this.searchPost.page_no = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.searchPost.page_no = val
      this.getList()
    },
    // util
    setDateParams() {
      if (Array.isArray(this.daterange) && this.daterange.length === 2) {
        this.searchPost.begin_time = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchPost.end_time = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchPost.begin_time = ''
        this.searchPost.end_time = ''
      }
    },
  },
  created() {
    const begin = new Date()
    const end = new Date()
    begin.setTime(begin.getTime() - 3600 * 1000 * 24 * 30)
    this.daterange = [begin, end]
    this.getList()
  },
}
</script>

<template>
  <div class="box">
    <div class="search-box">
      <div class="search-item">
        <el-input
          placeholder="商家名称"
          v-model="searchPost.merchantsName"
          @keyup.enter.native="handleSearch"
          clearable
        ></el-input>
      </div>
      <div class="search-item">
        <el-input placeholder="场馆名称" v-model="searchPost.busName" @keyup.enter.native="handleSearch" clearable></el-input>
      </div>
      <div class="search-item">
        <el-select v-model="searchPost.operation_type" placeholder="操作类型" clearable>
          <el-option label="添加" value="1"></el-option>
          <el-option label="编辑" value="2"></el-option>
          <el-option label="删除" value="3"></el-option>
        </el-select>
      </div>
      <div class="search-item">
        <el-date-picker
          v-model="daterange"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          :clearable="false"
        ></el-date-picker>
      </div>
      <el-button type="success" icon="el-icon-search" @click="handleSearch">搜索</el-button>
    </div>
    <div class="table-box">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="create_time" label="操作时间" width="150" align="center"></el-table-column>
        <el-table-column prop="realname" label="操作人" align="center"></el-table-column>
        <el-table-column prop="mer_name" label="商家名称" align="center"></el-table-column>
        <el-table-column prop="bus_name" label="场馆名称" align="center"></el-table-column>
        <el-table-column prop="operation" label="操作类型" width="100" align="center">
          <template slot-scope="scope">
            <span v-if="scope.row.operation == 1">添加</span>
            <span v-if="scope.row.operation == 2">编辑</span>
            <span v-if="scope.row.operation == 3">删除</span>
          </template>
        </el-table-column>
        <el-table-column prop="content" label="操作内容" align="center"></el-table-column>
      </el-table>
    </div>
    <div class="page-box">
      <div></div>
      <el-pagination
        background
        layout="prev, pager, next, sizes, jumper, ->, total"
        :total="total"
        prev-text="上一页"
        next-text="下一页"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="searchPost.page_size"
        @size-change="handleSizeChange"
        :current-page="searchPost.page_no"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<style lang="less" scoped>
.box {
  background-color: white;
  padding: 20px;

  .search-box {
    display: flex;
    flex-direction: row;
    align-items: center;

    .search-item {
      min-width: 200px;
      margin-right: 20px;
    }
  }

  .table-box {
    margin-top: 20px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .page-box {
    margin-top: 20px;
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
