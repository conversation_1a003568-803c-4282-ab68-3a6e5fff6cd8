<template>
  <div class="birdnets">
    <el-card>
      <span class="title" slot="header"
            style="display: flex; justify-content: space-between; padding: 0; align-items: center; margin: 0">
        <span>数据看板<Info value="场馆增量 = 初次购买 - 未续约 + 重新续约；第一次购买，为初次购买，按月度记录当月数据；到期时间在本月内，已完成的续约的，为续约；到期时间在本月内，且到期后未续约的，为“未续约”；续约时间过后重新续约的，为“重新续约”。"></Info></span>
        <div>记录时间： <el-date-picker type="month" @change="getData" :picker-options="pickerOptions" v-model="history_date" value-format="yyyy-MM"></el-date-picker></div>
      </span>
      <div ref="growChart" class="chart"></div>
    </el-card>
    <el-card style="margin-top: 20px;">
      <span class="title" slot="header">场馆数据</span>
      <div class="cards">
        <el-card class="card">
          <h3>{{stat.busCount}}</h3>
          <p>场馆总数<Info value="当前在勤鸟系统中注册的场馆总数量，已扣除删除的场馆数量。" /></p>
        </el-card>
        <el-card class="card">
          <h3>{{stat.busEffectiveCount}}</h3>
          <p>有效签约场馆<Info value="当前在付费使用的场馆数。标签为“初次付费”+“续费”" /></p>
        </el-card>
        <el-card class="card">
          <h3>{{stat.busProbationCount}}</h3>
          <p>试用场馆<Info value="当前处于试用状态的场馆数。标签为“试用”" /></p>
        </el-card>
        <el-card class="card">
          <h3>{{stat.firstPurchase}}</h3>
          <p>本月新签约<Info value="本月内“初次付费”的场馆数。等同于新签约场馆。" /></p>
        </el-card>
        <el-card class="card">
          <h3>{{stat.renewContract}}</h3>
          <p>本月续费<Info value="本月内到期，且在到期之前完成续费的场馆数。" /></p>
        </el-card>
      </div>
    </el-card>
    <el-card style="margin-top: 20px;">
      <span class="title" slot="header">活跃度</span>
      <div class="cards">
        <el-card class="card">
          <h3>{{stat.active_date_A}}</h3>
          <p>活跃<Info value="过往30天内，活跃天数大于等于22天的场馆数。" /></p>
          <p>(活跃天数≥22天)</p>
        </el-card>
        <el-card class="card">
          <h3>{{stat.active_date_B}}</h3>
          <p>异常<Info value="过往30天内，活跃天数小于22天的场馆数。" /></p>
          <p>(活跃天数<22天)</p>
        </el-card>
      </div>
    </el-card>
    <el-card style="margin-top: 20px;">
      <span class="title" slot="header">人员运营(累计)</span>
      <div class="cards">
        <el-card class="card">
          <h3>{{stat.member}}</h3>
          <p>会员<Info value="所有场馆相加，累计的有效会员数。" /></p>
        </el-card>
        <el-card class="card">
          <h3>{{stat.work}}</h3>
          <p>会籍<Info value="所有场馆相加，累计的会籍人员数。" /></p>
        </el-card>
        <el-card class="card">
          <h3>{{stat.coach}}</h3>
          <p>教练<Info value="所有场馆相加，累计的教练人员数。" /></p>
        </el-card>
      </div>
    </el-card>
    <el-card style="margin-top: 20px;">
      <span class="title" slot="header">续约数据<Info value="续约数据为当月需要续约的场馆数，以及到目前为止的已经续约的场馆数。本月续约率 = 本月已续约/本月需预约" /></span>
      <div ref="repayChart" class="chart"></div>
    </el-card>
  </div>
</template>

<script>
  import Echarts from 'echarts'
  import Info from 'src/components/info'
  import { formatDate } from 'src/assets/js/utils'

  export default {
    name: 'birdNets',
    components: {Info},
    data() {
      return {
        pickerOptions: {
          disabledDate(date) {
            return date - Date.now() > 0
          }
        },
        history_date: formatDate(new Date(), 'yyyy-MM'),
        stat: {
          active_date_A: 0,
          active_date_B: 0,
          busCount: 0,
          busEffectiveCount: 0,
          busProbationCount: 0,
          coach: 0,
          firstPurchase: 0,
          member: 0,
          renewContract: 0,
          work: 0
        },
        growChart: null,
        repayChart: null,
        growChartOption: {
          color: ['#52a4ea', '#1bd4c9', '#ff696a', '#a76de8', '#ff9c28'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              }
            }
          },
          legend: { data: ['初次购买', '续约', '未续约', '重新续约', '增量'] },
          xAxis: [
            {
              type: 'category',
              data: [],
              axisPointer: {
                type: 'shadow'
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '数量',
              min: 0,
              max: 400,
              axisLabel: {
                formatter: '{value} 家'
              }
            },
            {
              type: 'value',
              name: '增量',
              splitLine: { show: false },
              axisLabel: {
                formatter: '{value} 家'
              }
            }
          ],
          series: [
            {
              name: '初次购买',
              type: 'bar',
              label: { normal: { show: true, position: 'top' } },
              data: []
            },
            {
              name: '续约',
              type: 'bar',
              label: { normal: { show: true, position: 'top' } },
              data: []
            },
            {
              name: '未续约',
              type: 'bar',
              label: { normal: { show: true, position: 'top' } },
              data: []
            },
            {
              name: '重新续约',
              type: 'bar',
              label: { normal: { show: true, position: 'top' } },
              data: []
            },
            {
              name: '增量',
              type: 'line',
              yAxisIndex: 1,
              label: {
                normal: {
                  show: true,
                  // formatter: '{c} 家'
                }
              },
              data: []
            },
          ]
        },
        repayChartOption: {
          color: ['#52a4ea', '#1bd4c9', '#ff696a', '#a76de8', '#ff9c28'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              }
            }
          },
          legend: { data: ['需续约', '已续约', '续约率'] },
          xAxis: [
            {
              type: 'category',
              data: [],
              axisPointer: {
                type: 'shadow'
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '续约数据',
              min: 0,
              max: 400,
              axisLabel: {
                formatter: '{value} 家'
              }
            },
            {
              type: 'value',
              name: '续约率',
              splitLine: { show: false },
              min: -100,
              max: 100,
              axisLabel: {
                formatter: '{value} %'
              }
            }
          ],
          series: [
            {
              name: '需续约',
              type: 'bar',
              label: {
                normal: { show: true, position: 'top' }
              },
              data: []
            },
            {
              name: '已续约',
              type: 'bar',
              label: {
                normal: { show: true, position: 'top' }
              },
              data: []
            },
            {
              name: '续约率',
              type: 'line',
              yAxisIndex: 1,
              label: {
                normal: {
                  show: true,
                  formatter: '{c} %'
                },
              },
              data: []
            },
          ]
        },
      }
    },
    created() {
      this.getData();
    },
    mounted() {
      this.growChart = Echarts.init(this.$refs.growChart);
      this.repayChart = Echarts.init(this.$refs.repayChart);
    },
    methods: {
      getData() {
        const url = '/bird/BirdNest/getIndexList'
        this.$service.post(url, { history_date: this.history_date }).then(res => {
          if (res.data.errorcode === 0) {
            const { renew_contract_A, renew_contract_B, statistics } = res.data.data;
            this.stat = statistics;
            this.dealGrowData(renew_contract_A.list);
            this.dealRepayData(renew_contract_B);
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      dealGrowData(data) {
        const date = Object.keys(data);
        const values = Object.values(data);
        const firstBuy = values.map(item => item.first_purchase);
        const renew = values.map(item => item.renew_contract);
        const notRenew = values.map(item => item.no_renew_contract);
        const reRenew = values.map(item => item.again_renew_contra);
        const increment = values.map(item => item.business_increment);
        const maxValue = Math.max(...[...firstBuy, ...renew, ...notRenew, ...reRenew]);
        const maxY = Math.max(maxValue * 2, 200);
        const maxIncrementY = Math.max(Math.max(...increment) + 20, 100);
        const minIncrementY = Math.min(-(maxIncrementY - Math.min(...increment)), 0);
        this.growChartOption.xAxis[0].data = date;
        this.growChartOption.yAxis[0].max = maxY;
        this.growChartOption.yAxis[1].max = maxIncrementY;
        this.growChartOption.yAxis[1].min = minIncrementY;
        this.growChartOption.series[0].data = firstBuy;
        this.growChartOption.series[1].data = renew;
        this.growChartOption.series[2].data = notRenew;
        this.growChartOption.series[3].data = reRenew;
        this.growChartOption.series[4].data = increment;
        this.$nextTick(() => this.growChart.setOption(this.growChartOption))
      },
      dealRepayData({ list, next }) {
        const date = Object.keys(list).concat(Object.keys(next));
        const values = Object.values(list).concat(Object.values(next));
        const needRenew = values.map(item => item.renew_contract_A);
        const hasRenew = values.map(item => item.renew_contract_B);
        const maxValue = Math.max(...[...needRenew, ...hasRenew]);
        const maxY = Math.max(maxValue * 2, 200);
        const rate = values.map(item => item.rate);
        this.repayChartOption.xAxis[0].data = date;
        this.repayChartOption.yAxis[0].max = maxY;
        this.repayChartOption.series[0].data = needRenew;
        this.repayChartOption.series[1].data = hasRenew;
        this.repayChartOption.series[2].data = rate;
        this.$nextTick(() => this.repayChart.setOption(this.repayChartOption))
      },
    },
  }
</script>

<style scoped lang="less">
  .birdnets {
    .chart {
      height: 500px;
      min-height: 50vh;
    }

    .title {
      font-weight: bold;
    }

    .cards {
      display: flex;
      flex-wrap: wrap;

      .card {
        width: 180px;
        text-align: center;
        margin: 10px;
      }
    }

  }
</style>
