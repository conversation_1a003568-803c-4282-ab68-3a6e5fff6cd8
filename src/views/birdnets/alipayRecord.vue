<template>
  <div class="container">
    <header>
      <el-date-picker v-model="createTimeRange"
                      @change="createTimeRangeChange"
                      type="daterange"
                      align="right"
                      unlink-panels
                      range-separator="至"
                      start-placeholder="开始日期"
                      end-placeholder="结束日期"
                      value-format="yyyy-MM-dd">
      </el-date-picker>
      <el-input v-model="postData.order_sn" placeholder="订单号"></el-input>
      <el-input v-model="postData.phone" placeholder="手机"></el-input>
      <el-input v-model="postData.pid" placeholder="支付宝用户ID"></el-input>
      <el-input v-model="postData.name" placeholder="商品名称"></el-input>
      <el-input v-model="postData.bus_id" placeholder="门店ID"></el-input>
      <el-select v-model="postData.status" clearable filterable placeholder="状态">
        <el-option label="待付款" value="1"></el-option>
        <el-option label="已支付" value="2"></el-option>
        <el-option label="已取消" value="3"></el-option>
        <el-option label="已关闭" value="4"></el-option>
        <el-option label="已退款" value="5"></el-option>
      </el-select>
      <el-button type="success" @click="searchList">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column prop="create_time" label="订单创建时间" align="center"></el-table-column>
      <el-table-column prop="order_sn" label="订单编号" align="center"></el-table-column>
      <el-table-column prop="alipay_user_id" label="支付宝用户ID" align="center"></el-table-column>
      <el-table-column prop="phone" label="用户手机号" align="center"></el-table-column>
      <el-table-column prop="bus_name" label="门店名称" align="center"></el-table-column>
      <el-table-column prop="card_name" label="商品名称" align="center"></el-table-column>
      <el-table-column prop="number" label="数量" align="center"></el-table-column>
      <el-table-column prop="order_amount" label="订单金额" align="center"></el-table-column>
      <el-table-column prop="discount_type" label="优惠方式" align="center"></el-table-column>
      <el-table-column prop="discount_amount" label="优惠金额" align="center"></el-table-column>
      <el-table-column prop="amount" label="支付金额" align="center"></el-table-column>
      <el-table-column prop="pay_time" label="支付时间" align="center"></el-table-column>
      <el-table-column prop="merchant_no" label="收款商户号" align="center"></el-table-column>
      <el-table-column prop="status_txt" label="状态" align="center"></el-table-column>
      <el-table-column prop="remark" label="备注" align="center"></el-table-column>
    </el-table>
     <footer>
      <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
      </el-pagination>
    </footer>
  </div>
</template>
<script>
import { formatDate } from 'src/assets/js/utils'
export default {
  name: 'alipayList',
  data() {
    return {
      createTimeRange: [formatDate(new Date(Date.now() - 7 * 3600 * 1000 * 24), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd')],
      postData: {
        bus_id: '',
        status: '',
        pid: '',
        name: '',
        order_sn: '',
        phone: '',
        query_type: 1,
        s_date: formatDate(new Date(Date.now() - 7 * 3600 * 1000 * 24), 'yyyy-MM-dd'),
        e_date: formatDate(new Date(), 'yyyy-MM-dd'),
        page_size: 10,
        page_no: 1
      },
      tableData: [],
      dataCount: 0
    };
  },
  methods: {
    createTimeRangeChange (d) {
      const [s, e] = d || [];
      this.postData.s_date = s;
      this.postData.e_date = e;
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.getList();
    },
    searchList() {
      this.postData.page_no = 1;
      this.getList();
    },
    getList() {
      this.$service.post('Web/AlipayOrder/get_list', this.postData).then(res => {
        if (res.data.errorcode == 0) {
          this.tableData = res.data.data.list;
          this.dataCount = res.data.data.count;
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    }
  },
  created() {
    this.getList();
  }
};
</script>
