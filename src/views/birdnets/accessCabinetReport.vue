<script>
import { formatDate } from '@/utils/index'
import ExportCsv from 'src/components/form/csvExport'
import http from 'assets/js/http'

export default {
  name: 'AccessCabinetReport',
  mixins: [http],
  data() {
    return {
      // search
      daterange: [],
      searchPost: {
        page_no: 1,
        page_size: 10,
        merchantsName: '',
        busName: '',
        begin_time: '',
        end_time: '',
      },
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() > Date.now()
        },
        shortcuts: [
          {
            text: '近7天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '近30天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
              picker.$emit('pick', [start, end])
            },
          },
          {
            text: '近90天',
            onClick(picker) {
              const end = new Date()
              const start = new Date()
              start.setTime(start.getTime() - 3600 * 1000 * 24 * 90)
              picker.$emit('pick', [start, end])
            },
          },
        ],
      },

      // filter
      checkList: [1, 2, 3, 4, 5, 6, 7],
      isIndeterminate: false,
      checkAll: true,

      // table
      tableData: [],
      total: 0,
    }
  },
  computed: {
    checkOne() {
      return this.checkList.includes(1)
    },
    checkTwo() {
      return this.checkList.includes(2)
    },
    checkThree() {
      return this.checkList.includes(3)
    },
    checkFour() {
      return this.checkList.includes(4)
    },
    checkFive() {
      return this.checkList.includes(5)
    },
    checkSix() {
      return this.checkList.includes(6)
    },
    checkSeven() {
      return this.checkList.includes(7)
    },
    // checkEight() {
    //   return this.checkList.includes('8')
    // },
  },
  methods: {
    // request
    async getList(params = this.searchPost) {
      this.setDateParams()
      return await this.apiPost('/bird/DataAnalysis/getCabinetDataAnalysisList', params).then((res) => {
        if (res.errorcode == 0) {
          if (params.is_export === 1) {
            const list = res.data.list
            list.forEach((item) => {
              // access
              item.palmservice_access_use_rate_str = item.palmservice_access_use_rate + '%'
              item.assist_palmservice_access_use_rate_str = item.assist_palmservice_access_use_rate + '%'
              item.palmservice_access_rate_str = item.palmservice_access_rate + '%'
              item.qrcode_access_rate_str = item.qrcode_access_rate + '%'
              item.face_access_rate_str = item.face_access_rate + '%'
              item.nfc_access_rate_str = item.nfc_access_rate + '%'
              item.scan_access_rate_str = item.scan_access_rate + '%'
              item.vein_access_rate_str = item.vein_access_rate + '%'
              item.passwd_access_rate_str = item.passwd_access_rate + '%'
              item.firmament_palmservice_access_use_rate_str = item.firmament_palmservice_access_use_rate + '%'
              item.keygen_palmservice_access_use_rate_str = item.keygen_palmservice_access_use_rate + '%'
              // refund
              item.palmservice_refund_use_rate_str = item.palmservice_refund_use_rate + '%'
              item.palmservice_refund_rate_str = item.palmservice_refund_rate + '%'
              item.qrcode_refund_rate_str = item.qrcode_refund_rate + '%'
              item.face_refund_rate_str = item.face_refund_rate + '%'
              item.nfc_refund_rate_str = item.nfc_refund_rate + '%'
              item.scan_refund_rate_str = item.scan_refund_rate + '%'
              item.vein_refund_rate_str = item.vein_refund_rate + '%'
              item.passwd_refund_rate_str = item.passwd_refund_rate + '%'
            })
            return list
          } else {
            this.tableData = res.data.list
            this.total = res.data.count
          }
        }
      })
    },
    // event
    async handleExport() {
      this.setDateParams()
      const params = {
        ...this.searchPost,
        page_no: 1,
        page_size: this.total,
        is_export: 1,
      }
      const list = await this.getList(params)
      const columns = this.getColumns()
      ExportCsv(list, columns, '存取退柜数据分析')
    },
    handleSearch() {
      this.searchPost.page_no = 1
      this.getList()
    },
    handleFilter() {
      const checkedCount = this.checkList.length
      // this.checkAll = checkedCount === 8
      // this.isIndeterminate = checkedCount > 0 && checkedCount < 8
      this.checkAll = checkedCount === 7
      this.isIndeterminate = checkedCount > 0 && checkedCount < 7
    },
    handleCheckAllChange() {
      // this.checkList = this.checkAll ? ['1', '2', '3', '4', '5', '6', '7', '8'] : []
      this.checkList = this.checkAll ? [1, 2, 3, 4, 5, 6, 7] : []
      this.isIndeterminate = false
    },
    handleSizeChange(val) {
      this.searchPost.page_size = val
      this.searchPost.page_no = 1
      this.getList()
    },
    handleCurrentChange(val) {
      this.searchPost.page_no = val
      this.getList()
    },
    // util
    setDateParams() {
      if (Array.isArray(this.daterange) && this.daterange.length === 2) {
        this.searchPost.begin_time = formatDate(this.daterange[0], 'yyyy-MM-dd')
        this.searchPost.end_time = formatDate(this.daterange[1], 'yyyy-MM-dd')
      } else {
        this.searchPost.begin_time = ''
        this.searchPost.end_time = ''
      }
    },
    getColumns() {
      let columns = [
        { prop: 'mer_name', label: '商家名称' },
        { prop: 'bus_name', label: '场馆名称' },
        { prop: 'statistics_date', label: '日期' },
        { prop: 'access_num', label: '存取物次数' },
      ]
      // access
      if (this.checkOne) {
        columns.push({ prop: 'open_palmservice_user_access_num', label: '开通刷掌用户的存取物总次数' })
        columns.push({ prop: 'open_firmament_palmservice_user_access_num', label: '已开通空中绑掌用户的存取物总次数' })
        columns.push({ prop: 'open_keygen_palmservice_user_access_num', label: '已注册机开通刷掌用户的存取物总次数' })
        columns.push({ prop: 'open_assist_palmservice_user_access_num', label: '已开通协助绑掌用户的存取物总次数' })
        columns.push({ prop: 'palmservice_access_num', label: '刷掌存取物次数' })
        columns.push({ prop: 'palmservice_access_use_rate_str', label: '刷掌存取物可用就用率' })
        columns.push({ prop: 'firmament_palmservice_access_use_rate_str', label: '（空中绑掌）刷掌存取物可用就用率' })
        columns.push({ prop: 'keygen_palmservice_access_use_rate_str', label: '（注册机）刷掌存取物可用就用率' })
        columns.push({ prop: 'assist_palmservice_access_use_rate_str', label: '（协助绑掌）刷掌存取物可用就用率' })
        columns.push({ prop: 'palmservice_access_rate_str', label: '刷掌存取物渗透率' })
      }
      if (this.checkTwo) {
        columns.push({ prop: 'qrcode_access_num', label: '二维存取物次数' })
        columns.push({ prop: 'qrcode_access_rate_str', label: '二维码存取物渗透率' })
      }
      if (this.checkThree) {
        columns.push({ prop: 'face_access_num', label: '人脸存取物次数' })
        columns.push({ prop: 'face_access_rate_str', label: '人脸存取物渗透率' })
      }
      if (this.checkFour) {
        columns.push({ prop: 'nfc_access_num', label: 'NFC存取物次数' })
        columns.push({ prop: 'nfc_access_rate_str', label: 'NFC存取物渗透率' })
      }
      if (this.checkFive) {
        columns.push({ prop: 'scan_access_num', label: '扫码存取物次数' })
        columns.push({ prop: 'scan_access_rate_str', label: '扫码存取物渗透率' })
      }
      if (this.checkSix) {
        columns.push({ prop: 'vein_access_num', label: '指静脉存取物次数' })
        columns.push({ prop: 'vein_access_rate_str', label: '指静脉存取物渗透率' })
      }
      if (this.checkSeven) {
        columns.push({ prop: 'passwd_access_num', label: '密码存取物次数' })
        columns.push({ prop: 'passwd_access_rate_str', label: '密码存取物渗透率' })
      }
      // refund
      columns.push({ prop: 'refund_num', label: '退柜次数' })
      if (this.checkOne) {
        columns.push({ prop: 'open_palmservice_user_refund_num', label: '开通刷掌用户的退柜总次数' })
        columns.push({ prop: 'palmservice_refund_num', label: '刷掌退柜次数' })
        columns.push({ prop: 'palmservice_refund_use_rate_str', label: '刷掌退柜可用就用率' })
        columns.push({ prop: 'palmservice_refund_rate_str', label: '刷掌退柜渗透率' })
      }
      if (this.checkTwo) {
        columns.push({ prop: 'qrcode_refund_num', label: '二维码退柜次数' })
        columns.push({ prop: 'qrcode_refund_rate_str', label: '二维码退柜渗透率' })
      }
      if (this.checkThree) {
        columns.push({ prop: 'face_refund_num', label: '人脸退柜次数' })
        columns.push({ prop: 'face_refund_rate_str', label: '人脸退柜渗透率' })
      }
      if (this.checkFour) {
        columns.push({ prop: 'nfc_refund_num', label: 'NFC退柜次数' })
        columns.push({ prop: 'nfc_refund_rate_str', label: 'NFC退柜渗透率' })
      }
      if (this.checkFive) {
        columns.push({ prop: 'scan_refund_num', label: '扫码退柜次数' })
        columns.push({ prop: 'scan_refund_rate_str', label: '扫码退柜渗透率' })
      }
      if (this.checkSix) {
        columns.push({ prop: 'vein_refund_num', label: '指静脉退柜次数' })
        columns.push({ prop: 'vein_refund_rate_str', label: '指静脉退柜渗透率' })
      }
      if (this.checkSeven) {
        columns.push({ prop: 'passwd_refund_num', label: '密码退柜次数' })
        columns.push({ prop: 'passwd_refund_rate_str', label: '密码退柜渗透率' })
      }
      return columns
    },
  },
  created() {
    const begin = new Date()
    const end = new Date()
    begin.setTime(begin.getTime() - 3600 * 1000 * 24 * 30)
    this.daterange = [begin, end]
    this.getList()
  },
}
</script>

<template>
  <div class="box">
    <div class="search-box">
      <div class="search-item">
        <el-input
          placeholder="商家名称"
          v-model="searchPost.merchantsName"
          @keyup.enter.native="handleSearch"
          clearable
        ></el-input>
      </div>
      <div class="search-item">
        <el-input placeholder="场馆名称" v-model="searchPost.busName" @keyup.enter.native="handleSearch" clearable></el-input>
      </div>
      <div class="search-item">
        <el-date-picker
          v-model="daterange"
          type="daterange"
          unlink-panels
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          :picker-options="pickerOptions"
          :clearable="false"
        ></el-date-picker>
      </div>
      <el-button type="success" icon="el-icon-search" @click="handleSearch">搜索</el-button>
    </div>
    <div class="filter-box">
      <el-checkbox :indeterminate="isIndeterminate" v-model="checkAll" @change="handleCheckAllChange" style="margin-right: 20px">
        全选
      </el-checkbox>
      <el-checkbox-group v-model="checkList" @change="handleFilter">
        <el-checkbox :label="1">刷掌存取/退柜</el-checkbox>
        <el-checkbox :label="2">二维码存取/退柜</el-checkbox>
        <el-checkbox :label="3">人脸存取/退柜</el-checkbox>
        <el-checkbox :label="4">NFC存取/退柜</el-checkbox>
        <el-checkbox :label="5">扫码存取/退柜</el-checkbox>
        <el-checkbox :label="6">指静脉存取/退柜</el-checkbox>
        <el-checkbox :label="7">密码存取/退柜</el-checkbox>
        <!-- <el-checkbox label="6">系统退柜</el-checkbox>
        <el-checkbox label="7">离场自动退柜</el-checkbox>
        <el-checkbox label="8">智能结算退柜</el-checkbox> -->
      </el-checkbox-group>
    </div>
    <div class="table-box">
      <el-table :data="tableData" style="width: 100%">
        <el-table-column prop="mer_name" label="商家名称" width="150" align="center"></el-table-column>
        <el-table-column prop="bus_name" label="场馆名称" width="150" align="center"></el-table-column>
        <el-table-column prop="statistics_date" label="日期" width="150" align="center"></el-table-column>
        <el-table-column label="存取" align="center">
          <el-table-column prop="access_num" label="存取物次数" align="center"></el-table-column>
          <el-table-column
            prop="open_palmservice_user_access_num"
            v-if="checkOne"
            label="开通刷掌用户的存取物总次数"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="open_firmament_palmservice_user_access_num"
            v-if="checkOne"
            label="已开通空中绑掌用户的存取物总次数"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="open_keygen_palmservice_user_access_num"
            v-if="checkOne"
            label="已注册机开通刷掌用户的存取物总次数"
            align="center"
          ></el-table-column>
          <el-table-column
            prop="open_assist_palmservice_user_access_num"
            v-if="checkOne"
            label="已开通协助绑掌用户的存取物总次数"
            align="center"
          ></el-table-column>
          <el-table-column prop="palmservice_access_num" v-if="checkOne" label="刷掌存取物次数" align="center"></el-table-column>
          <el-table-column prop="palmservice_access_use_rate" v-if="checkOne" label="刷掌存取物可用就用率" align="center">
            <template slot-scope="scope">{{ scope.row.palmservice_access_use_rate }}%</template>
          </el-table-column>
          <el-table-column prop="firmament_palmservice_access_use_rate" v-if="checkOne" label="（空中绑掌）刷掌存取物可用就用率" align="center">
            <template slot-scope="scope">{{ scope.row.firmament_palmservice_access_use_rate }}%</template>
          </el-table-column>
          <el-table-column prop="keygen_palmservice_access_use_rate" v-if="checkOne" label="（注册机）刷掌存取物可用就用率" align="center">
            <template slot-scope="scope">{{ scope.row.keygen_palmservice_access_use_rate }}%</template>
          </el-table-column>
          <el-table-column prop="assist_palmservice_access_use_rate" v-if="checkOne" label="（协助绑掌）刷掌存取物可用就用率" align="center">
            <template slot-scope="scope">{{ scope.row.assist_palmservice_access_use_rate }}%</template>
          </el-table-column>
          <el-table-column prop="palmservice_access_rate" v-if="checkOne" label="刷掌存取物渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.palmservice_access_rate }}%</template>
          </el-table-column>
          <el-table-column prop="qrcode_access_num" v-if="checkTwo" label="二维存取物次数" align="center"></el-table-column>
          <el-table-column prop="qrcode_access_rate" v-if="checkTwo" label="二维码存取物渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.qrcode_access_rate }}%</template>
          </el-table-column>
          <el-table-column prop="face_access_num" v-if="checkThree" label="人脸存取物次数" align="center"></el-table-column>
          <el-table-column prop="face_access_rate" v-if="checkThree" label="人脸存取物渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.face_access_rate }}%</template>
          </el-table-column>
          <el-table-column prop="nfc_access_num" v-if="checkFour" label="NFC存取物次数" align="center"></el-table-column>
          <el-table-column prop="nfc_access_rate" v-if="checkFour" label="NFC存取物渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.nfc_access_rate }}%</template>
          </el-table-column>
          <el-table-column prop="scan_access_num" v-if="checkFive" label="扫码存取物次数" align="center"></el-table-column>
          <el-table-column prop="scan_access_rate" v-if="checkFive" label="扫码存取物渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.scan_access_rate }}%</template>
          </el-table-column>
          <el-table-column prop="vein_access_num" v-if="checkSix" label="指静脉存取物次数" align="center"></el-table-column>
          <el-table-column prop="vein_access_rate" v-if="checkSix" label="指静脉存取物渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.vein_access_rate }}%</template>
          </el-table-column>
          <el-table-column prop="passwd_access_num" v-if="checkSeven" label="密码存取物次数" align="center"></el-table-column>
          <el-table-column prop="passwd_access_rate" v-if="checkSeven" label="密码存取物渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.passwd_access_rate }}%</template>
          </el-table-column>
        </el-table-column>
        <el-table-column label="退柜" align="center">
          <el-table-column prop="refund_num" label="退柜次数" align="center"></el-table-column>
          <el-table-column
            prop="open_palmservice_user_refund_num"
            v-if="checkOne"
            label="开通刷掌用户的退柜总次数"
            align="center"
          ></el-table-column>
          <el-table-column prop="palmservice_refund_num" v-if="checkOne" label="刷掌退柜次数" align="center"></el-table-column>
          <el-table-column prop="palmservice_refund_use_rate" v-if="checkOne" label="刷掌退柜可用就用率" align="center">
            <template slot-scope="scope">{{ scope.row.palmservice_refund_use_rate }}%</template>
          </el-table-column>
          <el-table-column prop="palmservice_refund_rate" v-if="checkOne" label="刷掌退柜渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.palmservice_refund_rate }}%</template>
          </el-table-column>
          <el-table-column prop="qrcode_refund_num" v-if="checkTwo" label="二维码退柜次数" align="center"></el-table-column>
          <el-table-column prop="qrcode_refund_rate" v-if="checkTwo" label="二维码退柜渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.qrcode_refund_rate }}%</template>
          </el-table-column>
          <el-table-column prop="face_refund_num" v-if="checkThree" label="人脸退柜次数" align="center"></el-table-column>
          <el-table-column prop="face_refund_rate" v-if="checkThree" label="人脸退柜渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.face_refund_rate }}%</template>
          </el-table-column>
          <el-table-column prop="nfc_refund_num" v-if="checkFour" label="NFC退柜次数" align="center"></el-table-column>
          <el-table-column prop="nfc_refund_rate" v-if="checkFour" label="NFC退柜渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.nfc_refund_rate }}%</template>
          </el-table-column>
          <el-table-column prop="scan_refund_num" v-if="checkFive" label="扫码退柜次数" align="center"></el-table-column>
          <el-table-column prop="scan_refund_rate" v-if="checkFive" label="扫码退柜渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.scan_refund_rate }}%</template>
          </el-table-column>
          <el-table-column prop="vein_refund_num" v-if="checkSix" label="指静脉退柜次数" align="center"></el-table-column>
          <el-table-column prop="vein_refund_rate" v-if="checkSix" label="指静脉退柜渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.vein_refund_rate }}%</template>
          </el-table-column>
          <el-table-column prop="passwd_refund_num" v-if="checkSeven" label="密码退柜次数" align="center"></el-table-column>
          <el-table-column prop="passwd_refund_rate" v-if="checkSeven" label="密码退柜渗透率" align="center">
            <template slot-scope="scope">{{ scope.row.passwd_refund_rate }}%</template>
          </el-table-column>
          <!-- <el-table-column prop="name" v-if="checkSix" label="系统退柜次数"></el-table-column>
          <el-table-column prop="name" v-if="checkSix" label="系统退柜渗透率"></el-table-column>
          <el-table-column prop="name" v-if="checkSeven" label="离场自动退柜次数"></el-table-column>
          <el-table-column prop="name" v-if="checkSeven" label="离场自动退柜渗透离场"></el-table-column>
          <el-table-column prop="name" v-if="checkEight" label="智能结算退柜次数"></el-table-column>
          <el-table-column prop="name" v-if="checkEight" label="智能结算退柜渗透率"></el-table-column> -->
        </el-table-column>
      </el-table>
    </div>
    <div class="page-box">
      <el-button @click="handleExport">导出</el-button>
      <el-pagination
        background
        layout="prev, pager, next, sizes, jumper, ->, total"
        :total="total"
        prev-text="上一页"
        next-text="下一页"
        :page-sizes="[10, 20, 30, 40]"
        :page-size="searchPost.page_size"
        @size-change="handleSizeChange"
        :current-page="searchPost.page_no"
        @current-change="handleCurrentChange"
      ></el-pagination>
    </div>
  </div>
</template>

<style lang="less" scoped>
.box {
  background-color: white;
  padding: 20px;

  .search-box {
    display: flex;
    flex-direction: row;
    align-items: center;

    .search-item {
      min-width: 200px;
      margin-right: 20px;
    }
  }

  .filter-box {
    height: 40px;
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .table-box {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  }

  .page-box {
    margin-top: 20px;
    height: 40px;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
  }
}
</style>
