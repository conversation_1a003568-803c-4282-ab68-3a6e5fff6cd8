<template>
  <el-dialog title="添加场馆" :visible.sync="busValue" @close="visiableChange(false)">
      <div class="m-b-20 ovf-hd">
        <el-select v-model="postData.cu_buss_type" placeholder="场馆类型" class="w-150 m-r-10">
          <el-option label="场馆类型" value=""></el-option>
          <el-option label="健身房" value="1"></el-option>
          <el-option label="瑜伽馆" value="2"></el-option>
        </el-select>
        <el-input placeholder="搜索场馆名称" v-model="postData.cu_buss_name" class="w-150 m-r-10"></el-input>
        <el-select v-model="postData.city_id" placeholder="城市" class="w-150 m-r-10">
          <el-option label="城市" value=""></el-option>
          <el-option label="重庆" value="394"></el-option>
        </el-select>
        <el-button type="primary" icon="search"  @click="search">查询</el-button>
    </div>
    <el-table :data="tableData" @selection-change="changeSel">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column property="cu_buss_name" label="场馆名称"></el-table-column>
      <el-table-column property="cu_buss_type" label="类型">
        <template scope="scope">
          <span>{{ scope.row.cu_buss_type == 1 ? '健身房' : '瑜伽馆' }}</span>
        </template>
      </el-table-column>
      <el-table-column property="city_name" label="城市"></el-table-column>
      <el-table-column property="address" label="地址"></el-table-column>
    </el-table>
    <div class="pos-rel p-t-20">
      <div class="block pages">
        <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount">
        </el-pagination>
      </div>
    </div>
    <div slot="footer" class="dialog-footer">
      <el-button @click="visiableChange(false)">取 消</el-button>
      <el-button type="primary" @click="handleBusChoice">确 定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import http from 'assets/js/http'
  export default {
    name: 'busChoice',
    data() {
      return {
        tableData:[],
        busValue:this.value,
        postData: {
          cu_buss_name: '',
          city_id: this.cityId ? this.cityId : '',
          cu_buss_type: '',
          page_size: 10,
          page_no: 1,
          user_id: this.$store.state.userInfo.id
        },
        curSel:[],
        dataCount: 0,
      }
    },
    watch: {
      value(val) {
        this.busValue = val;
      },
      cityId(val) {
        this.postData.city_id = val;
      },
      busValue(val){
        this.$emit('input', val)
      }
    },
    props: ['value','cuCardId','cityId'],
    created() {
      this.getBusList();
    },
    methods: {
      getBusList() {
        let postData = this.postData;
        postData.cu_card_id = this.cuCardId;
        this.apiPost('customer/card/get_bus_list', this.postData).then((res) => {
          if(res.errorcode == 0){
            this.dataCount=res.data.count;
            this.tableData=res.data.list;
          }else{
             _g.toastMsg('success', res.errormsg)
          }
        })
      },
      changeSel(sel) {
        this.curSel = sel;
      },
      handleBusChoice() {
        if(this.curSel.length>0){
          this.$emit('saveBusChoice', this.curSel)
        }
        this.visiableChange(false);
      },
      visiableChange(isShow) {
        this.postData = {
          cu_buss_name: '',
          city_id: '',
          cu_buss_type: '',
          page_size: 10,
          page_no: 1
        };
        this.tableData = [];
        this.busValue = isShow;
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.getBusList();
      },
      search() {
        this.postData.page_no = 1;
        this.getBusList();
      }
    },
    mixins: [http]
  }
</script>


