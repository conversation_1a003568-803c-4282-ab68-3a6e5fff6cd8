

<style lang="less" scoped>
</style>

<template>
  <div class="container news-manage">
    <header>
      <el-input style="width: 300px" placeholder="搜索微信昵称或电话" v-model="title"></el-input>
      <el-button type="success" @click="getList" @keydown.enter="getList">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe ref="table">
      <el-table-column align="center" label="编号" prop="id"></el-table-column>
      <el-table-column align="center" label="微信昵称" prop="wx_nickname"></el-table-column>
      <el-table-column align="center" label="联系电话" prop="phone"></el-table-column>
      <el-table-column align="center" label="报名时间" prop="created">
        <template scope="scope">
          {{scope.row.created}}
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template scope="scope">
          <el-button size="small" type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
        <el-button type="success" @click="exportCsv" size="small">
          导出Excel
        </el-button>
        <!-- <el-dropdown size="small" @command="handleDropdown" placement="top" type="info"><el-button type="info" size="small" style="margin-left: 20px">
            其他操作
            <i class="el-icon-arrow-down el-icon--right"></i></el-button><el-dropdown-menu slot="dropdown"><el-dropdown-item command="0">批量删除</el-dropdown-item></el-dropdown-menu></el-dropdown> -->
      </div>
      <el-pagination layout="total, prev, pager, next, sizes"
                     background @size-change="sizeChange" @current-change="getList" :page-size="page_size" :current-page.sync="page_no" :page-sizes="[10, 20, 30, 40]" :total="total"></el-pagination>
    </footer>
  </div>
</template>

<script>
import Pager from '../../mixins/pager';
import ExportCsv from 'src/components/form/csvExport';
export default {
  name: 'activityMember',
  mixins: [Pager],
  data() {
    return {
      title: '',
      tableData: []
    };
  },
  created() {
    this.id = this.$route.query.id;
    this.getList();
  },
  watch: {},
  methods: {
    exportCsv() {
      const columns = this.$refs.table.$children.filter(t => t.prop != null);
      ExportCsv(this.tableData, columns);
    },
    formatDate(time) {
      return _g.formatDate(new Date(time * 1000), 'yyyy-MM-dd HH:mm');
    },
    handleDelete(id) {
      this.$confirm('确认要删除该报名吗?', '删除报名', {
        confirmButtonText: '删除',
        type: 'error'
      }).then(() => {
        this.deleteSign(id);
      });
    },
    deleteSign(id) {
      const url = '/customer/activity_apply/del_apply';
      this.$service
        .post(url, { id, act_id: this.id })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.getList();
            this.$message.success('删除成功');
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    getList() {
      const url = '/customer/activity_apply/index';
      const { page_no, page_size, title: param, id: act_id } = this;
      const postData = {
        page_no,
        page_size,
        param,
        act_id
      };
      this.$service
        .post(url, postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = data.count;
            this.tableData = data.list;
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    handlePost() {}
  }
};
</script>
