<template>
  <div class="container">
    <header>
      <div class="fl">
        <router-link class="btn-link-large add-btn" to="cardAdd">
          <i class="el-icon-plus"></i>&nbsp;&nbsp;新增健身卡
        </router-link>
      </div>
      <div class="fl m-l-30">
        <el-input placeholder="卡名称" v-model="postData.cu_card_name" class="w-150 m-r-10"></el-input>
        <el-select v-model="postData.cu_card_type" placeholder="卡类型" class="w-150 m-r-10">
          <el-option label="卡类型" value=""></el-option>
          <el-option label="期限卡" value="1"></el-option>
          <el-option label="次卡" value="2"></el-option>
        </el-select>
        <el-select v-model="postData.city_id" placeholder="城市" class="w-150 m-r-10">
          <el-option label="城市" value=""></el-option>
          <el-option label="重庆" value="394"></el-option>
        </el-select>
        <ChannelList v-model="postData.channel_id"></ChannelList>
        <el-button type="success" icon="search"  @click="search">搜索</el-button>
      </div>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column align="center" type="index" label="编号" width="80"></el-table-column>
      <el-table-column align="center" prop="city_name" label="城市"></el-table-column>
      <el-table-column align="center" label="区域" prop="channel_name">
        <template scope="scope">
          {{scope.row.channel_id == 0 ? '全部区域' : scope.row.channel_name}}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="cu_card_name" label="卡名称"></el-table-column>
      <el-table-column align="center" prop="cu_card_type" label="卡类型">
        <template scope="scope">
          <span>{{ scope.row.cu_card_type == 1 ? '期限卡' : '次卡' }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="cu_card_num" label="天数/次数"></el-table-column>
      <el-table-column align="center" prop="sell_price" label="金额"></el-table-column>
      <el-table-column align="center" prop="active_days" label="激活有效期"></el-table-column>
      <el-table-column align="center" prop="send_num" label="发行数"></el-table-column>
      <el-table-column align="center" prop="sort" label="顺序" width="80"></el-table-column>
      <el-table-column align="center" prop="status" label="状态" width="80">
        <template scope="scope">
          <span>{{ scope.row.status == 0 ? '停用' : '启用' }}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="100">
        <template scope="scope">
          <el-button size="small" type="primary" @click="$router.push({ name: 'cardEdit', params: { cardId: scope.row.cu_card_id }})">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pos-rel p-t-20 ovf-hd flexend">
      <div class="block">
        <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import http from 'assets/js/http';
import ChannelList from 'src/components/form/channelList';
export default {
  name: 'cardList',
  components: { ChannelList },
  data() {
    return {
      tableData: [],
      postData: {
        channel_id: JSON.parse(localStorage.userInfo).data.channel_id,
        cu_card_type: '',
        cu_card_name: '',
        city_id: '',
        status: 2, //状态0为启用1为禁用2为所有
        page_size: 10,
        page_no: 1
      },
      dataCount: 0
    };
  },
  methods: {
    getList() {
      this.apiPost('customer/card/get_card_list', this.postData).then(res => {
        if (res.errorcode == 0) {
          this.dataCount = res.data.count;
          this.tableData = res.data.list;
        } else {
          _g.toastMsg('success', res.errormsg);
        }
      });
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.getList();
    },
    search() {
      this.postData.page_no = 1;
      this.getList();
    }
  },
  created() {
    this.getList();
  },
  mixins: [http]
};
</script>
