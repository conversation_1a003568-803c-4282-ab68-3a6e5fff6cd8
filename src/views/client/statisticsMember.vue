<template>
  <div>
    <div class="m-b-20 ovf-hd">
      <div class="fl ">
        <el-date-picker
          v-model="rangeArray"
          type="daterange"
          @change="changeFn"
          placeholder="选择日期"> 
          </el-date-picker>
        <ChannelList v-model="postData.channel_id"></ChannelList>
        <el-button type="primary" icon="search"  @click="search">查询</el-button>
      </div>
    </div>
    <el-table :data="tableData" :summary-method="getSummaries" show-summary stripe>
      <el-table-column align="center" prop="stat_day" label="日期"></el-table-column>
      <el-table-column align="center" label="区域" prop="channel_name">
        <template scope="scope">
          {{scope.row.channel_id == 0 ? '全部区域' : scope.row.channel_name}}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="user_register_num" label="注册用户数"></el-table-column>
      <el-table-column align="center" prop="get_card_num" label="领卡用户数"></el-table-column>
      <el-table-column align="center" prop="active_card_num" label="激活用户数"></el-table-column>
      <el-table-column align="center" prop="active_today_card_num" label="当日领卡激活数"></el-table-column>
      
    </el-table>
    <div class="pos-rel p-t-20">
      <div class="block pages">
        <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import http from 'assets/js/http';
import ChannelList from 'src/components/form/channelList';
export default {
  name: 'statisticsMember',
  components: { ChannelList },
  data() {
    return {
      tableData: [],
      rangeArray: [new Date().setDate(1), new Date()],
      tableTotal: [],
      postData: {
        channel_id: JSON.parse(localStorage.userInfo).data.channel_id,
        start_time: '', // （开始时间）
        end_time: '', // (结束时间)
        page_no: 1, // 页码
        pgae_size: '' // 每页条数
      },
      dataCount: 0
    };
  },
  methods: {
    getCardList() {
      this.apiPost('customer/statistics/users', this.postData).then(res => {
        if (res.errorcode == 0) {
          this.dataCount = res.data.count;
          this.tableData = res.data.list;
          this.tableTotal = res.data.total;
        } else {
          _g.toastMsg('success', res.errormsg);
        }
      });
    },
    getSummaries() {
      if (this.tableTotal == '') {
        return this.tableTotal;
      } else {
        let array = Object.values(this.tableTotal[0]);
        let newArray = array.splice(0, 0, '合计', ' - ');
        return array;
      }
    },
    changeFn(value) {
      let sTime = value[0];
      let eTime = value[1];
      this.postData.start_time = _g.formatDate(sTime, 'yyyy-MM-dd');
      this.postData.end_time = _g.formatDate(eTime, 'yyyy-MM-dd');
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.getCardList();
    },
    search() {
      this.postData.page_no = 1;
      this.getCardList();
    }
  },
  created() {
    const date = new Date()
    date.setDate(1)
    this.postData.start_time = _g.formatDate(date, 'yyyy-MM-dd')
    this.postData.end_time = _g.formatDate(new Date(), 'yyyy-MM-dd')
    this.getCardList();
  },
  mixins: [http]
};
</script>
