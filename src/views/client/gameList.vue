<template>
  <div class="container twoTabs">
    <header>
      <router-link class="btn-link-large add-btn"
                    :to="{name:'gameAdd'}">
        <i class="el-icon-plus"></i>&nbsp;&nbsp;发布赛事
      </router-link>
      <el-input placeholder="赛事名称" @keyup.enter.native="search" v-model="postData.match_name" class="w-150 m-r-10"></el-input>
      <el-select v-if="auth=='0'" @change="search" clearable v-model="postData.channel_id" class="w-150 m-r-10" filterable>
        <el-option label="全部区域" value="0"></el-option>
        <el-option label="沙坪坝" value="3335"></el-option>
      </el-select>
      <el-button type="primary" icon="search" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column type="index" label="编号" width="80" align="center"></el-table-column>
      <el-table-column prop="channel_name" label="区域" width="90" align="center"></el-table-column>
      <el-table-column prop="match_name" label="赛事名称" width="90" align="center"></el-table-column>
      <el-table-column prop="match_address" label="赛事地点" width="110" align="center"></el-table-column>
      <el-table-column prop="match_time" label="赛事时间" width="145" align="center"></el-table-column>
      <el-table-column prop="channel_name" label="报名时间" width="145" align="center">
        <template scope="scope">
          <span>{{scope.row.begin_time}}~{{scope.row.end_time}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="match_photo" label="封面预览图" width="110" align="center">
        <template scope="scope">
          <div class="table-zoom-image">
            <img :src="scope.row.match_photo" alt="">
          </div>
        </template>
      </el-table-column>
      <el-table-column prop="sign_people" label="报名人数" width="90" align="center">
        <template scope="scope">
          <router-link :to="{name:'memberList', params: {match_id: scope.row.id}}">
            <el-button size="medium" class="qricon" type="text">{{scope.row.sign_people}}</el-button>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="更多操作" align="center">
        <template scope="scope">
          <el-dropdown>
            <el-button type="primary" size="small">
              更多操作
              <i class="el-icon-arrow-down el-icon--right"></i>
            </el-button>
            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item command="a" @click.native="showDetail(scope.row)">查看</el-dropdown-item>
              <el-dropdown-item command="b" @click.native="showEdit(scope.row)">编辑</el-dropdown-item>
              <el-dropdown-item command="c" @click.native="showDelete(scope.row)">删除</el-dropdown-item>
            </el-dropdown-menu>
          </el-dropdown>
        </template>
      </el-table-column>
    </el-table>

    <div class="pos-rel p-t-20 ovf-hd flexend" v-if="dataCount>0">
      <div class="block">
        <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
        </el-pagination>
      </div>
    </div>

  </div>
</template>

<script>
  import http from 'assets/js/http';
  import getAppId from 'assets/js/wxplat';
  export default {
    name: 'gameList',
    data() {
      return {
        dataCount: '',
        auth: '',
        postData: {
          match_name: '',
          channel_id: '',
          page_size: 10,
          page_no: 1
        },
        tableData: []
      };
    },
    methods: {
      showDelete(row) {
        this.$confirm('确认删除该赛事?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.apiPost('/customer/match/deleteMatch', { match_id: row.id }).then(res => {
            if (res.errorcode == 0) {
              _g.toastMsg('success',res.errormsg);
              this.getList();
            } else {
              _g.toastMsg('warning', res.errormsg);
            }
          });
        })
      },
      showEdit(row) {
        this.$router.push({path: 'gameAdd', query: {match_id: row.id, action: 'edit'}});
      },
      showDetail(row) {
        this.$router.push({path: 'gameAdd', query: {match_id: row.id, action: 'detail'}});
      },
      getList() {
        this.apiPost('/customer/match/getMatchList', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.tableData = res.data.list;
            this.dataCount = res.data.count;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage
        this.getList()
      },
      search() {
        this.postData.page_no = 1
        this.getList()
      }
    },

    created() {
      this.auth = String(JSON.parse(localStorage.userInfo).data.channel_id);
      this.postData.channel_id = this.auth;
      this.getList();
    },
    mixins: [http, getAppId]
  };
</script>

<style lang="less" scoped>
  .table-zoom-image {
    height: 50px;
    > img {
      height: 100%;
    }
  }
  .detailine {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    margin-top: 8px;
    margin-bottom: 8px;
  }
  .detaillabel {
    width: 90px;
    text-align: right;
  }
  .detailcontent {
    margin-left: 25px;
    width: 493px;
  }
  .detailpic {
    border: none;
    width: auto;
    height: 80px;
  }
  .detail-card-boxes {
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
  }
  .detail-card-box {
    margin-bottom: 10px;
    background: #ccc;
    display: flex;
    flex-direction: row;
    justify-content: center;
    flex-wrap: wrap;
    align-items: center;
    .groupitem-odd {
      width: 70%;
      margin-bottom: 10px;
      margin-top: 10px;
    }
    .groupitem-even {
      width: 30%;
      margin-bottom: 10px;
      margin-top: 10px;
    }
  } 
  
</style>








