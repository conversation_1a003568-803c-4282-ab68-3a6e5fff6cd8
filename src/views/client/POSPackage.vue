<template>
  <div class="m-b-20 ovf-hd">
    <header class="fl">
      <el-button @click="showEdit = true">编辑</el-button>
    </header>  
    <el-dialog :visible.sync="showEdit" title="编辑" :close-on-click-modal="false">
      <el-form ref="form" :model="postData">
        <el-form-item prop="url" label="更新包地址" label-width="110px" :rules="{required: true, message: '请输入更新包地址'}">
          <el-input v-model="postData.url"></el-input>
        </el-form-item>
        <el-form-item 
          prop="ver" 
          label="版本" 
          label-width="110px" 
          :rules="[{required: true, message: '请输入版本号'},{type: 'number', message: '请输入数字'}]">
          <el-input v-model.number="postData.ver"></el-input>
        </el-form-item>
        <el-form-item prop="type" label="范围" label-width="110px" :rules="{required: true, message: '请选择范围'}">
          <el-select v-model="postData.type">
            <el-option v-for="item in rangeSelect" :key="item.id" :value="item.id" :label="item.name"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item 
          prop="range" 
          label="场馆或商家ID" 
          label-width="110px" 
          v-if="postData.type == 1 || postData.type == 2" 
          :rules="[{required: true, message: '请输入场馆或商家ID'},
          {pattern: /^\d+(,\d+)*$/, message: '请正确输入场馆或商家ID，以英文逗号分隔，结尾不加逗号'}]">
          <el-input type="textarea" v-model="postData.range" placeholder="请输入场馆或商家ID，用英文逗号隔开"></el-input>
        </el-form-item>
        <el-form-item style="text-align:center;">
          <el-button :loading="isLoading" type="success" @click="handleSubmit">确认绑定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: "POSPackage",
  data() {
    return {
      postData: {
        url: '',
        ver: '',
        type: '',
        range: '',
      },
      rangeSelect: [
        { id: 1, name: "按场馆" }, { id: 2, name: "按商家" }, { id: 3, name: "全部" }
      ],
      showEdit: false,
      isLoading: false,
    }
  },
  methods: {
    async handleSubmit() {
      let checkForm = await this.$refs.form.validate();
      this.isLoading = checkForm;
      if (!checkForm) return;
      const url = '/web/TLPos/save_posapp_updateinfo';
      return this.$service.get(url, {
        params: { ...this.postData }
      }).then(res => {
        if (res.status === 200) {
          if (res.data.errorcode == 0) {
            this.$message.success(res.data.errormsg);
            this.clearPostData();
            this.showEdit = false;
          } else {
            this.$message.error(res.data.errormsg);
          }
        } else {
          console.error("服务器扑街！");
        }
        this.isLoading = false;
      }).catch(err => {
        console.error(err);
      })
    },
    clearPostData() {
      let newObj = Object.create(null);
      let obj = { ...this.postData };
      for (let item in obj) {
        if (obj[item] != null && obj[item] instanceof Array) {
          newObj[item] = [];
        } else {
          newObj[item] = '';
        }
      }
      this.postData = { ...newObj };
    },
  }
}
</script>

<style>

</style>
