<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="150px">
      <el-form-item label="标签类型" :rules="{ required: true, message: '请选择标签类型'}">
        <el-select v-model="postData.tag_type" class="w-320">
          <el-option label="特色" value="1"></el-option>
          <el-option label="品牌" value="2"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="标签名称" prop="tag_name" :rules="{ required: true, type: 'string', max:20, message: '必填项，最多20个字符'}">
        <el-input class="w-320" placeholder="最多20个字符" v-model="postData.tag_name"></el-input>
      </el-form-item>
      <el-form-item label="所在区域" prop="city_id" :rules="{ required: true, message: '请选择市'}" v-if="postData.tag_type==2">
        <el-select v-model="postData.province_id" @change="provinceChange" style="width: 156px;">
          <el-option label="省" value="">省</el-option>
          <el-option v-for="item in provinceList" :key="item.region_id" :label="item.region_name" :value="item.region_id"></el-option>
        </el-select>
        <el-select v-model="postData.city_id" style="width: 156px;">
          <el-option label="市" value="">市</el-option>
          <el-option v-for="item in cityList" :key="item.region_id" :label="item.region_name" :value="item.region_id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="启用">
        <el-switch v-model="postData.status" active-color="#13ce66" inactive-color="#ff4949" active-value="1" inactive-value="0"></el-switch>
      </el-form-item>
      <el-form-item label="显示顺序" prop="sort_num" :rules="{required: true,type: 'string', pattern: /^[1-9]\d*$/, message: '必填项，顺序号应正整数' }">
        <el-input class="w-320" placeholder="最多20个字符" v-model="postData.sort_num"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">保存</el-button>
        <el-button class="m-l-150" @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import http from 'assets/js/http';
export default {
  name: 'tagAdd',
  data() {
    return {
      postData:{
        tag_type: '1',
        tag_name: '',
        province_id: '',
        city_id:'',
        status: '1',
        sort_num: '1',
        action: 'add',
        tag_id: ''
      },
      provinceList: [],
      cityList: []
    }
  },
  methods: {
    onSubmit() {
      if(this.postData.tag_id==0){
        this.postData.tag_id = '';
      }
      this.apiPost('/customer/tags/add_bf_tag', this.postData).then((res) => {
        if(res.errorcode ==0 ){
          _g.toastMsg('success',res.errormsg);
          this.$router.back();
        }else{
          _g.toastMsg('warning',res.errormsg);
        }
      })
    },
    provinceChange(id) {
      this.postData.city_id = '';
      if(id){
        this.getcityList(id);
      }else{
        this.postData.city_id = '';
      }
    },
    getcityList(id) {
      let _this = this;
      this.$service.post('https://wx.rocketbird.cn/Web/Business/get_region', {province_id: id}).then((res)=>{
        if(res.data.errorcode==0){
          _this.cityList = res.data.data;
        }else{
          _g.toastMsg('warning',res.data.errormsg);
        }
      }).catch((error)=> {
        console.log(error)
      })
    },
    getprovinceList() {
      let _this = this;
      this.$service.post('https://wx.rocketbird.cn/Web/Business/get_region', {}).then((res) => {
        if(res.data.errorcode ==0 ){
          _this.provinceList = res.data.data;
        }else{
          _g.toastMsg('warning',res.data.errormsg);
        }
      }).catch((error)=> {
        console.log(error)
      })
    },
    getpageInfo() {
      let _this = this;
      this.apiPost('/customer/tags/bf_tag_info', {tag_id: _this.postData.tag_id}).then((res) => {
        if(res.errorcode ==0 ){
          _this.postData.province_id = String(res.data.tag_info.province_id);
          if(_this.postData.province_id){
            _this.getcityList(_this.postData.province_id);
          }
          _this.postData.city_id = String(res.data.tag_info.city_id);
          _this.postData.tag_name = res.data.tag_info.tag_name;
          _this.postData.tag_type = String(res.data.tag_info.tag_type);
          _this.postData.status = String(res.data.tag_info.status);
          _this.postData.sort_num = String(res.data.tag_info.sort_num);
        }else{
          _g.toastMsg('warning',res.errormsg);
        }
      }).catch((error)=> {
        console.log(error)
      })
    },
  },
  created() {
    this.getprovinceList();
    this.postData.tag_id = this.$route.params.tag_id;
    if(this.postData.tag_id){
      this.postData.action = 'edit';
      this.getpageInfo();
    }else{
      this.postData.action = 'add';
    }
  },
  mixins: [http]
}
</script>

<style scoped>
</style>
