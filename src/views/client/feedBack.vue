<template>
  <div class="container">
    <header>
      <div class="fl m-l-30">
        <el-input @keyup.enter.native="search" placeholder="手机号" v-model="postData.phone" class="w-150 m-r-10"></el-input>
        <ChannelList v-model="postData.channel_id"></ChannelList>
        <el-button type="success" icon="search" @click="search">搜索</el-button>
      </div>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column prop="date" label="日期" align="center"></el-table-column>
      <el-table-column align="center" label="区域" prop="channel_name">
        <template scope="scope">
          {{scope.row.channel_id == 0 ? '全部区域' : scope.row.channel_name}}
        </template>
      </el-table-column>
      <el-table-column prop="nick_name" label="会员昵称" align="center"></el-table-column>
      <el-table-column prop="phone" label="会员手机号" align="center">
        <template scope="scope">
        <el-button type="text" size="small" @click="seeDetail(scope.$index)">{{scope.row.phone}}</el-button>
      </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="describe" label="反馈意见" align="center"></el-table-column>
    </el-table>
    <div class="pos-rel p-t-20 ovf-hd flexend">
      <div class="block">
        <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount">
        </el-pagination>
      </div>
    </div>
    <el-dialog width="40%" title="赠送健身卡" :visible.sync="seedetailshow">
      <el-form :model="tableData[showindex]" v-if="tableData[showindex]">
        <div class="boxline-1">
          <div>用户昵称：{{tableData[showindex].nick_name}}</div>
          <div>手机号：{{tableData[showindex].phone}}</div>
          <div>反馈时间：{{tableData[showindex].date}}</div>
        </div>
        <div class="boxline-2">{{tableData[showindex].describe}}</div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="seedetailshow = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import http from 'assets/js/http';
import ChannelList from 'src/components/form/channelList';
export default {
  name: 'feedBack',
  components: { ChannelList },
  data() {
    return {
      postData: {
        channel_id: JSON.parse(localStorage.userInfo).data.channel_id,
        phone: '',
        page_size: 10,
        page_no: 1
      },
      currentPage: 1,
      dataCount: 0,
      tableData: [],
      seedetailshow: false,
      showindex: 0
    };
  },
  methods: {
    seeDetail(index) {
      this.seedetailshow = true;
      this.showindex = index;
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.getfbList();
    },
    search() {
      this.postData.page_no = 1;
      this.getfbList();
    },
    getfbList() {
      let _this = this;
      this.apiPost('/customer/users/suggest', this.postData).then(res => {
        let _this = this;
        if (res.errorcode == 0) {
          _this.tableData = res.data.suggestion;
          _this.dataCount = res.data.count;
        } else {
          _g.toastMsg('warning', res.errormsg);
        }
      });
    }
  },
  created() {
    this.getfbList();
  },
  mixins: [http]
};
</script>
<style scoped>
.editfont {
  color: #fff;
  font-size: 14px;
}
.linkfont {
  color: #20a0ff;
  font-size: 14px;
}
.boxline-2 {
  padding: 20px 0;
}
</style>
