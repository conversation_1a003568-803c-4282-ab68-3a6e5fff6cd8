<template>
  <div class="m-b-20 ovf-hd">
    <header class="fl">
      <el-form ref="form" :model="postData" inline>
        <el-form-item>
          <el-input placeholder="请输入名称" v-model="postData.pay_type_name"></el-input>
        </el-form-item>
        <el-form-item>
          <el-select placeholder="请选择使用类型" v-model="postData.scene" clearable>
            <el-option label="收款" value="1">收款</el-option>
            <el-option label="退款" value="2">退款</el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-select placeholder="请选择状态" v-model="postData.disable" clearable>
            <el-option label="启用" value="0">启用</el-option>
            <el-option label="禁用" value="1">禁用</el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="getPayTypes">查询</el-button>
          <el-button type="primary" @click="addPay">添加</el-button>
        </el-form-item>
      </el-form>
    </header>
    <el-table :data="tableData" stripe :default-sort="{prop: 'sort', order: 'descending'}">
      <el-table-column align="center" prop="pay_type_name" label="收退款方式名称"></el-table-column>
      <el-table-column align="center" prop="pay_type_id" label="ID"></el-table-column>
      <el-table-column align="center" prop="scene" label="使用类型"></el-table-column>
      <el-table-column align="center" prop="disable" label="状态"></el-table-column>
      <el-table-column align="center" prop="remark" label="备注"></el-table-column>
      <el-table-column align="center" prop="sort" label="排序权重"></el-table-column>
      <el-table-column align="center" prop="op" label="操作">
        <template scope="scope">
          <el-button :loading="editLoading" type="text" @click="onEdit(scope.$index, scope.row)">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pos-rel p-t-20 ovf-hd flexend">
      <div class="block">
        <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="pageData.page_size"
        :current-page="pageData.page_no"
        :total="dataCount">
        </el-pagination>
      </div>
    </div>
    <el-dialog title="添加" :visible.sync="showAdd" :close-on-click-modal="false">
      <el-form :model="addForm" ref="addForm" label-width="120px">
        <el-form-item label="收退款方式名称" prop="pay_type_name" :rules="{required: true, message: '请输入名称'}">
          <el-input placeholder="请输入" v-model="addForm.pay_type_name"></el-input>
        </el-form-item>
        <el-form-item label="支付方式ID" prop="pay_type_id" :rules="[{required: true, message: '请输入支付方式数字ID'}, 
        {type: 'number', message: 'ID必须是数字'}]">
          <el-input placeholder="请输入该方式的数字ID" v-model.number="addForm.pay_type_id"></el-input>
        </el-form-item>
        <el-form-item label="使用类型" prop="scene" :rules="{ required: true, message: '请选择使用类型'}">
          <el-checkbox-group v-model="addForm.scene">
            <el-checkbox label="1">收款</el-checkbox>
            <el-checkbox label="2">退款</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="状态" prop="disable" :rules="{required: true, message: '请选择默认状态'}">
          <el-radio v-model="addForm.disable" label="1">禁用</el-radio>
          <el-radio v-model="addForm.disable" label="0">启用</el-radio>
        </el-form-item>
        <el-form-item label="排序权重" prop="sort" :rules="[{required: true, message: '请输入默认排序权重'}, 
        {type: 'number', message: '排序权重必须是数字0-100'}, {pattern: /^(\d|[1-9]\d|100)$/, message: '排序权重必须是数字0-100'}]">
          <el-input placeholder="请输入权重" v-model.number="addForm.sort"></el-input>
        </el-form-item>
        <el-form-item style="text-align:center;">
          <el-button type="success" style="transform:translateX(-60px);" @click="onSave">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
    <el-dialog title="编辑" :visible.sync="showEdit" :close-on-click-modal="false">
      <el-form :model="editForm" ref="editForm" label-width="120px">
        <el-form-item label="收退款方式名称" prop="pay_type_name">
          <el-input disabled placeholder="请输入" v-model="editForm.pay_type_name"></el-input>
        </el-form-item>
        <el-form-item label="使用类型" prop="scene" :rules="{ required: true, message: '请选择使用类型'}">
          <el-checkbox-group v-model="editForm.scene">
            <el-checkbox disabled true-label="收款" label="收款">收款</el-checkbox>
            <el-checkbox disabled true-label="退款" label="退款">退款</el-checkbox>
          </el-checkbox-group>
        </el-form-item>
        <el-form-item label="状态" prop="disable" :rules="{required: true, message: '请选择默认状态'}">
          <el-radio v-model="editForm.disable" true-label="0" label="禁用">禁用</el-radio>
          <el-radio v-model="editForm.disable" true-label="1" label="启用">启用</el-radio>
        </el-form-item>
        <el-form-item label="排序权重" prop="sort" :rules="[{required: true, message: '请输入默认排序权重'}, 
        {type: 'number', message: '排序权重必须是数字0-100'}, {pattern: /^(\d|[1-9]\d|100)$/, message: '排序权重必须是数字0-100'}]">
          <el-input placeholder="请输入权重" v-model.number="editForm.sort"></el-input>
        </el-form-item>
        <el-form-item style="text-align:center;">
          <el-button style="transform:translateX(-60px);" type="success" @click="onEditSave">保存</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'payment',
  data() {
    return {
      showAdd: false,
      showEdit: false,
      editLoading: false,
      editForm: {},
      addForm: {
        scene: [],
        pay_type_name: '',
        disable: '',
        sort: '',
        pay_type_id: ''
      },
      postData: {
        pay_type_name: '',
        scene: '',
        disable: ''
      },
      tableData: [],
      dataCount: 0,
      pageData: {
        page_size: 10,
        page_no: 1,
      }
    }
  },
  created() {
    this.getPayTypes();
  },
  methods: {
    addPay() {
      this.addForm = this.clearObj(this.addForm);
      this.showAdd = true;
    },
    onEdit(index, params) {
      this.editForm = { ...params };
      this.showEdit = true; 
    },
    async onEditSave() {
      let checkForm = await this.$refs.editForm.validate();
      if (!checkForm) return;
      this.editLoading = true;
      let postdata = Object.create(null);
      const url = '/web/TLPos/sys_paytype_edit';
      postdata.id = this.editForm.id;
      postdata.sort = this.editForm.sort;
      postdata.disable = this.editForm.disable === '禁用' ? 1 : 0;
      let data = this.urlUtil(postdata);
      await this.$service.post(url, data, {
        headers: {'Content-Type': 'application/x-www-form-urlencoded'}
      }).then(res => {
        if (res.data.errorcode == 0) {
          return this.$message.success(res.data.errormsg);
        } else {
          return this.$message.error(res.data.errormsg);
        }
      });
      await this.getPayTypes();
      this.editLoading = false;
      this.showEdit = false;
    },
    onSave() {
      this.$refs.addForm.validate(valid => {
        if (!valid) return;
        this.addForm.pay_type_id = parseInt(this.addForm.pay_type_id);
        this.disable = parseInt(this.addForm.disable);
        this.sort = parseInt(this.sort);
        let postdata = this.addForm;
        postdata.scene = this.addForm.scene.length == 2 ? ['1', '1'] : this.addForm.scene.length == 1 &&
         this.addForm.scene[0] == '1' ? ['1', '0'] : ['0', '1'];
        const url = '/web/TLPos/sys_paytype_add';
        let data = this.urlUtil(postdata);
        this.$service.post(url, data, {
          headers: {'Content-Type': 'application/x-www-form-urlencoded'}
        }).then(async res => {
          if (res.data.errorcode == 0) {
            this.$message.success(res.data.errormsg);
            this.showAdd = false;
            this.addForm = {
              scene: [],
              pay_type_name: '',
              disable: '',
              sort: '',
              pay_type_id: ''
            }
            await this.getPayTypes();
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
      })
      
    },
    getPayTypes() {
      let postdata = this.urlUtil(this.postData);
      return this.$service.post('/web/TLPos/get_paytype_list', postdata, {
        headers: {'Content-Type': 'application/x-www-form-urlencoded'}
      }).then(res => {
        if (res.data.errorcode == 0) {
          let totalData = res.data.data;
          this.dataCount = res.data.data.length;
          totalData.map(item => {
            item.scene = item.scene == 1 ? '收款' : '退款';
            item.disable = item.disable == 1 ? '禁用' : '启用';
            item.sort = Number(item.sort);
            item.remark = item.remark.length === 0 && '无';
          })
          this.fakePager(totalData);
        } else {
          this.$message.error(res.data.errormsg);
        }
      }).catch(error => {
        console.error(error);
      })
    },
    handleCurrentChange(val) {
      this.pageData.page_no = val;
      this.getPayTypes();
    },
    fakePager(totalData) {
      if (!Array.isArray(totalData)) return;
      if (totalData.length <= 10) {
        this.tableData = totalData;
        return;
      };
      this.tableData = totalData.slice((this.pageData.page_no - 1) * 10, this.pageData.page_no * 10);
    },
    clearObj(obj) {
      let newObj = Object.create(null);
      for (let item in obj) {
        if (obj[item] != null && obj[item] instanceof Array) {
          newObj[item] = [];
        } else {
          newObj[item] = '';
        }
      }
      return newObj;
    },
    urlUtil(postData) {
      let postdata = new URLSearchParams();
      for (let item in postData) {
        if (postData[item] instanceof Array) {
          for (let i = 0; i < postData[item].length; i++) {
            postdata.append(String(item) + `[${i}]`, postData[item][i]);
          }
        } else {
          postdata.append(item, postData[item]);
        }
      }
      return postdata;
    },
  }
}
</script>
