<template>
<div>
  <div class="m-b-20 ovf-hd">
    <div class="fl m-l-30">
      <el-date-picker v-model="rangeArray" class="m-r-10" type="daterange" @change="changeDate" placeholder="选择日期">
      </el-date-picker>
      <el-input @keyup.enter.native="search" placeholder="手机号" v-model="postData.phone" class="w-150 m-r-10"></el-input>
      <el-button type="primary" icon="search" @click="search">查询</el-button>
    </div>
  </div>
  <el-table :data="tableData" stripe style="width: 100%">
    <el-table-column prop="apply_time" label="日期" width="150" align="center"></el-table-column>
    <el-table-column prop="apply_name" label="申请人姓名" width="150" align="center"></el-table-column>
    <el-table-column prop="apply_tel" label="申请手机号" width="150" align="center">
        <template scope="scope">
        <el-button type="text" size="small" @click="seeDetail(scope.$index)">{{scope.row.apply_tel}}</el-button>
      </template>
      </el-table-column>
    <el-table-column prop="bus_name" label="场馆名称" width="150" align="center"></el-table-column>
    <el-table-column prop="address" label="地址" width="200" align="center"></el-table-column>
    <el-table-column prop="remark" label="备注" align="center" :formatter="formatter"></el-table-column>
  </el-table>
  <div class="pos-rel p-t-20 ovf-hd flexend" v-if="dataCount>0">
    <div class="block">
      <el-pagination
      @current-change="handleCurrentChange"
      layout="prev, pager, next"
      :page-size="postData.page_size"
      :current-page="postData.page_no"
      :total="dataCount">
      </el-pagination>
    </div>
  </div>
  <el-dialog width="40%" title="申请详情" :visible.sync="seedetailshow">
    <el-form :model="tableData[showindex]" v-if="tableData[showindex]">
      <div class="boxline-1">
        <div>用户昵称：{{tableData[showindex].apply_name}}</div>
        <div>手机号：{{tableData[showindex].apply_tel}}</div>
        <div>场馆名称：{{tableData[showindex].bus_name}}</div>
        <div>地址：{{tableData[showindex].address}}</div>
      </div>
      <div class="boxline-2">{{tableData[showindex].remark}}</div>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" @click="seedetailshow = false">关 闭</el-button>
    </div>
  </el-dialog>
</div>
</template>

<script>
import http from 'assets/js/http';
  export default {
    name: 'applicationControl',
    data() {
      return {
        rangeArray: [this.getDate(), this.getDate()],
        postData: {
          start_time: this.getDate(), //（开始时间）
          end_time: this.getDate(),
          phone: '',
          page_size: 10,
          page_no: 1
        },
        currentPage: 1,
        dataCount: 0,
        tableData: [],
        seedetailshow: false,
        showindex: 0
      }
    },
    methods: {
      formatter(row,column) {
        if(row.remark.length>50){
          return row.remark.substr(0,50) + '...';
        }else {
          return row.remark;
        }
      },
      getDate() {
        let d = new Date()
        return `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()}`
      },
      changeDate(value) {
        let sTime = value[0];
        let eTime = value[1];
        this.postData.start_time = _g.formatDate(sTime,'yyyy-MM-dd')
        this.postData.end_time = _g.formatDate(eTime,'yyyy-MM-dd')
      },
      seeDetail(index) {
        this.seedetailshow = true;
        this.showindex = index;
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.getappliList();
      },
      search() {
        this.postData.page_no = 1;
        this.getappliList();
      },
      getappliList() {
        let _this = this;
        this.apiPost('/customer/business/apply_list', this.postData).then((res) => {
          let _this = this;
          if(res.errorcode ==0 ){
            _this.tableData = res.data.list;
            _this.dataCount = res.data.count;
          }else{
            _g.toastMsg('warning',res.errormsg);
          }
        })
      }
    },
    created() {
      this.getappliList()
    },
    mixins: [http]

  }
</script>

<style scoped>
.editfont {
  color: #fff;
  font-size: 14px;
}
.linkfont {
  color: #20a0ff;
  font-size: 14px;
}
.boxline-2 {
  padding: 20px 0;
}
</style>








