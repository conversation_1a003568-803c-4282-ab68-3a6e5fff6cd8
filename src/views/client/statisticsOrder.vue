<template>
<div>
  <div class="m-b-20 ovf-hd">
    <div class="fl">
      <el-date-picker v-model="rangeArray" class="m-r-10" type="daterange" @change="changeDate" placeholder="选择日期">
      </el-date-picker>
      <el-input placeholder="场馆名称" v-model="postData.bus_name" class="w-150 m-r-10"></el-input>
      <el-select v-model="postData.card_id" placeholder="健身卡名称" class="w-150 m-r-10" filterable clearable>
        <el-option v-for="item in tableCard" :key="item.card_id" :value="item.card_id" :label="item.card_name"></el-option>
      </el-select>
      <el-input placeholder="用户手机号" v-model="postData.phone" class="w-150 m-r-10"></el-input>
      <el-select v-model="postData.status" placeholder="状态" class="w-150 m-r-10">
        <el-option label="状态" value=""></el-option>
        <el-option label="已激活" value="1"></el-option>
        <el-option label="未激活" value="0"></el-option>
      </el-select>
      <el-button type="primary" icon="search" @click="search">查询</el-button>
    </div>
  </div>
  <el-table :data="tableData" stripe>
    <el-table-column align="center" prop="create_time" label="日期"></el-table-column>
    <el-table-column align="center" prop="phone" label="手机号"></el-table-column>
    <el-table-column align="center" prop="success_time" label="支付时间"></el-table-column>
    <el-table-column align="center" prop="money" label="支付金额"></el-table-column>
    <el-table-column align="center" prop="card_name" label="卡名称"></el-table-column>
    <el-table-column align="center" prop="status" label="状态">
      <template scope="scope">
        <span>{{ scope.row.status == 0 ? '未激活' : '已激活' }}</span>
      </template>
    </el-table-column>
    <el-table-column align="center" prop="bus_name" label="激活场馆"></el-table-column>
  </el-table>
  <div class="pos-rel p-t-20">
    <div class="block pages">
      <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
      </el-pagination>
    </div>
  </div>
  <div class="total-wrap">
    合计金额：{{totalMoney}}元 (<span>查询结果集的支付金额</span>)
  </div>
  <el-button style='margin-top:20px;' type="primary" icon="document" @click="handleDownload">导出excel</el-button>
</div>
</template>

<script>
import http from 'assets/js/http'
export default {
  name: 'statisticsOrder',
  data() {
    return {
      rangeArray: [this.getDate(), this.getDate()],
      tableCard: '',
      totalMoney: '',
      tableData: [],
      tableTotal: [],
      postData: {
        start_time: this.getDate(), //（开始时间）
        end_time: this.getDate(), // (结束时间)
        card_id: '', //c端卡id
        status: '', //1,激活，0未激活
        phone: '',
        bus_name: '',
        page_no: 1, //页码
        pgae_size: 10, //每页条数
      },
      dataCount: 0,
    }
  },
  created() {
    this.getCardType()
    this.getOrderList()
  },
  methods: {
    getDate() {
      let d = new Date()
      return `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()}`
    },
    formatJson(filterVal, jsonData) {
      return jsonData.map(v => filterVal.map(j => {
        let val = v[j];
        if (j == 'status') {
          val = v[j] == 0 ? '未激活' : '已激活'
        }
        return val
      }))
    },
    handleDownload() {
      require.ensure([], () => {
        const {
          export_json_to_excel
        } = require('./../../vendor/Export2Excel');
        const tHeader = ['日期', '手机号', '支付时间', '支付金额', '卡名称', '状态', '支付场馆'];
        const filterVal = ['create_time', 'phone', 'success_time', 'money', 'card_name', 'status', 'bus_name'];
        const list = this.tableData;
        const data = this.formatJson(filterVal, list);
        export_json_to_excel(tHeader, data, '订单统计');
      })
    },
    getOrderList() {
      this.apiPost('customer/statistics/orders', this.postData).then((res) => {
        if (res.errorcode == 0) {
          this.dataCount = res.data.count;
          this.tableData = res.data.list;
          this.totalMoney = res.data.total_money;
        } else {
          _g.toastMsg('success', res.errormsg)
        }
      })
    },
    getCardType() {
      this.apiPost('Customer/Card/get_card_type', this.postData).then((res) => {
        if (res.errorcode == 0) {
          this.tableCard = res.data.list;
        } else {
          _g.toastMsg('success', res.errormsg)
        }
      })
    },
    changeDate(value) {
      let sTime = value[0];
      let eTime = value[1];
      this.postData.start_time = _g.formatDate(sTime,'yyyy-MM-dd')
      this.postData.end_time = _g.formatDate(eTime,'yyyy-MM-dd')
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.getOrderList();
    },
    search() {
      this.postData.page_no = 1;
      this.getOrderList();
    }
  },
  mixins: [http]
}
</script>
<style media="screen">
.total-wrap {
  margin-top: 15px;
}

.total-wrap span {
  font-style: italic;
  font-size: 12px
}

.mt10 {
  margin-top: 10px;
}
</style>
