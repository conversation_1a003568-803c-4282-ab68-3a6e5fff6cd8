<template>
  <div class="m-b-20 ovf-hd">
    <header class="fl">
      <el-form ref="form" :model="postData" inline>
        <!-- <el-form-item>
          <el-input placeholder="商户号" v-model="postData.cusid"></el-input>
        </el-form-item> -->
        <el-form-item>
          <el-input placeholder="场馆ID" v-model="postData.bus_id"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="getposList">查询</el-button>
          <el-button type="primary" @click="openBind">添加绑定</el-button>
        </el-form-item>
      </el-form>
    </header>
    <el-table :data="tableData" stripe :empty-text="emptyText">
      <el-table-column align="center" prop="bus_id" label="场馆ID"></el-table-column>
      <el-table-column align="center" prop="pos_deviceName" label="pos唯一标识"></el-table-column>
      <el-table-column align="center" prop="name" label="设备名称"></el-table-column>
      <el-table-column align="center" prop="id" label="设备ID"></el-table-column>
      <el-table-column align="center" prop="bind_time" label="绑定时间"></el-table-column>
      <el-table-column align="center" label="操作">
        <template scope="scope">
          <el-button type="text" @click="onUnbind(scope.$index, scope.row)">解除绑定</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pos-rel p-t-20 ovf-hd flexend">
      <div class="block">
        <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount">
        </el-pagination>
      </div>
    </div>
    <el-dialog title="绑定pos到场馆" :visible.sync="showBind">
      <el-form :model="bindForm" ref="bindForm" label-width="140px">
        <el-form-item label="场馆ID" prop="bus_id" :rules="{required: true, message: '请输入场馆ID'}">
          <el-input @blur="getCusid(bindForm.bus_id)" placeholder="场馆ID" v-model="bindForm.bus_id"></el-input>
        </el-form-item>
        <el-form-item label="商户号" prop="cusid" :rules="{required: true, message: '请选择商户号'}">
          <el-select style="width:100%;" @change="getPos" v-model="bindForm.cusid" placeholder="请选择商户号">
            <el-option 
              v-for="item in paramsList"
              :key="item.cusid" 
              :label="item.cusid" 
              :value="item.cusid"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="pos识别码" prop="devicename" :rules="{required: true, message: '请输入pos机名称'}">
          <!-- <el-input placeholder="pos机名称" v-model="bindForm.name"></el-input> -->
          <el-select style="width:100%;" v-model="bindForm.devicename" placeholder="请选择">
            <el-option 
              v-for="item in posList" 
              :key="item.deviceName" 
              :label="item.deviceName" 
              :value="item.deviceName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="自定义pos机名称" prop="name" :rules="{required: true, message: '请设置pos名称'}">
          <el-input placeholder="请设置pos机名称" v-model="bindForm.name"></el-input>
        </el-form-item>
        <el-form-item style="text-align:center;">
          <el-button :loading="isLoading" type="success" style="transform:translateX(-60px);" @click="doBind">确认绑定</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { formatDate } from "../../utils";

export default {
  name: 'posRegister',
  data() {
    return {
      showBind: false,
      isLoading: false,
      bindForm: {
        bus_id: '',
        cusid: '',
        devicename: '',
        name: '',
      },
      paramsList: [],
      posList: [],
      dataCount: 0,
      postData: {
        page_no: 1, //页码
        page_size: 10,
        bus_id: '',
      },
      tableData: [],
    }
  },
  created() {
    
  },
  watch: {
    // 'bindForm.bus_id'(val) {
    //   if (val) {
    //     this.getPos(val);
    //   } else {
    //     this.posList = [];
    //   }
    // }
  },
  computed: {
    emptyText() {
      if (!!this.postData.bus_id) {
        return "该场馆暂未绑定pos机";
      } else {
        return "请先输入场馆ID，然后点击查询";
      }
    }
  },
  methods: {
    openBind() {
      this.bindForm = this.clearObj(this.bindForm);
      this.showBind = true;
    },
    getposList() {
      const url = '/web/TLPos/bus_pos_list';
      let postdata = this.urlUtil(this.postData)
      return this.$service.post(url, postdata, {
        headers: {'Content-Type': 'application/x-www-form-urlencoded'}
      }).then(res => {
        if (res.data.errorcode == 0) {
          let data = Array.from(res.data.list);
          data.map(item => {
            item.bind_time = formatDate(new Date(item.bind_time * 1000), 'yyyy-MM-dd HH:mm')
          })
          this.tableData = data;
        } else {
          this.$message.error(res.data.errormsg);
        }
      }).catch(e => {
        console.error(e);
      })
    },
    handleCurrentChange(val) {
      console.log(val);
    },
    onUnbind(index, row) {
      this.$confirm("解除绑定后，将无法使用这台pos机收款，确定要解除绑定？", "警告", {
        confirmButtonText: '解除绑定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        await this.doUnbind(row);
        await this.getposList();
      }).catch(action => {
        this.$message.error("取消操作");
      })
    },
    doUnbind(row) {
      const url = '/web/TLPos//bus_pos_del';
      let postdata = Object.create(null);
      postdata.devicename = row.pos_deviceName;
      postdata.bus_id = row.bus_id;
      let data = this.urlUtil(postdata);
      return this.$service.post(url, data, {
        headers: {'Content-Type': 'application/x-www-form-urlencoded'}
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.$message.success(res.data.errormsg);
        } else {
          this.$message.error(res.data.errormsg);
        }
      }).catch(e => {
        console.error(e);
      })
    },
    async doBind() {
      let checkForm = await this.$refs.bindForm.validate();
      this.isLoading = checkForm;
      if (!checkForm) return;
      const url = '/web/TLPos/bus_pos_add';
      let postdata = this.urlUtil(this.bindForm);
      return this.$service.post(url, postdata, {
        headers: {'Content-Type': 'application/x-www-form-urlencoded'}
      }).then(async res => {
        if (res.data.errorcode == 0) {
          this.$message.success(res.data.errormsg);
          await this.getposList();
        } else {
          this.$message.error(res.data.errormsg);
        }
        this.isLoading = this.showBind = false;
      }).catch(e => {
        console.error(e);
      })
    },
    getCusid(val) {
      const url = '/web/TLPos/pay_conf_list';
      let postdata = this.urlUtil({ bus_id: val });
      return this.$service.post(url, postdata, {
        headers: {'Content-Type': 'application/x-www-form-urlencoded'}
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.paramsList = res.data.data.list;
        } else {
          this.$message.error(res.data.errormsg);
        }
      }).catch(e => {
        this.$message.error("该场馆下没有可用的pos机")
      })
    },
    async getPos(cusid) {
      if (!cusid) return;
      const url = '/web/TLPos/get_registed_pos'
      let postdata = this.urlUtil({ cusid });
      return this.$service.post(url, postdata, {
        headers: {'Content-Type': 'application/x-www-form-urlencoded'}
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.posList = res.data.data.list;
          console.log(res.data.data)
        } else {
          this.$message.error(res.data.errormsg);
        }
      }).catch(e => {
        console.error(e);
      })
    },
    getParamsList(bus_id) {
      const url = '/web/TLPos/pay_conf_list';
      let params = new URLSearchParams();
      let postdata = {
        bus_id,
        page_no: 1
      }
      for (let item in postdata) {
        params.append(item, this.postData[item])
      }
      return this.$service.post(url, params, {
        headers: {'Content-Type': 'application/x-www-form-urlencoded'}
      }).then(res => {
        if (res.data.errorcode == 0) {
          return res.data.data.list;
        } else {
          this.$message.error(res.data.errormsg);
        }
      }).catch(e => {
        console.error(e);
      })
    },
    urlUtil(postData) {
      let postdata = new URLSearchParams();
      for (let item in postData) {
        if (postData[item] instanceof Array) {
          for (let i = 0; i < postData.length.length; i++) {
            postdata.append(String(item) + `[${i}]`, postData[item][i]);
          }
        } else {
          postdata.append(item, postData[item]);
        }
      }
      return postdata;
    },
    clearObj(obj) {
      let newObj = Object.create(null);
      for (let item in obj) {
        if (obj[item] != null && obj[item] instanceof Array) {
          newObj[item] = [];
        } else {
          newObj[item] = '';
        }
      }
      return newObj;
    },
  }
}
</script>