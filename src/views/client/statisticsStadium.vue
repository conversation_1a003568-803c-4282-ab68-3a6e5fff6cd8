<template>
  <div>
    <div class="m-b-20 ovf-hd">
      <div class="fl ">
        <el-date-picker
          v-model="rangeArray"
          type="daterange"
          @change="changeFn"
          placeholder="选择日期">
          </el-date-picker>
          <el-select
            v-model="postData.bus_name"
            placeholder="场馆名称"
            filterable
            clearable
            filterable style="width:200px">
            <el-option
              v-for="(item, index) in tableData"
              :key="index"
              :label="item.bus_name"
              :value="item.bus_name" ></el-option>
          </el-select>
        <ChannelList v-model="postData.channel_id"></ChannelList>
        <el-button type="primary" icon="search"  @click="search">查询</el-button>
      </div>
    </div>
    <el-table :data="tableData" :summary-method="getSummaries" show-summary stripe>
      <el-table-column align="center" prop="stat_day" label="日期"></el-table-column>
      <el-table-column align="center" label="区域" prop="channel_name">
        <template scope="scope">
          {{scope.row.channel_id == 0 ? '全部区域' : scope.row.channel_name}}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="bus_name" label="场馆名称"></el-table-column>
      <el-table-column align="center" prop="active_user_num" label="激活人次"></el-table-column>
      <el-table-column align="center" v-for="(item, index) in tableRows" :key="index" :prop='"row"+index' :label="item"></el-table-column>
      <el-table-column align="center" prop="buy_amount" label="购卡金额"></el-table-column>
    </el-table>
    <div class="pos-rel p-t-20">
      <div class="block pages">
        <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import http from 'assets/js/http';
import ChannelList from 'src/components/form/channelList';
export default {
  name: 'statisticsStadium',
  components: { ChannelList },
  data() {
    // 获取昨天的日期
    const today = new Date();
    today.setTime(today.getTime() - 24 * 60 * 60 * 1000);
    const yesterday = today.getFullYear() + '-' + (today.getMonth() + 1) + '-' + today.getDate();
    return {
      busStat: '',
      tableData: [],
      tableRows: [],
      rangeArray: [yesterday, yesterday],
      postData: {
        channel_id: JSON.parse(localStorage.userInfo).data.channel_id,
        start_time: yesterday, //（开始时间）
        end_time: yesterday, //(结束时间)
        bus_name: '', //场馆名
        page_no: 1, //页码
        pgae_size: '' //每页条数
      },
      dataCount: 0
    };
  },
  methods: {
    getCardList() {
      this.$service.post('customer/statistics/bus', this.postData).then(res => {
        if (res.data.errorcode === 0) {
          const data = res.data.data;
          this.busStat = data;
          this.dataCount = data.count;
          this.tableData = data.list;
          this.tableTotal = data.row;
          this.tableRows = data.cardRowCount;
        } else {
          this.$message.error(res.data.errormsg);
        }
      });
    },
    getSummaries({ columns, data }) {
      if (!this.busStat) return [];
      return ['合计', '-', '-', this.busStat.activeCount].concat(Object.values(this.tableTotal), [
        this.busStat.buyCount
      ]);

      // return columns.map((col, index) => {
      //   if (index === 0) return '合计';
      //   if ([1, 2].includes(index)) return '-';
      //   return data.reduce((total, item) => total + Number(item[col.property]), 0);
      // });
    },
    changeFn([sTime, eTime]) {
      this.postData.start_time = _g.formatDate(sTime, 'yyyy-MM-dd');
      this.postData.end_time = _g.formatDate(eTime, 'yyyy-MM-dd');
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.getCardList();
    },
    search() {
      this.postData.page_no = 1;
      this.getCardList();
    }
  },
  created() {
    this.getCardList();
  },
  mixins: [http]
};
</script>
