<template>
  <div  class="container">
    <header>
      <div class="fl m-l-30">
        <el-input @keyup.enter.native="search" placeholder="手机号" v-model="postData.phone" class="w-150 m-r-10"></el-input>
        <el-select @change="search" clearable v-model="postData.card_id" placeholder="健身卡种" class="w-150 m-r-10">
          <el-option v-for="item in cardsList" :label="item.card_name" :value="item.c_card_id"></el-option>
        </el-select>
        <ChannelList v-model="postData.channel_id"></ChannelList>
        <el-input @keyup.enter.native="search" placeholder="领卡数量" v-model="postData.get_card_num" class="w-150 m-r-10"></el-input>
        <el-input @keyup.enter.native="search" placeholder="激活数量" v-model="postData.active_num" class="w-150 m-r-10"></el-input>
        <el-button type="success" icon="search" @click="search">搜索</el-button>
      </div>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column type="index" label="编号" width="77" align="center"></el-table-column>
      <el-table-column align="center" label="区域" prop="channel_name">
        <template scope="scope">
          {{scope.row.channel_id == 0 ? '全部区域' : scope.row.channel_name}}
        </template>
      </el-table-column>
      <el-table-column prop="nick_name" label="会员昵称" align="center"></el-table-column>
      <el-table-column prop="phone" label="会员手机号" align="center">
        <template scope="scope">
        <el-button type="text" size="small">
          <router-link class='linkfont' :to="{name: 'memberEdit',params: {c_user_id: scope.row.c_user_id}}">{{scope.row.phone}}</router-link>
        </el-button>
      </template>
      </el-table-column>
      <el-table-column prop="get_card_num" label="健身卡数量" align="center"></el-table-column>
      <el-table-column prop="active_num" label="激活数量" align="center"></el-table-column>
      <el-table-column label="状态" align="center">
        <template scope="scope">
          <el-switch v-model="scope.row.status" @change="switchStatus(scope.row)" active-color="#13ce66" active-value="1" inactive-value="0"></el-switch>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template scope="scope">
          <router-link class='editfont' :to="{name: 'memberEdit',params: {c_user_id: scope.row.c_user_id}}">
            <el-button size="small" type="primary">编辑</el-button>
          </router-link>
      </template>
      </el-table-column>
    </el-table>
    <div class="pos-rel p-t-20 ovf-hd flexend">
      <div class="block">
        <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount">
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import http from 'assets/js/http';
import ChannelList from 'src/components/form/channelList';
export default {
  name: 'memberControl',
  components: { ChannelList },
  data() {
    return {
      postData: {
        channel_id: JSON.parse(localStorage.userInfo).data.channel_id,
        phone: '',
        card_type_id: '',
        get_card_num: '',
        active_num: '',
        card_id: '',
        page_size: 10,
        page_no: 1
      },
      tableData: [],
      cardsList: [],
      currentPage: 1,
      dataCount: 0
    };
  },
  methods: {
    switchStatus(row) {
      let _this = this;
      let finalstatus = '';
      if (row.status == '1') {
        finalstatus = '0';
      } else {
        finalstatus = '1';
      }
      let switchPost = {
        c_user_id: row.c_user_id,
        status: finalstatus
      };
      this.apiPost('/Customer/users/set', switchPost).then(res => {
        if (res.errorcode == 0) {
          _g.toastMsg('success', res.errormsg);
        } else {
          _g.toastMsg('warning', res.errormsg);
        }
      });
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.getMemberList();
    },
    search() {
      this.postData.page_no = 1;
      this.getMemberList();
    },
    getMemberList() {
      let _this = this;
      this.apiPost('/customer/users/getUserList', this.postData).then(res => {
        if (res.errorcode == 0) {
          _this.dataCount = res.data.count;
          _this.tableData = res.data.users;
          _this.cardsList = res.data.card;
        } else {
          _g.toastMsg('warning', res.errormsg);
        }
      });
    }
  },
  created() {
    this.getMemberList();
  },
  mixins: [http]
};
</script>
<style scoped>
.editfont {
  color: #fff;
  font-size: 14px;
}
.linkfont {
  color: #20a0ff;
  font-size: 14px;
}
</style>
