

<style lang="less" scoped>
</style>

<template>
  <div class="container news-manage">
    <header>
      <el-input style="width: 300px" placeholder="搜索新闻标题" v-model="title" @keydown.enter.native="getList"></el-input>
      <ChannelList v-model="channel_id"></ChannelList>
      <el-button type="success" @click="getList">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column align="center" label="编号" prop="id"></el-table-column>
      <el-table-column align="center" label="区域" prop="channel_name">
        <template scope="scope">
          {{scope.row.channel_id == 0 ? '全部区域' : scope.row.channel_name}}
        </template>
      </el-table-column>
      <el-table-column align="center" label="标题" prop="title"></el-table-column>
      <el-table-column align="center" label="发布人" prop="username"></el-table-column>
      <el-table-column align="center" label="发布时间" prop="created"></el-table-column>
      <el-table-column align="center" label="顺序" prop="sort"></el-table-column>
      <el-table-column align="center" label="阅读量" prop="look_number"></el-table-column>
      <el-table-column align="center" label="操作">
        <template scope="scope">
          <el-button size="small" type="primary" @click="handleEdit(scope.row.id)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
        <el-button type="success" @click="showModal = true" size="small" icon="el-icon-plus">
          发布新闻
        </el-button>
        <!-- <el-dropdown size="small" @command="handleDropdown" placement="top" type="info"><el-button type="info" size="small" style="margin-left: 20px">
            其他操作
            <i class="el-icon-arrow-down el-icon--right"></i></el-button><el-dropdown-menu slot="dropdown"><el-dropdown-item command="0">批量删除</el-dropdown-item></el-dropdown-menu></el-dropdown> -->
      </div>
      <el-pagination layout="total, prev, pager, next, sizes"
                     background @size-change="sizeChange" @current-change="getList" :page-size="page_size" :current-page.sync="page_no" :page-sizes="[10, 20, 30, 40]" :total="total"></el-pagination>
    </footer>
    <el-dialog :title="modalTitle" :visible.sync="showModal" :close-on-click-modal="false">
      <el-form :model="detail" :rules="rules" ref="form" label-width="80px">
        <el-form-item label="新闻标题" prop="title">
          <el-input v-model="detail.title" placeholder="标题限制50个字符"></el-input>
        </el-form-item>
        <el-form-item label="顺序" prop="sort">
          <el-input-number :min="0" v-model="detail.sort"></el-input-number>
        </el-form-item>
        <el-form-item label="新闻封面" prop="photo">
          <ImgUploader v-model="detail.photo"></ImgUploader>
        </el-form-item>
        <el-form-item label="新闻内容" prop="content">
          <Editor v-model="detail.content"></Editor>
        </el-form-item>
      </el-form>
      <footer slot="footer">
        <el-button type="success" @click="handleSave">保存</el-button>
        <el-button type="info" @click="showModal = false">取消</el-button>
      </footer>
    </el-dialog>
  </div>
</template>

<script>
import Pager from 'src/mixins/pager';
import ImgUploader from '../../components/form/imgUploader';
import Editor from '../../components/form/Editor';
import ChannelList from '../../components/form/channelList';
export default {
  name: 'newsManage',
  mixins: [Pager],
  components: { ImgUploader, Editor, ChannelList },
  data() {
    return {
      title: '',
      tableData: [],
      channel_id: JSON.parse(localStorage.userInfo).data.channel_id,
      user_channel_id: JSON.parse(localStorage.userInfo).data.channel_id,
      showModal: false,
      detail: {
        title: '',
        photo: '',
        content: '',
        sort: 1
      },
      rules: {
        title: [{ required: true, message: '请填写新闻名称', trigger: 'blur' }],
        // sort: [{ required: true, message: '请填写新闻展示顺序', trigger: 'blur' }],
        // photo: [{ required: true, message: '请上传新闻封面', trigger: 'change' }],
        content: [{ required: true, message: '请填写新闻内容', trigger: 'change' }]
      }
    };
  },
  created() {
    this.getList();
  },
  watch: {
    showModal(val) {
      if (!val) {
        this.detail = {};
      }
    }
  },
  methods: {
    handleDelete(id) {
      this.$confirm('确认要删除该条目吗？', '删除', {
        confirmButtonText: '删除',
        type: 'error'
      }).then(() => {
        this.deleteItem(id);
      });
    },
    deleteItem(id) {
      const url = '/customer/news/del_news';
      this.$service
        .post(url, { id })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.getList();
            this.$message.success('删除成功');
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    handleEdit(id) {
      this.showModal = true;
      this.detail = this.tableData.find(item => item.id == id);
    },
    handleSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) return false;
        let url = '/customer/news/add_news';
        if (this.detail.id) {
          url = '/customer/news/edit_news';
        }
        const { user_channel_id: channel_id } = this;
        const postData = { channel_id, user_id: Lockr.get('userInfo').id, ...this.detail };
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.showModal = false;
              this.getList();
              this.$message.success(`${this.detail.id ? '编辑成功' : '添加成功'}`);
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      });
    },
    getList() {
      const url = '/customer/news/index';
      const { page_no, page_size, title, channel_id } = this;
      const postData = {
        page_no,
        page_size,
        title,
        channel_id
      };
      this.$service
        .post(url, postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = data.count;
            this.tableData = data.list.map(item => {
              return {
                ...item,
                ...{
                  created: _g.formatDate(new Date(item.created * 1000), 'yyyy-MM-dd')
                }
              };
            });
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    }
  }
};
</script>
