<template>
  <div class="container twoTabs">
    <header>
      <el-input placeholder="请输入姓名或电话" @keyup.enter.native="search" v-model="postData.search" class="w-150 m-r-10"></el-input>
      <el-input placeholder="证件号码" @keyup.enter.native="search" v-model="postData.certificate_number" class="w-150 m-r-10"></el-input>
      <el-button type="primary" icon="search" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe style="width: 100%" ref="table">
      <el-table-column type="index" label="编号" width="80" align="center"></el-table-column>
      <el-table-column prop="nick_name" label="微信昵称" width="100" align="center"></el-table-column>
      <el-table-column prop="group_name" label="组别" width="90" align="center"></el-table-column>
      <el-table-column prop="user_name" label="姓名" width="90" align="center"></el-table-column>
      <el-table-column prop="user_sex" label="性别" width="90" align="center"></el-table-column>
      <el-table-column prop="certificate_type" label="证件类型" width="90" align="center"></el-table-column>
      <el-table-column prop="certificate_number" label="证件号码" width="140" align="center"></el-table-column>
      <el-table-column prop="phone" label="联系电话" width="120" align="center"></el-table-column>
      <el-table-column prop="create_time" label="报名时间" width="140" align="center"></el-table-column>
      <el-table-column label="操作" align="center">
        <template scope="scope">
          <el-button size="small" class="qricon" @click="deleteMember(scope.row)" type="primary">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer class="pos-rel p-t-20 ovf-hd flexend">
      <div class="left">
        <el-button type="success" @click="exportCsv" size="small">
          导出Excel
        </el-button>
      </div>
      <el-pagination 
          @current-change="handleCurrentChange" l
          ayout="prev, pager, next" 
          :page-size="postData.page_size" 
          :current-page="postData.page_no" 
          :total="dataCount"
          v-if="dataCount>0">
        </el-pagination>
    </footer>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  import getAppId from 'assets/js/wxplat';
import ExportCsv from 'src/components/form/csvExport';
  export default {
    name: 'wxConfig',
    data() {
      return {
        postData: {
          match_id: '',
          search: '',
          certificate_number: '',
          page_size: 10,
          page_no: 1
        },
        tableData: [],
        dataCount: 0
      };
    },
    methods: {
      exportCsv() {
        const columns = this.$refs.table.$children.filter(t => t.prop != null);
        let postd = {};
        postd = Object.assign(postd,this.postData);
        postd.page_size = this.dataCount;
        postd.page_no = 1;
        this.apiPost('/customer/match/getMatchSignList', postd).then(res => {
          if (res.errorcode == 0) {
            const fileName = '报名信息';
            let exportData = res.data.list;
            exportData.forEach((elem) => {
              elem.certificate_number = '="' + elem.certificate_number + '"';
              elem.phone = '="' + elem.phone + '"';
            })
            ExportCsv(exportData, columns, fileName);
          } else {
            _g.toastMsg('success', res.errormsg);
          }
        });
      },
      deleteMember(row) {
        this.$confirm('确认删除该参赛人员?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.apiPost('/customer/match/deleteMatchSign', { sign_id: row.id, user_id: row.user_id }).then(res => {
            if (res.errorcode == 0) {
              _g.toastMsg('success',res.errormsg);
              this.postData.page_no = 1;
              this.getList();
            } else {
              _g.toastMsg('warning', res.errormsg);
            }
          });
        })
      },
      getList() {
        this.apiPost('/customer/match/getMatchSignList', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.tableData = res.data.list;
            this.dataCount = res.data.count;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage
        this.getList()
      },
      search() {
        this.postData.page_no = 1
        this.getList()
      }
    },

    created() {
      this.postData.match_id = this.$route.params.match_id;
      this.getList();
    },
    mixins: [http]
  };
</script>

<style scoped>
  .table-zoom-image {
    height: 30px;
  }
  .table-zoom-image > img {
    height: 100%;
  }
  .oneline {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    align-items: center;
    margin-top: 10px;
  }
  .oneline:first-child {
    margin-top: 0;
  }
  .dynamadd i {
    width: 10%;
    height: 100%;
    margin-left: 10px;
  }
  .w90 {
    width: 90%;
  }
  .bluefont {
    color: #409eff;
    cursor: pointer;
    width: 100%;
    display: inline-block;
  }
  .qrpic {
    border: none;
    width: 32px;
    height: 32px;
    background: url(../../assets/images/qrcode.png) no-repeat center;
    background-size: 32px 32px;
  }
  .flex-center {
    width: 100%;
    display: flex;
    flex-direction: row;
    justify-content: center;
    align-items: center;
  }
</style>








