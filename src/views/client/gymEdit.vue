<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="150px">
      <el-form-item label="场馆名称">
        <el-input :disabled="true" class="w-320" placeholder="最多20个字符" v-model="gymBus.bus_name"></el-input>
      </el-form-item>
      <el-form-item label="所在城市">
        <el-select disabled placeholder="请选择" v-model="postData.city_id" class="w-320">
          <el-option v-for="item in cityList" :label="item.city_name" :value="item.city_id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="场馆地址">
        <el-input :disabled="true" class="w-320" placeholder="最多20个字符" v-model="gymBus.bus_addr"></el-input>
      </el-form-item>
      <el-form-item label="场馆类型">
        <el-select class="w-320" v-model="postData.cu_buss_type">
          <el-option v-for="(item, index) in busTypes" :key="index" :label="item.name" :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="场馆显示名称">
        <el-input class="w-320" v-model="postData.bus_alias"></el-input>
      </el-form-item>
      <el-form-item label="场馆坐标">
        <el-input class="w-320 maright-20" v-model="postData.lng_lat_str"></el-input>
        <!-- <el-button type="primary" @click="locateData">定位</el-button> -->
      </el-form-item>
      <el-form-item label="场馆相册">
        <el-upload class="upload-list" :action="actionPath" :before-upload="beforeUpload" :data="{savePath: './Uploads/'}" :on-success="handleUploadSuccess" :on-remove="handleRemove"
          :file-list="fileList" list-type="picture">
          <el-button size="small" type="primary">点击上传</el-button>
        </el-upload>
      </el-form-item>
      <el-form-item label="场馆简介" prop="bus_description">
        <quill-editor class="client-editor" ref="myTextEditor" v-model="postData.bus_description" :options="editorOption"></quill-editor>
      </el-form-item>
      <el-form-item label="场馆标签">
        <div class="taggroup">
          <div class="tagitem" v-for="(tag,index) in gymtagList" @click="selectTag(index,1)">
            <el-tag :type="!showdelete_gym&&tagStatus_gym[index]?'success':!showdelete_gym&&!tagStatus_gym[index]?'gray':showdelete_gym&&deltagStatus_gym[index]?'danger':'gray'">
              {{tag.tag_name}}
              <i class="el-icon-circle-check el-icon--right" v-if="!showdelete_gym"></i>
              <i class="el-icon-circle-cross el-icon--right" v-else></i>
            </el-tag>
          </div>
          <el-input class="w-80" v-if="showadd_gym" v-model="newtagname_gym" ref="saveTagInput" size="mini" @keyup.enter.native="addTag"
            @blur="addTag"></el-input>
          <el-button v-if="!showadd_gym&&!showdelete_gym" type="text" class="button-new-tag" size="mini" @click="showadd_gym = true">+ 添加标签</el-button>
          <el-button v-if="showdelete_gym" class="button-new-tag" type="text" size="mini" @click="deletetagConfirm">保存</el-button>
          <el-button v-if="!showadd_gym&&!showdelete_gym&&gymtagList.length>0" type="text" class="button-new-tag" size="mini" @click="activeDel">- 删除标签</el-button>
        </div>
      </el-form-item>
      <el-form-item label="品牌标签">
        <div class="taggroup">
          <div class="tagitem" v-for="(tag,index) in brandtagList" @click="selectTag(index,2)">
            <el-tag :type="tagStatus_brand[index]?'success':'gray'">
              {{tag.tag_name}}
              <i class="el-icon-circle-check el-icon--right"></i>
            </el-tag>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="特色标签">
        <div class="taggroup">
          <div class="tagitem" v-for="(tag,index) in featuretagList" @click="selectTag(index,3)">
            <el-tag :type="tagStatus_feature[index]?'success':'gray'">
              {{tag.tag_name}}
              <i class="el-icon-circle-check el-icon--right"></i>
            </el-tag>
          </div>
        </div>
      </el-form-item>
      <el-form-item label="健身卡绑定" v-if="postData.status==1">
        <el-checkbox-group class="cardcontrol" v-model="card_names">
          <el-checkbox class="m-l-15" :label="item.cu_card_name" v-for="item in gymCards"></el-checkbox>
        </el-checkbox-group>
      </el-form-item>
      <el-form-item label="是否启用">
        <el-radio-group v-model="postData.status" @change="radioChange">
          <el-radio class="radio" :label="1">是</el-radio>
          <el-radio class="radio" :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否放在推荐位">
        <el-radio-group v-model="postData.is_recommend">
          <el-radio class="radio" :label="1">是</el-radio>
          <el-radio class="radio" :label="0">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">保存</el-button>
        <el-button class="m-l-150" @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>
    <!-- 地图定位弹窗 -->
    <div class="meng" v-show="posilocateShow">
      <el-dialog width="50%" title="坐标拾取" :modal="false" visible :show-close="false" @open="init">
        <el-form :model="positemp" class="mapform">
          <el-form-item class="nomb" label="场馆地址" label-width="100px">
            <el-input class="inputlen" id="posinput" size="mini" v-model="positemp.bus_addr" auto-complete="off"></el-input>
          </el-form-item>
          <el-form-item class="nomb" label="场馆坐标" label-width="100px">
            <el-input id="lnglatinput" size="mini" class="inputlen" v-model="positemp.lng_lat_str" auto-complete="off"></el-input>
          </el-form-item>
        </el-form>
        <div class="maps">
          <div id="map_container">
            <div id="amap-vue" class="amap-demo"></div>
          </div>
          <div id="panel"></div>
        </div>
        <div slot="footer" id="bugbug" class="dialog-footer">
          <el-button @click="cancelLocate">取 消</el-button>
          <el-button type="primary" @click="posiLocate">确 定</el-button>
        </div>
      </el-dialog>
    </div>

  </div>
</template>
<script>
import 'quill/dist/quill.snow.css';
import http from 'assets/js/http';
import { quillEditor } from 'vue-quill-editor';
import Quill from 'quill';
const AlignStyle = Quill.import('attributors/style/align');
const BackgroundStyle = Quill.import('attributors/style/background');
const ColorStyle = Quill.import('attributors/style/color');
const DirectionStyle = Quill.import('attributors/style/direction');
const FontStyle = Quill.import('attributors/style/font');
const SizeStyle = Quill.import('attributors/style/size');
Quill.register(AlignStyle, true);
Quill.register(BackgroundStyle, true);
Quill.register(ColorStyle, true);
Quill.register(DirectionStyle, true);
Quill.register(FontStyle, true);
Quill.register(SizeStyle, true);
export default {
  name: 'gymEdit',
  data() {
    return {
      editorOption: {},
      busTypes: [
        /* {
          name: '健身房',
          id: 1
        },{
          name: '瑜伽馆',
          id: 2
        },{
          name: '跆拳道馆',
          id: 3
        },{
          name: '武道馆',
          id: 4
        },{
          name: '舞蹈馆',
          id: 5
        },{
          name: '公立体育馆',
          id: 6
        },{
          name: '健康步道',
          id: 7
        },{
          name: '森林公园',
          id: 8
        },{
          name: '社区健身',
          id: 9
        },{
          name: '球类场馆',
          id: 11
        },{
          name: '其他',
          id: 10
        } */
        { name: '请选择', id: '' },
        { name: '健身房', id: 1 },
        { name: '健身工作室', id: 9 },
        { name: '体育馆', id: 7 },
        { name: '游泳馆', id: 8 },
        { name: '瑜伽馆', id: 2 },
        { name: '跆拳道馆', id: 3 },
        { name: '武道馆', id: 4 },
        { name: '舞蹈馆', id: 5 },
        { name: '其他', id: 6 },
      ],
      postData: {
        c_bus_id: '',
        tag_ids: '',
        brand_tag_ids: '',
        feature_tag_ids: '',
        card_ids: '',
        status: 0,
        is_recommend: '',
        lng_lat_str: '',
        city_id: '',
        city_name: '',
        bus_alias: '',
        bus_description: '',
        images: [],
        cu_buss_type: ''
      },
      cityList: [],
      card_ids: [],
      card_names: [],
      gymBus: {},
      gymCards: [],

      gymtagList: [],
      showadd_gym: false,
      showdelete_gym: false,
      newtagname_gym: '',
      deltagArray_gym: [],
      deltagStatus_gym: [],
      tagArray_gym: [],
      tagStatus_gym: [],

      brandtagList: [],
      tagArray_brand: [],
      tagStatus_brand: [],

      featuretagList: [],
      tagArray_feature: [],
      tagStatus_feature: [],

      posilocateShow: false,
      positemp: {
        bus_addr: '',
        lng_lat_str: ''
      },
      placeSearch: {}
    };
  },
  computed: {
    actionPath() {
      return _g.getRbBaseUrl() + '/Admin/Public/upload';
    },
    fileList() {
      let images = this.postData.images;
      let filelist = [];
      images.forEach(item => {
        let file = {};
        file.url = item;
        file.name = '';
        filelist.push(file);
      });
      return filelist;
    }
  },
  components: {
    quillEditor
  },
  methods: {
    radioChange() {
      if (this.postData.status == 0) {
        _g.toastMsg('warning', '停用后会解除与所有健身卡的绑定关系');
      }
    },
    beforeUpload(file) {
      const fileType = ['image/jpg', 'image/png', 'image/jpeg'];
      const isJPG = fileType.includes(file.type);
      const isLt2M = file.size / 1024 / 1024 < 4;

      if (!isJPG) {
        this.$message.error('上传图片只能是 JPG/JPEG/PNG 格式!');
      }
      if (!isLt2M) {
        this.$message.error('上传图片大小不能超过 4MB!');
      }
      return isJPG && isLt2M;
    },
    handleUploadSuccess(res, file) {
      if (res.status === 1) {
        this.postData.images.push(res.info);
      } else {
        this.$message.error('上传失败');
      }
    },
    imgUpload(obj, fileList) {
      let imgData;
      let reader = new FileReader();
      reader.addEventListener(
        'load',
        () => {
          imgData = reader.result;
          let upData = {
            image_data: imgData,
            _type: 'platform'
          };
          this.$service
            .post(this.actionPath, upData)
            .then(res => {
              if (res.data.status == 1) {
                this.postData.images.push(res.data.path);
              }
              _g.toastMsg('success', res.data.info);
            })
            .catch(error => {
              console.log(error);
            });
        },
        false
      );
      if (obj.file) {
        reader.readAsDataURL(obj.file);
      }
    },
    handleRemove(file, fileList) {
      this.postData.images = [];
      fileList.forEach(item => {
        this.postData.images.push(item.url);
      });
    },
    cancelLocate() {
      this.positemp.lng_lat_str = this.postData.lng_lat_str;
      if (this.positemp.lng_lat_str) {
        if (Object.keys(this.placeSearch).length > 0) {
          this.placeSearch.clear();
        }
      }
      this.posilocateShow = false;
    },
    posiLocate() {
      this.postData.lng_lat_str = this.positemp.lng_lat_str;
      if (this.positemp.lng_lat_str) {
        if (Object.keys(this.placeSearch).length > 0) {
          this.placeSearch.clear();
        }
      }
      this.posilocateShow = false;
    },
    init() {
      let _this = this;
      var map = new AMap.Map('amap-vue', {
        center: _this.positemp.lng_lat_str.split('|'),
        resizeEnable: true,
        zoom: 13
      });
      AMap.plugin(['AMap.ToolBar', 'AMap.Scale'], function() {
        map.addControl(new AMap.ToolBar());
        map.addControl(new AMap.Scale());
      });
      AMap.plugin('AMap.Geocoder', function() {
        var geocoder = new AMap.Geocoder({
          city: _this.postData.city_id //城市，默认：“全国”
        });
        var marker = new AMap.Marker({
          map: map,
          bubble: true
        });
        map.on('click', function(e) {
          marker.setPosition(e.lnglat);
          _this.positemp.lng_lat_str = e.lnglat.lng + '|' + e.lnglat.lat;
        });
        lnglatinput.onchange = function(e) {
          let index = _this.positemp.lng_lat_str.indexOf('|');
          let numre = /^[-+]?(\d+)$|^[-+]?(\d+\.\d+)$/;
          let lngtemp = _this.positemp.lng_lat_str.substr(0, index);
          let lattemp = _this.positemp.lng_lat_str.substr(index + 1);
          let test = numre.test(lngtemp) && numre.test(lattemp);
          if (!test || index < 0) {
            _g.toastMsg('warning', '经纬度输入格式错误');
            return;
          }
          var lnglatXY = _this.positemp.lng_lat_str.split('|'); //地图上所标点的坐标
          geocoder.getAddress(lnglatXY, function(status, result) {
            if (status === 'complete' && result.info === 'OK') {
              marker.setPosition(lnglatXY);
              map.setCenter(marker.getPosition());
            } else {
              _g.toastMsg('warning', '获取地址失败');
            }
          });
        };
      });
      AMap.service(['AMap.PlaceSearch'], function() {
        _this.placeSearch = new AMap.PlaceSearch({
          //构造地点查询类
          pageSize: 5,
          pageIndex: 1,
          city: _this.postData.city_id, //城市
          map: map,
          panel: 'panel'
        });
        //关键字查询
        AMap.event.addListener(_this.placeSearch, 'markerClick', placeSearch_CallBack);

        function placeSearch_CallBack(data) {
          _this.positemp.bus_addr = data.data.address;
          _this.positemp.lng_lat_str = data.data.location.lng + '|' + data.data.location.lat;
        }
        AMap.event.addListener(_this.placeSearch, 'listElementClick', placeSearch_CallBack);

        function placeSearch_CallBack(data) {
          _this.positemp.bus_addr = data.data.address;
          _this.positemp.lng_lat_str = data.data.location.lng + '|' + data.data.location.lat;
        }
        posinput.onchange = function(e) {
          var posidetail = _this.postData.city_name + _this.positemp.bus_addr.replace(/\s+/g, '');
          _this.placeSearch.search(posidetail, function(status, result) {
            if (status === 'complete' && result.info === 'OK') {
              if (result.poiList.pois.length == 1) {
                _this.positemp.bus_addr = result.poiList.pois[0].address;
                _this.positemp.lng_lat_str =
                  result.poiList.pois[0].location.lng + '|' + result.poiList.pois[0].location.lat;
              }
            } else {
              _g.toastMsg('warning', '获取地址失败');
              if (Object.keys(_this.placeSearch).length > 0) {
                _this.placeSearch.clear();
              }
            }
          });
        };
      });
    },
    locateData() {
      let _this = this;
      this.positemp.bus_addr = this.gymBus.bus_addr;
      if (this.postData.lng_lat_str == '') {
        AMap.plugin('AMap.Geocoder', function() {
          var geocoder = new AMap.Geocoder({
            city: _this.postData.city_id //城市，默认：“全国”
          });
          geocoder.getLocation(_this.positemp.bus_addr, function(status, result) {
            if (status === 'complete' && result.info === 'OK') {
              _this.positemp.lng_lat_str = result.geocodes[0].location.lng + '|' + result.geocodes[0].location.lat;
              if (Object.keys(_this.placeSearch).length > 0) {
                _this.placeSearch.clear();
              }
              _this.init();
            } else {
              _g.toastMsg('warning', '获取位置失败');
              geocoder.getLocation(_this.postData.city_name, function(status, result) {
                if (status === 'complete' && result.info === 'OK') {
                  _this.positemp.bus_addr = result.geocodes[0].formattedAddress;
                  _this.positemp.lng_lat_str = result.geocodes[0].location.lng + '|' + result.geocodes[0].location.lat;
                  _this.init();
                }
              });
            }
          });
        });
      } else {
        this.positemp.lng_lat_str = this.postData.lng_lat_str;
        let numre = /^[-+]?(\d+)$|^[-+]?(\d+\.\d+)$/;
        let index = this.positemp.lng_lat_str.indexOf('|');
        let lngtemp = this.positemp.lng_lat_str.substr(0, index);
        let lattemp = this.positemp.lng_lat_str.substr(index + 1);
        let test = numre.test(lngtemp) && numre.test(lattemp);
        if (!test || index < 0) {
          _g.toastMsg('warning', '经纬度格式错误');
          AMap.plugin('AMap.Geocoder', function() {
            var geocoder = new AMap.Geocoder({
              city: _this.postData.city_id //城市，默认：“全国”
            });
            geocoder.getLocation(_this.postData.city_name, function(status, result) {
              if (status === 'complete' && result.info === 'OK') {
                _this.positemp.bus_addr = result.geocodes[0].formattedAddress;
                _this.positemp.lng_lat_str = result.geocodes[0].location.lng + '|' + result.geocodes[0].location.lat;
                _this.init();
              }
            });
          });
        } else {
          if (Object.keys(_this.placeSearch).length > 0) {
            _this.placeSearch.clear();
          }
          this.init();
        }
      }
      this.posilocateShow = true;
    },
    onSubmit() {
      this.postData.tag_ids = this.tagArray_gym.join(',');
      this.postData.brand_tag_ids = this.tagArray_brand.join(',');
      this.postData.feature_tag_ids = this.tagArray_feature.join(',');
      this.card_ids = [];
      for (let i = 0; i < this.gymCards.length; i++) {
        for (let j = 0; j < this.card_names.length; j++) {
          if (this.gymCards[i].cu_card_name == this.card_names[j]) {
            this.card_ids.push(this.gymCards[i].cu_card_id);
          }
        }
      }
      this.postData.card_ids = this.card_ids.join(',');
      this.apiPost('/customer/business/save', this.postData).then(res => {
        if (res.errorcode == 0) {
          _g.toastMsg('success', res.errormsg);
          this.$router.back();
        } else {
          _g.toastMsg('warning', res.errormsg);
        }
      });
    },
    activeDel() {
      this.showdelete_gym = true;
      this.deltagArray_gym = [];
      for (let i = 0; i < this.gymtagList.length; i++) {
        this.deltagStatus_gym[i] = false;
      }
    },
    deletetagConfirm() {
      if (this.deltagArray_gym.length == 0) {
        this.showdelete_gym = false;
        this.deltagArray_gym = [];
        this.deltagStatus_gym = [];
        return;
      }
      let tagdelPost = {
        c_bus_id: this.gymBus.c_bus_id,
        tag_ids: this.deltagArray_gym.join()
      };
      let _this = this;
      this.apiPost('/customer/tags/del', tagdelPost).then(res => {
        if (res.errorcode == 0) {
          for (let i = 0; i < _this.deltagArray_gym.length; i++) {
            for (let j = 0; j < _this.gymtagList.length; j++) {
              if (_this.gymtagList[j].tag_id == _this.deltagArray_gym[i]) {
                _this.gymtagList.splice(j, 1);
              }
            }
            for (let k = 0; k < _this.tagArray_gym.length; k++) {
              if (_this.deltagArray_gym[i] == _this.tagArray_gym[k]) {
                _this.tagArray_gym.splice(k, 1);
              }
            }
          }
          _this.showdelete_gym = false;
          _this.deltagArray_gym = [];
          _this.deltagStatus_gym = [];
          _this.gettagList();
        } else {
          _g.toastMsg('warning', res.errormsg);
          _this.showdelete_gym = false;
          _this.deltagArray_gym = [];
          _this.deltagStatus_gym = [];
        }
      });
    },
    addTag() {
      if (this.newtagname_gym) {
        if (this.newtagname_gym.length > 10) {
          this.$message.error('标签不能超过十个字符');
          this.newtagname_gym = '';
          this.showadd_gym = false;
          return false;
        }
        let tagPost = {
          tag_name: this.newtagname_gym
        };
        let _this = this;
        this.apiPost('/customer/tags/add', tagPost).then(res => {
          if (res.errorcode == 0) {
            _this.gettagList();
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      }
      this.newtagname_gym = '';
      this.showadd_gym = false;
    },
    selectTag(index, type) {
      if (type == 1) {
        if (!this.showdelete_gym) {
          if (this.tagStatus_gym[index]) {
            this.$set(this.tagStatus_gym, index, false);
            this.tagArray_gym.splice(this.tagArray_gym.indexOf(String(this.gymtagList[index].tag_id)), 1);
          } else {
            if (this.tagArray_gym.length == 5) {
              _g.toastMsg('warning', '场馆标签选择不得多于5个');
              return;
            } else {
              this.$set(this.tagStatus_gym, index, true);
              this.tagArray_gym.push(String(this.gymtagList[index].tag_id));
            }
          }
        } else {
          if (this.deltagStatus_gym[index]) {
            this.deltagArray_gym.splice(this.deltagArray_gym.indexOf(String(this.gymtagList[index].tag_id)), 1);
            this.$set(this.deltagStatus_gym, index, false);
          } else {
            this.deltagArray_gym.push(String(this.gymtagList[index].tag_id));
            this.$set(this.deltagStatus_gym, index, true);
          }
        }
      } else if (type == 2) {
        if (this.tagStatus_brand[index]) {
          this.$set(this.tagStatus_brand, index, false);
          this.tagArray_brand.splice(this.tagArray_brand.indexOf(String(this.brandtagList[index].tag_id)), 1);
        } else {
          if (this.tagArray_brand.length >= 1) {
            _g.toastMsg('warning', '品牌标签最多只能选1个');
            return;
          } else {
            this.$set(this.tagStatus_brand, index, true);
            this.tagArray_brand.push(String(this.brandtagList[index].tag_id));
          }
        }
      } else {
        if (this.tagStatus_feature[index]) {
          this.$set(this.tagStatus_feature, index, false);
          this.tagArray_feature.splice(this.tagArray_feature.indexOf(String(this.featuretagList[index].tag_id)), 1);
        } else {
          this.$set(this.tagStatus_feature, index, true);
          this.tagArray_feature.push(String(this.featuretagList[index].tag_id));
        }
      }
    },
    getpageInfo() {
      let _this = this;
      this.apiPost('/customer/business/info', this.postData).then(res => {
        if (res.errorcode == 0) {
          _this.gymBus = res.data.bus;
          _this.gymCards = res.data.card;
          _this.card_names = [];
          for (let i = 0; i < _this.gymCards.length; i++) {
            if (_this.gymCards[i].checked == 1) {
              _this.card_names[i] = _this.gymCards[i].cu_card_name;
            }
          }
          _this.tagArray_gym = res.data.bus.tag_ids.split(',');
          _this.tagArray_brand = res.data.bus.brand_tag_ids.split(',');
          _this.tagArray_feature = res.data.bus.feature_tag_ids.split(',');
          _this.gettagList();

          _this.postData.lng_lat_str = res.data.bus.lng_lat_str;
          _this.postData.status = res.data.bus.status;
          _this.postData.is_recommend = String(res.data.bus.is_recommend);
          _this.postData.city_id = res.data.bus.city_id;
          _this.postData.bus_alias = res.data.bus.bus_alias;
          _this.postData.bus_description = res.data.bus.bus_description;
          _this.postData.images = res.data.bus.images;
          this.postData = {
            ...this.postData,
            ...res.data.bus
          };
        } else {
          _g.toastMsg('warning', res.errormsg);
        }
      });
    },
    gettagList() {
      let _this = this;
      let postd = {
        c_bus_id: this.postData.c_bus_id
      };
      this.apiPost('customer/tags/getTagsList', postd).then(res => {
        if (res.errorcode == 0) {
          _this.gymtagList = res.data.bus_tag;
          _this.brandtagList = res.data.brand_tag;
          _this.featuretagList = res.data.feature_tag;
          _this.tagStatus_gym = [];
          _this.tagStatus_brand = [];
          _this.tagStatus_feature = [];
          // 设置标签的选中状态
          _this.setdefaultStatus(_this.gymtagList, _this.tagStatus_gym, _this.tagArray_gym);
          _this.setdefaultStatus(_this.brandtagList, _this.tagStatus_brand, _this.tagArray_brand);
          _this.setdefaultStatus(_this.featuretagList, _this.tagStatus_feature, _this.tagArray_feature);
        } else {
          _g.toastMsg('warning', res.errormsg);
        }
      });
    },
    setdefaultStatus(taglist, tagstatus, tagarray) {
      for (let i = 0; i < taglist.length; i++) {
        this.$set(tagstatus, i, false);
      }
      let checkexit = -1;
      for (let i = 0; i < tagarray.length; i++) {
        for (let j = 0; j < taglist.length; j++) {
          if (tagarray[i] == taglist[j].tag_id) {
            checkexit = j;
          }
        }
        if (checkexit > -1) {
          this.$set(tagstatus, checkexit, true);
          checkexit = -1;
        } else {
          tagarray.splice(i, 1);
        }
      }
    },
    getcityList() {
      let _this = this;
      let cityPost = {
        from: 1
      };
      this.apiPost('/customer/business/city', cityPost).then(res => {
        if (res.errorcode == 0) {
          _this.cityList = res.data.list;
          for (let i = 0; i < _this.cityList.length; i++) {
            if (_this.postData.city_id == _this.cityList[i].city_id) {
              _this.postData.city_name = _this.cityList[i].city_name;
            }
          }
        } else {
          _g.toastMsg('warning', res.errormsg);
        }
      });
    }
  },
  created() {
    this.postData.c_bus_id = this.$route.params.c_bus_id;
    this.postData.city_id = this.$route.params.city_id;
    this.getcityList();
    this.getpageInfo();
  },
  mixins: [http]
};
</script>
<style scoped>
.maright-20 {
  margin-right: 20px;
}

.taggroup {
  width: 50%;
}

.tagitem {
  display: inline-block;
  cursor: pointer;
  margin-right: 10px;
}

.cardcontrol {
  width: 400px;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}

.maps {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 300px;
}

#map_container {
  /*width: 500px;*/
  width: 100%;
  height: 300px;
}

.amap-demo {
  height: 300px;
}

.mapform {
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  height: 30px;
}

.mapform .nomb {
  margin-bottom: 10px;
}

.inputlen {
  width: 180px;
}

.meng {
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  position: fixed;
  overflow: auto;
  margin: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 2005;
}

#panel {
  /*position: absolute;*/
  background-color: white;
  max-height: 100%;
  overflow-y: auto;
  /*top: 100px;*/
  /*right: 10px;*/
  /*width: 271px;*/
}
</style>
