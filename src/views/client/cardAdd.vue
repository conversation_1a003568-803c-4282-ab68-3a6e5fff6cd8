<template>
  <el-form ref="form" :model="postData" label-width="150px">
    <el-form-item label="健身卡名称" prop="cu_card_name"
                  :rules="{ required: true, type: 'string', max:20, message: '必填项，最多20个字符'}">
      <el-input class="w-320" placeholder="最多20个字符" v-model="postData.cu_card_name"></el-input>
    </el-form-item>

    <el-form-item label="健身卡简称" prop="cu_card_nick"
                  :rules="{ required: true, type: 'string', max:20, message: '必填项，最多20个字符'}">
      <el-input class="w-320" placeholder="最多20个字符" v-model="postData.cu_card_nick"></el-input>
    </el-form-item>

    <el-form-item label="健身卡类型">
      <el-select placeholder="请选择" v-model="postData.cu_card_type" class="w-320">
        <el-option label="期限卡" value="1"></el-option>
        <el-option label="次卡" value="2"></el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="所在区域" prop="city_id" :rules="{ required: true, message: '请选择市'}">
      <el-select v-model="postData.province_id" @change="regionChange" style="width: 156px;">
        <el-option label="省" value="">省</el-option>
        <el-option label="北京" value="2">北京</el-option>
        <el-option label="安徽" value="3">安徽</el-option>
        <el-option label="福建" value="4">福建</el-option>
        <el-option label="甘肃" value="5">甘肃</el-option>
        <el-option label="广东" value="6">广东</el-option>
        <el-option label="广西" value="7">广西</el-option>
        <el-option label="贵州" value="8">贵州</el-option>
        <el-option label="海南" value="9">海南</el-option>
        <el-option label="河北" value="10">河北</el-option>
        <el-option label="河南" value="11">河南</el-option>
        <el-option label="黑龙江" value="12">黑龙江</el-option>
        <el-option label="湖北" value="13">湖北</el-option>
        <el-option label="湖南" value="14">湖南</el-option>
        <el-option label="吉林" value="15">吉林</el-option>
        <el-option label="江苏" value="16">江苏</el-option>
        <el-option label="江西" value="17">江西</el-option>
        <el-option label="辽宁" value="18">辽宁</el-option>
        <el-option label="内蒙古" value="19">内蒙古</el-option>
        <el-option label="宁夏" value="20">宁夏</el-option>
        <el-option label="青海" value="21">青海</el-option>
        <el-option label="山东" value="22">山东</el-option>
        <el-option label="山西" value="23">山西</el-option>
        <el-option label="陕西" value="24">陕西</el-option>
        <el-option label="上海" value="25">上海</el-option>
        <el-option label="四川" value="26">四川</el-option>
        <el-option label="天津" value="27">天津</el-option>
        <el-option label="西藏" value="28">西藏</el-option>
        <el-option label="新疆" value="29">新疆</el-option>
        <el-option label="云南" value="30">云南</el-option>
        <el-option label="浙江" value="31">浙江</el-option>
        <el-option label="重庆" value="32">重庆</el-option>
        <el-option label="香港" value="33">香港</el-option>
        <el-option label="澳门" value="34">澳门</el-option>
        <el-option label="台湾" value="35">台湾</el-option>
      </el-select>
      <el-select v-model="postData.city_id" style="width: 156px;">
        <el-option label="市" value="">市</el-option>
        <el-option v-for="item in citys" :key="item.region_id" :label="item.region_name"
                   :value="item.region_id"></el-option>
      </el-select>
    </el-form-item>

    <el-form-item label="天数/次数" prop="cu_card_num"
                  :rules="{required: true,type: 'string', pattern: /^[1-9]\d*$/, message: '必填项，天数/次数 应为正整数' }">
      <el-input class="w-320" v-model="postData.cu_card_num" placeholder="请输入"></el-input>
    </el-form-item>

    <el-form-item label="金额（元）" prop="sell_price"
                  :rules="{required: true,type: 'string', pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '必填项，金额必须大于等于0且只能保留两位小数' }">
      <el-input class="w-320" v-model="postData.sell_price" placeholder="请输入"></el-input>
    </el-form-item>

    <el-form-item label="单人领取次数" prop="single_limit_num"
                  :rules="{required: true,type: 'string', pattern: /^[1-9]\d*$/, message: '必填项，正整数'  }">
      <el-input class="w-320" v-model="postData.single_limit_num" placeholder="请输入"></el-input>
    </el-form-item>

    <el-form-item label="发行数量（张）" prop="send_num"
                  :rules="{required: true,type: 'string', pattern: /^[1-9]\d*$/, message: '必填项，正整数' }">
      <el-input class="w-320" v-model="postData.send_num" placeholder="请输入"></el-input>
    </el-form-item>

    <el-form-item label="卡激活有效期（天）" prop="active_days"
                  :rules="{required: true,type: 'string', pattern: /^[1-9]\d*|0$/, message: '必填项，正整数或0' }">
      <el-input class="w-320" v-model="postData.active_days" placeholder="请输入"></el-input>
    </el-form-item>

    <el-form-item label="次卡有效期（天）" prop="valid_days"
                  :rules="{required: true,type: 'string', pattern: /^[1-9]\d*|0$/, message: '必填项，正整数或0' }">
      <el-input class="w-320" v-model="postData.valid_days" placeholder="请输入"></el-input>
    </el-form-item>

    <el-form-item label="健身卡背景图" placeholder="请选择">
      <el-select v-model="postData.background_url" class="w-320">
        <el-option label="默认1" value="https://imagecdn.rocketbird.cn/default/customer/c_card_01.png"></el-option>
        <el-option label="默认2" value="https://imagecdn.rocketbird.cn/default/customer/c_card_02.png"></el-option>
        <el-option label="默认3" value="https://imagecdn.rocketbird.cn/default/customer/c_card_03.png"></el-option>
        <el-option label="自定义上传" value="">
          <el-upload class="upload-demo" :before-upload="beforeUpload" :action="uploadUrl"
                     :data="{savePath: './Uploads/'}" :on-success="handleUploadSuccess" :file-list="fileList"
                     :multiple="false">
            <el-button size="small" type="primary">自定义上传</el-button>
          </el-upload>
        </el-option>
      </el-select>
      <el-button class="act-button" type="text" @click="imgDialogShow = true">预览</el-button>
    </el-form-item>
    <el-dialog title="背景图预览" custom-class="img-dialog" :visible.sync="imgDialogShow">
      <img :src="postData.background_url"/>
    </el-dialog>
    <el-form-item label="顺序" prop="sort"
                  :rules="{required: true,type: 'string', pattern: /^[1-9]\d*|0$/, message: '必填项，正整数或0' }">
      <el-input class="w-320" v-model="postData.sort" placeholder="倒序排序"></el-input>
    </el-form-item>
    <el-form-item label="区域" prop="channel_id" v-if="adminAuth">
      <ChannelList v-model="postData.channel_id" showAll></ChannelList>
    </el-form-item>
    <el-form-item label="启用">
      <el-switch v-model="postData.status" active-color="#13ce66" inactive-color="#ff4949" active-value="1"
                 inactive-value="0" @change="statusChange"></el-switch>
    </el-form-item>

    <el-form-item label="健身卡说明" prop="describe" :rules="{required: true,message: '必填项' }">
      <Editor v-model="postData.describe"></Editor>
    </el-form-item>

    <el-form-item>
      <div class="table-top">
        <el-button class="act-button" type="text" @click="busChoiceShow = true">添加场馆</el-button>
        <h3>使用场馆</h3></div>
      <el-table :data="tableData" style="max-width:900px;" border>
        <el-table-column type="index" label="编号"></el-table-column>
        <el-table-column prop="cu_buss_name" label="场馆名称"></el-table-column>
        <el-table-column prop="cu_buss_type" label="类型">
          <template scope="scope">
            <span>{{ scope.row.cu_buss_type == 1 ? '健身房' : '瑜伽馆' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="city_name" label="城市"></el-table-column>
        <el-table-column prop="address" label="地址"></el-table-column>
        <el-table-column label="操作">
          <template scope="scope">
            <el-button size="small" @click="handleDelete(scope.$index, scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </el-form-item>

    <el-form-item>
      <el-button type="primary" @click="onSubmit">提交</el-button>
      <el-button @click="$router.back()">取消</el-button>
    </el-form-item>
    <bus-choice @saveBusChoice="saveBusChoice" v-if="busChoiceShow" v-model="busChoiceShow" :cuCardId="cardId"
                :cityId="postData.city_id"></bus-choice>
  </el-form>
</template>
<script>
  import http from 'assets/js/http';
  import busChoice from './busChoice.vue';
  import ChannelList from 'src/components/form/channelList';
  import Editor from 'components/form/Editor';

  export default {
    name: 'cardAdd',
    components: {
      Editor,
      busChoice,
      ChannelList
    },
    data() {
      return {
        uploadUrl: _g.getRbBaseUrl() + '/Admin/Public/upload',
        fileList: [],
        tableData: [],
        imgDialogShow: false,
        busChoiceShow: false,
        editorOption: {
          AlignStyle: true
        },
        cardId: this.$route.params.cardId,
        editInitStatus: this.$route.params.cardId ? true : false, //是否为编辑的初始状态
        postData: {
          channel_id: '',
          cu_card_name: '',
          cu_card_nick: '',
          province_id: '',
          city_id: '',
          cu_card_type: '1',
          status: '1', //1启用 0禁用
          cu_card_num: '', //天数/次数
          sell_price: '', //金额
          single_limit_num: '3', //单人领取次数
          send_num: '2000', //发行数量
          active_days: '15', //卡激活有效期
          valid_days: '15', //卡有效天数
          background_url: 'https://imagecdn.rocketbird.cn/default/customer/c_card_01.png', //背景图
          sort: '', //排序
          describe: '', //健身卡说明
          cu_buss_ids: [] //[1,2,3] //c端场馆id(数组)
        },
        citys: []
      };
    },
    created() {
      this.$route.params.cardId && this.getCardInfo(this.$route.params.cardId);
    },
    watch: {
      $route() {
        this.$route.params.cardId && this.getCardInfo(this.$route.params.cardId);
      },
      tableData(tableData) {
        this.postData.cu_buss_ids = [];
        for (let value of tableData) {
          this.postData.cu_buss_ids.push(value.cu_buss_id);
        }
      }
    },
    computed: {
      actionPath() {
        return _g.getRbBaseUrl() + '/Admin/Public/upload';
      },
      adminAuth() {
        return JSON.parse(localStorage.userInfo).data.channel_id == 0;
      }
    },
    methods: {
      beforeUpload(file) {
        const fileType = ['image/jpg', 'image/png', 'image/jpeg'];
        const isJPG = fileType.includes(file.type);
        const isLt2M = file.size / 1024 / 1024 < 4;

        if (!isJPG) {
          this.$message.error('上传图片只能是 JPG/JPEG/PNG 格式!');
        }
        if (!isLt2M) {
          this.$message.error('上传图片大小不能超过 4MB!');
        }
        return isJPG && isLt2M;
      },
      handleUploadSuccess(res, file) {
        if (res.status === 1) {
          // this.$emit('input', res.info);
          this.postData.background_url = res.info;
        } else {
          this.$message.error('上传失败');
        }
      },
      imgUpload(obj, fileList) {
        let imgData;
        let reader = new FileReader();
        reader.addEventListener(
          'load',
          () => {
            imgData = reader.result;
            let upData = {
              image_data: imgData,
              _type: 'platform'
            };
            this.$service
              .post(this.actionPath, upData)
              .then(res => {
                if (res.data.status == 1) {
                  this.postData.background_url = res.data.path + '?t=' + new Date().getTime();
                }
                _g.toastMsg('success', res.data.info);
              })
              .catch(error => {
                console.log(error);
              });
          },
          false
        );
        if (obj.file) {
          reader.readAsDataURL(obj.file);
        }
      },
      getCardInfo(cardId) {
        let infoData = { cu_card_id: cardId };
        this.apiPost('customer/card/get_card_info', infoData).then(res => {
          if (res.errorcode == 0) {
            let data = res.data;
            this.postData = { ...data, ...{ channel_id: +data.channel_id } };
            this.postData.cu_card_name = data.cu_card_name;
            this.postData.cu_card_nick = data.cu_card_nick;
            this.postData.cu_card_type = data.cu_card_type.toString();
            this.postData.province_id = data.province_id && data.province_id.toString();
            data.province_id && this.$route.params.cardId && this.regionChange(this.postData.province_id);
            this.postData.city_id = data.city_id;
            this.postData.valid_days = data.valid_days.toString();
            this.postData.background_url = data.background_url;
            this.postData.single_limit_num = data.single_limit_num.toString();
            this.postData.status = data.status.toString();
            this.postData.cu_card_num = data.cu_card_num.toString();
            this.postData.sell_price = data.sell_price.toString();
            this.postData.send_num = data.send_num.toString();
            this.postData.active_days = data.active_days.toString();
            this.postData.sort = data.sort.toString();
            this.postData.describe = data.describe;
            this.tableData = data.bus_list;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      //去重（场馆数组中去除重复的cu_buss_id项）
      unique(arr) {
        let newArray = [],
          newArrayObj = [];
        for (let value of arr) {
          if (newArray.indexOf(value.cu_buss_id) == -1) {
            newArray.push(value.cu_buss_id);
            newArrayObj.push(value);
          }
        }
        return newArrayObj;
      },
      saveBusChoice(selData) {
        this.tableData = this.unique(this.tableData.concat(selData));
      },
      handleDelete(index, row) {
        if (this.cardId) {
          let postData = {
            cu_card_id: this.cardId,
            cu_buss_id: row.cu_buss_id
          };
          this.apiPost('/Customer/Card/deleted_card', postData).then(res => {
            if (res.errorcode == 0) {
              this.tableData.splice(index, 1);
              _g.toastMsg('success', res.errormsg);
            } else {
              _g.toastMsg('warning', res.errormsg);
            }
          });
        } else {
          this.tableData.splice(index, 1);
        }
      },
      statusChange(val) {
        // if(val==0){
        //   this.$confirm('禁用后此健身卡对应的场馆关系将被清除, 是否继续?', '提示', {
        //     confirmButtonText: '确定',
        //     cancelButtonText: '取消',
        //     type: 'warning'
        //   }).then(() => {
        //     this.postData.status = '0'
        //     this.postData.cu_buss_ids = [];
        //     this.tableData = [];
        //   }).catch(() => {
        //     this.postData.status = '1'
        //   });
        // }
      },
      onSubmit() {
        this.$refs.form.validate(valid => {
          if (valid) {
            let postData = this.postData;
            let url = 'customer/card/add_card';
            if (this.$route.params.cardId) {
              url = 'customer/card/update_card';
              postData.cu_card_id = this.$route.params.cardId;
            }
            this.apiPost(url, postData).then(res => {
              if (res.errorcode == 0) {
                _g.toastMsg('success', res.errormsg);
                this.$router.back();
              } else {
                _g.toastMsg('warning', res.errormsg);
              }
            });
          } else {
            _g.toastMsg('warning', '请先正确填写数据！');
          }
        });
      },
      regionChange(id) {
        if (!this.editInitStatus) {
          this.postData.city_id = '';
        }
        this.$service
          .post('/Web/Merchants/ajaxGetRegion', { region_id: id })
          .then(res => {
            this.citys = res.data.data;
          })
          .catch(error => {
            console.log(error);
          });
        this.editInitStatus = false;
      }
    },
    mixins: [http]
  };
</script>
<style scoped>
  .upload-div {
    width: 100%;
    height: 100%;
  }

  .table-top {
    height: 40px;
    line-height: 40px;
    max-width: 880px;
    background: #fff;
    border: 1px solid #ddd;
    padding: 0 10px;
  }

  .table-top h3 {
    display: inline-block;
    margin: 0;
  }

  .table-top .act-button {
    float: right;
  }
</style>
