

<style lang="less" scoped>
.table-zoom-image {
  height: 50px;
  > img {
    height: 100%;
  }
}
</style>

<template>
  <div class="container banner-manage">
    <header>
      <el-input style="width: 300px" placeholder="轮播图标题" @keydown.enter.native="getList" v-model="title"></el-input>
      <ChannelList v-model="channel_id"></ChannelList>
      <el-button type="success" @click="getList">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column align="center" label="编号" prop="id"></el-table-column>
      <el-table-column align="center" label="区域" prop="channel_name">
        <template scope="scope">
          {{scope.row.channel_id == 0 ? '全部区域' : scope.row.channel_name}}
        </template>
      </el-table-column>
      <el-table-column align="center" label="内容名称" prop="title"></el-table-column>
      <el-table-column align="center" label="图片预览" prop="photo">
        <template scope="scope">
          <div class="table-zoom-image">
            <img :src="scope.row.photo" alt="">
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="类型" prop="type"></el-table-column>
      <el-table-column align="center" label="发布时间" prop="created"></el-table-column>
      <el-table-column align="center" label="顺序" prop="sort"></el-table-column>
      <el-table-column align="center" label="操作">
        <template scope="scope">
          <el-button size="small" type="primary" @click="handleEdit(scope.row.id)">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
        <el-button type="success" @click="newRoll" size="small" icon="el-icon-plus">
          新增轮播
        </el-button>
        <!-- <el-dropdown size="small" @command="handleDropdown" placement="top" type="info"><el-button type="info" size="small" style="margin-left: 20px">
            其他操作
            <i class="el-icon-arrow-down el-icon--right"></i></el-button><el-dropdown-menu slot="dropdown"><el-dropdown-item command="0">批量删除</el-dropdown-item></el-dropdown-menu></el-dropdown> -->
      </div>
      <el-pagination layout="total, prev, pager, next, sizes"
                     background @size-change="sizeChange" @current-change="getList" :page-size="page_size" :current-page.sync="page_no" :page-sizes="[10, 20, 30, 40]" :total="total"></el-pagination>
    </footer>
    <el-dialog :title="modalTitle" :visible.sync="showModal" :close-on-click-modal="false">
      <el-form :model="detail" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="轮播内容" prop="indexi">
          <el-select v-model="detail.indexi" style="width: 100%" clearable filterable :disabled="isEdit">
            <el-option v-for="(item, index) in titleList" :value="index" :label="item.type == 1 ? `【新闻】${item.title}` : `【活动】${item.title}`" :key="index"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="轮播顺序" prop="sort">
          <el-input-number :min="0" v-model="detail.sort"></el-input-number>
        </el-form-item>
        <el-form-item label="轮播封面" prop="photo">
          <ImgUploader v-model="detail.photo"></ImgUploader>
        </el-form-item>
      </el-form>
      <footer slot="footer">
        <el-button type="success" @click="handleSave">保存</el-button>
        <el-button type="info" @click="showModal = false">取消</el-button>
      </footer>
    </el-dialog>
  </div>
</template>

<script>
import Pager from '../../mixins/pager';
import ImgUploader from '../../components/form/imgUploader';
import Editor from '../../components/form/Editor';
import ChannelList from '../../components/form/channelList';
export default {
  name: 'bannerManage',
  mixins: [Pager],
  components: { ImgUploader, Editor, ChannelList },
  data() {
    return {
      title: '',
      isEdit: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 86400000;
        }
      },
      channel_id: JSON.parse(localStorage.userInfo).data.channel_id,
      user_channel_id: JSON.parse(localStorage.userInfo).data.channel_id,
      tableData: [],
      titleList: [],
      modalTitle: '添加',
      showModal: false,
      detail: {
        indexi: '',
        foreign_key: '',
        photo: '',
        sort: ''
      },
      rules: {
        title: [{ required: true, message: '请填写活动名称', trigger: 'blur' }],
        address: [{ required: true, message: '请填写活动地址', trigger: 'blur' }],
        indexi: [{ required: true, type: 'number', message: '请选择轮播内容', trigger: 'change' } ],
        registrationTime: [{ required: true, message: '请选择报名时间', trigger: 'change' }],
        enrolment_limit: [{ required: true, message: '请填写可报名人数', trigger: 'blur' }],
        photo: [{ required: true, message: '请上传封面', trigger: 'change' }],
        content: [{ required: true, message: '请填写活动介绍', trigger: 'change' }]
      }
    };
  },
  created() {
    this.getList();
    this.getTitles();
  },
  watch: {
    showModal(val) {
      if (!val) {
        this.detail = {
          indexi: '',
          foreign_key: '',
          photo: '',
          sort: ''
        };
        this.isEdit = false;
      }
    }
  },
  methods: {
    newRoll() {
      this.showModal = true;
      if (this.$refs.form!=undefined) {
        this.$refs.form.resetFields();
      }
    },
    getTitles() {
      const url = '/customer/carousel/get_news_and_activity';
      this.$service
        .post(url, { channel_id: this.channel_id })
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.titleList = data;
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    handleDelete(id) {
      this.$confirm('确认要删除该轮播图吗?', '删除轮播图', {
        confirmButtonText: '删除',
        type: 'error'
      }).then(() => {
        this.deleteBanner(id);
      });
    },
    deleteBanner(id) {
      const url = '/customer/carousel/del_carousel';
      this.$service
        .post(url, { id })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.getList();
            this.$message.success('删除成功');
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    handleEdit(id) {
      this.getDetail(id);
      this.showModal = true;
      this.isEdit = true;
      if (this.$refs.form!=undefined) {
        this.$refs.form.resetFields();
      }
    },
    getDetail(id) {
      this.$service
        .post('/customer/carousel/get_single_carousel_info', { id })
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data.carousel;
            let indexitem = -1;
            this.titleList.forEach((elem,index) => {
              if(elem.title == data.title) {
                indexitem = index;
              }
            })
            this.detail = {
              ...data,
              ...{
                indexi: indexitem
              }
            };
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    handleSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) return false;
        let url = '/customer/carousel/add_carousel';
        let postData = {
          ...this.detail,
          ...{
            channel_id: this.user_channel_id,
            foreign_key: this.detail.id
          }
        };
        if (this.detail.id) {
          url = '/customer/carousel/edit_carousel';
        } else {
          postData = {
            ...postData,
            ...{
              title: this.titleList[this.detail.indexi].title,
              type: this.titleList[this.detail.indexi].type,
              foreign_key: this.titleList[this.detail.indexi].id
            }
          };
        }
        delete postData.time;
        delete postData.registrationTime;
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.showModal = false;
              this.getList();
              this.$message.success('添加成功');
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      });
    },
    getList() {
      const url = '/customer/carousel/index';
      const { page_no, page_size, title, channel_id } = this;
      const postData = {
        page_no,
        page_size,
        title,
        channel_id
      };
      this.$service
        .post(url, postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = data.count;
            this.tableData = data.list.map(item => {
              return {
                ...item,
                ...{
                  type: item.type == 2 ? '活动' : '新闻',
                  created: _g.formatDate(new Date(item.created * 1000), 'yyyy-MM-dd')
                }
              };
            });
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    handlePost() {}
  }
};
</script>
