<template>
  <div class="form-frame">
    <div class="line">
      <span>会员卡名称：{{card_info.card_name}}</span>
      <span>会员卡类型：{{card_info.card_type==1?'次卡':'期卡'}}</span>
      <span>有效期：{{card_info.valid_time}}</span>
      <span>领取时间： {{card_info.get_time}}</span>
      <span>卡激活时间：{{card_info.active_time}}</span>
      <span>使用场馆： {{card_info.use_bus}}</span>
      <span>剩余次数或天数：{{card_info.last_num}}</span>
      <span>状态： {{card_info.status==0?'未激活':card_info.status==1?'使用中':card_info.status==2?'已过期':'已销卡'}}</span>
      <span>验证码： {{card_info.serial_num}}</span>
      <span>支付金额： {{card_info.money}}</span>
    </div>
    <div class="oneblock">
      <span>备注:</span>
      <span>{{card_info.remark}}</span>
    </div>
    <div class="oneblock">
      <span>{{card_info.nick_name}}（{{card_info.phone}}）</span>
    </div>
  </div>
</template>
<script>
import http from 'assets/js/http';
export default {
  name: 'cardDetail',
  data() {
    return {
      postData: {
        c_user_id: '',
        card_user_id: '',
        c_user_card_id: '',
      },
      card_info: {}
    }
  },
  methods: {
    getcardInfo() {
      let  _this = this;
      this.apiPost('/Customer/users/card_info', this.postData).then((res) => {
        this.handelResponse(res, (data) => {
          _this.card_info = res.data.card_info;
        })
      })
    }
  },
  created() {
    this.postData.c_user_id = this.$route.params.c_user_id;
    this.postData.card_user_id = this.$route.params.card_user_id;
    this.postData.c_user_card_id = this.$route.params.c_user_card_id;
    this.getcardInfo();
  },
  mixins: [http]
}
</script>
<style scoped>
.line {
  width: 100%;
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  justify-content: flex-start;
  align-items: center;
}
.line span {
  width: 40%;
  padding-left: 30px;
  padding-right: 30px;
  margin-bottom: 10px;
  box-sizing: border-box;
}
.oneblock {
  padding-left: 30px;
  padding-right: 30px;
  margin-top: 30px;
  width: 80%;
}
</style>
