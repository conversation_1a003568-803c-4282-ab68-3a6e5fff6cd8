<template>
  <div>
    <div class="m-b-20 ovf-hd">
      <div class="fl">
        <router-link class="btn-link-large add-btn" :to="{name:'tagAdd', params:{tag_id:0}}">
          <i class="el-icon-plus"></i>&nbsp;&nbsp;新增标签
        </router-link>
      </div>
      <div class="fl m-l-30">
        <el-input placeholder="标签名称" @keyup.enter.native="search" v-model="postData.tag_name" class="w-150 m-r-10"></el-input>
        <el-select v-model="postData.tag_type" @change="search" clearable placeholder="标签类型" class="w-150 m-r-10">
          <el-option label="特色" value="1"></el-option>
          <el-option label="品牌" value="2"></el-option>
        </el-select>
        <el-select @change="search" clearable v-model="postData.city_id" placeholder="城市" class="w-150 m-r-10">
          <el-option v-for="item in cityList" :label="item.city_name" :value="item.city_id"></el-option>
        </el-select>
        <el-button type="primary" icon="search"  @click="search">搜索</el-button>
      </div>
    </div>
    
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column type="index" label="编号" width="100" align="center"></el-table-column>
      <el-table-column prop="city_name" label="城市" width="120" align="center"></el-table-column>
      <el-table-column prop="tag_name" label="标签名称" width="170" align="center"></el-table-column>
      <el-table-column prop="tag_type" label="标签类型" width="170" align="center">
        <template scope="scope">
          <span>{{ scope.row.tag_type == 1 ? '特色标签' : '品牌标签' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="sort_num" label="顺序" width="150" align="center"></el-table-column>
      <el-table-column prop="status" label="状态" width="100" align="center">
        <template scope="scope">
          <span>{{ scope.row.status == 1 ? '启用' : '停用' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center">
        <template scope="scope">
        <el-button
          size="small"
          type="primary">
          <router-link class='editfont' :to="{name: 'tagAdd',params: {tag_id: scope.row.tag_id}}">编辑</router-link>
        </el-button>
      </template>
      </el-table-column>
    </el-table>

    <div class="pos-rel p-t-20 ovf-hd flexend" v-if="dataCount>0">
      <div class="block">
        <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import http from 'assets/js/http'
export default {
  name: 'tagControl',
  data() {
    return {
      postData:{
        tag_type: '',
        tag_name: '',
        city_id:'',
        page_size: 10,
        page_no: 1
      },
      tableData: [],
      dataCount: 0,
      cityList: []
      }
  },
  methods: {
    gettableList() {
      let _this = this;
      this.apiPost('/customer/tags/bf_tag_list', this.postData).then((res) => {
        if(res.errorcode ==0 ){
          _this.tableData = res.data.bf_tags;
          _this.dataCount = res.data.count;
        }else{
          _g.toastMsg('warning',res.errormsg);
        }
      })
    },
    getcityList() {
      let _this = this;
      this.apiPost('/customer/tags/tag_city').then((res) => {
        if(res.errorcode ==0 ){
          _this.cityList = res.data.list;

        }else{
          _g.toastMsg('warning',res.errormsg);
        }
      })
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.gettableList();
    },
    search() {
      this.postData.page_no = 1;
      this.gettableList();
    },
  },
  created() {
    this.getcityList();
    this.gettableList();
  },
  mixins: [http]
}
</script>
<style scoped>
.editfont {
  color: #fff;
  font-size: 14px;
}
.linkfont {
  color: #20a0ff;
  font-size: 14px;
}
</style>
















