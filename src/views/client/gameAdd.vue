<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="150px">
      <el-form-item label="赛事名称" prop="match_name" :rules="{ required: true, message: '请填写赛事名称'}">
        <el-input :disabled="actionName=='detail'" class="w-400 maright-20" placeholder="标题限制50个字符"  v-model="postData.match_name"></el-input>
      </el-form-item>
      <el-form-item label="赛事分类" prop="match_type" :rules="{ required: true, message: '请选择赛事类型'}">
        <el-select v-if="actionName!='detail'" v-model="postData.match_type" clearable class="w-400">
          <el-option v-for='item in categoryList' :label="item.type_name" :value="item.id" :key="item.id"></el-option>
        </el-select>
        <el-input v-else disabled class="w-400 maright-20" v-model="postData.match_type_name"></el-input>
      </el-form-item>
      <el-form-item label="赛事时间" prop="match_time" :rules="{ required: true, message: '请选择赛事时间'}">
        <el-date-picker
          :disabled="actionName=='detail'"
          class="w-400"
          v-model="postData.match_time"
          type="date"
          placeholder="开始时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="报名时间" prop="register_time" :rules="{ required: true, message: '请选择报名时间'}">
        <el-date-picker
          :disabled="actionName=='detail'"
          v-model="postData.register_time"
          type="datetimerange"
          range-separator="至"
          start-placeholder="报名开始时间"
          end-placeholder="报名结束时间">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="赛事地点" prop="match_address" :rules="{ required: true, message: '请填写赛事地点'}">
        <el-input :disabled="actionName=='detail'" class="w-400 maright-20" placeholder="地点描述限制50个字符"  v-model="postData.match_address"></el-input>
      </el-form-item>
      <el-form-item label="赛事封面" prop="match_photo" :rules="{ required: true, message: '请上传封面'}">
        <ImgUploader v-if="actionName!='detail'" v-model="postData.match_photo"></ImgUploader>
        <img class="detailpic" :src="postData.match_photo" v-else />
      </el-form-item>
      <el-form-item label="赛事说明" prop="match_explain" :rules="{ required: true, message: '请填写赛事说明'}">
        <game-editor :disabled="actionName=='detail'" v-model="postData.match_explain"></game-editor>
      </el-form-item>
      <el-form-item label="免责声明" prop="disclaimer" :rules="{ required: true, message: '请填写免责声明'}">
        <disclaimer-editor :disabled="actionName=='detail'" v-model="postData.disclaimer"></disclaimer-editor>
      </el-form-item> 
      <el-form-item label="在线报名" v-if="actionName=='add'">
        <el-switch v-model="postData.sign_up" :active-value="1" :inactive-value="2"></el-switch>
      </el-form-item>
      <el-form-item label="比赛组别设置" v-if="postData.sign_up == 1">
        <div class="box-cards">
          <el-card v-for="(item,index) in postData.group" class="box-card" :key="index">
            <div class="card-inside">
              <el-form-item label="组别名称" :prop="'group.' + index + '.group_name'" :rules="{ required: true, message: '请填写组别名称'}">
                <el-input :disabled="actionName=='detail'" class="w-220 m-r-10" v-model="item.group_name"></el-input>
              </el-form-item>
              <el-form-item label="报名费用" :prop="'group.' + index + '.cost'" :rules="{ required: true, pattern: /^[0-9]+(.[0-9]{1,2})?$/, message: '必填项，费用必须大于等于0且只能保留两位小数'}">
                <el-input :disabled="actionName=='detail'" class="w-220 m-r-10" v-model="item.cost"></el-input>
              </el-form-item>
              <el-form-item label="组别开始时间" :prop="'group.' + index + '.begin_time'" :rules="{ required: true, message: '请选择开始时间'}">
                <el-date-picker
                  :disabled="actionName=='detail'"
                  class="w-220"
                  v-model="item.begin_time"
                  type="datetime"
                  placeholder="开始时间"
                  format="yyyy-MM-dd HH:mm">
                </el-date-picker>
              </el-form-item>
              <el-form-item label="名额数量" :prop="'group.' + index + '.quota'" :rules="{ required: true, pattern: /^[0-9]\d*$/, message: '必填，名额数量为大于等于0的整数'}">
                <el-input :disabled="actionName=='detail'" class="w-220 m-r-10" v-model="item.quota"></el-input>
              </el-form-item>
              <el-button type="danger" size="medium" @click="deleteGroup(index)" v-if="actionName!='detail'&&postData.group.length!=1">
                <i class="el-icon-minus"></i>&nbsp;&nbsp;删除本组别</el-button>
            </div>
          </el-card>
          <el-button type="success" size="medium" @click="addGroup" v-if="actionName!='detail'">
                <i class="el-icon-plus"></i>&nbsp;&nbsp;添加组别</el-button>
        </div>
      </el-form-item>
      <el-form-item label="报名文档" prop="match_photo" :rules="{ required: true, message: '请上传office文件'}" v-else>
        <OfficeUpload v-if="actionName!='detail'" v-model="postData.file_url" @on-compelate="uploadComplate"></OfficeUpload>
        <a v-if="postData.file_url" :href="postData.file_url" style="color: #409eff;margin-left:10px">{{postData.file_name}}</a>
      </el-form-item>
      <el-form-item class='m-t-15' v-if="actionName!='detail'">
        <el-button type="primary" @click="submitAdd">保存</el-button>
        <el-button class="m-l-150" @click="cancelAdd">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import http from 'assets/js/http';
import ImgUploader from '../../components/form/imgUploader';
import OfficeUpload from '../../components/form/OfficeUpload.vue';
import Editor from '../../components/form/Editor';
import {formatDate} from 'assets/js/utils';
export default {
  name: 'gameAdd',
  components: { 
    ImgUploader,
    OfficeUpload,
    GameEditor: Editor,
    disclaimerEditor: Editor
  },
  data() {
    return {
      actionName: '',
      postData: {
        match_id: '',
        file_url: '',
        file_name: '',
        sign_up: 1,
        match_photo: '',
        group: [{
          id: '',
          group_name: '',
          cost: '',
          begin_time: '',
          quota: ''
        }],
        match_time: '',
        register_time: []
      },
      categoryList: []
    }
  },
  methods: {
    cancelAdd() {
      this.$router.back();
    },
    uploadComplate(res) {
      if(res.status === 1) {
        this.postData.file_url = res.path
        this.postData.file_name = res.name
      } else {
        this.postData.file_url = ''
        this.postData.file_name = ''
      }
    },
    submitAdd() {
      this.$refs.form.validate((valid) => {
        let url = '';
        //date组件报bug处理
        let postd = {};
        Object.assign(postd, this.postData);
        let group = [];
        postd.group && postd.group.forEach((item, index) => {
          group[index] = {};
          Object.assign(group[index],item)
        })
        postd.group = group;

        if (valid) {
          postd.begin_time = formatDate(postd.register_time[0],'yyyy-MM-dd HH:mm');
          postd.end_time = formatDate(postd.register_time[1],'yyyy-MM-dd HH:mm');
          postd.group && postd.group.forEach((item) => {
            item.begin_time = formatDate(item.begin_time,'yyyy-MM-dd HH:mm');
          })
          postd.channel_id = JSON.parse(localStorage.userInfo).data.channel_id;
          if(this.actionName == 'edit') {
            url = '/customer/match/editMatch';
          } else {
            url = '/customer/match/addMatch';
          }
          this.apiPost(url, postd).then((res) => {
            if(res.errorcode ==0 ) {
              _g.toastMsg('success',res.errormsg);
              this.$router.back();
            }else{
              _g.toastMsg('warning',res.errormsg);
            }
          })
        }
      })
    },
    deleteGroup(index) {
      this.postData.group.splice(index,1)
    },
    addGroup() {
      let item = {
          id: '',
          group_name: '',
          cost: '',
          begin_time: '',
          quota: ''
        };
      this.postData.group.push(item);
    },
    getpageInfo() {
      this.apiPost('/customer/match/getMatchInfo', { match_id: this.postData.match_id }).then((res) => {
        if(res.errorcode == 0) {
          let infoData = res.data
          infoData.register_time = [new Date(infoData.begin_time), new Date(infoData.end_time)]
          this.postData = infoData;
          this.postData.group&&this.postData.group.forEach((item) => {
            item.begin_time = new Date(item.begin_time)
          })
        } else {
          _g.toastMsg('warning', res.errormsg);
        }
      }).catch((error) => {
        console.log(error)
      })
    },
    getCateList() {
      this.apiPost('/customer/match/getMatchTypeList').then(res => {
        if (res.errorcode == 0) {
          this.categoryList = res.data;
        } else {
          _g.toastMsg('warning', res.errormsg);
        }
      });
    }
  },
  created() {
    this.postData.match_id = this.$route.query.match_id;
    this.actionName = this.$route.query.action;
    if(this.actionName == 'edit'){
      this.actionName = 'edit';
      this.getpageInfo();
      this.getCateList();
    } else if (this.actionName == 'detail') {
      this.actionName = 'detail';
      this.getpageInfo();
    } else {
      this.actionName = 'add'
      this.getCateList();
    }
  },
  mixins: [http]
}
</script>

<style lang="less" scoped>
.box-cards {
  width: 100%;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: center;
}
.box-card {
  margin-bottom: 20px;
}
.card-inside {
  display: flex;
  flex-direction: row;
  justify-content: center;
  flex-wrap: wrap;
  align-items: center;
  .el-form-item {
    width: 50%;
    margin-bottom: 10px;
    margin-top: 10px;
  }
}
.w-220 {
  width: 220px;
}
.detailpic {
  height: 200px;
}
</style>
