<template>
  <div class="m-b-20 ovf-hd">
    <el-tabs type="border-card">
      <el-tab-pane>
        <span slot="label"><i class="el-icon-connection"></i> Pos机绑定管理</span>
        <posBinding />
      </el-tab-pane>
      <el-tab-pane>
        <span slot="label"><i class="el-icon-edit"></i> 场馆支付参数</span>
        <paymentParams />
      </el-tab-pane>
      <el-tab-pane>
        <span slot="label"><i class="el-icon-cpu"></i> POS机软件包管理</span>
        <POSPackage />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import posBinding from "./posBinding.vue";
import paymentParams from "./paymentParams.vue";
import POSPackage from "./POSPackage.vue";

export default {
  name: 'posSettings',
  components: {
    posBinding,
    paymentParams,
    POSPackage,
  }
}
</script>

