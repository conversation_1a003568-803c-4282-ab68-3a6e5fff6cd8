<template>
  <div class="m-b-20 ovf-hd">
    <header class="fl">
      <el-form ref="form" :model="postData" inline>
        <el-form-item>
          <el-input placeholder="账户名称" v-model="postData.keyword"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input placeholder="场馆ID" v-model="postData.bus_id"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="getConfList">查询</el-button>
          <el-button type="primary" @click="openAdd">新增参数</el-button>
        </el-form-item>
      </el-form>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column align="center" prop="appid" label="appid"></el-table-column>
      <el-table-column align="center" prop="title" label="账户名称"></el-table-column>
      <el-table-column align="center" prop="store_sn" label="store_sn"></el-table-column>
      <el-table-column align="center" prop="brand_code" label="brand_code"></el-table-column>
      <el-table-column align="center" prop="bus_name" label="场馆名称"></el-table-column>
      <el-table-column align="center" prop="bus_id" label="场馆ID"></el-table-column>
      <el-table-column align="center" label="操作">
        <template scope="scope">
          <el-button type="text" @click="openEdit(scope.$index, scope.row)">修改参数</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pos-rel p-t-20 ovf-hd flexend">
      <div class="block">
        <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount">
        </el-pagination>
      </div>
    </div>
    <el-dialog :title="isAdd?'新增参数':'修改参数'" :visible.sync="showEdit" @close="onDialogClose" :close-on-click-modal="false">
      <el-form :model="editForm" ref="editForm" label-width="120px">
        <el-form-item label="场馆ID" prop="bus_id" :rules="{required: true, message: '请输入场馆ID'}">
          <el-input :disabled="!isAdd" placeholder="场馆ID" v-model="editForm.bus_id"></el-input>
        </el-form-item>
        <el-form-item label="APPID" prop="appid" :rules="{required: true, message: '请输入APPID'}">
          <el-input placeholder="APPID" v-model="editForm.appid"></el-input>
        </el-form-item>
        <el-form-item label="store_sn" prop="store_sn" :rules="{required: true, message: '请输入store_sn'}">
          <el-input placeholder="store_sn" v-model="editForm.store_sn"></el-input>
        </el-form-item>
        <el-form-item label="账户标题" prop="title" :rules="{required: true, message: '请输入账户标题'}">
          <el-input placeholder="请输入账户标题" v-model="editForm.title"></el-input>
        </el-form-item>
        <el-form-item label="brand_code" prop="brand_code" :rules="{required: true, message: '请输入brand_code'}">
          <el-input placeholder="brand_code" v-model="editForm.brand_code"></el-input>
        </el-form-item>
        <el-form-item label="请求私钥" prop="req_pri_key" :rules="{required: true, message: '请输入请求私钥'}">
          <el-input placeholder="请求私钥" type="textarea" autosize v-model="editForm.req_pri_key"></el-input>
        </el-form-item>
        <el-form-item label="响应公钥" prop="res_pub_key" :rules="{required: true, message: '请输入响应公钥'}">
          <el-input placeholder="响应公钥" type="textarea" autosize v-model="editForm.res_pub_key"></el-input>
        </el-form-item>
        <el-form-item style="text-align:center;">
          <el-button v-if="isAdd" :loading=isLoading type="success" style="transform:translateX(-60px);" @click="onSubmitClick">确认新增</el-button>
          <el-button v-else :loading=isLoading type="success" style="transform:translateX(-60px);" @click="onSubmitClickModify">确认修改</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'posRegister',
  data() {
    return {
      showEdit: false,
      isLoading: false,
      isAdd: false,
      editForm: {
        bus_id: '',
        title: '',
        brand_code: '',
        req_pri_key: '',
        res_pub_key: '',
        appid: '',
        store_sn: ''
      },
      dataCount: 0,
      postData: {
        page_no: 1,
        page_size: 10,
        keyword: '',
        bus_id: ''
      },
      tableData: [],
    }
  },
  mounted() {
    this.getConfList(this.postData)
  },
  methods: {
    // 获取配置列表
    getConfList(postdata) {
      const url = "/web/SQBPos/pay_conf_list";
      return this.$service.get(url, {
        params:   { ...postdata }
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.dataCount = res.data.data.count;
          this.tableData = res.data.data.list
        } else {
          this.$message.error(res.data.errormsg);
        }
      })
    },
    async onSubmitClick() {
      let checkForm = await this.$refs.editForm.validate();
      if (!checkForm) return;
      const url = "/web/SQBPos/pay_conf_add";
      let postdata = this.urlUtil(this.editForm)
      return this.$service.post(url, postdata, {
        headers: {'Content-Type': 'application/x-www-form-urlencoded'}
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.$message.success(res.data.errormsg);
          this.showEdit = false;
          this.getConfList()
        } else {
          this.$message.error(res.data.errormsg);
        }
      })
    },
    // 编辑
    async onSubmitClickModify() {
      let checkForm = await this.$refs.editForm.validate();
      if (!checkForm) return;
      const url = "/web/SQBPos/pay_conf_edit";
      let postdata = this.urlUtil(this.editForm)
      delete postdata.bus_id;
      return this.$service.post(url, postdata, {
        headers: {'Content-Type': 'application/x-www-form-urlencoded'}
      }).then(res => {
        if (res.data.errorcode === 0) {
          this.$message.success(res.data.errormsg);        
          this.showEdit = false;
          this.getConfList()
        } else {
          this.$message.error(res.data.errormsg);
        }
      })
    },
    handleCurrentChange(v) {
      let post = {
        ...this.postData,
        page_no: v
      }
      this.getConfList(post)
    },
    openEdit(idx, row) {
      this.showEdit = true;
      this.editForm = {
        ...this.editForm,
        ...row
      }
    },
    openAdd() {
      this.showEdit = true;
      this.isAdd = true;
    },
    onDialogClose() {
      this.clearForm()
      if (this.isAdd) {
        this.isAdd = false;
      }
    },
    urlUtil(postData) {
      let postdata = new URLSearchParams();
      for (let item in postData) {
        if (postData[item] instanceof Array) {
          for (let i = 0; i < postData.length.length; i++) {
            postdata.append(String(item) + `[${i}]`, postData[item][i]);
          }
        } else {
          postdata.append(item, postData[item]);
        }
      }
      return postdata;
    },
    clearForm() {
      for(let item in this.editForm) {
        this.editForm[item] = ''
      }
    }
  }
}
</script>