<template>
  <div class="container">
    <header>
      <el-input @keyup.enter.native="search" placeholder="场馆名称" v-model="postData.bus_name" class="w-150 m-r-10"></el-input>
      <el-select @change="search" clearable v-model="postData.bus_type" placeholder="场馆类型" class="w-150 m-r-10">
        <!-- <el-option label="健身房" value="1"></el-option>
        <el-option label="瑜伽馆" value="2"></el-option>
        <el-option label="跆拳道馆" value="3"></el-option>
        <el-option label="武道馆" value="4"></el-option>
        <el-option label="舞蹈馆" value="5"></el-option>
        <el-option label="公立体育馆" value="6"></el-option>
        <el-option label="健康步道" value="7"></el-option>
        <el-option label="森林公园" value="8"></el-option>
        <el-option label="社区健身" value="9"></el-option>
        <el-option label="球类场馆" value="11"></el-option>
        <el-option label="其他" value="10"></el-option> -->
        <el-option label="健身房" value="1"></el-option>
        <el-option label="健身工作室" value="9"></el-option>
        <el-option label="体育馆" value="7"></el-option>
        <el-option label="游泳馆" value="8"></el-option>
        <el-option label="瑜伽馆" value="2"></el-option>
        <el-option label="跆拳道馆" value="3"></el-option>
        <el-option label="武道馆" value="4"></el-option>
        <el-option label="舞蹈馆" value="5"></el-option>
        <el-option label="其他" value="6"></el-option>
      </el-select>
      <el-select @change="search" clearable v-model="postData.city_id" placeholder="城市" class="w-150 m-r-10">
        <el-option v-for="item in cityList" :label="item.city_name" :value="item.city_id"></el-option>
      </el-select>
      <el-input @keyup.enter.native="search" placeholder="场馆地址" title='postData.bus_addr' v-model="postData.bus_addr" class="w-150 m-r-10"></el-input>
      <el-select @change="search" clearable v-model="postData.status" placeholder="状态" class="w-150 m-r-10">
        <el-option label="启用" value="1"></el-option>
        <el-option label="停用" value="0"></el-option>
      </el-select>
      <el-button type="success" icon="search" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column type="index" label="编号" width="77" align="center"></el-table-column>
      <el-table-column show-overflow-tooltip prop="bus_name" label="场馆名称" align="center">
        <template scope="scope">
        <el-button type="text" size="small">
          <router-link class='linkfont' :to="{name: 'gymEdit',params: {city_id: scope.row.city_id, c_bus_id: scope.row.c_bus_id}}">{{scope.row.bus_name}}</router-link>
        </el-button>
      </template>
      </el-table-column>
      <el-table-column prop="bus_type" label="场馆类型" align="center">
        <template scope="scope">
          {{scope.row.bus_type_name}}
        </template>
      </el-table-column>
      <el-table-column show-overflow-tooltip prop="bus_addr" label="场馆地址" align="center"></el-table-column>
      <el-table-column prop="is_lbs" label="LBS定位" align="center"></el-table-column>
      <el-table-column prop="card_num" label="健身卡数量" align="center"></el-table-column>
      <el-table-column prop="status" label="状态" align="center"></el-table-column>
      <el-table-column label="操作" align="center">
        <template scope="scope">
          <router-link class='editfont' :to="{name: 'gymEdit',params: {city_id: scope.row.city_id, c_bus_id: scope.row.c_bus_id}}">
            <el-button size="small" type="primary">编辑</el-button>
          </router-link>
      </template>
      </el-table-column>
    </el-table>
    <div class="pos-rel p-t-20 ovf-hd flexend">
      <div class="block">
        <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount">
        </el-pagination>
      </div>
    </div>
  </div>
</template>
<script>
import http from 'assets/js/http';
export default {
  name: 'gymControl',
  data() {
    return {
      postData: {
        channel_id: JSON.parse(localStorage.userInfo).data.channel_id,
        bus_name: '',
        bus_type: '',
        city_id: '',
        bus_addr: '',
        status: '',
        page_size: 10,
        page_no: 1
      },
      tableData: [],
      cityList: [],
      currentPage: 1,
      dataCount: 0
    };
  },
  methods: {
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.getgymList();
    },
    search() {
      this.postData.page_no = 1;
      this.getgymList();
    },
    getgymList() {
      let _this = this;
      this.apiPost('customer/business/getBusinessList', this.postData).then(res => {
        if (res.errorcode == 0) {
          _this.tableData = res.data.list;
          _this.dataCount = res.data.count;
        } else {
          _g.toastMsg('warning', res.errormsg);
        }
      });
    },
    getcityList() {
      let _this = this;
      let cityPost = {
        from: 1
      };
      this.apiPost('/customer/business/city', cityPost).then(res => {
        if (res.errorcode == 0) {
          _this.cityList = res.data.list;
        } else {
          _g.toastMsg('warning', res.errormsg);
        }
      });
    }
  },
  created() {
    this.getcityList();
    this.getgymList();
  },
  mixins: [http]
};
</script>
<style scoped>
.editfont {
  color: #fff;
  font-size: 14px;
}
.linkfont {
  color: #20a0ff;
  font-size: 14px;
}
</style>
