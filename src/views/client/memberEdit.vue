<template>
  <div>
    <div class="m-b-20 ovf-hd">
      <div class="headline">用户昵称：{{userinfo.nick_name}}</div>
      <div class="headline">用户手机号：{{userinfo.phone}}</div>
    </div>
    <el-row class="rowpara">
      <el-col :span="16"><div class="grid-content bg-grey">健身卡信息</div></el-col>
      <el-col :span="8" class="grid-content bg-grey">
        <template>
          <el-button type="text" @click="giftcardDialog = true" class='grid-height'>赠体验卡</el-button>
        </template>
      </el-col>
    </el-row>

    <el-table :data="tableData" stripe style="width: 100%">
        <el-table-column label="卡名称" prop="card_name" width="170" align="center">
          <template scope="scope">
            <div :class="{'is-gift':scope.row.card_from==1}">{{scope.row.card_name}}</div>
          </template>
        </el-table-column>
        <!-- <el-table-column label="卡号" prop="card_no" width="80" align="center">{{card_no}}</el-table-column> -->
        <el-table-column label="使用状态" width="170" align="center">
          <template scope="scope">
            <div class="inlineb">总计{{scope.row.total_num}},</div>
            <div class="inlineb">剩余{{scope.row.last_num}}</div>
          </template>
        </el-table-column>
        <el-table-column label="有效期" prop="valid_time" width="200" align="center">{{valid_time}}</el-table-column>
        <el-table-column label="验证码" prop="serial_num" width="170" align="center">{{serial_num}}</el-table-column>
        <el-table-column label="卡状态" prop="status" width="100" align="center">
          <template scope="scope">
            {{scope.row.status==0?'未激活':scope.row.status==1?'使用中':scope.row.status==2?'已过期':'已销卡'}}
          </template>
        </el-table-column>
        <el-table-column label="支付金额" prop="money" align="center"></el-table-column>
        <el-table-column label="详情" width="70" align="center">
          <template scope="scope">
            <el-button type="text" size="small">
              <router-link class='linkfont' :to="{name: 'cardDetail',params: {c_user_id: scope.row.c_user_id,card_user_id: scope.row.card_user_id,c_user_card_id: scope.row.c_user_card_id}}">详情</router-link>
            </el-button>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center">
          <template scope="scope">
            <el-select v-model="operation[scope.$index]" @change="cardOper(scope.$index,scope.row.c_user_card_id,scope.row.card_name)" placeholder="操作" size="mini" class="w-80">
              <el-option :disabled="scope.row.status==1||scope.row.status==3" label="销卡" value="1"></el-option>
            </el-select>
          </template>
        </el-table-column>
    </el-table>
    <div class="pos-rel p-t-20 ovf-hd flexend">
      <div class="block">
        <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount">
        </el-pagination>
      </div>
    </div>
    <!-- 赠体验卡弹窗 -->
    <el-dialog width="40%" title="赠送健身卡" :visible.sync="giftcardDialog">
      <el-form :model="gcPost" class="padrt">
        <el-form-item label="赠送卡种" label-width="100px">
          <el-select v-model="gcPost.card_id" clearable placeholder="请选择卡种">
            <el-option v-for="item in cardlist" :label="item.card_name" :value="item.card_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="备注" label-width="100px">
          <el-input type='textarea' v-model="gcPost.remark" auto-complete="off"></el-input>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="giftcardDialog = false">取 消</el-button>
        <el-button type="primary" @click="giftCard">确 定</el-button>
      </div>
    </el-dialog>
    <!-- 销卡弹窗 -->
    <el-dialog
      title="销卡"
      :visible.sync="cancelCardshow"
      width="40%"
      :before-close="handleClose"
      :show-close="false">
      <span>是否消除{{cardName}}健身卡</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="cancelcardWind">取 消</el-button>
        <el-button type="primary" @click="cancelCard">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>
<script>
import http from 'assets/js/http'
  export default {
    name: 'memberEdit',
    data() {
      return {
        postData: {
          c_user_id: '',
          page_size: 10,
          page_no: 1
        },
        userinfo: {},
        tableData: [],
        cardlist: [],
        giftcardDialog: false,
        gcPost: {
          c_user_id: '',
          card_id: '',
          remark: ''
        },
        operation: [],
        dataCount: 0,
        cancelCardshow: false,
        cardName: '',
        ccPost: {
          c_user_card_id: '',
          action: 1
        }
      }
    },
    methods: {
      cancelcardWind() {
        this.cancelCardshow = false;
        for(let i=0;i<this.operation.length;i++){
          if(this.operation[i]=='1'){
            this.$set(this.operation,i,'');
          }
        }
      },
      cancelCard() {
        let _this = this;
        this.apiPost('/customer/users/card_operate',this.ccPost).then((res) => {
          if(res.errorcode ==0 ){
            _this.postData.page_no = 1;
            _g.toastMsg('success',res.errormsg);
            _this.getuserInfo();
          }else{
            _g.toastMsg('warning',res.errormsg);
          }
        })
        this.cancelCardshow = false;
        for(let i=0;i<this.operation.length;i++){
          if(this.operation[i]!=''){
            this.$set(this.operation,i,'');
          }
        }
      },
      cardOper(index,valid,valname) {
        if(this.operation[index]==1){
          this.cardName = valname;
          this.cancelCardshow = true;
          this.ccPost.c_user_card_id = valid;
        }
      },
      giftCard() {
        let _this = this;
        this.gcPost.c_user_id = this.postData.c_user_id;
        this.apiPost('/Customer/card/present', this.gcPost).then((res) => {
          if(res.errorcode ==0 ){
            _this.postData.page_no = 1;
            _g.toastMsg('success',res.errormsg);
            _this.getuserInfo();
          }else{
            _g.toastMsg('warning',res.errormsg);
          }
        })
        this.giftcardDialog = false;
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.getuserInfo();
      },
      getuserInfo() {
        let _this = this;
        this.apiPost('/Customer/users/card_list', this.postData).then((res) => {
          if(res.errorcode ==0 ){
            _this.dataCount = res.data.count;
            _this.tableData = res.data.card;
            for(let i=0;i<_this.tableData.length;i++){
              _this.$set(_this.operation,i,'');
            }
            _this.userinfo = res.data.user;
            _this.cardlist = res.data.card_type;
          }else{
            _g.toastMsg('warning',res.errormsg);
          }
        })
      }
    },
    created() {
      this.postData.c_user_id = this.$route.params.c_user_id;
      this.getuserInfo();
    },
    mixins: [http]

  }
</script>
<style scoped>
.headline {
  display: inline-block;
  font-size: 14px;
  margin-right: 30px;
  color: #1f2d3d;
}
.inlineb {
  display: inline-block;
}
.editfont {
  color: #fff;
  font-size: 14px;
}
.linkfont {
  color: #20a0ff;
  font-size: 14px;
}
.bg-grey {
  background: #eef1f6;
}
.rowpara {
  border-top: 1px solid #dfe6ec;
  border-left: 1px solid #dfe6ec;
  border-right: 1px solid #dfe6ec;
}
.grid-content {
  height: 40px;
  box-sizing: border-box;
  font-size: 14px;
  color: #1f2d3d;
  vertical-align: middle;
  text-align: center;
  font-weight: bold;
  line-height: 40px;
}
.grid-height {
  height: 40px;
  box-sizing: border-box;
  font-size: 14px;
  vertical-align: middle;
  text-align: center;
  font-weight: bold;
}
.padrt {
  padding-right: 50px;
}
.is-gift:before {
  position: absolute;
  content: '';
  left: 0;
  top: 0;
  width: 25px;
  height: 25px;
  background: url(../../assets/images/gift_title.png) no-repeat;
}
</style>
