
<style lang="less" scoped>
a {
  color: #409eff;
}
</style>

<template>
  <div class="container news-manage">
    <header>
      <el-input style="width: 300px" placeholder="活动名称" @keydown.enter.native="getList" v-model="title"></el-input>
      <ChannelList v-model="channel_id"></ChannelList>
      <el-button type="success" @click="getList">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe ref="table">
      <el-table-column align="center" label="编号" prop="id"></el-table-column>
      <el-table-column align="center" label="区域" prop="channel_name">
        <template scope="scope">
          {{scope.row.channel_id == 0 ? '全部区域' : scope.row.channel_name}}
        </template>
      </el-table-column>
      <el-table-column align="center" label="活动名称" prop="title"></el-table-column>
      <el-table-column align="center" label="活动时间" prop="start_time">
        <template scope="scope">
          {{formatDate(scope.row.start_time)}} ~ {{formatDate(scope.row.end_time)}}
        </template>
      </el-table-column>
      <el-table-column align="center" label="报名时间" prop="registration_end_time">
        <template scope="scope">
          {{formatDate(scope.row.registration_start_time)}} ~ {{formatDate(scope.row.registration_end_time)}}
        </template>
      </el-table-column>
      <el-table-column align="center" label="已报名/可报名" prop="enrolment">
        <template scope="scope">
          <router-link :to="{path: '/c/activityMember', query: { id: scope.row.id }}">{{scope.row.enrolment}}/{{scope.row.enrolment_limit}}</router-link>
        </template>
      </el-table-column>
      <el-table-column align="center" label="状态" prop="status">
        <template scope="scope">
          <el-switch v-model="scope.row.status" active-text="开启" active-color="#13ce66" inactive-text="关闭" :active-value="1" :inactive-value="0" @change="handleSwitch(scope.row.id, $event)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作">
        <template scope="scope">
          <el-button size="small" @click="handleEdit(scope.row.id)" type="primary">编辑</el-button>
          <el-button size="small" type="danger" @click="handleDelete(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
        <el-button type="success" @click="showModal = true" size="small" icon="el-icon-plus">
          发布活动
        </el-button>
        <!-- <el-dropdown size="small" @command="handleDropdown" placement="top" type="info"><el-button type="info" size="small" style="margin-left: 20px">
            其他操作
            <i class="el-icon-arrow-down el-icon--right"></i></el-button><el-dropdown-menu slot="dropdown"><el-dropdown-item command="0">批量删除</el-dropdown-item></el-dropdown-menu></el-dropdown> -->
      </div>
      <el-pagination layout="total, prev, pager, next, sizes"
                     background @size-change="sizeChange" @current-change="getList" :page-size="page_size" :current-page.sync="page_no" :page-sizes="[10, 20, 30, 40]" :total="total"></el-pagination>
    </footer>
    <el-dialog :title="modalTitle" :visible.sync="showModal" :close-on-click-modal="false">
      <el-form :model="detail" :rules="rules" ref="form" label-width="100px">
        <el-form-item label="活动名称" prop="title">
          <el-input v-model="detail.title" placeholder="标题限制50个字符"></el-input>
        </el-form-item>
        <el-form-item label="活动时间" prop="time">
          <el-date-picker type="datetimerange" v-model="detail.time" value-format="yyyy-MM-dd HH:mm" :picker-options="pickerOptions"></el-date-picker>
        </el-form-item>
        <el-form-item label="报名时间" prop="registrationTime">
          <el-date-picker type="datetimerange" v-model="detail.registrationTime" value-format="yyyy-MM-dd HH:mm" :picker-options="pickerOptions"></el-date-picker>
        </el-form-item>
        <el-form-item label="可报名人数" prop="enrolment_limit">
          <el-input-number v-model="detail.enrolment_limit" :min="1"></el-input-number>
        </el-form-item>
        <el-form-item label="活动地点" prop="address">
          <el-input v-model="detail.address"></el-input>
        </el-form-item>
        <el-form-item label="顺序" prop="sort">
          <el-input-number :min="0" v-model="detail.sort"></el-input-number>
        </el-form-item>
        <el-form-item label="活动封面" prop="photo">
          <ImgUploader v-model="detail.photo"></ImgUploader>
        </el-form-item>
        <el-form-item label="活动介绍" prop="content">
          <Editor v-model="detail.content"></Editor>
        </el-form-item>
      </el-form>
      <footer slot="footer">
        <el-button type="success" @click="handleSave">保存</el-button>
        <el-button type="info" @click="showModal = false">取消</el-button>
      </footer>
    </el-dialog>
  </div>
</template>

<script>
import Pager from '../../mixins/pager';
import ImgUploader from '../../components/form/imgUploader';
import Editor from '../../components/form/Editor';
import ChannelList from 'src/components/form/channelList';
export default {
  name: 'activityManage',
  mixins: [Pager],
  components: { ImgUploader, Editor, ChannelList },
  data() {
    return {
      title: '',
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now() - 86400000;
        }
      },
      channel_id: JSON.parse(localStorage.userInfo).data.channel_id,
      user_channel_id: JSON.parse(localStorage.userInfo).data.channel_id,
      tableData: [],
      modalTitle: '添加',
      showModal: false,
      detail: {
        title: '',
        time: '',
        registrationTime: '',
        enrolment_limit: '',
        photo: '',
        content: '',
        address: '',
        sort: ''
      },
      rules: {
        title: [{ required: true, message: '请填写活动名称', trigger: 'blur' }],
        address: [{ required: true, message: '请填写活动地址', trigger: 'blur' }],
        sort: [{ required: true, message: '请填写活动展示顺序', trigger: 'blur' }],
        time: [{ required: true, message: '请选择活动时间', trigger: 'change' }],
        registrationTime: [{ required: true, message: '请选择报名时间', trigger: 'change' }],
        enrolment_limit: [{ required: true, message: '请填写可报名人数', trigger: 'blur' }],
        photo: [{ required: true, message: '请上传活动封面', trigger: 'change' }],
        content: [{ required: true, message: '请填写活动介绍', trigger: 'change' }]
      }
    };
  },
  created() {
    this.getList();
  },
  watch: {
    showModal(val) {
      if (!val) {
        this.detail = {};
      }
    }
  },
  methods: {
    handleSwitch(id, status) {
      const url = '/customer/activity/switching_state';
      this.$service
        .post(url, { id, status })
        .then(res => {
          if (res.data.errorcode === 0) {
          } else {
            this.$message.error(res.data.errormsg);
            this.getList();
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    formatDate(time) {
      return _g.formatDate(new Date(time * 1000), 'yyyy-MM-dd HH:mm');
    },
    handleDelete(id) {
      this.$confirm('确认要删除该活动吗?', '删除活动', {
        confirmButtonText: '删除',
        type: 'error'
      }).then(() => {
        this.deleteActivity(id);
      });
    },
    deleteActivity(id) {
      const url = '/customer/activity/del_acts';
      this.$service
        .post(url, { id })
        .then(res => {
          if (res.data.errorcode === 0) {
            this.getList();
            this.$message.success('删除成功');
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    handleEdit(id) {
      const detail = this.tableData.find(item => item.id == id);
      this.detail = {
        ...detail,
        ...{
          time: [new Date(detail.start_time * 1000), new Date(detail.end_time * 1000)],
          registrationTime: [
            new Date(detail.registration_start_time * 1000),
            new Date(detail.registration_end_time * 1000)
          ]
        }
      };
      this.showModal = true;
    },
    handleSave() {
      this.$refs['form'].validate(valid => {
        if (!valid) return false;
        let url = '/customer/activity/add_act';
        if (this.detail.id) {
          url = '/customer/activity/edit_act';
        }
        const postData = {
          ...this.detail,
          ...{
            channel_id: this.user_channel_id,
            user_id: Lockr.get('userInfo').id,
            start_time: this.detail.time[0],
            end_time: this.detail.time[1],
            registration_start_time: this.detail.registrationTime[0],
            registration_end_time: this.detail.registrationTime[1]
          }
        };
        delete postData.time;
        delete postData.registrationTime;
        this.$service
          .post(url, postData)
          .then(res => {
            if (res.data.errorcode === 0) {
              this.showModal = false;
              this.getList();
              this.$message.success(res.data.errormsg);
            } else {
              this.$message.error(res.data.errormsg);
            }
          })
          .catch(err => {
            console.error(err);
          });
      });
    },
    getList() {
      const url = '/customer/activity/index';
      const { page_no, page_size, title, channel_id } = this;
      const postData = {
        page_no,
        page_size,
        title,
        channel_id
      };
      this.$service
        .post(url, postData)
        .then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.total = data.count;
            this.tableData = data.list;
          } else {
            this.$message.error(res.data.errormsg);
          }
        })
        .catch(err => {
          console.error(err);
        });
    },
    handlePost() {}
  }
};
</script>
