<template>
  <div>
    <div class="m-b-20 ovf-hd">
      <div class="fl ">
        <el-date-picker
          v-model="rangeArray"
          type="daterange"
          @change="changeFn"
          placeholder="选择日期">
          </el-date-picker>
        <el-select v-model="postData.city_name" clearable filterable placeholder="城市">
          <el-option v-for="city in citiesList"
            :value="city.city_name" :label="city.city_name" :key="city.city_id"></el-option>
        </el-select>
        <el-select
          v-model="postData.card_id"
          filterable
          clearable
          allow-create
          placeholder="健身卡名称">
          <el-option
            v-for="item in tableCard"
            :key="item.card_id"
            :value="item.card_id"
            :label="item.card_name">

          </el-option>
        </el-select>
        <el-button type="primary" icon="search"  @click="search">查询</el-button>
      </div>
    </div>
    <el-table :data="tableData" :summary-method="getSummaries" show-summary stripe>
      <el-table-column align="center" prop="stat_day" label="日期"></el-table-column>
      <el-table-column align="center" prop="city_name" label="城市"></el-table-column>
      <el-table-column align="center" prop="cu_card_nick" label="卡名称"></el-table-column>
      <el-table-column align="center" prop="get_card_num" label="领卡次数"></el-table-column>
      <el-table-column align="center" prop="active_card_num" label="激活次数"></el-table-column>
      <el-table-column align="center" prop="active_buss_num" label="激活场馆数"></el-table-column>
      <el-table-column align="center" prop="transform_ratio" label="激活转化率"></el-table-column>
      <el-table-column align="center" prop="buy_amount" label="购卡金额"></el-table-column>
    </el-table>
    <div class="pos-rel p-t-20">
      <div class="block pages">
        <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import http from 'assets/js/http';
export default {
  name: 'statisticsCard',
  data() {
    return {
      rangeArray: [new Date(new Date().setDate(1)).toJSON().slice(0, 10), new Date()],
      tableCard: '',
      tableData: [],
      tableTotal: [],
      citiesList: [],
      postData: {
        start_time: new Date(new Date().setDate(1)).toJSON().slice(0, 10), //（开始时间）
        end_time: new Date(), // (结束时间)
        city_name: '', //城市名
        card_id: '', //c端卡id
        page_no: 1, //页码
        pgae_size: '' //每页条数
      },
      dataCount: 0
    };
  },
  methods: {
    getCardList() {
      this.apiPost('customer/statistics/card', this.postData).then(res => {
        if (res.errorcode == 0) {
          this.dataCount = res.data.count;
          this.tableData = res.data.list;
          this.tableCard = res.data.card;
          this.tableTotal = res.data.total;
        } else {
          _g.toastMsg('success', res.errormsg);
        }
      });
    },
    getCityList() {
      this.apiPost('customer/business/city', {
        from: 2
      }).then(res => {
        if (res.errorcode == 0) {
          this.citiesList = res.data.list;
          // this.postData.city_id = res.data.list[0].city_id;
          // this.postData.city_name = res.data.list[0].city_name;
        }
      });
    },
    getSummaries() {
      if (this.tableTotal == '') {
        return this.tableTotal;
      } else {
        let array = Object.values(this.tableTotal[0]);
        let newArray = array.splice(0, 0, '合计', '-', '-');
        return array;
      }
    },
    changeFn(value) {
      let sTime = value[0];
      let eTime = value[1];
      this.postData.start_time = _g.formatDate(sTime, 'yyyy-MM-dd');
      this.postData.end_time = _g.formatDate(eTime, 'yyyy-MM-dd');
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.getCardList();
    },
    search() {
      this.postData.page_no = 1;
      this.getCardList();
    }
  },
  created() {
    const date = new Date();
    date.setDate(1);
    this.postData.start_time = _g.formatDate(date, 'yyyy-MM-dd');
    this.postData.end_time = _g.formatDate(new Date(), 'yyyy-MM-dd');
    this.getCardList();
    this.getCityList();
  },
  mixins: [http]
};
</script>
