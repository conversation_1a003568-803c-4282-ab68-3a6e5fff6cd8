<template>
  <div class="m-b-20 ovf-hd">
    <header class="fl">
      <el-form ref="form" :model="postData" inline>
        <el-form-item>
          <el-input placeholder="商户号" v-model="postData.keyword"></el-input>
        </el-form-item>
        <el-form-item>
          <el-input placeholder="场馆ID" v-model="postData.bus_id"></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="success" @click="getParamsList(search=true)">查询</el-button>
          <el-button type="primary" @click="openAdd">新增参数</el-button>
        </el-form-item>
      </el-form>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column align="center" prop="orgid" label="父商户号"></el-table-column>
      <el-table-column align="center" prop="cusid" label="商户号"></el-table-column>
      <el-table-column align="center" prop="appid" label="appid"></el-table-column>
      <el-table-column align="center" prop="appkey" label="appkey"></el-table-column>
      <el-table-column align="center" prop="bus_name" label="场馆名称"></el-table-column>
      <el-table-column align="center" prop="bus_id" label="场馆ID"></el-table-column>
      <el-table-column align="center" label="操作">
        <template scope="scope">
          <el-button type="text" @click="openEdit(scope.$index, scope.row)">修改参数</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pos-rel p-t-20 ovf-hd flexend">
      <div class="block">
        <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount">
        </el-pagination>
      </div>
    </div>
    <el-dialog :title="isAdd?'新增参数':'修改参数'" :visible.sync="showEdit" :close-on-click-modal="false">
      <el-form :model="editForm" ref="editForm" label-width="120px">
        <el-form-item label="场馆ID" prop="bus_id" :rules="{required: true, message: '请输入场馆ID'}">
          <el-input :disabled="!isAdd" placeholder="场馆ID" v-model="editForm.bus_id"></el-input>
        </el-form-item>
        <el-form-item label="父商户号" prop="orgid" :rules="{required: true, message: '请输入父商户号'}">
          <el-input placeholder="父商户号" v-model="editForm.orgid"></el-input>
        </el-form-item>
        <el-form-item label="商户号" prop="cusid" :rules="{required: true, message: '请输入商户号'}">
          <el-input placeholder="请输入商户号" v-model="editForm.cusid"></el-input>
        </el-form-item>
        <el-form-item label="appID" prop="appid" :rules="{required: true, message: '请输入appID'}">
          <el-input placeholder="appID" v-model="editForm.appid"></el-input>
        </el-form-item>
        <el-form-item label="appKey" prop="appkey" :rules="{required: true, message: '请输入appKey'}">
          <el-input placeholder="appKey" v-model="editForm.appkey"></el-input>
        </el-form-item>
        <el-form-item style="text-align:center;">
          <el-button :loading=isLoading type="success" style="transform:translateX(-60px);" @click="onSubmitClick">{{isAdd?'确认新增':'确认修改'}}</el-button>
        </el-form-item>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
export default {
  name: 'posRegister',
  data() {
    return {
      showEdit: false,
      isLoading: false,
      isAdd: false,
      editForm: {
        bus_id: '',
        orgid: '',
        cusid: '',
        appid: '',
        appkey: '',
      },
      dataCount: 0,
      postData: {
        page_no: 1,
        page_size: 10,
        keyword: '',
        bus_id: ''
      },
      tableData: [],
    }
  },
  created() {
    this.getParamsList();
  },
  methods: {
    openEdit(index, row) {
      this.isAdd = false;
      this.editForm = { ...row };
      this.showEdit = true;
    },
    openAdd() {
      for (let item in this.editForm) {
        this.editForm[item] = '';
      }
      this.isAdd = true;
      this.showEdit = true;
    },
    async onSubmitClick() {
      if (this.isAdd) {
        await this.onAdd();
        await this.getParamsList();
        this.showEdit = this.isLoading = false;
      } else {
        await this.onEdit();
        await this.getParamsList();
        this.showEdit = this.isLoading = false;
      }
    },
    getParamsList(search) {
      if (search) {
        this.postData.page_no = 1;
      }
      const url = '/web/TLPos/pay_conf_list';
      let params = new URLSearchParams();
      for (let item in this.postData) {
        params.append(item, this.postData[item])
      }
      return this.$service.post(url, params, {
        headers: {'Content-Type': 'application/x-www-form-urlencoded'}
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.tableData = res.data.data.list;
          this.dataCount = res.data.data.count;
        } else {
          this.$message.error(res.data.errormsg);
        }
      })
    },
    handleCurrentChange(val) {
      this.postData.page_no = val;
      this.getParamsList();
    },
    async onEdit() {
      const url = '/web/TLPos/pay_conf_edit';
      let checkForm = await this.$refs.editForm.validate();
      this.isLoading = checkForm;
      if (!checkForm) return;
      let params = new URLSearchParams();
      for (let item in this.editForm) {
        params.append(item, this.editForm[item])
      }
      return this.$service.post(url, params, {
        headers: {'Content-Type': 'application/x-www-form-urlencoded'}
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.$message.success(res.data.errormsg);
        } else {
          this.$message.error(res.data.errormsg);
        }
      })
    },
    async onAdd() {
      const url = '/web/TLPos/pay_conf_add';
      let checkForm = await this.$refs.editForm.validate();
      this.isLoading = checkForm;
      if (!checkForm) return;
      delete this.editForm.bus_name;
      delete this.editForm.id;
      let params = new URLSearchParams();
      for (let item in this.editForm) {
        params.append(item, this.editForm[item])
      }
      return this.$service.post(url, params, {
        headers: {'Content-Type': 'application/x-www-form-urlencoded'}
      }).then(res => {
        if (res.data.errorcode == 0) {
          this.$message.success(res.data.errormsg);
        } else {
          this.$message.error(res.data.errormsg);
        }
      })
    },
  }
}
</script>