<template>
  <div class="container" ref="container">
    <header>
      <el-date-picker v-model="rangeArray" class="m-r-10" type="daterange" @change="changeDate"
                      value-format="yyyy-MM-dd" placeholder="选择日期">
      </el-date-picker>
      <el-input placeholder="场馆名称" @keyup.enter.native="search" v-model="postData.bus_name"
                class="w-150 m-r-10"></el-input>
      <el-input placeholder="设备ID" @keyup.enter.native="search" v-model="postData.device_id"
                class="w-150 m-r-10"></el-input>
      <DeviceType v-model="postData.device_type" class="w-150 m-r-10">
        <el-option value="" label="设备类型"></el-option>
      </DeviceType>
      <el-select style="width: 150px" v-model="postData.order" clearable placeholder="通过率排序">
        <el-option value="asc" label="通过率正序"></el-option>
        <el-option value="desc" label="通过率倒序"></el-option>
      </el-select>
      <el-checkbox style="margin-left: 15px" v-model="postData.only_increment" label="只看新增"></el-checkbox>
      <el-button type="primary"
                 icon="search"
                 @click="search">搜索
      </el-button>
    </header>
    <el-table ref="table" :data="tableData" stripe style="width: 100%">
      <el-table-column type="index" label="序号" width="100" align="center"></el-table-column>
      <el-table-column prop="bus_name" label="场馆名称" width="200" align="center"></el-table-column>
      <el-table-column prop="device_id" label="设备ID" width="150" align="center"></el-table-column>
      <el-table-column prop="device_name" label="设备名称" width="200" align="center"></el-table-column>
      <el-table-column prop="date" label="日期" width="220" align="center"></el-table-column>
      <el-table-column prop="requestCount" label="请求总数" width="120" align="center"></el-table-column>
      <el-table-column prop="rate" label="成功率" width="140" align="center">
        <template scope="scope">
          <span>{{scope.row.rate?scope.row.rate:'-'}}</span>
        </template>
      </el-table-column>
    </el-table>

    <footer>
      <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next"
                     :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
      </el-pagination>
    </footer>

    <div class="graph-frame">
      <div class="search-line">
        <el-select v-model="devIds" class="w-300 m-r-10 m-l-20" multiple filterable placeholder="请选择">
          <el-option
            v-for="item in devList"
            :key="item.device_id"
            :label="item.device_name"
            :value="item.device_id">
          </el-option>
        </el-select>
        <el-button type="primary"
                   icon="search"
                   class="m-l-20"
                   @click="genGraph">生成图表
        </el-button>
        <el-button type="success" @click="exportTable">导出Excel</el-button>
        <Export ref="export"></Export>
      </div>
      <div class="m-t-10" v-if="option.series.length>0">
        <div id="devStat" :style="{width: '90%', height: '500px'}">
        </div>

      </div>
    </div>

  </div>
</template>

<script>
  import http from 'assets/js/http';
  import DeviceType from 'src/components/form/deviceType';
  import echarts from 'echarts';
  import Export from 'src/components/Export'
  import { formatDate } from 'src/assets/js/utils'

  export default {
    name: 'devStat',
    components: { DeviceType, Export },
    data() {
      return {
        width: '100%',
        height: '600px',
        postData: {
          device_id: '',
          device_type: '',
          bus_name: '',
          begin_time: '',
          end_time: '',
          order: '',
          only_increment: false,
          page_size: 10,
          page_no: 1
        },
        rangeArray: [this.getDate(), this.getDate()],
        tableData: [],
        dataCount: 0,
        devIds: [],
        devList: [],
        option: {
          title: {
            text: '设备通过率(%)'
          },
          tooltip: {
            trigger: 'axis',
            formatter: function (params) {
              var result = params[0].name + '<br />';
              params.forEach(function (item) {
                if (item.value) {
                  result += item.marker + " " + item.seriesName + " : " + item.value + '%' + "</br>";
                } else {
                  result += item.marker + " " + item.seriesName + " : " + '-' + "</br>";
                }

              });
              return result;
            }
          },
          legend: {
            left: '14%',
            data: []
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          toolbox: {
            feature: {
              saveAsImage: {}
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: []
          },
          yAxis: {
            axisLabel: {
              formatter: '{value}'
            }
          },
          series: []
        }
      };
    },

    methods: {
      genGraph(isExport = false) {
        let postd = {
          begin_time: this.postData.begin_time ? this.postData.begin_time : this.rangeArray[0],
          end_time: this.postData.end_time ? this.postData.end_time : this.rangeArray[1],
          device_ids: this.devIds.join(','),
          device_id: this.postData.device_id,
          device_type: this.postData.device_type,
          bus_name: this.postData.bus_name,
        }
        return this.apiPost('/device/Statistics/deviceAdoptStatistics', postd).then(res => {
          if (res.errorcode == 0) {
            this.option.series = res.data.data;

            let legend = [];
            this.option.series.forEach((ele, i) => {
              ele.data.forEach((ele, j) => {
                this.option.series[i].data[j] = ele.replace(/%/g, "");
              })
              this.$set(ele, 'type', 'line');
              this.$set(ele, 'showAllSymbol', true)
              legend.push(ele.name);
            })
            this.option.xAxis.data = res.data.date;
            this.option.legend.data = legend;
            this.$nextTick(() => {
              let main = document.getElementById("devStat");
              if (echarts.getInstanceByDom(main)) {
                echarts.dispose(main);
              }
              const myChart = echarts.init(document.querySelector('#devStat'));
              setTimeout(() => {
                myChart.setOption(this.option);
              })
            })
            return res.data;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      gettableList() {
        this.apiPost('/device/Statistics/deviceStatistics', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.tableData = res.data.lists;
            this.dataCount = res.data.count;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      getdevList() {
        this.apiPost('/device/Statistics/getDeviceLists').then(res => {
          if (res.errorcode == 0) {
            this.devList = res.data;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      getDate() {
        let d = new Date()
        return `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()}`
      },
      changeDate(value) {
        const [s, e] = value || [];
        this.postData.begin_time = s
        this.postData.end_time = e
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.gettableList();
      },
      search() {
        this.postData.page_no = 1;
        this.gettableList();
      },
      getExportData() {
        return this.apiPost('/device/Statistics/deviceStatistics', this.postData).then(res => {
          if (res.errorcode == 0) {
            return res.data.lists;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      async exportTable() {
        const res = await this.genGraph();
        const resData = res.data;
        let data = []
        for (let item of resData) {
          for (let [index, day] of res.date.entries()) {
            data.push({
              date: day,
              data: item.data[index],
              bus_name: item.bus_name,
              device_id: item.device_id,
              name: item.name
            })
          }
        }
        const columns = [
          {
            title: '设备名',
            key: 'name'
          }, {
            title: '场馆名',
            key: 'bus_name'
          }, {
            title: '设备id',
            key: 'device_id'
          }, {
            title: '日期',
            key: 'date'
          }, {
            title: '通过率',
            key: 'data'
          },
        ]
        this.$refs.export.export({
          filename: '设备通过率',
          data,
          columns
        })
      }
    },
    mounted() {
      this.width = this.$refs.container.getBoundingClientRect().right - this.$refs.container.getBoundingClientRect().left;
      this.height = this.width * 2 / 3 + 'px';
      this.width = this.width + 'px';
    },
    created() {
      const date = formatDate(new Date(), 'yyyy-MM-dd')
      const { begin_time = date, end_time = date, only_increment, type } = this.$route.params;
      this.rangeArray = [begin_time, end_time]
      this.postData.begin_time = begin_time;
      this.postData.end_time = end_time;
      this.postData.device_type = +type || '';
      this.postData.only_increment = !!only_increment;
      this.gettableList();
      this.getdevList();
    },
    mixins: [http]
  };
</script>

<style scoped>
  .w100 {
    width: 100%;
  }

  .search-line {
    min-height: 80px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border-top: 1px solid #ececec;
    border-bottom: 1px solid #ececec;
  }
</style>








