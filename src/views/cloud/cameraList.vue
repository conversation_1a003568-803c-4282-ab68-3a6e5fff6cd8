<template>

  <div class="container">
    <header>
      <div class="fl">
        <el-button type="success" size="small" icon="el-icon-plus"  @click="$router.push({ name: 'cameraAdd'})">
          新增
        </el-button>
      </div>
      <div class="fl m-l-30">
        <BusSelect class="w-150 m-r-10" v-model="postData.bus_id" />
        <el-input placeholder="设备SN" @keyup.enter.native="search" v-model="postData.device_sn" class="w-150 m-r-10"></el-input>
        <el-button type="primary" icon="search" @click="search">搜索</el-button>
      </div>
    </header>

    <el-table :data="tableData" id="infotable" stripe style="width: 100%">
      <el-table-column align="center" type="index" label="编号" width="80">
        <template scope="scope">
          {{scope.$index + postData.page_size*(postData.page_no-1) + 1}}
        </template>
      </el-table-column>
      <el-table-column prop="bus_name" label="场馆名称" width="100" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.bus_name ? scope.row.bus_name: '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="device_sn" label="设备SN" width="120" align="center"></el-table-column>
      <el-table-column prop="device_name" label="设备名称" width="120" align="center"></el-table-column>
      <el-table-column prop="remark" label="备注" align="center"></el-table-column>
      <el-table-column prop="created_time" label="添加时间" align="center"></el-table-column>
      <el-table-column prop="updated_time" label="最后编辑时间" align="center"></el-table-column>
      <el-table-column label="操作" align="center">
        <template scope="scope">
          <div>
            <router-link class='editfont m-r-5' :to="{name: 'cameraAdd',query: { id: scope.row.id }}">
              <el-button size="small" type="primary">编辑</el-button>
            </router-link>
            <router-link class='editfont m-r-5' :to="{name: 'cameraRecord',query: {device_sn: scope.row.device_sn}}">
              <el-button size="small" type="primary">操作记录</el-button>
            </router-link>
            <router-link class='editfont m-r-5' :to="{name: 'cameraLog',query: {device_sn: scope.row.device_sn}}">
              <el-button size="small" type="primary">设备日志</el-button>
            </router-link>
            <el-button size="small" @click="confirmDelete(scope.row)" type="danger">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <footer>
      <div class="left">
      </div>
        <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next, sizes" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount" :page-sizes="[10, 20, 50, 100]" @size-change="handleSizeChange">
        </el-pagination>
    </footer>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  import BusSelect from 'src/components/Common/BusSelect.vue'
  export default {
    name: 'cameraList',
    data() {
      return {
        postData: {
          device_sn: '',
          bus_id: '',
          page_size: 10,
          page_no: 1
        },
        tableData: [],
        dataCount: 0,
      };
    },
    components: {
      BusSelect
    },
    methods: {     
       confirmDelete(item) {
        this.$confirm('确认删除?', '提示',)
          .then(() => {
            _g.openGlobalLoading();
            this.apiGet('/device/CameraDevice/delete_device', { camera_device_id: item.id }).then(res => {
              _g.closeGlobalLoading();
              if (res.errorcode == 0) {
                _g.toastMsg('success', '操作成功');
                this.gettableList()
              } else {
                _g.toastMsg('warning', res.errormsg);
              }
            });
          })
          .catch(() => {
            // handle error
          });
      }, 
      handleSizeChange(val) {
        this.postData.page_size = val
        this.postData.page_no = 1
        this.gettableList()
      },
      gettableList() {
        this.apiGet('/device/CameraDevice/get_device_list', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.tableData = res.data.list;
            this.dataCount = res.data.count;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.gettableList();
      },
      search() {
        this.postData.page_no = 1;
        this.gettableList();
      }
    },
    created() {
      this.gettableList();
    },
    deactivated() {
    },
    mixins: [http]
  };
</script>

<style lang="less" scoped>
  
</style>








