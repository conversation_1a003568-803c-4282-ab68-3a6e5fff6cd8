<template>

  <div class="container">
    <div class="ovf-hd searchbar">
      <div class="fl m-l-30">
        <el-input placeholder="设备ID" @keyup.enter.native="search" v-model="postData.device_id" class="w-150 m-r-10 p-t-20"></el-input>
        <el-input placeholder="刷掌SN" @keyup.enter.native="search" v-model="postData.palmservice_sn" class="w-150 m-r-10 p-t-20"></el-input>
        <el-input placeholder="场馆名称" @keyup.enter.native="search" v-model="postData.bus_name" class="w-150 m-r-10 p-t-20"></el-input>
        <DeviceType v-model="postData.device_type_id" class="w-150 m-r-10 p-t-20">
          <el-option value="" label="设备类型"></el-option>
        </DeviceType>
        <el-input placeholder="渠道名称" @keyup.enter.native="search" v-model="postData.channel_name" class="w-150 m-r-10 p-t-20"></el-input>
        <el-select @change="search" clearable v-model="postData.assign_status" placeholder="划拨状态" class="w-150 m-r-10 p-t-20">
          <el-option label="已划拨" value="1"></el-option>
          <el-option label="未划拨" value="0"></el-option>
        </el-select>
        <el-select @change="search" clearable v-model="postData.device_status" placeholder="设备状态" class="w-150 m-r-10 p-t-20">
          <el-option label="启用" value="1"></el-option>
          <el-option label="关闭" value="0"></el-option>
        </el-select>
         <el-select @change="search" clearable v-model="postData.active_status" placeholder="活跃状态" class="w-150 m-r-10 p-t-20">
          <el-option label="活跃" value="1"></el-option>
          <el-option label="离线" value="0"></el-option>
        </el-select>
        <el-input placeholder="版本号" @keyup.enter.native="search" v-model="postData.version" class="w-150 m-r-10 p-t-20"></el-input>
        
        <el-button type="primary" icon="search" @click="search" class="m-t-20">搜索</el-button>
      </div>
    </div>

    <el-table :data="tableData" id="infotable" stripe style="width: 100%" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column align="center" type="index" label="编号" width="80">
        <template scope="scope">
          {{scope.$index + postData.page_size*(postData.page_no-1) + 1}}
        </template>
      </el-table-column>
      <el-table-column prop="bus_name" label="场馆名称" width="100" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.bus_name ? scope.row.bus_name: '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="device_id" label="设备ID" width="120" align="center"></el-table-column>
      <el-table-column prop="palmservice_sn" label="刷掌SN" width="120" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.palmservice_sn ? scope.row.palmservice_sn: '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="device_type_name" label="设备类型" width="100" align="center"></el-table-column>
      <el-table-column prop="device_name" label="设备名称" width="120" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.device_name ? scope.row.device_name: '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="channel_name" label="渠道名称" width="100" align="center">
        <template slot-scope="scope">
           <el-link type="primary" @click="$router.push({ name: 'cloudChannel', query: { device_type_id: scope.row.device_type_id }})">{{scope.row.channel_name ? scope.row.channel_name: '--'}}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="assign_status" label="划拨状态" width="100" align="center">
        <template scope="scope">
          <span>{{ scope.row.assign_status == 1 ? '已划拨' : '未划拨'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="device_status" label="设备状态" width="80" align="center">
        <template scope="scope">
          <span>{{ scope.row.device_status == 1 ? '启用' : '关闭'}}</span>
        </template>
      </el-table-column>
       <el-table-column prop="active_status" label="活跃状态" width="80" align="center">
        <template scope="scope">
          <span>{{ scope.row.active_status == 1 ? '活跃' : '离线'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="device_vesion" label="设备版本号" width="100" align="center">
         <template slot-scope="scope">
          <el-link type="primary" @click="$router.push({ name: 'cloudVersion', query: { device_type_id: scope.row.device_type_id }})">{{scope.row.device_vesion}}</el-link>
        </template>
      </el-table-column>
      <el-table-column prop="device_fireward" label="固件版本号" width="100" align="center">
      </el-table-column>
      <el-table-column prop="exception_count" label="异常记录" width="70" align="center">
        <template slot-scope="scope">
          <el-link type="primary" @click="$router.push({ name: 'devBugDetail',query: {device_id: scope.row.device_id}})">{{scope.row.exception_count}}</el-link>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="ali_device_status" label="在线状态" width="100" align="center">
      </el-table-column> -->
      <el-table-column label="操作" align="center">
        <template scope="scope">
          <div>
            <router-link class='editfont m-r-5' :to="{name: 'devEdit',params: {id: scope.row.id, action: '0'}}">
              <el-button size="small" type="primary">编辑</el-button>
            </router-link>
            <router-link class='editfont m-r-5' :to="{name: 'logs',query: {device_id: scope.row.device_id}}">
              <el-button size="small" type="primary">日志查看</el-button>
            </router-link>
            
            <el-button size="small" v-if="scope.row.ali_device_status!='--'" @click="checkStatus(scope.$index,scope.row)" type="primary">检测状态</el-button>
             <router-link class='editfont m-r-5' :to="{name: 'devChange',params: {device_id: scope.row.device_id}}">
              <el-button size="small" type="primary">变更记录</el-button>
            </router-link>
             <!-- <router-link class='editfont m-r-5' :to="{name: 'devBugDetail',query: {device_id: scope.row.device_id}}">
              <el-button size="small" type="primary">异常记录</el-button>
            </router-link> -->
          </div>
          <div class="m-t-5">
            <router-link 
              class='editfont m-r-5' 
              v-if="scope.row.device_type_name.includes('中控')||scope.row.device_type_name.includes('水控')" 
              :to="{name: 'lockerArr',params: {id:scope.row.id, device_id: scope.row.device_id}}">
              <el-button size="small" type="primary">分配柜号</el-button>
            </router-link>
            <router-link 
              v-if="scope.row.device_type_name.includes('中控')||scope.row.device_type_name.includes('水控')"
              class="editfont m-r-5" 
              :to="{name: 'devOpenRecord', query: {device_id: scope.row.device_id}}">
              <el-button size="small" type="primary">开柜记录</el-button>
            </router-link>
            <el-button size="small" class="qricon" v-if="(scope.row.device_type_name.includes('中控')&&!scope.row.device_type_name.includes('Mini中控-nfc'))&&scope.row.assign_status == 1" @click="downloadQcAll(scope.row.bus_id,scope.row.device_id)" type="primary"></el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <footer>
      <div class="left">
      <el-dropdown @command="handleCommand">
        <el-button type="success">
          更多操作<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="1">渠道修改</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      </div>
        <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next, sizes" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount" :page-sizes="[10, 20, 50, 100]" @size-change="handleSizeChange">
        </el-pagination>
    </footer>
    <el-dialog :visible.sync="showQrcode" :width="qraddrNew ? '540px' : '280px'" :close="handleClose">
       <div class="qr-wrap">
        <div class="qr-con">
          <img class="image" :src="qraddr" width="240" />
          <p>运动生活管家</p>
          <a v-if="qraddr" download="运动生活管家" :href="qraddr">
            <el-button type="primary">印刷尺寸下载</el-button>
          </a>
        </div>
        <div class="qr-con" v-if="qraddrNew">
          <img class="image" :src="qraddrNew" width="240" />
          <p>勤鸟+</p>
          <a download="勤鸟+" :href="qraddrNew">
            <el-button type="primary">印刷尺寸下载</el-button>
          </a>
        </div>
      </div>
    </el-dialog>
    <el-dialog :visible.sync="showEditChannel" class="modal-form" width="300" :close="handleClose">
      <el-form ref="modalForm" :model="modalData" label-width="150px">
        <el-form-item label="设备渠道" prop="channel_id" :rules="{ required: true, message: '请选择设备渠道'}">
          <el-select clearable placeholder="请选择" v-model="modalData.channel_id" class="w-320">
            <el-option v-for="item in channelList" :label="item.channel_name" :value="item.id" :key="item.id"></el-option>
          </el-select>
        </el-form-item>
      </el-form>
      <footer slot="footer">
          <el-button type="primary" @click="onSubmit">保存</el-button>
          <el-button class="m-l-50" @click="showEditChannel=false">取消</el-button>
        </footer>
    </el-dialog>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  import DeviceType from 'src/components/form/deviceType';
  export default {
    name: 'devControl',
    data() {
      return {
        postData: {
          device_id: '',
          bus_name: '',
          device_type_id: '',
          palmservice_sn: '',
          channel_name: '',
          assign_status: '',
          device_status: '',
          version: '',
          page_size: 10,
          page_no: 1
        },
        modalData: {
          channel_id: '',
          device_ids: ''
        },
        devicetypeList: [],
        selectedDevice: [],
        channelList: [],
        tableData: [],
        dataCount: 0,
        showQrcode: false,
        showEditChannel: false,
        qraddrNew: '',
        qraddr: ''
      };
    },
    components: { DeviceType },
    computed: {
      selectedDeviceIds() {
        let ids = [];
        this.selectedDevice.forEach(item => {
          ids.push(item.device_id);
        });
        return ids;
      }
    },
    methods: {
      handleCommand(command) {
        if(command == 1) {
          this.editChannel()
        }
      },
      onSubmit() {
        this.modalData.device_ids = Array.isArray(this.selectedDeviceIds) ? this.selectedDeviceIds.join(',') : ''
        this.$refs.modalForm.validate(valid => {
          if (valid) {
            this.apiPost('/device/DeviceManagement/changeChannel', this.modalData).then(res => {
              if (res.errorcode == 0) {
                this.showEditChannel = false
                this.gettableList()
                _g.toastMsg('success', res.errormsg);
              } else {
                _g.toastMsg('warning', res.errormsg);
              }
            });
          }
        });
      },
      getchannelList(deviceTypeId) {
        this.apiGet('/device/deviceManagement/channelList', { device_type_id: deviceTypeId }).then(
          res => {
            if (res.errorcode == 0) {
              this.channelList = res.data.list;
              this.showEditChannel = true
            } else {
              _g.toastMsg('warning', res.errormsg);
            }
          }
        );
      },
      editChannel() {
        if (this.selectedDevice && this.selectedDevice.length < 1) {
          _g.toastMsg('warning', '请先勾选设备');
          return;
        } 
        let hasEqual = this.selectedDevice.find((element)=> this.selectedDevice[0].device_type_id !== element.device_type_id);
        if (hasEqual === undefined) {
          this.getchannelList(this.selectedDevice[0].device_type_id)
        } else {
          _g.toastMsg('warning', '设备类型不唯一');
          return;
        }
      },
      handleSelectionChange(val) {
        this.selectedDevice = val;
      },
      handleSizeChange(val) {
        this.postData.page_size = val
        this.postData.page_no = 1
        this.gettableList()
      },
      checkStatus(index,row) {
        this.apiGet('device/deviceManagement/get_device_status', { ali_device_id: row.ali_device_id, ali_device_apk: row.ali_device_apk }).then(res => {
          if (res.errorcode == 0) {
            this.tableData[index].ali_device_status = res.data.status;
            _g.toastMsg('success', '更新成功');
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      handleClose() {
        this.qraddr = '';
        this.qraddrNew = '';
        this.showQrcode = false;
      },
      downloadQcNew(busid, deviceid) {
        return this.apiPost('/device/MobileTerminal/get_bus_new_qn_qrcode', { device_id: deviceid, bus_id: busid }).then(res => {
          if (res.errorcode == 0) {
            this.qraddrNew = res.data;
          } else {
            this.qraddrNew = ''
          }
          return res
        });
      },
      downloadQc(busid, deviceid) {
        return this.apiPost('/device/MobileTerminal/get_bus_qrcode', { device_id: deviceid, bus_id: busid }).then(res => {
          if (res.errorcode == 0) {
            this.qraddr = res.data;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
          return res
        });
      },
      downloadQcAll(busid, deviceid) {
        Promise.all([this.downloadQc(busid, deviceid),this.downloadQcNew(busid, deviceid)]).then(res=> {
          this.showQrcode = true;
        })
      },
      getdevicetypeList() {
        this.apiGet('/device/deviceManagement/deviceTypeList').then(res => {
          if (res.errorcode == 0) {
            this.devicetypeList = res.data.list;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      gettableList() {
        this.apiGet('/device/deviceManagement/listData', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.tableData = res.data.list;
            this.dataCount = res.data.count;
            if(this.$route.query.bus_name) {
              this.$router.replace({name:'devControl'})
            }
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.gettableList();
      },
      search() {
        this.postData.page_no = 1;
        this.gettableList();
      }
    },
    created() {
      this.postData.bus_name = this.$route.query.bus_name;
      this.getdevicetypeList();
    },
    activated() {
      if(this.$route.query.bus_name) {
        this.postData.bus_name = this.$route.query.bus_name;
        this.postData.device_id = '';
        this.postData.device_type_id = '';
        this.postData.channel_name = '';
        this.postData.assign_status = '';
        this.postData.device_status = '';
        this.postData.version = '';
        this.postData.page_size = 10;
        this.postData.page_no = 1;
        this.gettableList();
      } else {
        this.gettableList();
      }
    },
    deactivated() {
    },
    mixins: [http]
  };
</script>

<style lang="less" scoped>
  .qr-wrap {
    width: 100%;
    margin: 0 auto;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .qr-con {
      border-right: 1px solid #000;
      display: flex;
      flex-direction: column;
      align-items: center;
      &:last-child {
        border-right: 0 none;
      }
    }
  }
  .m-r-5 {
    margin-right: 5px;
  }
  .editfont {
    color: #fff;
  }
  .searchbar {
    background-color: #fff;
    min-height: 80px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    border-bottom: 1px solid #ececec;
    padding-bottom: 20px;
  }
  .el-dialog__footer {
    padding: 0;
  }
  .qricon {
    border: none;
    width: 32px;
    height: 32px;
    background: url(../../assets/images/qrcode.png) no-repeat center;
    background-size: 32px 32px;
  }
</style>








