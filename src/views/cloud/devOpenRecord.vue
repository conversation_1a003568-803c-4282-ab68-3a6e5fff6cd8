<template>
  <div class="container">
    <div class="ovf-hd searchbar">
      <div class="fl m-l-30">
        <el-date-picker v-model="daterange" type="daterange" clearable range-separator="~" start-placeholder="开始时间"
          end-placeholder="结束时间" value-format="yyyy-MM-dd" class="m-r-10"></el-date-picker>
        <el-input placeholder="设备ID" @keyup.enter.native="search" v-model="postData.device_id"
          class="w-150 m-r-10 p-t-20" clearable></el-input>
        <el-input placeholder="刷掌SN" @keyup.enter.native="search" v-model="postData.palmservice_sn"
          class="w-150 m-r-10 p-t-20" clearable></el-input>
        <el-input placeholder="场馆名称" @keyup.enter.native="search" v-model="postData.bus_name"
          class="w-150 m-r-10 p-t-20" clearable></el-input>
        <el-select v-model="postData.instruct_type" clearable placeholder="全部指令" class="w-150 m-r-10 p-t-20">
          <el-option label="开锁" value="1"></el-option>
          <el-option label="巡检" value="2"></el-option>
        </el-select>
        <el-select v-model="postData.from_type" clearable placeholder="全部操作" class="w-150 m-r-10 p-t-20">
          <el-option v-for="item in fromTypeList" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-select v-model="postData.status" clearable placeholder="全部状态" class="w-150 m-r-10 p-t-20">
          <el-option v-for="item in statusList" :label="item.label" :value="item.value"></el-option>
        </el-select>
        <el-button type="primary" icon="search" @click="search" class="m-t-20">搜索</el-button>
      </div>
    </div>
    <el-table :data="tableData" id="infotable" stripe style="width: 100%">
      <el-table-column align="center" type="index" label="编号" width="80">
        <template scope="scope">
          {{ scope.$index + postData.page_size * (postData.page_no - 1) + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="request_time" label="请求时间" align="center" width="160"></el-table-column>
      <el-table-column prop="device_id" label="设备ID" align="center"></el-table-column>
      <el-table-column prop="palmservice_sn" label="刷掌SN" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.palmservice_sn ? scope.row.palmservice_sn : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="cabinetNo" label="柜号" align="center" width="50"></el-table-column>
      <el-table-column prop="from_type_label" label="操作类型" align="center" width="150"></el-table-column>
      <el-table-column prop="bus_name" label="场馆名称" align="center">
        <template slot-scope="scope">
          <span>{{ scope.row.bus_name ? scope.row.bus_name : '--' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="instruct_type" label="指令类型" align="center" width="80">
        <template slot-scope="scope">
          <span>{{ scope.row.instruct_type == 1 ? '开锁' : '巡检' }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="status_label" label="状态" align="center" width="80">
        <template slot-scope="scope">
          <span :style="{color: scope.row.status_color}">{{ scope.row.status_label }}</span>
        </template>
      </el-table-column>
      <el-table-column prop="content" label="数据内容" align="left">
        <template slot-scope="scope">
          <div>线路：{{scope.row.content.plateNo}} 锁板 {{ scope.row.content.lineNo }} 线路</div>
          <div>指令：{{ scope.row.content.command }}</div>
          <div v-if="scope.row.instruct_type == 2">巡检：{{ scope.row.content.result }}</div>
          <div v-if="scope.row.instruct_type == 2 && scope.row.content.state == 0">状态：未上锁</div>
          <div v-else-if="scope.row.instruct_type == 2 && scope.row.content.state == 1">状态：已上锁</div>
          <div v-else-if="scope.row.instruct_type == 2">状态：--</div>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
      </div>
      <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next, sizes"
        :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount"
        :page-sizes="[10, 20, 50, 100]" @size-change="handleSizeChange">
      </el-pagination>
    </footer>
  </div>
</template>

<script>
import http from 'assets/js/http';
import dateFormat from 'dateformat'

export default {
  name: 'devControl',
  data() {
    return {
      daterange: [],
      // qrcode: 二维码, vein: 指静脉, vein_single: 指静脉, passwd: 密码, nfc: nfc, face: 人脸, palmservice: 微信刷掌, manager: 手动开柜
      fromTypeList: [
        { label: '二维码', value: 'qrcode' },
        // { label: '指静脉', value: 'vein' },
        // { label: '单个指静脉', value: 'vein_single' },
        { label: '密码', value: 'passwd' },
        { label: 'nfc', value: 'nfc' },
        // { label: '人脸', value: 'face' },
        { label: '微信刷掌', value: 'palmservice' },
        { label: '手动开柜', value: 'manager' }
      ],
      statusList: [
        { label: '失败', value: 1, color: 'red' },
        { label: '成功', value: 0, color: 'green' }
      ],
      postData: {
        begin_time: '',
        end_time: '',
        device_id: '',
        palmservice_sn: '',
        bus_name: '',
        instruct_type: '',
        from_type: '',
        status: '',
        page_size: 10,
        page_no: 1
      },
      tableData: [],
      dataCount: 0,
    };
  },
  methods: {
    handleSizeChange(val) {
      this.postData.page_size = val
      this.postData.page_no = 1
      this.gettableList()
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.gettableList();
    },
    gettableList() {
      if (Array.isArray(this.daterange) && this.daterange.length === 2) {
        this.postData.begin_time = this.daterange[0];
        this.postData.end_time = this.daterange[1];
      } else {
        this.postData.begin_time = '';
        this.postData.end_time = '';
      }
      return this.apiGet('/device/CupboardManagement/getCabinetCallbackList', this.postData).then(res => {
        if (res.errorcode == 0) {
          const list = res.data.list;
          list.forEach(item => {
            const fromType = this.fromTypeList.find(v => v.value == item.from_type);
            if (fromType) {
              item.from_type_label = fromType.label;
            } else {
              item.from_type_label = '--';
            }
            const status = this.statusList.find(v => v.value == item.status);
            if (status) {
              item.status_label = status.label;
              item.status_color = status.color;
            } else {
              item.status_label = '--';
              item.status_color = 'gray';
            }
          })
          this.tableData = list;
          this.dataCount = Number(res.data.count || 0);
        } else {
          _g.toastMsg('warning', res.errormsg);
        }
      });
    },
    search() {
      this.postData.page_no = 1;
      this.gettableList();
    }
  },
  created() {
    const today = dateFormat(new Date(), 'yyyy-mm-dd');
    this.daterange = [today, today];
    this.postData.bus_name = this.$route.query.bus_name;
  },
  activated() {
    this.postData.page_no = 1;
    this.postData.page_size = 10;
    if (this.$route.query.device_id) {
      this.postData.device_id = this.$route.query.device_id;
    } else {
      this.postData.device_id = '';
    }
    this.gettableList();
  },
  deactivated() {
  },
  mixins: [http]
};
</script>

<style lang="less" scoped>
.searchbar {
  background-color: #fff;
  min-height: 80px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  border-bottom: 1px solid #ececec;
  padding-bottom: 20px;
}
</style>
