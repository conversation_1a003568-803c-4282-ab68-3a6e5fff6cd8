<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="150px">
      <el-form-item label="场馆名称" prop="bus_id" :rules="{ required: true, message: '请选择场馆'}">
        <el-select clearable placeholder="请选择" v-model="postData.bus_id" class="w-320" filterable :disabled="action=='1'">
          <el-option v-for="item in busList" :label="item.bus_name" :value="item.bus_id" :key="item.bus_id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备ID" prop="device_id">
        <el-input class="w-320 maright-20" :disabled="true" v-model="postData.device_id"></el-input>
      </el-form-item>
      <el-form-item label="设备版本" prop="device_vesion">
        <el-input class="w-320 maright-20" :disabled="true" v-model="postData.device_vesion"></el-input>
      </el-form-item>
      <el-form-item label="设备固件" prop="device_fireward">
        <el-input class="w-320 maright-20" :disabled="true" v-model="postData.device_fireward"></el-input>
      </el-form-item>
      <el-form-item v-if="device_type_name.includes('Mini中控-nfc')" label="管理卡ID" prop="manage_card_id">
        <el-input class="w-320 maright-20" v-model="postData.manage_card_id"></el-input>
      </el-form-item>
      <!-- BUG19998 掌静脉device_type_id==25时不显示 -->
      <!-- <el-form-item v-if="!device_type_name.includes('Mini中控-nfc') && !device_type_name.includes('D3') && !device_type_name.includes('ST20') && postData.device_type_id != 25 && postData.device_type_id != 26 && postData.device_type_id != 28" label="设备主题"> -->
      <el-form-item v-if="device_type_name.includes('110')" label="设备主题">
        <el-radio-group v-model="postData.theme" :disabled="action =='1'" @change="changeTheme">
          <el-radio class="radio" label="1">指静脉</el-radio>
          <el-radio class="radio" label="2">二维码</el-radio>
          <el-radio class="radio" label="3">NFC</el-radio>
          <el-radio v-if="[11, 14, 16, 19].includes(Number(postData.device_type_id))" class="radio" label="4">手机</el-radio>
          <el-radio v-if="postData.device_type_id == 13" class="radio" label="5">手机尾号</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- BUG19998 掌静脉device_type_id==25时不显示 -->
      <el-form-item label="手机扫码" v-if="device_type_name.includes('中控') && !device_type_name.includes('中控柜-ST20') && !device_type_name.includes('中控柜-D3') && postData.device_type_id != 25">
        <el-radio-group v-model="postData.cabinet_qr_code_display" :disabled="action =='1' || postData.theme == 4">
          <el-radio class="radio" :label="1">开启</el-radio>
          <el-radio class="radio" :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="指静脉模块" v-if="postData.theme == 1 && !device_type_name.includes('Mini中控-nfc') && !device_type_name.includes('-二维码') && !device_type_name.includes('-D3') && !device_type_name.includes('一体机-ST20')">
        <el-select v-model="postData.module" disabled class="w-320">
          <el-option label="148模块" value="1"></el-option>
          <el-option label="110模块" value="2"></el-option>
          <el-option label="无指静脉模块" value="3"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="postData.device_type_id != 28" label="设备类型" prop="device_type_id">
        <el-select v-model="postData.device_type_id" disabled class="w-320">
          <el-option v-for="item in devicetypeList" :label="item.device_type_name" :value="item.id" :key="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item v-if="['Mini中控-微信刷掌'].includes(device_type_name)" label="柜控编组" prop="cabinet_group">
        <el-select v-model="postData.cabinet_group" class="w-320">
          <el-option label="A" value="A"></el-option>
          <el-option label="B" value="B"></el-option>
          <el-option label="C" value="C"></el-option>
          <el-option label="D" value="D"></el-option>
          <el-option label="E" value="E"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="中控柜类型" v-if="device_type_name.includes('中控') && !device_type_name.includes('Mini中控-nfc')" :rules="{required: true, message: '请选择中控柜类型'}" prop="type">
        <el-radio-group v-model="postData.type" :disabled="action=='1'">
          <el-radio class="radio" :label="0">临时租柜</el-radio>
          <el-radio class="radio" :label="1" v-if="!device_type_name.includes('-二维码')">长租柜</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="中控柜类型" v-if="device_type_name.includes('Mini中控-nfc')" :rules="{required: true, message: '请选择中控柜类型'}" prop="type">
        <el-radio-group v-model="postData.type" disabled>
          <el-radio class="radio" :label="0">临时租柜</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- BUG19998 掌静脉device_type_id==25时不显示 -->
      <el-form-item label="中控柜尺寸" v-if="device_type_name.includes('中控') && !device_type_name.includes('-二维码') && !device_type_name.includes('Mini中控-nfc') && !device_type_name.includes('中控柜-ST20') && !device_type_name.includes('中控柜-D3') && postData.device_type_id != 25" :rules="{required: true, message: '请选择中控柜尺寸'}" prop="type">
        <el-radio-group v-model="postData.size" :disabled="action=='1'">
          <el-radio class="radio" :label="1">21.5寸</el-radio>
          <el-radio class="radio" :label="2">32寸</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="中控柜尺寸" v-if="device_type_name.includes('Mini中控-nfc') || device_type_name.includes('-二维码')" :rules="{required: true, message: '请选择中控柜尺寸'}" prop="type">
        <el-radio-group v-model="postData.size" :disabled="action=='1'">
          <el-radio class="radio" :label="1">{{device_type_name.includes('中控柜-二维码')?'21.5':'10.1'}}寸</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- <el-form-item label="开锁指令" v-if="device_type_name.includes('中控柜-110') || device_type_name.includes('Mini中控-110') || device_type_name.includes('中控柜-D3') || device_type_name.includes('中控柜-ST20')" :rules="{required: true, message: '请选择开锁指令'}" prop="unlock_order"> -->
      <el-form-item label="开锁指令" v-if="['Mini中控-微信刷掌', '中控柜-110', 'Mini中控-110', '中控柜-D3', '中控柜-ST20'].includes(device_type_name)" :rules="{required: true, message: '请选择开锁指令'}" prop="unlock_order">
        <el-radio-group v-model="postData.unlock_order" :disabled="action=='1'">
          <el-radio class="radio" :label="0">MOS版本</el-radio>
          <el-radio class="radio" :label="1">继电器版本</el-radio>
          <el-radio class="radio" :label="2">MOS_16版本</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="退柜提示" v-if="device_type_name.includes('中控')" :rules="{required: true, message: '请选择退柜提示'}" prop="exit_tip">
        <el-radio-group v-model="postData.exit_tip" :disabled="action=='1'">
          <el-radio class="radio" :label="0">关</el-radio>
          <el-radio class="radio" :label="1">开</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="显示退柜" v-if="device_type_name.includes('中控')" :rules="{required: true, message: '请选择显示退柜'}" prop="display_exit">
        <el-radio-group v-model="postData.display_exit" :disabled="action=='1'">
          <el-radio class="radio" :label="0">关</el-radio>
          <el-radio class="radio" :label="1">开</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="闸机类型" v-if="device_type_name.includes('闸机')" :rules="{required: true, message: '请选择闸机类型'}" prop="type">
        <el-radio-group v-model="postData.type" :disabled="action=='1'">
          <el-radio class="radio" :label="0">进场</el-radio>
          <el-radio class="radio" :label="1">出场</el-radio>
        </el-radio-group>
      </el-form-item>
        <el-form-item label="闸机编组" v-if="device_type_name.includes('闸机')" prop="device_group">
        <el-select v-model="postData.device_group" class="w-320">
          <el-option label="A" value="A"></el-option>
          <el-option label="B" value="B"></el-option>
          <el-option label="C" value="C"></el-option>
          <el-option label="D" value="D"></el-option>
          <el-option label="E" value="E"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="闸机欢迎语" v-if="device_type_name.includes('闸机')" prop="prompt_message">
        <el-input class="w-320 maright-20" v-model="postData.prompt_message" :disabled="action=='1'"></el-input>
      </el-form-item>
      <el-form-item label="守护鸡" v-if="device_type_name.includes('闸机')" :rules="{required: true, message: '请选择守护鸡类型'}" prop="guard_switch">
        <el-radio-group v-model="postData.guard_switch" :disabled="action=='1'">
          <el-radio class="radio" :label="0">停用</el-radio>
          <el-radio class="radio" :label="1">启用</el-radio>
      </el-radio-group>
      </el-form-item>
      <el-form-item label="广告类型" v-if="device_type_name.includes('中控柜-110') || device_type_name.includes('中控柜-148') || device_type_name.includes('中控柜-二维码')" :rules="{required: true, message: '请选择广告类型'}" prop="ad_type">
        <el-radio-group v-model="postData.ad_type" :disabled="action=='1'">
          <el-radio class="radio" :label="0">视频</el-radio>
          <el-radio class="radio" :label="1">图片</el-radio>
          <el-radio class="radio" :label="2">云广告</el-radio>
      </el-radio-group>
      </el-form-item>
      
      <el-form-item label="生成特征验证" v-if="device_type_name.includes('一体机') && !device_type_name.includes('一体机-D3') && !device_type_name.includes('一体机-ST20')" :rules="{required: true, message: '请选择是否生成特征验证'}" prop="validate_binding">
        <el-radio-group v-model="postData.validate_binding" :disabled="action=='1'">
          <el-radio class="radio" :label="1">开启</el-radio>
          <el-radio class="radio" :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- BUG19998 掌静脉device_type_id==25时不显示 -->
      <el-form-item label="手机号后四位验证" v-if="!device_type_name.includes('D3') && !device_type_name.includes('ST20') && ((device_type_name.includes('一体机') || device_type_name.includes('中控') || device_type_name.includes('水控') || device_type_name.includes('挂门禁')) && !device_type_name.includes('Mini中控-nfc') && !device_type_name.includes('-二维码')) && postData.device_type_id != 25" :rules="{required: true, message: '请选择是否启用手机号后四位验证'}" prop="four_phone">
        <el-radio-group v-model="postData.four_phone" :disabled="action=='1'">
          <el-radio class="radio" :label="1">开启</el-radio>
          <el-radio class="radio" :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <!-- BUG19998 掌静脉device_type_id==25时不显示 -->
      <el-form-item v-if="!device_type_name.includes('Mini中控-nfc') && !device_type_name.includes('-二维码') && !device_type_name.includes('D3') && !device_type_name.includes('ST20') && postData.device_type_id != 25 && postData.device_type_id != 26 && postData.device_type_id != 28" label="验证参数" prop="validate" :rules="[{ required: true, message: '请填写验证参数', trigger: 'blur' }, { validator: checkvali, trigger: 'change'}]">
        <el-input class="w-320 maright-20" v-model="postData.validate" :disabled="action=='1'"></el-input>
      </el-form-item>
      <!-- BUG19998 掌静脉device_type_id==25时不显示 -->
      <el-form-item v-if="!device_type_name.includes('Mini中控-nfc') && !device_type_name.includes('-二维码') && !device_type_name.includes('D3') && !device_type_name.includes('ST20') && postData.device_type_id != 25 && postData.device_type_id != 26 && postData.device_type_id != 28" label="验证上限分数" prop="validateUp" :rules="[{ validator: checkvaliUp, trigger: 'change'}]">
        <el-input class="w-320 maright-20" v-model="postData.validateUp" :disabled="action=='1'"></el-input>
      </el-form-item>
      <el-form-item label="设备名称" prop="device_name" :rules="{ required: true, message: '请填写设备名称'}">
        <el-input class="w-320 maright-20" v-model="postData.device_name" :disabled="action=='1'"></el-input>
      </el-form-item>
      <el-form-item label="设备方案" prop="device_plan_id" v-if="postData.device_type_id != 26" :rules="{ required: true, message: '请选择设备方案'}">
        <el-select clearable placeholder="请选择" v-model="postData.device_plan_id" class="w-320" :disabled="action=='1'">
          <el-option label="无需签到" value="0"></el-option>
          <el-option label="需要签到" value="1"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备渠道" prop="channel_id" :rules="{ required: true, message: '请选择设备渠道'}">
        <el-select clearable placeholder="请选择" v-model="postData.channel_id" class="w-320" :disabled="action=='1'">
          <el-option v-for="item in channelList" :label="item.channel_name" :value="item.id" :key="item.id"></el-option>
        </el-select>
      </el-form-item>
       <el-form-item v-if="!device_type_name.includes('D3') && !device_type_name.includes('ST20') && postData.device_type_id != 26" label="nfc厂商" prop="nfc_firm">
        <el-select clearable placeholder="请选择" v-model="postData.nfc_firm" class="w-320" :disabled="action=='1'">
          <el-option v-for="item in nfcList" :label="item.name" :value="item.id" :key="item.id"></el-option>
        </el-select>
      </el-form-item>

        <el-form-item label="读取顺序" v-if="!device_type_name.includes('D3') && !device_type_name.includes('ST20') && postData.device_type_id != 26" prop="reading_order">
          <el-radio-group v-model="postData.reading_order" :disabled="action=='1'">
            <el-radio class="radio" :label="1">顺序</el-radio>
            <el-radio class="radio" :label="2">逆序</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="柜锁线路数" v-if="device_type_name.includes('Mini中控-微信刷掌')" prop="cabinet_line_limit">
          <el-input class="w-320 maright-20" :disabled="userId !== '1'" v-model="postData.cabinet_line_limit"></el-input>
        </el-form-item>
        <!-- <el-form-item label="刷掌SN" v-if="device_type_name.includes('Mini中控-微信刷掌') || device_type_name.includes('一体机-微信刷掌')" prop="palmservice_sn"> -->
        <el-form-item label="刷掌SN" v-if="['Mini中控-微信刷掌', '一体机-微信刷掌', '门禁-微信刷掌'].includes(device_type_name)" prop="palmservice_sn">
          <el-input class="w-320 maright-20" v-model="postData.palmservice_sn"></el-input>
        </el-form-item>
        <el-form-item label="使用场景" v-if="device_type_name.includes('一体机-D3') || device_type_name.includes('一体机-ST20')" prop="training_course_type">
        <el-radio-group v-model="postData.training_course_type" :disabled="action=='1'">
          <el-radio class="radio" :label="0">私教消课</el-radio>
          <el-radio class="radio" :label="1">培训班消课</el-radio>
        </el-radio-group>
      </el-form-item>
     <el-form-item v-if="device_type_name.includes('ST20')" label="商汤账号" prop="st_username">
        <el-input class="w-320 maright-20" v-model="postData.st_username"></el-input>
      </el-form-item>
      <el-form-item v-if="device_type_name.includes('ST20')" label="商汤密码" prop="st_password">
      <el-input class="w-320 maright-20" v-model="postData.st_password"></el-input>
      </el-form-item>
      <el-form-item label="二维码读头" v-if="device_type_name.includes('-D3') || device_type_name.includes('ST20')" prop="device_type_name">
        <el-radio-group v-model="postData.qr_code_head">
          <el-radio class="radio" :label="1">启用</el-radio>
          <el-radio class="radio" :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="人脸识别" v-if="device_type_name.includes('中控柜-D3')" prop="is_face">
        <el-radio-group v-model="postData.is_face">
          <el-radio class="radio" :label="1">启用</el-radio>
          <el-radio class="radio" :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否开门" v-if="device_type_name.includes('团操课一体机')" prop="device_type_name">
        <el-radio-group v-model="postData.is_open">
          <el-radio class="radio" :label="1">启用</el-radio>
          <el-radio class="radio" :label="0">关闭</el-radio>
        </el-radio-group>
      </el-form-item>

      <el-form-item label="设备状态">
        <el-switch v-model="postData.device_status" active-color="#13ce66" inactive-color="#ff4949" active-value="1" inactive-value="0" :disabled="action=='1'"></el-switch>
      </el-form-item>

      <el-form-item v-if="action=='0'">
        <el-button type="primary" @click="onSubmit">保存</el-button>
        <el-button class="m-l-150" @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  export default {
    name: 'devEdit',
    data() {
      return {
        userId: this.$store.state.userInfo.id,
        action: '',
        device_type_name: '',
        nfcList: [],
        postData: {
          manage_card_id: '',
          id: '',
          bus_id: '',
          unlock_order: 0,
          device_type_id: '',
          device_name: '',
          device_plan_id: '',
          channel_id: '',
          device_status: '',
          type: '',
          size: 1,
          guard_switch: 0,
          ad_type: 2,
          prompt_message: '',
          device_id: '',
          device_group: 'A',
          device_vesion: '',
          device_fireward: '',
          validate: '',
          validateUp: '0.65',
          validate_binding: 0,
          four_phone: 0,
          theme: 1,
          nfc_firm: 0,
          reading_order: 1,
          palmservice_sn: '',
          cabinet_line_limit: 65,
          training_course_type: 0,
          qr_code_head: 0,
          is_face: 0,
          is_open: 1,
          module: '',
          cabinet_qr_code_display:0,
          st_username:'',
          st_password:'',
          cabinet_group: 'A',
        },
        busList: [],
        devicetypeList: [],
        channelList: []
      };
    },
    methods: {
      getNfcList() {
        this.apiGet('/device/deviceManagement/nfcList').then(res => {
          if (res.errorcode == 0) {
            this.nfcList = res.data.list;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      changeTheme(val){
        if(this.postData.theme == 4){
           this.postData.cabinet_qr_code_display = 1;
        }
      },
      checkvali(rule, value, cb) {
        if (!value) {
          return cb(new Error('参数不能为空'));
        }
        const reg = /^0\.(0[1-9]|[1-9][0-9]{0,1})$/;
        if (!reg.test(value)) {
          cb(new Error('参数为大于0小于1的数字，并保持小数点后两位有效数字'));
        }
        cb();
      },
      checkvaliUp(rule, value, callback) {
         if (!value) {
          return callback();
        }
        const reg = /^0\.(0[1-9]|[1-9][0-9]{0,1})$/;
        if (!reg.test(value)) {
          callback(new Error('为大于0小于1的数字，并保持小数点后两位有效数字'));
        }
        callback();
      },
      onSubmit() {
        this.$refs.form.validate(valid => {
          if (valid) {
            if (!(this.device_type_name.includes('中控') || this.device_type_name.includes('闸机'))) {
              this.postData.type = '';
            }
            if (this.postData.nfc_firm !== 0) {
              this.postData.reading_order = ''
            }
            this.apiPost('/device/deviceManagement/save', this.postData).then(res => {
              if (res.errorcode == 0) {
                _g.toastMsg('success', res.errormsg);
                this.$router.back();
              } else {
                _g.toastMsg('warning', res.errormsg);
              }
            });
          }
        });
      },
      async getpageInfo() {
        return new Promise((resolve, reject) => {
          this.apiGet('/device/deviceManagement/info', { device_id: this.postData.id }).then(res => {
            if (res.errorcode == 0) {
              const resData = res.data
              this.postData = {
                ...resData,
                ...{
                  device_plan_id: String(res.data.device_plan_id),
                  device_status: String(res.data.device_status),
                  type: Number(res.data.type),
                  theme: String(res.data.theme),
                  validate: res.data.validate ? String(res.data.validate) : '0.63',
                  validateUp: res.data.validateUp ? String(res.data.validateUp) : '0.65',
                  module: String(res.data.module),
                  cabinet_group: res.data.cabinet_group || 'A'
                }
              };
              this.device_type_name = resData.device_type_name;

              // // zj mock
              // this.postData.device_type_id = 28;
              // this.device_type_name = '门禁-微信刷掌'
              resolve();
            } else {
              reject();
              _g.toastMsg('warning', res.errormsg);
            }
          });
        });
      },
      getbusList() {
        this.apiGet('/device/deviceManagement/busList').then(res => {
          if (res.errorcode == 0) {
            this.busList = res.data.list;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      getdevicetypeList() {
        this.apiGet('/device/deviceManagement/deviceTypeList').then(res => {
          if (res.errorcode == 0) {
            this.devicetypeList = res.data.list;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      getchannelList() {
        this.apiGet('/device/deviceManagement/channelList', { device_type_id: this.postData.device_type_id }).then(
          res => {
            if (res.errorcode == 0) {
              this.channelList = res.data.list;
            } else {
              _g.toastMsg('warning', res.errormsg);
            }
          }
        );
      }
    },
    async created() {
      this.postData.id = this.$route.params.id;
      this.action = this.$route.params.action;

      // // zj mock
      // this.postData.id = 366;
      // this.action = '0';

      this.getbusList();
      this.getNfcList();
      this.getdevicetypeList();
      await this.getpageInfo();
      this.getchannelList();
    },
    mixins: [http]
  };
</script>

<style scoped>
  .maright-20 {
    margin-right: 20px;
  }
</style>
