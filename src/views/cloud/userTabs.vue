<template>
  <div class="m-b-20 ovf-hd">
    <el-tabs type="border-card">
      <el-tab-pane>
        <span slot="label">微信刷掌</span>
        <WxUser />
      </el-tab-pane>
      <el-tab-pane>
        <span slot="label">指静脉</span>
        <User />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import User from './user.vue'
import WxUser from './wxUser.vue'

export default {
  name: 'userTabs',
  components: {
    User,
    WxUser
  },
}
</script>

