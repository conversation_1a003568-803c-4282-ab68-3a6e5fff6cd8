<template>
  <div class="container">
    <header>
      <el-date-picker type="daterange" v-model="dateRange" @change="dateChange" value-format="yyyy-MM-dd"
                      start-placeholder="开始时间" end-placeholder="结束时间"></el-date-picker>
      <el-input placeholder="手机号" v-model="postData.phone"></el-input>
      <el-input placeholder="场馆名称" v-model="postData.bus_name"></el-input>
      <el-input placeholder="失败次数>" v-model="postData.fail_num"></el-input>
      <el-button type="primary" @click="doSearch">查询</el-button>
    </header>
    <el-table :data="tableData">
      <el-table-column label="编号" type="index"></el-table-column>
      <el-table-column label="场馆名称" prop="bus_name"></el-table-column>
      <el-table-column label="日期" prop="date"></el-table-column>
      <el-table-column label="手机号" prop="phone">
        <template scope="scope">
          <router-link :to="{ name: 'cloudUserDetail', params: { veinUid: scope.row.vein_uid } }">{{ scope.row.phone }}
          </router-link>
        </template>
      </el-table-column>
      <el-table-column label="成功次数" prop="success_num"></el-table-column>
      <el-table-column label="失败次数" prop="failNum"></el-table-column>
      <el-table-column label="失败率" prop="failRate"></el-table-column>
      <el-table-column label="失败平均分" prop="veinScore_arg"></el-table-column>
      <el-table-column label="验证最小值" prop="veinScore_min"></el-table-column>
      <el-table-column label="验证最大值" prop="veinScore_max"></el-table-column>
    </el-table>
    <footer>
      <!--<Pager :total="total" :postData="postData" @on-change="onPageChange"></Pager>-->
    </footer>
  </div>
</template>

<script>
  import { formatDate } from 'src/assets/js/utils'
  import Pager from 'src/components/pager'

  export default {
    name: 'userPassStat',
    components: { Pager },
    data() {
      return {
        dateRange: [formatDate(new Date(), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd')],
        total: 0,
        postData: {
          phone: '',
          bus_name: '',
          begin_time: '',
          end_time: '',
          fail_num: ''
        },
        tableData: []
      }
    },
    created() {
      const { begin_time, end_time, bus_name } = this.$route.query;
      this.postData.begin_time = begin_time;
      this.postData.end_time = end_time;
      this.postData.bus_name = bus_name;
      if (begin_time) {
        this.dateRange = [begin_time, end_time]
      }
      this.getList()
    },
    methods: {
      dateChange([s, e]) {
        this.postData.begin_time = s;
        this.postData.end_time = e;
      },
      onPageChange({ page_no, page_size }) {
        this.postData.page_no = page_no;
        this.postData.page_size = page_size;
        this.getList();
      },
      doSearch() {
        this.postData.page_no = 1;
        this.getList()
      },
      getList() {
        const url = '/device/Statistics/user_pass_rate'
        this.$service.post(url, this.postData).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.tableData = data.map(item => {
              return {
                ...item,
                failNum: `${item['fail_num_0.63']} | ${item['fail_num_0.40']}`,
                failRate: `${item['fail_num_rate_0.63']} | ${item['fail_num_rate']}`
              }
            });
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      }
    },
  }
</script>

<style scoped>

</style>
