<template>
  <div class="container">
    <header>
       <el-date-picker v-model="rangeArray" :clearable="true" class="m-r-10" type="daterange" @change="pickChange" placeholder="选择日期"></el-date-picker>
       <el-select v-model="postData.change_type" placeholder="全部类型">
        <el-option value="" label="全部类型"></el-option>
        <el-option value="channel_id" label="渠道记录"></el-option>
        <el-option value="device_vesion" label="软件版本"></el-option>
        <el-option value="device_fireward" label="固件版本"></el-option>
      </el-select>
        <el-input placeholder="操作账号" v-model="postData.operator_name" class="w-150 m-r-10"></el-input>
        <el-input placeholder="值" v-model="postData.new_value" class="w-150 m-r-10"></el-input>
        <el-input placeholder="设备ID" v-model="postData.device_id" class="w-150 m-r-10"></el-input>
        <el-button type="success" @click="search">查询</el-button>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column align="center" prop="update_time" label="时间"></el-table-column>
      <el-table-column align="center" prop="device_id" label="设备ID"></el-table-column>
      <el-table-column align="center" prop="change_type" label="类型"></el-table-column>
      <el-table-column align="center" prop="new_value" label="值"></el-table-column>
      <el-table-column align="center" prop="operator_name" label="操作账号"></el-table-column>
      <el-table-column align="center" prop="remark" label="操作备注"></el-table-column>
    </el-table>
     <footer>
      <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount">
        </el-pagination>
    </footer>

  </div>
</template>

<script>

export default {
  name: 'DevVersion',
  data() {
    return {
      dataCount: 0,
      rangeArray: [],
      postData: {
        page_no: 1, //页码
        page_size: 10,
        operator_name: '',
        change_type: '',
        device_id: '',
        new_value: '',
        start_time: '',
        end_time: '',
      },
      tableData: [],
    }
  },
  created() {
    this.postData.device_id = this.$route.params.device_id || ''
    this.getList()
  },
  watch: {},
  methods: {
    search() {
      this.postData.page_no = 1
      this.getList()
    },
    pickChange(val) {
      this.postData.start_time = val ? _g.formatDate(val[0], 'yyyy-MM-dd') : '';
      this.postData.end_time = val ? _g.formatDate(val[1], 'yyyy-MM-dd') : '';
    },
    getDate() {
      let d = new Date();
      return `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()}`;
    },
    handleCurrentChange(val) {
      this.postData.page_no = val
      this.getList()
    },
    getList() {
      return this.$service.post('/device/deviceManagement/deivceVersionUpdateLogList', this.postData).then(res => {
        if (res.data.errorcode == 0) {
          this.tableData = res.data.data.list;
          this.dataCount = res.data.data.count;
        } else {
          this.$message.error(res.data.errormsg);
        }
      }).catch(e => {
        console.error(e);
      })
    }
  }
}
</script>