<template>
  <div class="container" ref="container">
    <header>
      <el-date-picker v-model="rangeArray" :clearable="false" class="m-r-10" type="daterange" @change="changeDate" placeholder="选择日期">
      </el-date-picker>
      <el-input placeholder="场馆名称" @keyup.enter.native="search" v-model="postData.bus_name" class="w-150 m-r-10"></el-input>
      <el-input placeholder="场馆ID" @keyup.enter.native="search" v-model="postData.bus_id" class="w-150 m-r-10"></el-input>
      <el-button type="primary" icon="search" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe style="width: 100%">
      <el-table-column type="index" label="序号" width="100" align="center"></el-table-column>
      <el-table-column prop="bus_name" label="场馆名称" align="center">
        <template scope="scope">
          <div>{{scope.row.bus_name}}({{scope.row.bus_bind_count}})</div>
        </template>
      </el-table-column>
      <el-table-column prop="bus_id" label="场馆ID" align="center"></el-table-column>
      <el-table-column prop="date" label="日期" align="center"></el-table-column>
      <el-table-column prop="deviceCount" label="设备数量" align="center">
        <template scope="scope">
          <router-link :to="{name:'devControl', query:{bus_name:scope.row.bus_name}}">
            <el-button size="medium" class="qricon" type="text">{{scope.row.deviceCount}}</el-button>
          </router-link>
        </template>
      </el-table-column>
      <el-table-column prop="requestCount" label="请求总数" align="center">
        <template scope="scope">
          <span class="bluefont overtext" size="medium" @click="showDetail(scope.row)" type="text">{{scope.row.requestCount}}</span>
        </template>
      </el-table-column>
      <el-table-column label="验证失败">
        <template scope="scope">
          <router-link :to="{ name: 'userPassStat', query: { begin_time: postData.begin_time, end_time: postData.end_time, bus_name: scope.row.bus_name }}">{{ scope.row.failure }}({{scope.row.valid_tel_fail_num}}人)</router-link>
        </template>
      </el-table-column>
      <el-table-column prop="valid_tel_num" label="今日人数" align="center"></el-table-column>
      <el-table-column prop="successRate" label="成功率" align="center">
        <template scope="scope">
          <span>{{scope.row.successRate?scope.row.successRate:'-'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="学习特征占比" align="center">
        <template scope="scope">
          <span>{{scope.row.freeStudyRate?scope.row.freeStudyRate:'-'}}</span>
        </template>
      </el-table-column>
    </el-table>

    <footer>
      <div class="w100 pos-rel ovf-hd flexend" v-if="dataCount>0">
        <div class="block">
          <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
          </el-pagination>
        </div>
      </div>
    </footer>

    <div class="graph-frame">
      <div class="search-line">
        <el-select v-model="busIds" class="w-300 m-r-10 m-l-20" multiple filterable placeholder="请选择">
          <el-option v-for="item in busList" :key="item.bus_id" :label="item.bus_name" :value="item.bus_id">
          </el-option>
        </el-select>
        <el-button type="primary" icon="search" class="m-l-20" @click="genGraph">生成图表</el-button>
      </div>
      <div class="m-t-10" v-if="option.series.length>0">
        <div id="busStat" :style="{width: '90%', height: '500px'}">
        </div>

      </div>
    </div>

    <el-dialog title="接口详情" :visible.sync="showDia" width="80%">
      <div class="dia-header">
        <span>{{diaData.bus_name}}</span>
        <span>{{diaData.dateRange}}</span>
      </div>
      <el-table :data="diaData.data" class="dia-table" stripe style="width: 100%">
        <el-table-column prop="name" label="接口名称" align="center"></el-table-column>
        <el-table-column prop="all" label="请求次数" align="center"></el-table-column>
        <el-table-column prop="success" label="成功次数" align="center"></el-table-column>
        <el-table-column prop="fail" label="失败次数" align="center"></el-table-column>
        <el-table-column prop="rate" label="成功率" align="center">
          <template scope="scope">
            <span>{{scope.row.rate?scope.row.rate:'-'}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="BaseNum" label="原始特征" align="center"></el-table-column>
        <el-table-column label="原始特征占比" align="center">
          <template scope="scope">
            <span>{{scope.row.BaseRate?scope.row.BaseRate:'-'}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="freeStudyNum" label="学习特征" align="center"></el-table-column>
        <el-table-column label="学习特征占比" align="center">
          <template scope="scope">
            <span>{{scope.row.freeStudyRate?scope.row.freeStudyRate:'-'}}</span>
          </template>
        </el-table-column>
        <el-table-column prop="OptimizeNum" label="优化特征" align="center"></el-table-column>
        <el-table-column label="优化特征占比" align="center">
          <template scope="scope">
            <span>{{scope.row.OptimizeRate?scope.row.OptimizeRate:'-'}}</span>
          </template>
        </el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  import echarts from 'echarts';
  export default {
    name: 'busStat',
    data() {
      return {
        width: '80%',
        height: '100px',
        postData: {
          bus_name: '',
          bus_id: '',
          begin_time: '',
          end_time: '',
          page_size: 10,
          page_no: 1
        },
        rangeArray: [this.getDate(), this.getDate()],
        tableData: [],
        dataCount: 0,
        busIds: [],
        busList: [],
        option: {
          title: {
            text: '场馆通过率(%)'
          },
          tooltip: {
            trigger: 'axis',
            formatter: function(params) {
              var result = params[0].name + '<br />';
              params.forEach(function(item) {
                if (item.value) {
                  result += item.marker + ' ' + item.seriesName + ' : ' + item.value + '%' + '</br>';
                } else {
                  result += item.marker + ' ' + item.seriesName + ' : ' + '-' + '</br>';
                }
              });
              return result;
            }
          },
          legend: {
            left: '14%',
            data: []
          },
          grid: {
            left: '3%',
            right: '4%',
            bottom: '3%',
            containLabel: true
          },
          toolbox: {
            feature: {
              saveAsImage: {}
            }
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: []
          },
          yAxis: {
            axisLabel: {
              formatter: '{value}'
            }
          },
          series: []
        },
        showDia: false,
        diaData: {
          bus_name: '',
          dateRange: '',
          data: []
        }
      };
    },
    watch: {
      showDia(val) {
        if (!val) {
          this.diaData.bus_name = '';
          this.diaData.dateRange = '';
          this.diaData.data = [];
        }
      }
    },
    methods: {
      getSummaries(param) {
        const { columns, data } = param;
        const sums = [];
        columns.forEach((column, index) => {
          if (index === 0) {
            sums[index] = '合计';
            return;
          }
          if (index === 6) {
            sums[index] = ((sums[5] / (sums[2])) * 100).toFixed() + '%';
            return;
          }
          if (!column.label.includes('成功率')) {
            sums[index] = 0;
            data.forEach(item => {
              sums[index] += Number(item[column.property]) || 0;
            });
          } else {
            if (index === 4) {
              sums[index] = ((sums[2] / sums[1]) * 100).toFixed() + '%';
            } else {
              sums[index] = ((sums[index - 1] / sums[2]) * 100).toFixed() + '%';
            }
          }
        });

        return sums;
      },
      showDetail(row) {
        this.showDia = true;
        this.diaData.bus_name = row.bus_name;
        this.diaData.dateRange = row.date;
        let postda = {
          bus_id: row.bus_id,
          begin_time: this.postData.begin_time ? this.postData.begin_time : this.rangeArray[0],
          end_time: this.postData.end_time ? this.postData.end_time : this.rangeArray[1]
        };
        this.apiPost('/device/Statistics/getEachDevice', postda).then(res => {
          if (res.errorcode == 0) {
            const data = res.data;
            const all = data.reduce((all, item) => all + item['all'], 0);
            const success = data.reduce((success, item) => success + item['success'], 0);
            const bind = data.find(item => item.name === '绑定') || { success: 0 };
            const exceptBind = success - bind.success;
            const BaseNum = data.reduce((BaseNum, item) => BaseNum + (item['BaseNum'] || 0), 0);
            const freeStudyNum = data.reduce((freeStudyNum, item) => freeStudyNum + (item['freeStudyNum'] || 0), 0);
            const OptimizeNum = data.reduce((OptimizeNum, item) => OptimizeNum + (item['OptimizeNum'] || 0), 0);
            data.push({
              name: '合计',
              all,
              success,
              fail: data.reduce((fail, item) => fail + Number(item['fail']), 0),
              rate: (success / all * 100).toFixed() + '%',
              BaseNum,
              BaseRate: (BaseNum / exceptBind * 100).toFixed() + '%',
              freeStudyNum,
              freeStudyRate: (freeStudyNum / exceptBind * 100).toFixed() + '%',
              OptimizeNum,
              OptimizeRate: (OptimizeNum / exceptBind * 100).toFixed() + '%',
            });
            this.diaData.data = data;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      genGraph() {
        if (this.busIds.length == 0) {
          this.option.series = [];
          this.option.xAxis.data = [];
          this.option.legend.data = [];
          const myChart = echarts.init(document.querySelector('#busStat'));
          setTimeout(() => {
            myChart.setOption(this.option);
          });
          _g.toastMsg('warning', '请选择场馆');
          return false;
        } else {
          let postd = {
            begin_time: this.postData.begin_time ? this.postData.begin_time : this.rangeArray[0],
            end_time: this.postData.end_time ? this.postData.end_time : this.rangeArray[1],
            bus_ids: this.busIds.join(',')
          };
          this.apiPost('/device/Statistics/businessAdoptCurve', postd).then(res => {
            if (res.errorcode == 0) {
              this.option.series = res.data.data;

              let legend = [];
              this.option.series.forEach((ele, i) => {
                ele.data.forEach((ele, j) => {
                  this.option.series[i].data[j] = ele.replace(/%/g, '');
                });
                this.$set(ele, 'type', 'line');
                this.$set(ele, 'showAllSymbol', true);
                legend.push(ele.name);
              });
              this.option.xAxis.data = res.data.date;
              this.option.legend.data = legend;
              this.$nextTick(() => {
                let main = document.getElementById('busStat');
                if (echarts.getInstanceByDom(main)) {
                  echarts.dispose(main);
                }
                const myChart = echarts.init(document.querySelector('#busStat'));
                setTimeout(() => {
                  myChart.setOption(this.option);
                });
              });
            } else {
              _g.toastMsg('warning', res.errormsg);
            }
          });
        }
      },
      gettableList() {
        this.apiPost('/device/Statistics/businessStatistics', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.tableData = res.data.lists;
            this.dataCount = res.data.count;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      getbusList() {
        this.apiPost('/device/Statistics/getBusLists').then(res => {
          if (res.errorcode == 0) {
            this.busList = res.data;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      getDate() {
        let d = new Date();
        return `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()}`;
      },
      changeDate(value) {
        let sTime = value[0];
        let eTime = value[1];
        this.postData.begin_time = _g.formatDate(sTime, 'yyyy-MM-dd');
        this.postData.end_time = _g.formatDate(eTime, 'yyyy-MM-dd');
        this.gettableList();
        if (this.busIds.length > 0) {
          this.genGraph();
        }
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.gettableList();
      },
      search() {
        this.postData.page_no = 1;
        this.gettableList();
      }
    },
    mounted() {
      this.width = this.$refs.container.getBoundingClientRect().right - this.$refs.container.getBoundingClientRect().left;
      this.height = (this.width * 2) / 3 + 'px';
      this.width = this.width + 'px';
    },
    created() {
      this.gettableList();
      this.getbusList();
    },
    mixins: [http]
  };
</script>

<style scoped>
  .w100 {
    width: 100%;
  }
  .search-line {
    min-height: 80px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border-top: 1px solid #ececec;
    border-bottom: 1px solid #ececec;
  }
  .bluefont {
    color: #409eff;
    cursor: pointer;
    width: 100%;
    display: inline-block;
  }
  .overtext {
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .dia-header {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    font-size: 18px;
    padding-bottom: 15px;
    padding-left: 10px;
    padding-right: 10px;
  }
  .dia-table {
    margin-bottom: 20px;
  }
</style>








