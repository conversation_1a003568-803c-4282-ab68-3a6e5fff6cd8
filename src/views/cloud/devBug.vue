<template>

  <div class="container">
    <header>
      <div class="fl">
        <router-link :to="{name: 'devBugAdd'}">
        <el-button type="success" size="small" icon="el-icon-plus">
          添加记录
        </el-button>
        </router-link>
      </div>
      <div class="fl m-l-30">
        <el-input placeholder="设备ID" @keyup.enter.native="search" v-model="postData.device_id" class="w-150 m-r-10"></el-input>
        <el-input placeholder="场馆名称" @keyup.enter.native="search" v-model="postData.bus_name" class="w-150 m-r-10"></el-input>
        <DeviceType v-model="postData.device_type_id" class="w-150 m-r-10">
          <el-option value="" label="设备类型"></el-option>
        </DeviceType>
        <el-select v-model="postData.type" class="w-150 m-r-10" placeholder="异常类型">
          <el-option value="" label="异常类型"></el-option>
          <el-option :value="1" label="识别异常"></el-option>
          <el-option :value="2" label="网络异常"></el-option>
          <el-option :value="3" label="设备异常"></el-option>
          <el-option :value="4" label="开柜异常"></el-option>
          <el-option :value="0" label="其它"></el-option>
        </el-select>
        <el-select v-model="postData.status" class="w-150 m-r-10" placeholder="处理状态">
          <el-option value="" label="处理状态"></el-option>
          <el-option :value="0" label="未解决"></el-option>
          <el-option :value="1" label="解决中"></el-option>
          <el-option :value="2" label="观察中"></el-option>
          <el-option :value="3" label="已解决"></el-option>
        </el-select>
        <el-button type="primary" icon="search" @click="search">搜索</el-button>
      </div>
    </header>

    <el-table :data="tableData" id="infotable" stripe>
      <el-table-column prop="device_id" label="设备ID" align="center"></el-table-column>
      <el-table-column prop="bus_name" label="场馆名称" align="center"></el-table-column>
      <el-table-column prop="device_type_name" label="设备类型" align="center"></el-table-column>
      <el-table-column prop="type" label="异常类型" align="center">
        <template scope="{row}">
          <span>{{ row.type == 1 ? '识别异常' : row.type == 2 ? '网络异常' : row.type == 3 ? '设备异常' : row.type == 4 ? '开柜异常' : '其它'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="question" label="问题描述" align="center"></el-table-column>
      <el-table-column prop="result" label="预处理结果" align="center"></el-table-column>
      <el-table-column prop="member_info" label="人员信息" align="center"></el-table-column>
      <el-table-column prop="status" label="处理状态" align="center">
        <template scope="{row}">
          <span>{{ row.status == 1 ? '解决中' : row.status == 2 ? '观察中' : row.status == 3 ? '已解决' : '未解决'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="create_time" label="记录时间" align="center"></el-table-column>
      <el-table-column label="操作" align="center">
        <template scope="scope">
          <div>
            <router-link class='editfont m-r-5' :to="{name: 'devBugDetail',query: {device_id: scope.row.device_id}}">
              <el-button size="small" type="primary">查看</el-button>
            </router-link>
            <router-link class='editfont m-r-5' :to="{name: 'devBugAdd',query: {id: scope.row.id, device_id: scope.row.device_id}}">
              <el-button size="small" type="primary">编辑</el-button>
            </router-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <footer>
        <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next, sizes" :page-size="postData.page_size" :current-page="postData.page" :total="dataCount" :page-sizes="[10, 20, 50, 100]" @size-change="handleSizeChange">
        </el-pagination>
    </footer>
  </div>
</template>

<script>
  import DeviceType from 'src/components/form/deviceType';
  export default {
    name: 'DevBug',
    components: { DeviceType },
    data() {
      return {
        postData: {
          device_id: '',
          bus_name: '',
          type: '',
          status: '',
          page_size: 10,
          page: 1
        },
        tableData: [],
        dataCount: 0
      };
    },
    methods: {
      handleSizeChange(val) {
        this.postData.page_size = val
        this.postData.page = 1
        this.gettableList()
      },
      checkStatus(index,row) {
        this.$service.post('device/deviceManagement/faceDeviceInfo', { deviceSn: row.deviceSn }).then(res => {
          if (res.data.errorcode == 0) {
            this.tableData[index].LastestApkVersion = res.data.data.LastestApkVersion;
            this.tableData[index].apkType = res.data.data.apkType;
            this.tableData[index].apkVersion = res.data.data.apkVersion;
            this.tableData[index].onLine = res.data.data.onLine;
            _g.toastMsg('success', '更新成功');
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      updateDev(index,row) {
        this.$service.post('device/deviceManagement/faceDeviceUpdate', { deviceSn: row.deviceSn }).then(res => {
          if (res.data.errorcode == 0) {
            _g.toastMsg('success', '升级推送已发送');
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      gettableList() {
        this.$service.get('/device/DeviceExceptionLog/getList', {params: this.postData}).then(res => {
          if (res.data.errorcode == 0) {
            this.tableData = res.data.data.data;
            this.dataCount = res.data.data.total;
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page = curPage;
        this.gettableList();
      },
      search() {
        this.postData.page = 1;
        this.gettableList();
      }
    },
    created() {
      this.postData.device_id = this.$route.params.device_id || ''
      this.gettableList()
    }
  };
</script>

<style scoped>
  .m-r-5 {
    margin-right: 5px;
  }
  .editfont {
    color: #fff;
  }
  .searchbar {
    background-color: #fff;
    min-height: 80px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    border-bottom: 1px solid #ececec;
    padding-bottom: 20px;
  }
  .el-dialog__footer {
    padding: 0;
  }
  .centeritem {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .qricon {
    border: none;
    width: 32px;
    height: 32px;
    background: url(../../assets/images/qrcode.png) no-repeat center;
    background-size: 32px 32px;
  }
</style>








