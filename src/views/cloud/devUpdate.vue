<template>

  <div>
    <div class="ovf-hd searchbar">
      <div class="fl m-l-30">
        <el-input placeholder="设备ID" @keyup.enter.native="search" v-model="postData.device_id" class="w-200 m-r-10 p-t-20"></el-input>
        <el-button type="primary" icon="search" @click="search" class="m-t-20">搜索</el-button>
      </div>
    </div>
    
    <el-table :data="tableData" id="infotable" stripe style="width: 100%">
      <el-table-column align="center" type="index" label="编号" width="80">
        <template scope="scope">
          {{scope.$index + postData.page_size*(postData.page_no-1) + 1}}
        </template>
      </el-table-column>
      <el-table-column prop="device_id" label="设备ID" align="center"></el-table-column>
      <el-table-column prop="old_type_name" label="原设备类型" align="center"></el-table-column>
      <el-table-column prop="bus_name" label="原场馆名称" align="center">
        <template scope="scope">
          {{scope.row.bus_name ? scope.row.bus_name : '--'}}
        </template>
      </el-table-column>
      <el-table-column prop="new_type_name" label="新设备类型" align="center"></el-table-column>
      <el-table-column label="操作" align="center">
        <template scope="scope">
          <el-button size="small" class='editfont m-r-5' type="primary" @click="confirmUpdate(scope.row)">确认更新</el-button>
          <el-button size="small" type="primary" @click="cancelUpdate(scope.row)">取消更新</el-button>
        </template>
      </el-table-column>
    </el-table>

    <div class="pos-rel p-t-20 ovf-hd flexend" v-if="dataCount>0">
      <div class="block">
        <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
        </el-pagination>
      </div>
    </div>

  </div>
</template>

<script>
  import http from 'assets/js/http';
  export default {
    name: 'devUpdate',
    data() {
      return {
        postData: {
          device_id: '', 
          page_size: 10, 
          page_no: 1
        }, 
        tableData: [],
        dataCount: 0 
      };
    }, 
    methods: {
      confirmUpdate(row) {
        this.$confirm(`确认更新设备${row.device_id}?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.apiPost('/device/DeviceExamine/updateDevice', {id: row.id, device_id: row.device_id}).then(res => {
              this.handelResponse(res, data => {
                _g.toastMsg('success', '更新成功');
                setTimeout(() => {
                  this.gettableList();
                }, 1500);
              });
            });
          })
          .catch(() => {
            console.log('err')
          });
      },
      cancelUpdate(row) {
        this.$confirm(`确认取消设备${row.device_id}的更新?`, '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
          .then(() => {
            this.apiPost('/device/DeviceExamine/deleteExamine', {id: row.id, device_id: row.device_id}).then(res => {
              this.handelResponse(res, data => {
                _g.toastMsg('success', '取消更新成功');
                setTimeout(() => {
                  this.gettableList();
                }, 1500);
              });
            });
          })
          .catch(() => {
            console.log('err')
          });
      },
      gettableList() {
        this.apiPost('/device/DeviceExamine/getExamineList', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.tableData = res.data.list;
            this.dataCount = res.data.count;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.gettableList();
      },
      search() {
        this.postData.page_no = 1;
        this.gettableList();
      }
    },
    created() {
      this.gettableList();
    },
    mixins: [http]
  };
</script>

<style scoped>
  .m-r-5 {
    margin-right: 5px;
  }
  .editfont {
    color: #fff;
  }
  .searchbar {
    background-color: #fff;
    min-height: 80px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    border-bottom: 1px solid #ececec;
    padding-bottom: 20px;
  }
 
</style>








