<template>
  <div class="m-b-20 ovf-hd">
    <el-tabs type="border-card">
      <el-tab-pane>
        <span slot="label">版本记录</span>
        <DevVersion />
      </el-tab-pane>
      <el-tab-pane>
        <span slot="label">配置记录</span>
        <DevSet />
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script>
import DevVersion from "./components/DevVersion.vue";
import DevSet from "./components/DevSet.vue";

export default {
  name: 'devChange',
  components: {
    DevVersion,
    DevSet
  }
}
</script>

