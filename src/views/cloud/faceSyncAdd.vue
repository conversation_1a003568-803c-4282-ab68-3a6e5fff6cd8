<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="150px">
      <el-form-item label="商家名称">
        <MerchantSelect v-model="postData.m_id" />
      </el-form-item>
      <el-form-item label="人脸同步">
        <el-switch
          v-model="postData.face_sync"
          :active-value="1"
          :inactive-value="0"
          active-text="开"
          inactive-text="关"
        ></el-switch>
        <el-alert style="width: 380px; margin-top: 10px" type="warning" :closable="false" show-icon>
          开启后，通卡会员在上传人脸时同步人脸特征到通卡门店
        </el-alert>
      </el-form-item>
      <el-form-item label="人脸清除">
        <span>过期</span>
        <el-select v-model="postData.face_clean" size="mini" style="margin: 0 10px" placeholder="请选择">
          <el-option :value="1" label="1天"></el-option>
          <el-option :value="3" label="3天"></el-option>
          <el-option :value="7" label="7天"></el-option>
          <el-option :value="15" label="15天"></el-option>
          <el-option :value="30" label="30天"></el-option>
        </el-select>
        <span>清除</span>
        <el-alert style="width: 540px; margin-top: 10px" type="warning" :closable="false" show-icon>
          商家归属下的场馆会员，所有卡（会籍、私教）都过期超过设置天数，清除人脸特征。
        </el-alert>
      </el-form-item>
      
      <el-form-item label="入场方式">
        <el-radio-group v-model="postData.entry_mode">
          <el-radio :label="0">不限制</el-radio>
          <el-radio :label="1">人脸&二维码二选一</el-radio>
        </el-radio-group>
      </el-form-item>
      
      <el-form-item v-if="postData.entry_mode == 1" label="">
        <span>二维码进场—进—出</span>
        <el-radio-group v-model="postData.qr_one_in_one_out" style="margin-left: 20px;">
          <el-radio :label="0">关闭</el-radio>
          <el-radio :label="1">开启</el-radio>
        </el-radio-group>
        <template v-if="postData.qr_one_in_one_out == 1">
          <span style="margin-left: 20px;">进场间隔</span>
          <el-input-number
            v-model="postData.qr_entry_interval" 
            size="mini" 
            style="width: 90px; margin: 0 10px"
            controls-position="right"
            :min="0"
            :max="600"
          ></el-input-number>
          <span>秒</span>
        </template>
      </el-form-item>
      
      <el-form-item label="备注">
        <el-input class="w-320" type="textarea" :rows="4" placeholder="请输入" v-model="postData.remark"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button data-cy="save-button" type="primary" @click="onSubmit" :disabled="!postData.m_id">保存</el-button>
        <el-button class="m-l-150" @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import MerchantSelect from '@/components/Payment/MerchantSelect.vue'

export default {
  name: 'FaceSyncAdd',
  components: {
    MerchantSelect,
  },
  data() {
    return {
      postData: {
        id: this.$route.query.id || '',
        m_id: '',
        face_sync: 0, // 0关1开，默认关
        face_clean: 1,
        entry_mode: 0, // 入场方式: 0 不限制, 1 人脸&二维码二选一
        qr_one_in_one_out: 0, // 二维码进场开关: 0 关闭, 1 开启
        qr_entry_interval: 30, // 进场间隔，默认 30 秒
        remark: '',
      },
    }
  },
  methods: {
    onSubmit() {
      // if entry_mode is 0, qr_one_in_one_out must be 0, qr_entry_interval must be null
      if (this.postData.entry_mode == 0) {
        this.postData.qr_one_in_one_out = 0
        this.postData.qr_entry_interval = null
      }
      // qr_entry_interval is required when qr_one_in_one_out is 1
      if (this.postData.qr_one_in_one_out == 1 && (this.postData.qr_entry_interval === null || this.postData.qr_entry_interval === undefined || this.postData.qr_entry_interval === '')) {
        this.$message.error('进场间隔不能为空')
        return
      }

      let url = '/device/FaceSync/add_setting'
      if (this.$route.query.id) {
        url = '/device/FaceSync/update_setting'
      }
      this.$service.post(url, this.postData).then((res) => {
        if (res.data.errorcode == 0) {
          _g.toastMsg('success', res.data.errormsg)
          this.$router.back()
        } else {
          _g.toastMsg('warning', res.data.errormsg)
        }
      })
    },
    getInfo() {
      this.$service.post('/device/FaceSync/get_setting', { id: this.postData.id }).then((res) => {
        if (res.data.errorcode == 0) {
          const info = res.data.data.result
          this.postData = {
            id: info.id,
            m_id: String(info.m_id),
            face_sync: Number(info.face_sync),
            face_clean: Number(info.face_clean),
            entry_mode: Number(info.entry_mode) || 0,
            qr_one_in_one_out: Number(info.qr_one_in_one_out) || 0,
            qr_entry_interval: Number(info.qr_entry_interval) || 0,
            remark: info.remark,
          }
        } else {
          _g.toastMsg('warning', res.data.errormsg)
        }
      })
    },
  },
  created() {
    if (this.$route.query.id) {
      this.getInfo()
    }
  },
}
</script>

<style scoped>
.maright-20 {
  margin-right: 20px;
}

.inputlen {
  width: 180px;
}
.maps {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 300px;
}
.amap-demo {
  height: 300px;
}
#map_container {
  width: 100%;
  height: 300px;
}
#panel {
  background-color: white;
  max-height: 100%;
  overflow-y: auto;
}
</style>
