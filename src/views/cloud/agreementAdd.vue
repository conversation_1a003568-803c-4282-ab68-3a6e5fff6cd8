<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="150px">
      <el-form-item label="文件名称" prop="name" :rules="{ required: true, message: '请填写渠道名称'}">
        <el-input class="w-320" v-model="postData.name"></el-input>
      </el-form-item>
      <el-form-item label="文件类型" prop="channel_name">
        <el-select class="w-320" v-model="postData.type" placeholder="文件类型">
          <el-option label="隐私协议" :value="1"></el-option>
          <el-option label="用户协议" :value="0"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="版本号" prop="version" :rules="{ required: true, message: '请填写'}">
        <el-input class="w-320" v-model="postData.version"></el-input>
      </el-form-item>
      <el-form-item label="内容" prop="content" :rules="{ required: true, message: '请填写'}">
        <Editor v-model="postData.content" :compress="false" />
      </el-form-item>
      <el-form-item label="状态">
         <el-switch v-model="postData.status" :active-value="1" :inactive-value="0"></el-switch>
      </el-form-item>
      <el-form-item label="备注" prop="remark" :rules="{ required: true, message: '请填写'}">
        <el-input class="w-320" type="textarea" :rows="2" placeholder="请输入备注内容" v-model="postData.remark"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">保存</el-button>
        <el-button class="m-l-150" @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import http from 'assets/js/http';
import Editor from 'components/form/Editor';
export default {
  name: 'channelAdd',
  data() {
    return {
      postData: {
        id: this.$route.query.id,
        name: '',
        content: '',
        type: 0,
        version: '',
        status: '',
        remark: ''
      }
    }
  },
  components: { Editor },
  methods: {
    onSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const url = this.postData.id?'device/UserAgreement/edit':'device/UserAgreement/add'
          this.apiPost(url, this.postData).then(
            res => {
              if (res.errorcode == 0) {
                _g.toastMsg('success', res.errormsg)
                this.$router.back()
              } else {
                _g.toastMsg('warning', res.errormsg)
              }
            }
          )
        }
      })
    },
    getChannelInfo() {
      this.apiGet('/device/UserAgreement/getInfoById', {
        id: this.postData.id
      })
        .then(res => {
          if (res.errorcode == 0) {
            this.postData = res.data
          } else {
            _g.toastMsg('warning', res.errormsg)
          }
        })
        .catch(error => {
          console.log(error)
        })
    }
  },
  created() {
    if (this.$route.query.id) {
      this.getChannelInfo()
    }
  },
  mixins: [http]
}
</script>

<style scoped>
.maright-20 {
  margin-right: 20px;
}

.inputlen {
  width: 180px;
}
.maps {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 300px;
}
.amap-demo {
  height: 300px;
}
#map_container {
  width: 100%;
  height: 300px;
}
#panel {
  background-color: white;
  max-height: 100%;
  overflow-y: auto;
}
</style>
