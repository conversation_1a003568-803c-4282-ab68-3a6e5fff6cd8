<template>

  <div class="container">
    <div>
      <div class="table-toptit">
        设备信息
      </div>
      <div class="table-topcon" v-if="devInfo && devInfo.device_id">
        <p>场馆名称：{{devInfo.bus_name}}</p>
        <p>设备ID：{{devInfo.device_id}}</p>
        <p>设备类型：{{devInfo.device_type_name}}</p>
        <p>设备名称：{{devInfo.device_name}}</p>
        <p>设备渠道：{{devInfo.channel_name}}</p>
        <p>软件版本：{{devInfo.device_vesion}}</p>
        <p>固件版本：{{devInfo.device_fireward}}</p>
      </div>
    </div>
    <header>
      <div class="fl">
        <div class="table-toptit">
          异常记录
        </div>
      </div>
      <div class="fl m-l-30">
        <router-link :to="{name: 'devBugAdd',query: {device_id: postData.device_id}}">
        <el-button type="success" size="small" icon="el-icon-plus">
          添加记录
        </el-button>
        </router-link>
      </div>
    </header>

    <el-table :data="tableData" id="infotable" stripe>
      <el-table-column prop="type" label="异常类型" align="center">
        <template scope="{row}">
          <span>{{ row.type == 1 ? '识别异常' : row.type == 2 ? '网络异常' : row.type == 3 ? '设备异常' : row.type == 4 ? '开柜异常' : '其它'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="question" label="问题描述" align="center"></el-table-column>
      <el-table-column prop="result" label="预处理结果" align="center"></el-table-column>
      <el-table-column prop="member_info" label="人员信息" align="center"></el-table-column>
      <el-table-column prop="status" label="处理状态" align="center">
        <template scope="{row}">
          <span>{{ row.status == 1 ? '解决中' : row.status == 2 ? '观察中' : row.status == 3 ? '已解决' : '未解决'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="create_time" label="记录时间" align="center"></el-table-column>
    </el-table>
    
    <footer>
        <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next, sizes" :page-size="postData.page_size" :current-page="postData.page" :total="dataCount" :page-sizes="[10, 20, 50, 100]" @size-change="handleSizeChange">
        </el-pagination>
    </footer>
  </div>
</template>

<script>
  import DeviceType from 'src/components/form/deviceType';
  export default {
    name: 'DevBug',
    components: { DeviceType },
    data() {
      return {
        postData: {
          device_id: '',
          bus_name: '',
          type: '',
          status: '',
          page_size: 10,
          page: 1
        },
        devInfo: {},
        tableData: [],
        dataCount: 0
      };
    },
    methods: {
      handleSizeChange(val) {
        this.postData.page_size = val
        this.postData.page = 1
        this.gettableList()
      },
      getDevInfo() {
        this.$service.post('/device/DeviceExceptionLog/detail', {device_id: this.postData.device_id}).then(res => {
          if (res.data.errorcode == 0) {
            this.devInfo = res.data.data;
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      gettableList() {
        if(!this.postData.device_id) {
          return false;
        }
        this.$service.get('/device/DeviceExceptionLog/getList', {params: this.postData}).then(res => {
          if (res.data.errorcode == 0) {
            this.tableData = res.data.data.data;
            this.dataCount = res.data.data.total;
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page = curPage;
        this.gettableList();
      },
      search() {
        this.postData.page = 1;
        this.gettableList();
      }
    },
    created() {
      this.postData.device_id = this.$route.query.device_id || ''
      this.gettableList()
      this.getDevInfo()
    }
  };
</script>

<style lang="less" scoped>
  .m-r-5 {
    margin-right: 5px;
  }
  .editfont {
    color: #fff;
  }
  .searchbar {
    background-color: #fff;
    min-height: 80px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    border-bottom: 1px solid #ececec;
    padding-bottom: 20px;
  }
  .el-dialog__footer {
    padding: 0;
  }
  .centeritem {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .qricon {
    border: none;
    width: 32px;
    height: 32px;
    background: url(../../assets/images/qrcode.png) no-repeat center;
    background-size: 32px 32px;
  }
  .table-toptit {
    padding: 15px;
    font-size: 18px;
    font-weight: bold;
  }
  .table-topcon {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    p {
      padding-left: 15px;
      width: 20%;
      font-size: 14px;
      color: #666;
    }
  }
</style>








