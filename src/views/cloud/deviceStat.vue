<template>
  <div>
    <el-card header="设备新增看板">
      <div ref="deviceChart" class="chart"></div>
    </el-card>
    <el-card style="margin-top: 30px;">
      <div class="container">
        <header>
          <el-date-picker type="daterange" :picker-options="pickerOptions" v-model="dateRange" format="yyyy-MM-dd"
                          value-format="yyyy-MM-dd"
                          @change="dateChange"></el-date-picker>
          <el-checkbox v-model="postData.only_increment" style="margin-left: 30px;">只看新增</el-checkbox>
          <el-button type="primary" @click="getData">统计</el-button>
        </header>
        <main>
          <div class="total-stat">
            <div class="total-main-lef">
              <div class="total-tit">
                指静脉设备数据
              </div>
              <div class="total-con">
                <el-card>
                  <h3>设备总量</h3>
                  <h3>{{totalStat.total}}</h3>
                </el-card>
                <el-card>
                  <h3>新增设备</h3>
                  <h3>{{totalStat.device_increment}}</h3>
                </el-card>
                <el-card>
                  <h3>未划拨设备</h3>
                  <h3>{{totalStat.device_notassign}}</h3>
                </el-card>
                <el-card>
                  <h3>停用设备</h3>
                  <h3>{{totalStat.device_StopNum}}</h3>
                </el-card>
              </div>
            </div>
            <div class="total-main-rig">
              <div class="total-tit">
                人脸设备数据
              </div>
              <div class="total-con">
                 <el-card>
                  <h3>设备总量</h3>
                  <h3>{{totalStat.face_device_all_num}}</h3>
                </el-card>
                <el-card>
                  <h3>新增设备</h3>
                  <h3>{{totalStat.face_device_add_num}}</h3>
                </el-card>
              </div>
            </div>
          </div>
          <div class="table-circle">
            <el-table :data="tableData110">
              <el-table-column label=" " prop="deviceName"></el-table-column>
              <el-table-column label="设备数量" prop="device_num"></el-table-column>
              <el-table-column label="设备通过率" prop="successRate"></el-table-column>
              <el-table-column label="高于0.5分设备通过率" prop="highRate"></el-table-column>
              <el-table-column label=" " header-align="center">
                <template scope="scope">
                  <router-link :to="{ name: 'devStat', params: { begin_time: postData.add_begin_time, end_time: postData.add_end_time, only_increment: postData.only_increment, type: scope.row.deviceType } }">详情</router-link>
                </template>
              </el-table-column>
            </el-table>
            <div class="circle" ref="pieChart110"></div>
          </div>
          <div class="table-circle" style="margin-top: 30px;">
            <el-table :data="tableData148">
              <el-table-column label=" " prop="deviceName"></el-table-column>
              <el-table-column label="设备数量" prop="device_num"></el-table-column>
              <el-table-column label="设备通过率" prop="successRate"></el-table-column>
              <el-table-column label="高于0.5分设备通过率" prop="highRate"></el-table-column>
              <el-table-column label=" " header-align="center">
                <template scope="scope">
                  <router-link :to="{ name: 'devStat', params: { begin_time: postData.add_begin_time, end_time: postData.add_end_time, only_increment: postData.only_increment, type: scope.row.deviceType } }">详情</router-link>
                </template>
              </el-table-column>
            </el-table>
            <div class="circle" ref="pieChart148"></div>
          </div>
        </main>
      </div>
      <div class="container">
        <header>
          <el-select v-model="statType" placeholder="选择设备类型">
            <el-option :value="110" label="110"></el-option>
            <el-option :value="148" label="148"></el-option>
          </el-select>
          <el-checkbox v-model="onlyHigh" style="margin-left: 30px;">只显示高于0.5分的通过率</el-checkbox>
          <el-button @click="generateStat" type="primary">生成图表</el-button>
        </header>
        <div ref="statChart" class="chart"></div>
      </div>
    </el-card>
  </div>
</template>

<script>
  import Echarts from 'echarts'
  import { formatDate } from 'src/assets/js/utils'
  import { mapState, mapActions } from 'vuex';

  export default {
    name: 'deviceStat',
    data() {
      return {
        postData: {
          only_increment: false,
          // add_begin_time: formatDate(new Date(Date.now() - 30 * 24 * 3600 * 1000), 'yyyy-MM-dd'),
          add_begin_time: formatDate(new Date(new Date().setDate(1)), 'yyyy-MM-dd'),
          add_end_time: formatDate(new Date(), 'yyyy-MM-dd')
        },
        statType: 110,
        onlyHigh: false,
        dateRange: [formatDate(new Date(new Date().setDate(1)), 'yyyy-MM-dd'), formatDate(new Date(), 'yyyy-MM-dd')],
        tableData110: [],
        tableData148: [],
        pickerOptions: {
          disabledDate(date) {
            return Date.now() - date < 0
          }
        },
        totalStat: {
          device_StopNum: 0,
          device_increment: 0,
          device_notassign: 0,
          total: 0,
        },
        deviceChart: null,
        deviceChartOption: {
          color: ['#1bd4c9', '#ff696a', '#ff9c28'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              }
            }
          },
          legend: { data: ['指静脉设备','人脸设备', '增量'] },
          xAxis: [
            {
              type: 'category',
              data: [],
              axisPointer: {
                type: 'shadow'
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '数量',
              min: 0,
              max: 400,
            },
            {
              type: 'value',
              name: '增量',
              splitLine: { show: false },
              min: -100,
              max: 200,
              axisLabel: {
                formatter: '{value} %'
              }
            }
          ],
          series: [
            {
              name: '指静脉设备',
              type: 'bar',
              stack: '数量',
              label: {
                normal: { show: true}
              },
              data: []
            },
            {
              name: '人脸设备',
              type: 'bar',
              stack: '数量',
              label: {
                normal: { show: true}
              },
              data: []
            },
            {
              name: '增量',
              type: 'line',
              yAxisIndex: 1,
              label: {
                normal: {
                  show: true,
                  formatter: '{c} %'
                },
              },
              data: []
            },
          ]
        },
        statChart: null,
        statChartOption: {
          color: ['#52a4ea', '#1bd4c9', '#ff696a', '#a76de8', '#ff9c28'],
          tooltip: {
            trigger: 'axis',
            axisPointer: {
              type: 'cross',
              crossStyle: {
                color: '#999'
              }
            }
          },
          legend: { data: ['通过率'] },
          xAxis: [
            {
              type: 'category',
              data: [],
              axisPointer: {
                type: 'shadow'
              }
            }
          ],
          yAxis: [
            {
              type: 'value',
              name: '通过率',
              splitLine: { show: false },
              min: 0,
              max: 100,
              axisLabel: {
                formatter: '{value} %'
              }
            },
          ],
          series: [
            {
              name: '通过率',
              type: 'line',
              label: {
                normal: {
                  show: true,
                  formatter: '{c} %'
                },
              },
              data: []
            },
          ]
        },
        pieChart148: null,
        pieChart148Option: {
          legend: { data: [] },
          tooltip: {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          series: [
            {
              type: 'pie',
              radius: 100,
              name: '设备数量',
              data: [],
              label: {
                normal: { show: false }
              }
            }
          ]
        },
        pieChart110: null,
        pieChart110Option: {
          legend: { data: [] },
          tooltip: {
            trigger: 'item',
            formatter: "{b} : {c} ({d}%)"
          },
          series: [
            {
              type: 'pie',
              radius: 100,
              name: '设备数量',
              data: [],
              label: {
                normal: { show: false }
              }
            }
          ]
        },
        statChartData110: {},
        statChartData148: {}
      }
    },
    mounted() {
      this.deviceChart = Echarts.init(this.$refs.deviceChart);
      this.statChart = Echarts.init(this.$refs.statChart);
      this.pieChart148 = Echarts.init(this.$refs.pieChart148);
      this.pieChart110 = Echarts.init(this.$refs.pieChart110);
    },
    computed: {
      ...mapState(['deviceTypeList']),
    },
    async created() {
      if (this.deviceTypeList && this.deviceTypeList.length == 0) {
        await this.getDeviceTypeList();
      }
      this.getData()
    },
    methods: {
      ...mapActions(['getDeviceTypeList']),
      generateStat() {
        if (this.statType === 110) {
          this.dealStatChart(this.statChartData110)
        } else {
          this.dealStatChart(this.statChartData148)
        }
      },
      getData() {
        const url = '/device/Statistics/deviceSummarizeStatistic'
        this.$service.post(url, this.postData).then(res => {
          if (res.data.errorcode === 0) {
            const data = res.data.data;
            this.dealDeviceData(data.monthStatistic)
            this.totalStat = data.devicetotalStatistic
            this.dealTableData(data.deviceStatistic)
            this.dealStatChartData(data.typeMonthStatistic)
          } else {
            this.$message.error(res.data.errormsg)
          }
        }).catch(err => {
          throw new Error(err);
        });
      },
      dateChange(d) {
        const [s, e] = d || [];
        this.postData.add_begin_time = s;
        this.postData.add_end_time = e;
      },
      getDeviceName(key) {
        let name = ''
        for (const iterator of this.deviceTypeList) {
          if(iterator.id == key) {
            name = iterator.device_type_name;
            break;
          }
        }
        return name;
      },
      dealTableData(data) {
        this.tableData110 = [];
        this.tableData148 = []
        const series148 = []
        const legend148 = []
        const series110 = []
        const legend110 = []
        for (let [key, value] of Object.entries(data)) {
          if (key <= 6) {
            this.tableData148.push({ ...value, highRate: value['0.5successRate'], deviceType: key, deviceName: this.getDeviceName(key) })
            series148.push({ value: value.device_num, name: this.getDeviceName(key) })
            legend148.push(this.getDeviceName(key))
          } else {
            this.tableData110.push({ ...value, highRate: value['0.5successRate'], deviceType: key, deviceName: this.getDeviceName(key) })
            series110.push({ value: value.device_num, name: this.getDeviceName(key) })
            legend110.push(this.getDeviceName(key))
          }
        }
        this.pieChart148Option.legend.data = legend148;
        this.pieChart148Option.series[0].data = series148;
        this.pieChart110Option.legend.data = legend110;
        this.pieChart110Option.series[0].data = series110;
        this.$nextTick(() => {
          this.pieChart148.setOption(this.pieChart148Option)
          this.pieChart110.setOption(this.pieChart110Option)
        })
      },
      dealStatChartData(data) {
        if (data['110']) {
          this.statChartData110 = data['110']
          this.statType = 110;
          this.dealStatChart(this.statChartData110)
        }
        if (data['148']) {
          this.statChartData148 = data['148']
        }
      },
      dealStatChart(data) {
        const date = Object.keys(data);
        const values = Object.values(data);
        let rate = values.map(item => parseFloat(item.successRate) || 0)
        if (this.onlyHigh) { rate = values.map(item => parseFloat(item['0.5successRate']) || 0) }
        this.statChartOption.xAxis[0].data = date;
        this.statChartOption.series[0].data = rate;
        this.$nextTick(() => { this.statChart.setOption(this.statChartOption) })
      },
      dealDeviceData(data) {
        const date = Object.keys(data);
        const values = Object.values(data);
        let deviceNum=[]
        let faceDeviceNum=[]
        let otherdeviceNum=[]
        values.forEach(item=>{
          deviceNum.push(item.num+item.face_num)
          faceDeviceNum.push(item.face_num)
          otherdeviceNum.push(item.num)
        })
        const maxValue = Math.max(...deviceNum);
        const maxY = Math.max(maxValue * 2, 200);
        const rate = values.map(item => parseFloat(item.add_rate) || 0);
        const maxRate = Math.max(...rate, 100);
        const minRate = Math.min(...rate, -100);
        this.deviceChartOption.xAxis[0].data = date;
        this.deviceChartOption.yAxis[0].max = maxY;
        this.deviceChartOption.yAxis[1].max = maxRate;
        this.deviceChartOption.yAxis[1].min = minRate;
        this.deviceChartOption.series[0].data = otherdeviceNum;
        this.deviceChartOption.series[1].data = faceDeviceNum;
        this.deviceChartOption.series[2].data = rate;
        this.$nextTick(() => this.deviceChart.setOption(this.deviceChartOption))
      },
    },
  }
</script>

<style scoped lang="less">
  .chart {
    height: 500px;
    min-height: 50vh;
  }

  .table-circle {
    display: flex;

    .circle {
      width: 300px;
      height: 400px;
    }
  }

  .total-stat {
    display: flex;
    padding: 30px 10px;
    text-align: center;
    .total-main-lef {
      flex: 4;
    }
    .total-main-rig {
      flex: 2;
    }
    .total-tit {
      font-size: 18px;
      font-weight: bold;
      text-align: left;
      line-height: 2.75;
    }
    .total-con {
      display: flex;
    }
    .el-card {
      margin-right: 30px;
      min-width: 140px;
    }
  }
</style>
