<template>
  <div class="container">
    <header>
      <el-input placeholder="商家名称" v-model="postData.mer_name" class="w-150 m-r-10" @keyup.enter.native="search"
        clearable></el-input>
      <el-input placeholder="手机号" v-model="postData.phone" class="w-150 m-r-10" @keyup.enter.native="search"
        clearable></el-input>
      <!-- <el-select v-model="postData.user_type" placeholder="类型" class="w-150 m-r-10">
        <el-option label="全部" value=""></el-option>
        <el-option v-for="(item,index) in userType" :label="item" :value="index" :key="index"></el-option>
      </el-select> -->
      <el-button type="primary" icon="search" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column align="center" type="index" label="编号" width="80">
        <template scope="scope">
          {{ scope.$index + postData.page_size * (postData.page_no - 1) + 1 }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="mer_name" label="商家名称"></el-table-column>
      <el-table-column align="center" prop="phone" label="手机号"></el-table-column>
      <!-- <el-table-column align="center" prop="user_type" label="类型">
        <template scope="scope">
          {{ userType[scope.row.user_type] }}
        </template>
      </el-table-column> -->
      <el-table-column align="center" prop="create_time" label="绑定时间"></el-table-column>
      <el-table-column align="center" label="操作" width="150">
        <template scope="scope">
          <!-- <el-button size="small" type="primary"
                     @click="$router.push({ name: 'cloudUserDetail', params: { veinUid: scope.row.vein_uid }})">查看
          </el-button> -->
          <el-button size="small" type="primary" @click="delUser(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pos-rel p-t-20 ovf-hd flexend">
      <div class="block">
        <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size"
          :current-page="postData.page_no" :total="dataCount">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import http from 'assets/js/http';

export default {
  name: 'userManagement',
  data() {
    return {
      tableData: [],
      userType: ['会员', '会籍', '教练', '运营'],
      postData: {
        mer_name: '',
        phone: '',
        // user_type: '',
        page_size: 10,
        page_no: 1
      },
      dataCount: 0
    };
  },
  methods: {
    getList() {
      this.apiGet('/device/userManagement/palmserviceUserList', this.postData).then(res => {
        if (res.errorcode == 0) {
          this.dataCount = res.data.count;
          this.tableData = res.data.list;
        } else {
          _g.toastMsg('success', res.errormsg);
        }
      });
    },
    delUser(userId) {
      this.$confirm(`<div>
          <div style="text-align:left">该系统<strong style="color:orange">无法清除</strong>用户“微信刷掌”的生物特征，只能取消用户在场馆的运动刷掌服务。</div>
          <div style="text-align:left">如需关闭刷掌生物特征，请用户在【微信会员端-我的页面-刷掌服务中关闭】</div>
        </div>`, '要清除用户的微信刷掌信息吗？', {
        confirmButtonText: '仍要清除',
        cancelButtonText: '取消',
        type: 'warning',
        center: true,
        dangerouslyUseHTMLString: true
      })
        .then(res => {
          if (res === 'confirm') {
            this.apiGet('/device/userManagement/palmserviceUserDel', {id: userId}).then(res => {
              if (res.errorcode == 0) {
                _g.toastMsg('success', '删除成功！');
                this.getList()
              } else {
                _g.toastMsg('error', res.errormsg);
              }
            });
          }
        })

    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.getList();
    },
    search() {
      this.postData.page_no = 1;
      this.getList();
    }
  },
  created() {
    // this.getList();
  },
  activated() {
    // console.log("--activated--");
    if (this.$route.query.mer_name) {
      this.postData.mer_name = this.$route.query.mer_name;
      this.postData.phone = '';
      // this.postData.user_type = '';
      this.postData.page_size = 10;
      this.postData.page_no = 1;
      this.getList();
    } else {
      this.getList();
    }
  },
  deactivated() {
  },
  mixins: [http]
};
</script>
