<template>
  <div class="container" ref="container">
    <header>
      <el-date-picker v-model="rangeArray" :clearable="false" class="m-r-10" type="daterange" @change="changeDate" placeholder="选择日期">
      </el-date-picker>
    </header>
    <div class="graph-frame">
      <div class="m-t-10" v-if="option.series.length>0">
        <div id="busStat" :style="{width: '90%', height: '300px'}">
        </div>
      </div>
    </div>
    <el-table :data="tableData" stripe style="width: 100%;margin-top:15px">
      <el-table-column type="index" label="序号" width="100" align="center"></el-table-column>
      <el-table-column prop="date" label="日期" align="center"></el-table-column>
      <el-table-column prop="all" label="设备总量" align="center"></el-table-column>
      <el-table-column prop="active" label="活跃数量" align="center">
        <template scope="scope">
          <span class="bluefont overtext" size="medium" @click="showDetail(scope.row)" type="text">{{scope.row.active}}</span>
        </template>
      </el-table-column>
       <el-table-column prop="chain_ratio" label="活跃环比" align="center"></el-table-column>
       <el-table-column prop="off_line" label="离线数量" align="center"></el-table-column>
    </el-table>

    <footer>
      <div class="w100 pos-rel ovf-hd flexend" v-if="dataCount>0">
        <div class="block">
          <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page" :total="dataCount">
          </el-pagination>
        </div>
      </div>
    </footer>

    
    <el-dialog title="设备详情" :visible.sync="showDia" width="80%">
      <el-table :data="diaData" class="dia-table" stripe style="width: 100%">
        <el-table-column prop="name" label="设备类型" align="center"></el-table-column>
        <el-table-column prop="all" label="设备总量" align="center"></el-table-column>
        <el-table-column prop="active" label="活跃数量" align="center"></el-table-column>
        <el-table-column prop="chain_ratio" label="活跃环比" align="center"></el-table-column>
        <el-table-column prop="off_line" label="离线数量" align="center"></el-table-column>
      </el-table>
    </el-dialog>
  </div>
</template>

<script>
import echarts from 'echarts'
export default {
  name: 'FaceDevActiveStat',
  data() {
    return {
      width: '80%',
      height: '100px',
      postData: {
        begin_date: _g.formatDate(
          new Date(new Date().getTime() - 3600 * 1000 * 24 * 30),
          'yyyy-MM-dd'
        ),
        end_date: _g.formatDate(new Date(), 'yyyy-MM-dd'),
        page_size: 10,
        page: 1
      },
      rangeArray: [
        _g.formatDate(
          new Date(new Date().getTime() - 3600 * 1000 * 24 * 30),
          'yyyy-MM-dd'
        ),
        _g.formatDate(new Date(), 'yyyy-MM-dd')
      ],
      tableData: [],
      dataCount: 0,
      busIds: [],
      busList: [],
      myEcharts: '',
      option: {
        color: ['#ff9c28','#52a4ea'],
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow' // 默认为直线，可选为：'line' | 'shadow'
          }
        },
        legend: {
          data: ['设备活跃量', '离线数量']
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: []
        },
        yAxis: {
          type: 'value'
        },
        series: [{
          name: '设备活跃量',
          type: 'bar',
          stack: '总量',
          label: {
            normal: {
              show: true,
              position: 'insideRight'
            }
          },
          data: []
        },{
          name: '离线数量',
          type: 'bar',
          stack: '总量',
          label: {
            normal: {
              show: true,
              position: 'insideRight'
            }
          },
          data: []
        }]
      },
      showDia: false,
      diaData: []
    }
  },
  watch: {
    showDia(val) {
      if (!val) {
        this.diaData = []
      }
    }
  },
  methods: {
    getSummaries(param) {
      const { columns, data } = param
      const sums = []
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计'
          return
        }
        if (index === 6) {
          sums[index] = (sums[5] / sums[2] * 100).toFixed() + '%'
          return
        }
        if (!column.label.includes('成功率')) {
          sums[index] = 0
          data.forEach(item => {
            sums[index] += Number(item[column.property]) || 0
          })
        } else {
          if (index === 4) {
            sums[index] = (sums[2] / sums[1] * 100).toFixed() + '%'
          } else {
            sums[index] = (sums[index - 1] / sums[2] * 100).toFixed() + '%'
          }
        }
      })

      return sums
    },
    showDetail(row) {
      this.showDia = true
      this.$service.get('/device/Statistics/getFaceEquipmentInfo', {params: {
        date: row.date
      }}).then(res => {
        if (res.data.errorcode == 0) {
          this.diaData = res.data.data
        } else {
          _g.toastMsg('warning', res.data.errormsg)
        }
      })
    },
    genGraph() {
        this.$service.get('/device/Statistics/faceEquipmentBrokenLine', {params: {
          begin_date: this.postData.begin_date,
          end_date: this.postData.end_date
        }}).then(res => {
            if (res.data.errorcode == 0) {
              const resData = res.data.data
              this.option.xAxis.data = Object.keys(resData)
              this.option.series[0].data = []
              this.option.series[1].data = []
              for (const key in resData) {
                if (resData.hasOwnProperty(key)) {
                  const element = resData[key];
                  this.option.series[0].data.push(element.active)
                  this.option.series[1].data.push(element.off_line)
                }
              }
              this.$nextTick(() => {
               this.myEcharts.setOption(this.option)
              })
            } else {
              _g.toastMsg('warning', res.errormsg)
            }
          }
        )
    },
    gettableList() {
      this.$service.get('/device/Statistics/getFaceEquipmentActive', {params: this.postData}).then(
        res => {
          if (res.data.errorcode == 0) {
            const resData = res.data.data
            this.tableData = resData.data
            this.dataCount = resData.total
          } else {
            _g.toastMsg('warning', res.errormsg)
          }
        }
      )
    },
    changeDate(value) {
      let sTime = value[0]
      let eTime = value[1]
      this.postData.begin_date = _g.formatDate(sTime, 'yyyy-MM-dd')
      this.postData.end_date = _g.formatDate(eTime, 'yyyy-MM-dd')
      this.postData.page = 1
      this.gettableList()
      this.genGraph()
    },
    handleCurrentChange(curPage) {
      this.postData.page = curPage
      this.gettableList()
    }
  },
  mounted() {
    this.width =
      this.$refs.container.getBoundingClientRect().right -
      this.$refs.container.getBoundingClientRect().left
    this.height = this.width * 2 / 3 + 'px'
    this.width = this.width + 'px'
    this.myEcharts =  echarts.init(document.getElementById('busStat'))
  },
  created() {
    this.gettableList()
    this.genGraph()
  }
}
</script>

<style scoped>
.w100 {
  width: 100%;
}
.search-line {
  min-height: 80px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  border-top: 1px solid #ececec;
  border-bottom: 1px solid #ececec;
}
.bluefont {
  color: #409eff;
  cursor: pointer;
  width: 100%;
  display: inline-block;
}
.overtext {
  text-overflow: ellipsis;
  white-space: nowrap;
}
.dia-header {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  align-items: center;
  font-size: 18px;
  padding-bottom: 15px;
  padding-left: 10px;
  padding-right: 10px;
}
.dia-table {
  margin-bottom: 20px;
}
</style>








