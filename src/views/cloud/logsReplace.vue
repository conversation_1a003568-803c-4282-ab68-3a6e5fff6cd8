<template>
  <div class="iframe-box">
    <iframe :src="iframeSrc" class="frame-content"></iframe>
  </div>
</template>
<script>
export default {
  name: 'logsReplace',
  data() {
    return {
      iframeSrc: '',
      betaSrc: 'http://**************:5601',
      boSrc: "https://**************:8601/app/kibana"
    }
  },
  methods: {
    getiframeUrl() {
      let host = window.location.host;
      let subDomain = host.split('.')[0];
      if (subDomain === 'bo') {
        this.iframeSrc = this.boSrc;
      } else {
        this.iframeSrc = this.betaSrc;
      } 
    }
  },
  created() {
    this.getiframeUrl();
  }
}
</script>
<style scoped lang="less">
.iframe-box {
  position: absolute;
  left: 20px;
  right: 20px;
  top: 20px;
  bottom: 10px;
  .frame-content {
    width: 100%;
    height: 100%
  }
}
</style>
