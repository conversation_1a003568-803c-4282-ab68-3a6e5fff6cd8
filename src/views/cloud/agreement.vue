<template>
  <div class="container">
    <header>
      <div class="fl">
        <el-button type="success" size="small" icon="el-icon-plus" @click="$router.push({ name: 'cloudAgreementAdd'})">
          新增协议
        </el-button>
      </div>
      <div class="fl m-l-30">
        <el-input placeholder="文件名称" v-model="postData.name" class="w-150 m-r-10"></el-input>
        <el-select v-model="postData.type" placeholder="文件类型" class="w-150 m-r-10">
          <el-option label="文件类型" value=""></el-option>
          <el-option label="隐私协议" value="1"></el-option>
          <el-option label="用户协议" value="0"></el-option>
        </el-select>
        <el-input placeholder="版本号" v-model="postData.version" class="w-150 m-r-10"></el-input>
        <el-button type="primary" icon="search"  @click="search">搜索</el-button>
      </div>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column align="center" type="index" label="序号" width="80">
        <template scope="scope">
          {{scope.$index + postData.page_size*(postData.page-1) + 1}}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="name" label="文件名称"></el-table-column>
      <el-table-column align="center" prop="type" label="文件类型">
        <template scope="scope">
          {{scope.row.type===0 ? '用户协议' : '隐私协议'}}
        </template>
      </el-table-column>
     
      <el-table-column align="center" prop="version" label="版本号"></el-table-column>
      <el-table-column align="center" prop="create_time" label="创建时间"></el-table-column>
       <el-table-column align="center" label="状态">
        <template scope="scope">
          <el-switch v-model="scope.row.status" :active-value="1" :inactive-value="0" :disabled="scope.row.status==1" @change="statusChange(scope.row, scope.$index)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column align="center" prop="remark" label="备注"></el-table-column>
      <el-table-column align="center" label="操作" width="150">
        <template scope="scope">
          <el-button size="small" type="primary" @click="$router.push({ name: 'cloudAgreementAdd', query: { id: scope.row.id }})">编辑</el-button>
          <el-button size="small" type="danger" @click="delChannel(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
       <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page" :total="dataCount"></el-pagination>
    </footer>
    
  </div>
</template>

<script>
import http from 'assets/js/http';
export default {
  name: 'channel',
  data() {
    return {
      tableData: [],
      postData: {
        version: '',
        name: '',
        type: '',
        page_size: 10,
        page: 1
      },
      dataCount: 0
    };
  },
  methods: {
    delChannel(id) {
      this.$confirm('确认删除吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          })
          .then(() => {
            this.apiPost('device/UserAgreement/delOne', { id }).then(res => {
              if (res.errorcode == 0) {
                _g.toastMsg('success', '删除成功');
                this.getList()
              } else {
                _g.toastMsg('warning', res.errormsg);
              }
            });
          })
          .catch(() => {
          });
    },
    getList() {
      this.apiGet('device/UserAgreement/getList', this.postData).then(res => {
        if (res.errorcode == 0) {
          this.dataCount = res.data.total;
          this.tableData = res.data.data;
        } else {
          _g.toastMsg('warning', res.errormsg);
        }
      });
    },
    statusChange(info,_index) {
      this.$confirm(`是否要启用《${info.name}》版本${info.version}的${info.type===0 ? '用户协议' : '隐私协议'}`, '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          })
          .then(() => {
            this.apiPost('device/UserAgreement/turnOn', { id: info.id }).then(res => {
              if (res.errorcode == 0) {
                this.getList();
                _g.toastMsg('success', res.errormsg);
              } else {
                this.tableData[_index].status = 0
                _g.toastMsg('warning', res.errormsg);
              }
            });
          }).catch(()=>{
            this.tableData[_index].status = 0
          })
      
    },
    handleCurrentChange(curPage) {
      this.postData.page = curPage;
      this.getList();
    },
    search() {
      this.postData.page = 1;
      this.getList();
    }
  },
  created() {
    this.getList();
  },
  mixins: [http]
};
</script>
