<template>
  <div class="container">
    <header>
      <el-input placeholder="场馆名称" v-model="postData.bus_name" class="w-150 m-r-10"></el-input>
      <el-input placeholder="手机号" v-model="postData.phone" class="w-150 m-r-10"></el-input>
      <el-select v-model="postData.user_type" placeholder="类型" class="w-150 m-r-10">
        <el-option label="全部" value=""></el-option>
        <el-option v-for="(item,index) in userType" :label="item" :value="index" :key="index"></el-option>
      </el-select>
      <el-button type="primary" icon="search" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe>
      <el-table-column align="center" type="index" label="编号" width="80">
        <template scope="scope">
          {{scope.$index + postData.page_size*(postData.page_no-1) + 1}}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="bus_name" label="场馆名称"></el-table-column>
      <el-table-column align="center" prop="phone" label="手机号"></el-table-column>
      <el-table-column align="center" prop="user_type" label="类型">
        <template scope="scope">
          {{ userType[scope.row.user_type] }}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="bind_time" label="绑定时间"></el-table-column>
      <el-table-column align="center" label="操作" width="150">
        <template scope="scope">
          <el-button size="small" type="primary"
                     @click="$router.push({ name: 'cloudUserDetail', params: { veinUid: scope.row.vein_uid }})">查看
          </el-button>
          <el-button size="small" type="primary" @click="delUser(scope.row.vein_uid)">删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    <div class="pos-rel p-t-20 ovf-hd flexend">
      <div class="block">
        <el-pagination
          @current-change="handleCurrentChange"
          layout="prev, pager, next"
          :page-size="postData.page_size"
          :current-page="postData.page_no"
          :total="dataCount">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
  import http from 'assets/js/http';

  export default {
    name: 'userManagement',
    data() {
      return {
        tableData: [],
        userType: ['会员', '会籍', '教练', '运营'],
        postData: {
          bus_name: '',
          phone: '',
          user_type: '',
          page_size: 10,
          page_no: 1
        },
        dataCount: 0
      };
    },
    methods: {
      getList() {
        this.apiGet('device/userManagement/listData', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.dataCount = res.data.count;
            this.tableData = res.data.list;
          } else {
            _g.toastMsg('success', res.errormsg);
          }
        });
      },
      delUser(userId) {
        this.$confirm('确认删除吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          })
          .then(() => {
            this.apiGet('device/UserManagement/delVeinfeature', {vein_uid: userId}).then(res => {
              if (res.errorcode == 0) {
                _g.toastMsg('success', '删除成功！');
                this.getList()
              } else {
                _g.toastMsg('error', res.errormsg);
              }
            });
          })
        
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.getList();
      },
      search() {
        this.postData.page_no = 1;
        this.getList();
      }
    },
    created() {
      // this.getList();
    },
    activated() {
      // console.log("--activated--");
      if(this.$route.query.bus_name) {
        this.postData.bus_name = this.$route.query.bus_name;
        this.postData.phone = '';
        this.postData.user_type = '';
        this.postData.page_size = 10;
        this.postData.page_no = 1;
        this.getList();
      } else {
        this.getList();
      }
    },
    deactivated() {
    },
    mixins: [http]
  };
</script>
