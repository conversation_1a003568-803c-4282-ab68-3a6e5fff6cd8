<template>
  <div class="container">
    <header>
      <div class="fl m-l-30">
        <MerchantSelect v-model="postData.m_id" />
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          :clearable="false"
          range-separator="~"
          start-placeholder="开始时间"
          end-placeholder="结束时间"
          value-format="yyyy-MM-dd"
          class="w-300 m-r-10"
          @change="dateChange"
        ></el-date-picker>
        <el-button type="primary" icon="search" @click="search">搜索</el-button>
      </div>
    </header>

    <el-table :data="tableData" id="infotable" stripe style="width: 100%">
      <el-table-column align="center" type="index" label="序号" width="80">
        <template scope="scope">
          {{ scope.$index + postData.page_size * (postData.page_no - 1) + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="m_name" label="商家名称" width="100" align="center"></el-table-column>
      <el-table-column prop="operator_name" label="操作人" align="center"></el-table-column>
      <el-table-column prop="create_time" label="操作时间" align="center"></el-table-column>
      <el-table-column prop="remark" label="操作记录" align="center"></el-table-column>
    </el-table>

    <footer>
      <div class="left"></div>
      <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next, sizes"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
      ></el-pagination>
    </footer>
  </div>
</template>

<script>
import MerchantSelect from '@/components/Payment/MerchantSelect.vue'

export default {
  components: {
    MerchantSelect,
  },
  data() {
    return {
      dateRange: [
        _g.formatDate(new Date(Date.now() - 7 * 3600 * 1000 * 24), 'yyyy-MM-dd'),
        _g.formatDate(new Date(), 'yyyy-MM-dd'),
      ],
      postData: {
        m_id: '',
        s_date: _g.formatDate(new Date(Date.now() - 7 * 3600 * 1000 * 24), 'yyyy-MM-dd'),
        e_date: _g.formatDate(new Date(), 'yyyy-MM-dd'),
        page_size: 10,
        page_no: 1,
      },
      tableData: [],
      dataCount: 0,
    }
  },
  methods: {
    dateChange(arr) {
      arr = arr || ['', '']
      const [s_date, e_date] = arr
      this.postData.s_date = s_date
      this.postData.e_date = e_date
    },
    handleSizeChange(val) {
      this.postData.page_size = val
      this.postData.page_no = 1
      this.gettableList()
    },
    gettableList() {
      this.$service.post('/device/FaceSync/get_operator_log_list', this.postData).then((res) => {
        if (res.data.errorcode == 0) {
          this.tableData = res.data.data.list
          this.dataCount = res.data.data.count
        } else {
          _g.toastMsg('warning', res.data.errormsg)
        }
      })
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage
      this.gettableList()
    },
    search() {
      this.postData.page_no = 1
      this.gettableList()
    },
  },
  async created() {
    this.postData.m_id = String(this.$route.query.m_id || '')
    this.gettableList()
  },
}
</script>
