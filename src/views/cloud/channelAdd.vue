<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="150px">
      <el-form-item label="设备类型">
        <DeviceType class="w-320" v-model="postData.device_type_id"></DeviceType>
      </el-form-item>
      <el-form-item label="渠道名称" prop="channel_name" :rules="{ required: true, message: '请填写渠道名称'}">
        <el-input class="w-320" v-model="postData.channel_name"></el-input>
      </el-form-item>
      <el-form-item label="软件版本">
        <el-select class="w-320" v-model="postData.version_id" placeholder="设备类型">
          <el-option v-for="item in versionList" :value="item.id" :label="item.version_code" :key="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="渠道状态">
         <el-switch v-model="postData.channel_status" :active-value="1" :inactive-value="0"></el-switch>
      </el-form-item>
      <el-form-item label="备注">
        <el-input class="w-320" type="textarea" :rows="2" placeholder="请输入备注内容" v-model="postData.channel_remark"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">保存</el-button>
        <el-button class="m-l-150" @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import http from 'assets/js/http'
import DeviceType from 'src/components/form/deviceType'
export default {
  name: 'channelAdd',
  data() {
    return {
      postData: {
        id: this.$route.query.id,
        device_type_id: this.$route.query.device_type_id || 1,
        channel_name: '',
        version_id: '',
        channel_status: 1,
        channel_remark: ''
      },
      versionList: []
    }
  },
  components: { DeviceType },
  watch: {
    'postData.device_type_id'(val, oldVal) {
      if (val !== oldVal) {
        this.getVersionList();
      }
    }
  },
  methods: {
    onSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.apiPost('/device/channelManagement/save', this.postData).then(
            res => {
              if (res.errorcode == 0) {
                _g.toastMsg('success', res.errormsg)
                this.$router.back()
              } else {
                _g.toastMsg('warning', res.errormsg)
              }
            }
          )
        }
      })
    },
    getVersionList() {
      this.apiGet('/device/channelManagement/versionList', {
        device_type_id: this.postData.device_type_id
      })
        .then(res => {
          if (res.errorcode == 0) {
            this.versionList = res.data.data
          } else {
            _g.toastMsg('warning', res.errormsg)
          }
        })
        .catch(error => {
          console.log(error)
        })
    },
    getChannelInfo() {
      this.apiGet('/device/channelManagement/info', {
        channel_id: this.postData.id
      })
        .then(res => {
          if (res.errorcode == 0) {
            this.postData = res.data.data
            this.$nextTick(() => {
              this.getVersionList()
            })
          } else {
            _g.toastMsg('warning', res.errormsg)
          }
        })
        .catch(error => {
          console.log(error)
        })
    }
  },
  created() {
    this.getVersionList()
    if (this.$route.query.id) {
      this.getChannelInfo()
    }
  },
  mixins: [http]
}
</script>

<style scoped>
.maright-20 {
  margin-right: 20px;
}

.inputlen {
  width: 180px;
}
.maps {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 300px;
}
.amap-demo {
  height: 300px;
}
#map_container {
  width: 100%;
  height: 300px;
}
#panel {
  background-color: white;
  max-height: 100%;
  overflow-y: auto;
}
</style>
