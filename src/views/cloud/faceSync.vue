<template>
  <div class="container">
    <header>
      <div class="fl">
        <router-link :to="{ name: 'faceSyncAdd' }">
          <el-button data-cy="add-button" type="success" size="small" icon="el-icon-plus">新增</el-button>
        </router-link>
      </div>
      <div class="fl m-l-30">
        <MerchantSelect v-model="postData.m_id" />
        <el-button data-cy="search-button" type="primary" icon="search" @click="search">搜索</el-button>
      </div>
    </header>

    <el-table :data="tableData" id="infotable" stripe>
      <el-table-column align="center" type="index" label="编号" width="80">
        <template scope="scope">
          {{ scope.$index + postData.page_size * (postData.page_no - 1) + 1 }}
        </template>
      </el-table-column>
      <el-table-column prop="m_name" label="商家名称" align="center"></el-table-column>
      <el-table-column prop="create_time" label="最新编辑时间" align="center"></el-table-column>
      <el-table-column prop="face_sync" label="同步状态" align="center">
        <template scope="{row}">
          <!-- 1开，0关 -->
          <el-tag v-if="row.face_sync == 1" type="success" size="mini">开启</el-tag>
          <el-tag v-else type="danger" size="mini">关闭</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="face_clean" label="过期清除" align="center">
        <template scope="{row}">
          <el-tag size="mini">{{ row.face_clean }} 天</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="remark" label="备注" align="center"></el-table-column>
      <el-table-column label="操作" align="center">
        <template scope="scope">
          <div>
            <router-link class="editfont m-r-5" :to="{ name: 'faceSyncLog', query: { m_id: scope.row.m_id } }">
              <el-button data-cy="log-button" size="small" type="primary">操作日志</el-button>
            </router-link>
            <router-link class="editfont m-r-5" :to="{ name: 'faceSyncAdd', query: { id: scope.row.id } }">
              <el-button size="small" type="primary">编辑</el-button>
            </router-link>
            <el-button data-cy="delete-button" size="small" @click="confirmDelete(scope.row)" type="danger">删除</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>

    <footer>
      <el-pagination
        @current-change="handleCurrentChange"
        layout="prev, pager, next, sizes"
        :page-size="postData.page_size"
        :current-page="postData.page_no"
        :total="dataCount"
        :page-sizes="[10, 20, 50, 100]"
        @size-change="handleSizeChange"
      ></el-pagination>
    </footer>
  </div>
</template>

<script>
import MerchantSelect from '@/components/Payment/MerchantSelect.vue'

export default {
  name: 'FaceSync',
  components: {
    MerchantSelect,
  },
  data() {
    return {
      postData: {
        m_id: '',
        page_size: 10,
        page_no: 1,
      },
      tableData: [],
      dataCount: 0,
    }
  },
  methods: {
    confirmDelete(item) {
      this.$confirm(`确认删除 "${item.m_name}" ?`, '提示')
        .then(() => {
          _g.openGlobalLoading()
          this.$service.post('/device/FaceSync/delete_setting', { id: item.id }).then((res) => {
            _g.closeGlobalLoading()
            if (res.data.errorcode == 0) {
              _g.toastMsg('success', '操作成功')
              this.gettableList()
            } else {
              _g.toastMsg('warning', res.errormsg)
            }
          })
        })
        .catch(() => {
          // handle error
        })
    },
    gettableList() {
      this.$service.post('/device/FaceSync/get_setting_list', this.postData).then((res) => {
        if (res.data.errorcode == 0) {
          this.tableData = res.data.data.list
          this.dataCount = res.data.data.count
        } else {
          _g.toastMsg('warning', res.data.errormsg)
        }
      })
    },
    handleSizeChange(val) {
      this.postData.page_size = val
      this.postData.page_no = 1
      this.gettableList()
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage
      this.gettableList()
    },
    search() {
      this.postData.page_no = 1
      this.gettableList()
    },
  },
  created() {
    this.gettableList()
  },
}
</script>

<style scoped>
.m-r-5 {
  margin-right: 5px;
}
.editfont {
  color: #fff;
}
.searchbar {
  background-color: #fff;
  min-height: 80px;
  display: flex;
  justify-content: flex-start;
  align-items: center;
  flex-wrap: wrap;
  border-bottom: 1px solid #ececec;
  padding-bottom: 20px;
}
.el-dialog__footer {
  padding: 0;
}
.centeritem {
  display: flex;
  align-items: center;
  justify-content: center;
}
.qricon {
  border: none;
  width: 32px;
  height: 32px;
  background: url(../../assets/images/qrcode.png) no-repeat center;
  background-size: 32px 32px;
}
</style>
