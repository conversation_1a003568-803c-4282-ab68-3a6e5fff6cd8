<template>

  <div class="container">
    <header>
      <div class="fl m-l-30">
        <BusSelect class="w-150 m-r-10" v-model="postData.bus_id" />
        <el-input placeholder="设备SN" @keyup.enter.native="search" v-model="postData.device_sn" class="w-150 m-r-10"></el-input>
        <el-date-picker v-model="dateRange" type="daterange" :clearable="false" range-separator="~" start-placeholder="开始时间"
          end-placeholder="结束时间" value-format="yyyy-MM-dd" class="w-300 m-r-10" @change="dateChange"></el-date-picker>
        <el-button type="primary" icon="search" @click="search">搜索</el-button>
      </div>
    </header>

    <el-table :data="tableData" id="infotable" stripe style="width: 100%">
      <el-table-column align="center" type="index" label="编号" width="80">
        <template scope="scope">
          {{scope.$index + postData.page_size*(postData.page_no-1) + 1}}
        </template>
      </el-table-column>
      <el-table-column prop="bus_name" label="场馆名称" width="100" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.bus_name ? scope.row.bus_name: '--'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="device_sn" label="设备SN" align="center"></el-table-column>
      <el-table-column prop="device_name" label="设备名称" align="center"></el-table-column>
      <el-table-column prop="created_time" label="时间" align="center"></el-table-column>
      <el-table-column prop="head_count" label="区域人数" width="120" align="center"></el-table-column>
      <el-table-column label="图像" align="center">
        <template scope="scope">
          <div v-if="scope.row.picture_url">
            <el-button @click="showImgModal(scope.row.picture_url)" size="small" type="text">预览</el-button>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <footer>
      <div class="left">
      </div>
        <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next, sizes" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount" :page-sizes="[10, 20, 50, 100]" @size-change="handleSizeChange">
        </el-pagination>
    </footer>
    <el-dialog :visible.sync="showModal" title="图像">
      <img :src="modalImg" style="width: 100%" alt="" />
    </el-dialog>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  import BusSelect from 'src/components/Common/BusSelect.vue'
  export default {
    data() {
      return {
        dateRange: [
          _g.formatDate(new Date(Date.now() - 7 * 3600 * 1000 * 24), 'yyyy-MM-dd'), 
          _g.formatDate(new Date(), 'yyyy-MM-dd')
        ],
        postData: {
          s_date: _g.formatDate(new Date(Date.now() - 7 * 3600 * 1000 * 24), 'yyyy-MM-dd'),
          e_date: _g.formatDate(new Date(), 'yyyy-MM-dd'),
          device_sn: this.$route.query.device_sn || '',
          bus_id: '',
          page_size: 10,
          page_no: 1
        },
        tableData: [],
        dataCount: 0,
        modalImg: '',
        showModal: false,
      };
    },
    components: {
      BusSelect
    },
    methods: {    
      
      showImgModal(img) {
        this.modalImg = img;
        this.showModal = true;
      },  
      dateChange(arr) {
        arr = arr || ['',''];
        const [s_date, e_date] = arr;
        this.postData.s_date = s_date;
        this.postData.e_date = e_date;
      },
      handleSizeChange(val) {
        this.postData.page_size = val
        this.postData.page_no = 1
        this.gettableList()
      },
      gettableList() {
        this.apiGet('/device/CameraDevice/get_device_log_list', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.tableData = res.data.list;
            this.dataCount = res.data.count;
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.gettableList();
      },
      search() {
        this.postData.page_no = 1;
        this.gettableList();
      }
    },
    created() {
      this.gettableList();
    },
    deactivated() {
    },
    mixins: [http]
  };
</script>

<style lang="less" scoped>
  
</style>








