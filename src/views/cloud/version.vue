<template>
  <div class="container">
    <header>
      <div class="fl">
        <el-button type="success" size="small" icon="el-icon-plus" @click="$router.push({ name: 'cloudVersionAdd',query: { device_type_id: postData.device_type_id } })">
          新增版本
        </el-button>
      </div>
      <div class="fl m-l-30">
        <DeviceType v-model="postData.device_type_id">
           <el-option value="" label="设备类型"></el-option>
        </DeviceType>
        <el-input placeholder="版本号" v-model="postData.version_code" class="w-150 m-r-10"></el-input>
        <el-input placeholder="备注" v-model="postData.version_remark" class="w-150 m-r-10"></el-input>
        <el-select v-model="postData.version_status" placeholder="状态" class="w-150 m-r-10">
          <el-option label="状态" value=""></el-option>
          <el-option label="启用" value="1"></el-option>
          <el-option label="停用" value="0"></el-option>
        </el-select>
        <el-button type="primary" icon="search"  @click="search">搜索</el-button>
      </div>
    </header>
    <el-table :data="tableData" stripe @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55"></el-table-column>
      <el-table-column align="center" type="index" label="编号" width="80">
        <template scope="scope">
          {{scope.$index + postData.page_size*(postData.page_no-1) + 1}}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="device_type_name" label="设备类型"></el-table-column>
      <el-table-column align="center" prop="version_code" label="版本号"></el-table-column>
      <el-table-column align="center" prop="download_link" label="地址"></el-table-column>
      <el-table-column align="center" prop="version_remark" label="备注"></el-table-column>
      <el-table-column align="center" label="状态">
        <template scope="scope">
          <el-switch v-model="scope.row.version_status" :active-value="1" :inactive-value="0" @change="statusChange(scope.row.version_status,scope.row.id)"></el-switch>
        </template>
      </el-table-column>
      <el-table-column align="center" label="操作" width="100">
        <template scope="scope">
          <el-button size="small" type="primary" @click="$router.push({ name: 'cloudVersionAdd', query: { id: scope.row.id,device_type_id: postData.device_type_id }})">编辑</el-button>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
      <el-dropdown @command="handleCommand">
        <el-button type="success">
          更多操作<i class="el-icon-arrow-down el-icon--right"></i>
        </el-button>
        <el-dropdown-menu slot="dropdown">
          <el-dropdown-item command="1">删除版本</el-dropdown-item>
        </el-dropdown-menu>
      </el-dropdown>
      </div>
       <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount"></el-pagination>
    </footer>
    
  </div>
</template>

<script>
import http from 'assets/js/http';
import DeviceType from 'src/components/form/deviceType';
import { mapState } from 'vuex';
export default {
  name: 'versionCtrl',
  data() {
    return {
      tableData: [],
      selectedDevice: [],
      postData: {
        version_code: '',
        version_remark: '',
        version_status: '',
        device_type_id: '',
        page_size: 10,
        page_no: 1
      },
      dataCount: 0
    };
  },
  components: { DeviceType },
  computed: {
    selectedDeviceIds() {
      let ids = [];
      this.selectedDevice.forEach(item => {
        ids.push(item.id);
      });
      return ids;
    }
  },
  methods: {
    handleSelectionChange(val) {
      this.selectedDevice = val;
    },
    handleCommand(command) {
      if(command == 1) {
        this.delChannel()
      }
    },
    delChannel() {
      if (this.selectedDevice && this.selectedDevice.length < 1) {
        _g.toastMsg('warning', '请先勾选');
        return;
      } 
      this.$confirm('确认删除吗?', '提示', {
            confirmButtonText: '确定',
            cancelButtonText: '取消'
          })
          .then(() => {
            this.apiPost('device/versionManagement/deletesVersion', { version_ids: this.selectedDeviceIds.join(',') }).then(res => {
              if (res.errorcode == 0) {
                _g.toastMsg('success', '删除成功');
                this.getList()
              } else {
                _g.toastMsg('warning', res.errormsg);
              }
            });
          })
          .catch(() => {
          });
    },
    getList() {
      this.apiGet('device/versionManagement/listData', this.postData).then(res => {
        if (res.errorcode == 0) {
          this.dataCount = res.data.count;
          this.tableData = res.data.list;
        } else {
          _g.toastMsg('success', res.errormsg);
        }
      });
    },
    statusChange(newVal,id) {
      this.apiPost('device/versionManagement/updateStatus', { version_id: id, version_status: newVal}).then(res => {
        _g.toastMsg('success', res.errormsg);
      });
    },
    handleCurrentChange(curPage) {
      this.postData.page_no = curPage;
      this.getList();
    },
    search() {
      this.postData.page_no = 1;
      this.getList();
    }
  },
  created() {
    // this.postData.device_type_id = +this.$route.query.id;
    // this.getList();
    // console.log("--created--");
  },
  activated() {
      // console.log("--activated--");
      if(this.$route.query.device_type_id) {
        this.postData.device_type_id = +this.$route.query.device_type_id;
        this.postData.version_code = '';
        this.postData.version_remark = '';
        this.postData.version_status = '';
        this.postData.page_size = 10;
        this.postData.page_no = 1;
        this.getList();
      } else {
        this.getList();
      }
    },
    deactivated() {
    },
  mixins: [http]
};
</script>
