<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="150px">
      <el-form-item label="场馆名称">
        <BusSelect class="w-320" v-model="postData.bus_id"></BusSelect>
      </el-form-item>
      <el-form-item label="设备SN" prop="device_sn" :rules="{ required: true, message: '请填写'}">
        <el-input class="w-320" v-model="postData.device_sn"></el-input>
      </el-form-item>
      <el-form-item label="设备名称" prop="device_name">
        <el-input class="w-320" v-model="postData.device_name"></el-input>
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input class="w-320" type="textarea" :rows="2" placeholder="请输入备注内容" v-model="postData.remark"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">保存</el-button>
        <el-button class="m-l-150" @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import http from 'assets/js/http'
import BusSelect from 'src/components/Common/BusSelect.vue'
export default {
  name: 'cameraAdd',
  data() {
    return {
      postData: {
        camera_device_id: this.$route.query.id || '',
        bus_id: '',
        device_sn: '',
        device_name: '',
        remark: ''
      },
    }
  },
  components: { BusSelect },
  methods: {
    onSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          const url = this.$route.query.id ? '/device/CameraDevice/update_device' : '/device/CameraDevice/add_device'
          this.apiPost(url, this.postData).then(
            res => {
              if (res.errorcode == 0) {
                _g.toastMsg('success', res.errormsg)
                this.$router.back()
              } else {
                _g.toastMsg('warning', res.errormsg)
              }
            }
          )
        }
      })
    },
    getInfo() {
      this.apiGet('/device/CameraDevice/get_device_info', {
        camera_device_id: this.postData.camera_device_id
      })
        .then(res => {
          if (res.errorcode == 0) {
            this.postData = {
              ...res.data.info,
              camera_device_id: this.postData.camera_device_id
            }
          } else {
            _g.toastMsg('warning', res.errormsg)
          }
        })
    }
  },
  created() {
    if (this.$route.query.id) {
      this.getInfo()
    }
  },
  mixins: [http]
}
</script>

<style scoped>
.maright-20 {
  margin-right: 20px;
}

.inputlen {
  width: 180px;
}
.maps {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 300px;
}
.amap-demo {
  height: 300px;
}
#map_container {
  width: 100%;
  height: 300px;
}
#panel {
  background-color: white;
  max-height: 100%;
  overflow-y: auto;
}
</style>
