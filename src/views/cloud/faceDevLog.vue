<template>

  <div class="container">
    <div class="ovf-hd searchbar">
      <div class="fl m-l-30">
        <el-date-picker v-model="rangeArray" :clearable="true" class="m-r-10" type="daterange" @change="pickChange" placeholder="选择日期"></el-date-picker>
        <el-select v-model="postData.change_type" class="w-150 m-r-10" placeholder="类型">
          <el-option value="" label="全部类型"></el-option>
          <el-option :value="1" label="划拨"></el-option>
          <el-option :value="0" label="激活"></el-option>
        </el-select>
        <el-input placeholder="操作账号" @keyup.enter.native="search" v-model="postData.operator_name" class="w-150 m-r-10 p-t-20"></el-input>
        <el-button type="primary" icon="search" @click="search" class="m-t-20">搜索</el-button>
      </div>
    </div>

    <el-table :data="tableData" id="infotable" stripe>
      <el-table-column prop="created_time" label="时间" align="center"></el-table-column>
      <el-table-column prop="device_id" label="设备ID" align="center"></el-table-column>
      <el-table-column prop="change_type" label="类型" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.change_type === 1 ? '划拨': '激活'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="old_bus_name" label="值" align="center">
        <template slot-scope="scope">
          <span>{{`${scope.row.old_bus_name}->${scope.row.bus_name}`}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="operator_name" label="操作账号" align="center"></el-table-column>
    </el-table>
    
    <footer>
        <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next, sizes" :page-size="postData.page_size" :current-page="postData.page" :total="dataCount" :page-sizes="[10, 20, 50, 100]" @size-change="handleSizeChange">
        </el-pagination>
    </footer>
  </div>
</template>

<script>
  export default {
    name: 'FaceDevLog',
    data() {
      return {
        rangeArray: [],
        postData: {
          device_id: this.$route.query.device_id||'',
          start_time: '',
          operator_name: '',
          bus_name: '',
          change_type: '',
          page_size: 10,
          page: 1
        },
        tableData: [],
        dataCount: 0
      };
    },
    methods: {
      pickChange(val) {
        this.postData.start_time = val ? _g.formatDate(val[0], 'yyyy-MM-dd') : '';
        this.postData.end_time = val ? _g.formatDate(val[1], 'yyyy-MM-dd') : '';
      },
      handleSizeChange(val) {
        this.postData.page_size = val
        this.postData.page = 1
        this.gettableList()
      },
      checkStatus(index,row) {
        this.$service.post('device/deviceManagement/faceDeviceInfo', { deviceSn: row.deviceSn }).then(res => {
          if (res.data.errorcode == 0) {
            this.tableData[index].LastestApkVersion = res.data.data.LastestApkVersion;
            this.tableData[index].apkType = res.data.data.apkType;
            this.tableData[index].apkVersion = res.data.data.apkVersion;
            this.tableData[index].onLine = res.data.data.onLine;
            _g.toastMsg('success', '更新成功');
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      updateDev(index,row) {
        this.$service.post('device/deviceManagement/faceDeviceUpdate', { deviceSn: row.deviceSn }).then(res => {
          if (res.data.errorcode == 0) {
            _g.toastMsg('success', '升级推送已发送');
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      gettableList() {
        this.$service.get('/device/FaceDeviceChangeLog/getList', {params: this.postData}).then(res => {
          if (res.data.errorcode == 0) {
            this.tableData = res.data.data.data;
            this.dataCount = res.data.data.total;
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page = curPage;
        this.gettableList();
      },
      search() {
        this.postData.page = 1;
        this.gettableList();
      }
    },
    created() {
      this.postData.bus_name = this.$route.query.bus_name;
      this.gettableList()
    }
  };
</script>

<style scoped>
  .m-r-5 {
    margin-right: 5px;
  }
  .editfont {
    color: #fff;
  }
  .searchbar {
    background-color: #fff;
    min-height: 80px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    border-bottom: 1px solid #ececec;
    padding-bottom: 20px;
  }
  .el-dialog__footer {
    padding: 0;
  }
  .centeritem {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .qricon {
    border: none;
    width: 32px;
    height: 32px;
    background: url(../../assets/images/qrcode.png) no-repeat center;
    background-size: 32px 32px;
  }
</style>








