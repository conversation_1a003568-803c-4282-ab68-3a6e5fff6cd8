<template>

  <div class="container">
    <div class="ovf-hd searchbar">
      <div class="fl m-l-30">
        <el-input placeholder="场馆名称" @keyup.enter.native="search" v-model="postData.bus_name" class="w-150 m-r-10 p-t-20"></el-input>
        <el-input placeholder="设备ID" @keyup.enter.native="search" v-model="postData.device_id" class="w-150 m-r-10 p-t-20"></el-input>
        <el-select v-model="postData.face_server" class="w-150 m-r-10" placeholder="设备类型">
          <el-option value="" label="设备类型"></el-option>
          <el-option value="MDfaceService" label="魔点"></el-option>
          <el-option value="KWfaceService" label="开为"></el-option>
        </el-select>
        <el-button type="primary" icon="search" @click="search" class="m-t-20">搜索</el-button>
      </div>
    </div>

    <el-table :data="tableData" id="infotable" stripe>
      <el-table-column align="center" type="index" label="编号" width="80">
        <template scope="scope">
          {{scope.$index + postData.page_size*(postData.page_no-1) + 1}}
        </template>
      </el-table-column>
      <el-table-column prop="busName" label="场馆名称" align="center"></el-table-column>
      <el-table-column prop="deviceSn" label="设备ID" align="center"></el-table-column>
      <el-table-column prop="device_type_name" label="设备类型" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.faceServer === 'MDfaceService' ? '魔点': '开为'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="apkType" label="ApkType" align="center"></el-table-column>
      <el-table-column prop="apkVersion" label="设备版本号" align="center"></el-table-column>
      <el-table-column prop="LastestApkVersion" label="最新版本号" align="center"></el-table-column>
      <el-table-column prop="onLine" label="在线状态" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.onLine ? '在线': '离线'}}</span>
        </template>
      </el-table-column>
      <el-table-column prop="authType" label="鉴权方式" align="center">
        <template slot-scope="scope">
          <span>{{scope.row.authType == 0 ? '默认鉴权': '三方鉴权'}}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="300">
        <template scope="scope">
          <div>
            <!-- 显示方式取消:  && scope.row.authType == 0 -->
            <el-button size="small" v-show="scope.row.faceServer == 'MDfaceService'" @click="authentication(scope.$index,scope.row)" type="primary">鉴权设置</el-button>
            <el-button size="small" @click="checkStatus(scope.$index,scope.row)" type="primary">检查</el-button>
            <el-button size="small" @click="updateDev(scope.$index,scope.row)" type="primary">升级</el-button>
            <router-link class='editfont m-l-5' :to="{name: 'faceDevLog', query: {device_id: scope.row.deviceSn}}">
              <el-button size="small" type="primary">记录</el-button>
            </router-link>
          </div>
        </template>
      </el-table-column>
    </el-table>
    
    <footer>
        <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next, sizes" :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount" :page-sizes="[10, 20, 50, 100]" @size-change="handleSizeChange">
        </el-pagination>
    </footer>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  export default {
    name: 'FaceDev',
    data() {
      return {
        postData: {
          device_id: '',
          bus_name: '',
          face_server: '',
          page_size: 10,
          page_no: 1
        },
        tableData: [],
        dataCount: 0
      };
    },
    methods: {
      handleSizeChange(val) {
        this.postData.page_size = val
        this.postData.page_no = 1
        this.gettableList()
      },
      // 鉴权设置
      authentication(index, row) {
        this.$confirm('是否更新三方鉴权配置?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          this.$service.post('device/DeviceManagement/faceDeviceAuthUpdate', { deviceSn: row.deviceSn }).then(res => {
            if (res.data.errorcode == 0) {
              _g.toastMsg('success', '更新成功');
              this.gettableList()
            } else {
              _g.toastMsg('warning', res.data.errormsg);
            }
          });
        }).catch(() => {
          _g.toastMsg('warning', '已取消');
        });
      },
      checkStatus(index,row) {
        this.$service.post('device/deviceManagement/faceDeviceInfo', { deviceSn: row.deviceSn }).then(res => {
          if (res.data.errorcode == 0) {
            this.tableData[index].LastestApkVersion = res.data.data.LastestApkVersion;
            this.tableData[index].apkType = res.data.data.apkType;
            this.tableData[index].apkVersion = res.data.data.apkVersion;
            this.tableData[index].onLine = res.data.data.onLine;
            _g.toastMsg('success', '更新成功');
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      updateDev(index,row) {
        this.$service.post('device/deviceManagement/faceDeviceUpdate', { deviceSn: row.deviceSn }).then(res => {
          if (res.data.errorcode == 0) {
            _g.toastMsg('success', '升级推送已发送');
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      gettableList() {
        this.$service.post('/device/deviceManagement/faceDevicelists', this.postData).then(res => {
          if (res.data.errorcode == 0) {
            this.tableData = res.data.data.lists;
            this.dataCount = res.data.data.count;
          } else {
            _g.toastMsg('warning', res.data.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.gettableList();
      },
      search() {
        this.postData.page_no = 1;
        this.gettableList();
      }
    },
    created() {
      this.postData.bus_name = this.$route.query.bus_name;
      this.gettableList()
    }
  };
</script>

<style scoped>
  .m-r-5 {
    margin-right: 5px;
  }
  .editfont {
    color: #fff;
  }
  .searchbar {
    background-color: #fff;
    min-height: 80px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-wrap: wrap;
    border-bottom: 1px solid #ececec;
    padding-bottom: 20px;
  }
  .el-dialog__footer {
    padding: 0;
  }
  .centeritem {
    display: flex;
    align-items: center;
    justify-content: center;
  }
  .qricon {
    border: none;
    width: 32px;
    height: 32px;
    background: url(../../assets/images/qrcode.png) no-repeat center;
    background-size: 32px 32px;
  }
</style>








