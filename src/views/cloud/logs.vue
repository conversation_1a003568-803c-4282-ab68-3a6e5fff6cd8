<template>
  <div class="container">
    <header>
     <el-date-picker v-model="rangeArray" :clearable="false" class="m-r-10" type="daterange" @change="pickChange" placeholder="选择日期"></el-date-picker>
      <el-input placeholder="appID" v-model="postData.appid" class="w-150 m-r-10"></el-input>
      <el-input placeholder="设备ID" v-model="postData.device_id" class="w-150 m-r-10"></el-input>
      <el-input placeholder="手机号" v-model="postData.phone" class="w-150 m-r-10"></el-input>
      <el-select v-model="postData.auth" placeholder="全部日志">
        <el-option value="0" label="全部日志"></el-option>
        <el-option value="1" label="验证通过"></el-option>
        <el-option value="2" label="验证失败"></el-option>
      </el-select>
      <el-button type="primary" icon="search" @click="search">搜索</el-button>
    </header>
    <el-table :data="tableData" stripe ref="table">
      <el-table-column align="center" type="index" label="编号" width="80">
        <template scope="scope">
          {{scope.$index + postData.page_size*(postData.page_no-1) + 1}}
        </template>
      </el-table-column>
      <el-table-column align="center" prop="create_time" label="请求时间"></el-table-column>
      <el-table-column align="center" prop="device_id" label="设备ID"></el-table-column>
      <el-table-column align="center" prop="action_name" label="事件名称"></el-table-column>
      <el-table-column align="center" prop="phone" label="用户手机号"></el-table-column>
      <el-table-column align="center" prop="vein_score" label="认证分数"></el-table-column>
      <el-table-column align="center" prop="time_consuming" label="处理耗时(秒)">
        <template slot-scope="scope">
          <span>{{scope.row.time_consuming.substr(0,5)}}</span>
        </template>
      </el-table-column>
      <el-table-column align="center" width="350" prop="response_data" label="响应数据">
        <template scope="scope">
          <!-- {{parse(scope.row.response_data)}} -->
          <div>
            <vue-json-pretty :data="JSON.parse(scope.row.response_data)" :deep="2" :deepCollapseChildren="true" :show-length="true"> </vue-json-pretty>
          </div>
        </template>
      </el-table-column>
      <el-table-column align="center" label="指静脉图片">
        <template scope="scope">
          <img @click="showImgModal(scope.row.decrypt_img)" style="width: 100px; cursor: pointer"
               :src="scope.row.decrypt_img" alt="">
        </template>
      </el-table-column>
      <el-table-column align="center" label="加密图片">
        <template scope="scope">
          <a :href="scope.row.encrypt_img" style="color: #409eff" download="加密图片.bmp">下载</a>
        </template>
      </el-table-column>
    </el-table>
    <footer>
      <div class="left">
        <el-button type="success" @click="exportCsv" size="small">
          导出Excel
        </el-button>
      </div>
      <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next" :page-size="postData.page_size"
                     :current-page="postData.page_no" :total="dataCount">
      </el-pagination>
    </footer>

    <el-dialog :visible.sync="showModal" title="指静脉图片">
      <img :src="modalImg" style="width: 100%" alt="">
    </el-dialog>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  import ExportCsv from 'src/components/form/csvExport';
  import VueJsonPretty from 'vue-json-pretty';
  import 'vue-json-pretty/lib/styles.css';

  export default {
    name: 'SystemLog',
    components: {
     VueJsonPretty
    },
    data() {
      return {
        modalImg: '',
        showModal: false,
        tableData: [],
        rangeArray: [this.getDate(), this.getDate()],
        userInfo: {},
        postData: {
          start_time: this.getDate(),
          end_time: this.getDate(),
          appid: '',
          device_id: '',
          phone: '',
          page_size: 10,
          page_no: 1,
          auth: ''
        },
        dataCount: 0
      };
    },
    methods: {
      getDate() {
        let d = new Date();
        return `${d.getFullYear()}-${d.getMonth() + 1}-${d.getDate()}`;
      },
      showImgModal(img) {
        this.modalImg = img;
        this.showModal = true;
      },
      exportCsv() {
        const columns = [
          { prop: 'create_time', label: '请求时间' },
          { prop: 'device_id', label: '设备ID' },
          { prop: 'action_name', label: '事件方法名' },
          { prop: 'phone', label: '用户手机号' },
          { prop: 'response_data', label: '响应数据' },
          { prop: 'appid', label: '场馆appId' },
          { prop: 'vein_score', label: '指静脉评分分数' }
        ];
        this.exportDataList(columns);
      },
      exportDataList(columns) {
        let postd = { is_export: 1 };
        postd = Object.assign(postd, this.postData);
        postd.page_size = 5000;
        this.apiGet('/device/DeviceLogManagement/listData', postd).then(res => {
          if (res.errorcode == 0) {
            let exportData = res.data.list;
            ExportCsv(exportData, columns);
          } else {
            _g.toastMsg('success', res.errormsg);
          }
        });
      },
      parse(str) {
        str = str ? str.replace(/\\/g, '%') : '';
        return unescape(str);
      },
      pickChange(val) {
        this.postData.start_time = val ? _g.formatDate(val[0], 'yyyy-MM-dd') : '';
        this.postData.end_time = val ? _g.formatDate(val[1], 'yyyy-MM-dd') : '';
      },
      getList() {
        this.apiGet('/device/DeviceLogManagement/listData', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.dataCount = res.data.count;
            if (Array.isArray(res.data.list)) {
              this.tableData = res.data.list.map(item => {
                return {...item, vein_score: Number(item.vein_score).toFixed(2)}
              });
            } else {
              this.tableData = []
            }
            
          } else {
            _g.toastMsg('success', res.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.getList();
      },
      search() {
        this.postData.page_no = 1;
        this.getList();
      }
    },
    created() {
      // this.getList();
    },
    activated() {
      // console.log("--activated--");
      if(this.$route.query.device_id) {
        this.postData.device_id = this.$route.query.device_id;
        this.postData.appid = '';
        this.postData.phone = '';
        this.postData.auth = '';
        this.postData.page_size = 10;
        this.postData.page_no = 1;
        this.getList();
      } else {
        this.getList();
      }
    },
    deactivated() {
    },
    mixins: [http]
  };
</script>
