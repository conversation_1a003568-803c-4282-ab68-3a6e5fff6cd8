<template>
  <div>
    <div class="m-b-20 ovf-hd">
      <div class="fl m-l-30">
        <el-input placeholder="锁板号"
                  @keyup.enter.native="generate"
                  v-model="postData.plate_num"
                  class="w-150 m-r-10"></el-input>
        <el-input placeholder="起始线路号"
                  @keyup.enter.native="generate"
                  v-model="postData.line_num"
                  class="w-150 m-r-10"></el-input>
        <el-input placeholder="起始柜号"
                  @keyup.enter.native="generate"
                  v-model="postData.cup_num"
                  class="w-150 m-r-10"></el-input>
        <el-input placeholder="数量"
                  @keyup.enter.native="generate"
                  v-model="postData.count"
                  class="w-150 m-r-10"></el-input>
        <el-checkbox label="跳过4" v-model="check4"></el-checkbox>
        <el-checkbox label="跳过7" v-model="check7"></el-checkbox>
        <el-button type="primary"
                   class="m-l-30"
                   icon="el-icon-circle-plus-outline"
                   @click="generate">生成</el-button>
        <el-button type="danger"
                   icon="el-icon-delete"
                   @click="preClearAll">清空</el-button>
        <el-button type="warning"
                   icon="el-icon-refresh"
                   @click="preSync">同步柜号</el-button>
      </div>
    </div>

    <el-table :data="tableData"
              stripe
              style="width: 100%">
      <el-table-column type="index"
                       label="序号"
                       align="center"></el-table-column>
      <el-table-column prop="plate_num"
                       label="锁板号"
                       align="center">
      </el-table-column>
      <el-table-column prop="line_num"
                       label="线路号"
                       align="center"></el-table-column>
      <el-table-column prop="cup_num"
                       label="柜号"
                       align="center"></el-table-column>
      <el-table-column label="操作"
                       align="center">
                      <template scope="scope">
                        <el-button size="small" type="primary" @click="preDelete(scope.row.id)">删除</el-button>
                      </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
  import http from 'assets/js/http'
  export default {
    name: 'lockerArr',
    data() {
      return {
        postData: {
          device_id: '',
          plate_num: '',
          line_num: '',
          cup_num: '',
          count: '',
          exclude: ''
        },
        check4: false,
        check7: false,
        tableData: [],
        dataCount: 0,   
      }
    },
    methods: {
      preClearAll() {
        this.$confirm('确认要清空所有柜号,此操作将不可恢复?', '清空柜号', {
          confirmButtonText: '清空',
          type: 'error'
        }).then(() => {
          this.clearAll();
        });
      },
      clearAll() {
        this.apiPost('/device/cupboardManagement/clear', {device_id: this.postData.device_id}).then(res => {
          if (res.errorcode == 0) {
             this.$message.success(res.errormsg);
             this.gettableList();
          } else {
            _g.toastMsg('warning', res.errormsg)
          }
        })
      },
      preSync(){
        this.$confirm('同步柜号前，请确保储物柜无人使用',"同步柜号",{
          confirmButtonText: '同步',
          type: 'warning'
        }).then(() => {
          this.handleSync();
        });
      },
      handleSync() {
        this.apiPost('/device/MobileTerminal/synchronizationCabinet', {device_id: this.postData.device_id,bus_id:this.postData.bus_id}).then(res => {
          if (res.errorcode == 0) {
             this.$message.success(res.errormsg);
            //  this.gettableList();
          } else {
            _g.toastMsg('warning', res.errormsg)
          }
        })
      },
      deleteLock(id) {
        this.apiPost('/device/cupboardManagement/delete', {cupboard_id: id}).then(res => {
          if (res.errorcode == 0) {
             this.$message.success(res.errormsg);
             this.gettableList();
          } else {
            _g.toastMsg('warning', res.errormsg)
          }
        })
      },
      preDelete(id) {
        this.$confirm('确认要删除该柜号吗?', '删除柜号', {
          confirmButtonText: '删除',
          type: 'error'
        }).then(() => {
          this.deleteLock(id);
        });
      },
      gettableList() {
        this.apiPost('/device/cupboardManagement/listData', {device_id: this.postData.device_id}).then(res => {
          if (res.errorcode == 0) {
            this.tableData = res.data.list
            this.dataCount = res.data.count
          } else {
            _g.toastMsg('warning', res.errormsg)
          }
        })
      },
      async getpageInfo() {
        return new Promise((resolve, reject) => {
          this.apiGet('/device/deviceManagement/info', { device_id: this.postData.id }).then(res => {
            if (res.errorcode == 0) {
              // const resData = res.data
              // this.postData = {
              //   ...resData,
              //   ...{
              //     device_plan_id: String(res.data.device_plan_id),
              //     device_status: String(res.data.device_status),
              //     type: Number(res.data.type),
              //     theme: String(res.data.theme),
              //     validate: res.data.validate ? String(res.data.validate) : '0.63',
              //     validateUp: res.data.validateUp ? String(res.data.validateUp) : '0.65',
              //     module: String(res.data.module)
              //   }
              // };
              // this.device_type_name = resData.device_type_name;
              this.postData.bus_id = res.data.bus_id;
              resolve();
            } else {
              reject();
              _g.toastMsg('warning', res.errormsg);
            }
          });
        });
      },
      generate() {
        let exarr = [];
        if(this.check4) {
          exarr.push('4');
        }
        if(this.check7) {
          exarr.push('7');
        }
        this.postData.exclude = exarr.join(',');
        this.apiPost('/device/cupboardManagement/generate', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.$message.success('生成成功');
            this.gettableList();
            this.postData.plate_num = '';
            this.postData.line_num = '';
            this.postData.cup_num = '';
            this.postData.count = '';
            this.postData.exclude = '';
            this.check4 = false;
            this.check7 = false;
          } else {
            _g.toastMsg('warning', res.errormsg)
          }
        })
      }

    },
    async created() {
      this.postData.id = this.$route.params.id;
      this.postData.device_id = this.$route.params.device_id;
      this.gettableList();
      await this.getpageInfo();
    },
    mixins: [http]
  }
</script>

<style scoped>
  .editfont {
    color: #fff;
  }
</style>








