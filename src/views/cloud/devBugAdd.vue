<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="150px">
      <el-form-item label="设备ID">
         <el-input class="w-320" :disabled="!!postData.id" v-model="postData.device_id" @blur="getDevInfo"></el-input>
         <div v-if="devInfo && devInfo.device_id">
           场馆名称：{{devInfo.bus_name}}     设备类型：{{devInfo.device_type_name}} 
         </div>
      </el-form-item>
      <el-form-item label="问题描述">
        <el-input class="w-320" type="textarea" :rows="2" placeholder="请输入" v-model="postData.question"></el-input>
      </el-form-item>
      <el-form-item label="处理方案">
        <el-input class="w-320" type="textarea" :rows="2" placeholder="请输入" v-model="postData.result"></el-input>
      </el-form-item>
      <el-form-item label="涉及人员信息">
        <el-input class="w-320" type="textarea" :rows="2" placeholder="请输入" v-model="postData.member_info"></el-input>
      </el-form-item>
      <el-form-item label="异常类型">
          <el-select v-model="postData.type" class="w-320" placeholder="异常类型">
          <el-option :value="1" label="识别异常"></el-option>
          <el-option :value="2" label="网络异常"></el-option>
          <el-option :value="3" label="设备异常"></el-option>
          <el-option :value="4" label="开柜异常"></el-option>
          <el-option :value="0" label="其它"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="处理状态">
         <el-select v-model="postData.status" class="w-320" placeholder="处理状态">
          <el-option :value="0" label="未解决"></el-option>
          <el-option :value="1" label="解决中"></el-option>
          <el-option :value="2" label="观察中"></el-option>
          <el-option :value="3" label="已解决"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit" :disabled="!devInfo.device_id">保存</el-button>
        <el-button class="m-l-150" @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import DeviceType from 'src/components/form/deviceType'
export default {
  name: 'channelAdd',
  data() {
    return {
      postData: {
        id: this.$route.query.id||'',
        device_id: this.$route.query.device_id||'',
        question: '',
        result: '',
        member_info: '',
        type: '',
        status: ''
      },
      devInfo: {},
      versionList: []
    }
  },
  components: { DeviceType },
  watch: {
    'postData.device_type_id'(val, oldVal) {
      if (val !== oldVal) {
        this.getVersionList();
      }
    }
  },
  methods: {
    getDevInfo() {
      if(!this.postData.device_id) {
        this.devInfo = {}
        return false;
      }
      this.$service.post('/device/DeviceExceptionLog/detail', {device_id: this.postData.device_id}).then(res => {
        if (res.data.errorcode == 0) {
          this.devInfo = res.data.data;
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    },
    onSubmit() {
      let url = '/device/DeviceExceptionLog/add'
      if (this.$route.query.id) {
        url = '/device/DeviceExceptionLog/edit' 
        this.postData.log_id = this.$route.query.id
      }
      this.$service.post(url, this.postData).then(
        res => {
          if (res.data.errorcode == 0) {
            _g.toastMsg('success', res.data.errormsg)
            this.$router.back()
          } else {
            _g.toastMsg('warning', res.data.errormsg)
          }
        }
      )
     
    },
    getInfo() {
      this.$service.post('/device/DeviceExceptionLog/detailLog', {id: this.postData.id}).then(res => {
        if (res.data.errorcode == 0) {
          this.postData = res.data.data;
        } else {
          _g.toastMsg('warning', res.data.errormsg);
        }
      });
    }
  },
  created() {
    this.postData.device_id = this.$route.query.device_id || ''
    if(this.postData.device_id) {
      this.getDevInfo()
    }
    if (this.$route.query.id) {
      this.getInfo()
    }
  }
}
</script>

<style scoped>
.maright-20 {
  margin-right: 20px;
}

.inputlen {
  width: 180px;
}
.maps {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 300px;
}
.amap-demo {
  height: 300px;
}
#map_container {
  width: 100%;
  height: 300px;
}
#panel {
  background-color: white;
  max-height: 100%;
  overflow-y: auto;
}
</style>
