<template>
  <div class="form-frame">
    <el-form ref="form" :model="postData" label-width="150px">
      <el-form-item label="设备类型">
        <DeviceType class="w-320" v-model="postData.device_type_id"></DeviceType>
      </el-form-item>
      <el-form-item label="版本号" prop="version_code" :rules="{ required: true, message: '请填写渠道名称'}">
        <el-input class="w-320" placeholder="同类型中保持唯一" v-model="postData.version_code"></el-input>
      </el-form-item>
      <el-form-item label="软件包地址">
       <el-input class="w-320" v-model="postData.download_link"></el-input>
       <!-- <ApkUploader v-model="apkInfo"></ApkUploader> -->
       <OssUploaderApk v-model="apkInfo"></OssUploaderApk>
      </el-form-item>
      <el-form-item label="版本状态">
         <el-switch v-model="postData.version_status" :active-value="1" :inactive-value="0"></el-switch>
      </el-form-item>
      <el-form-item label="是否强制更新" v-if="postData.device_type_id === 127 || postData.device_type_id === 128">
        <el-radio-group
          v-model="postData.is_force_upgrade"
        >
          <el-radio :label="0">否</el-radio>
          <el-radio :label="1">是</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="指定门店" v-if="postData.device_type_id === 127 || postData.device_type_id === 128">
        <el-radio-group
          class="w-320"
          v-model="postData.type"
          @change="typeChange"
        >
          <el-radio :label="1">全部</el-radio>
          <el-radio :label="2">指定门店</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item prop="bus_id" v-if="(postData.device_type_id === 127 || postData.device_type_id === 128) && (postData.type === 2)">
        <el-select
          v-model="postData.bus_id"
          multiple
          filterable
          class="w-320"
          placeholder="请选择"
        >
          <el-option
            v-for="item in busList"
            :key="item.bus_id"
            :label="item.name"
            :value="item.bus_id"
          >
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="备注">
        <el-input class="w-320" type="textarea" :rows="2" placeholder="请输入备注内容" v-model="postData.version_remark"></el-input>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="onSubmit">保存</el-button>
        <el-button class="m-l-150" @click="$router.back()">取消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import http from 'assets/js/http';
import DeviceType from 'src/components/form/deviceType';
// import ApkUploader from 'src/components/form/apkUploader';
import OssUploaderApk from 'src/components/form/OssUploaderApk';

export default {
  name: 'versionCtrlAdd',
  data() {
    return {
      apkInfo:{
        filepath: '',
        filesize: ''
      },
      busList: [],
      postData: {
        id: this.$route.query.id,
        device_type_id: this.$route.query.device_type_id,
        version_code: '',
        download_link: '',
        version_status: '',
        version_remark: '',
        file_size: '',
        bus_id: [],
        is_force_upgrade: 0,
        type: 1,
        channel_remark: ''
      }
    };
  },
  components: { DeviceType,OssUploaderApk },
  watch:{
    apkInfo(val){
      console.log("apkInfo watch");
      console.log(JSON.stringify(val));
      this.postData.download_link = val.filepath
      this.postData.file_size = val.filesize
    }
  },
  methods: {
    getBusList() {
      this.$service.post('/ivep/IvepBase/getIvepBusList').then(res => {
        if (res.data.errorcode == 0) {
          this.busList = res.data.data.data
        } else {
          this.$message.error(res.data.errormsg)
        }
      })
    },
    onSubmit() {
      this.$refs.form.validate(valid => {
        if (valid) {
          this.apiPost('/device/versionManagement/save', this.postData).then(res => {
            if (res.errorcode == 0) {
              _g.toastMsg('success', res.errormsg);
              this.$router.back();
            } else {
              _g.toastMsg('warning', res.errormsg);
            }
          });
        }
      });
    },
    typeChange(val) {
      if (val == 1) {
        this.postData.bus_id = []
      }
    },
    getVersionInfo() {
      this.apiGet('/device/versionManagement/info', { version_id: this.postData.id })
        .then(res => {
          if (res.errorcode == 0) {
            this.postData = res.data
            this.apkInfo.filepath =  res.data.download_link
            this.apkInfo.filesize =  res.data.file_size
          } else {
            _g.toastMsg('warning', res.errormsg);
          }
        })
        .catch(error => {
          console.log(error);
        });
    }
  },
  created() {
    this.getBusList()
    if(this.$route.query.id){
      this.getVersionInfo();
    }
  },
  mixins: [http]
};
</script>

<style scoped>
.maright-20 {
  margin-right: 20px;
}

.inputlen {
  width: 180px;
}
.maps {
  display: flex;
  flex-direction: row;
  flex-wrap: nowrap;
  justify-content: flex-start;
  align-items: flex-start;
  width: 100%;
  height: 300px;
}
.amap-demo {
  height: 300px;
}
#map_container {
  width: 100%;
  height: 300px;
}
#panel {
  background-color: white;
  max-height: 100%;
  overflow-y: auto;
}
</style>
