<template>
  <div>
    <el-card class="con-wrap" shadow="hover">
      <h3>用户详情</h3>
      <div class="item">场馆名称：{{userInfo.bus_name}}</div>
      <div class="item">手机号：{{userInfo.phone}}</div>
      <div class="item">用户类型：{{userInfo.user_type_text}}</div>
      <div class="item">绑定时间：{{userInfo.bind_time}} <span v-show="userInfo.agreement_name">《{{userInfo.agreement_name}}》</span> <span v-show="userInfo.agreement_version">版本：{{userInfo.agreement_version}}</span></div>
      <div class="item">绑定设备：{{userInfo.device_id}}</div>
      <div class="item">
        <span>原始特征：</span>
        <span v-for="(item,index) in userInfo.src_img" :key="index" @click="openSrc(item,index)" class="bluefont m-r-10"
              size="medium" type="text">{{index==0?'图一':index==1?'图二':'图三'}}</span>
      </div>
      <div class="item">
        <span>加密特征：</span>
        <a v-for="(item,index) in userInfo.encrypt_src_img" :key="index" class="bluefont m-r-10" :href="item"
           :download="`加密特征${index == 0 ? '图一' : index == 1 ? '图二' :'图三'}.bmp`">{{index==0?'图一':index==1?'图二':'图三'}}</a>
      </div>
      <div class="item">
        <span>7.0学习特征：</span>
        <span v-if="userInfo.create_feature_src_1" @click="openSrc(userInfo.create_feature_src_1,3)" class="bluefont"
              size="medium" type="text">图一</span>
        <span v-else>未生成</span>
        <span class="m-l-20">{{userInfo.create_feature_time_1}}</span>
      </div>
      <div class="item">
        <span>8.5学习特征：</span>
        <span v-if="userInfo.create_feature_src_2" @click="openSrc(userInfo.create_feature_src_2,4)" class="bluefont"
              size="medium" type="text">图一</span>
        <span v-else>未生成</span>
        <span class="m-l-20">{{userInfo.create_feature_time_2}}</span>
      </div>
      <div class="item">
        <span>自由学习特征：</span>
        <span v-if="userInfo.create_feature_src_free" @click="openSrc(userInfo.create_feature_src_free,5)"
              class="bluefont" size="medium" type="text">图一</span>
        <span v-else>未生成</span>
        <span class="m-l-20">{{userInfo.create_feature_time_free}}</span>
      </div>
    </el-card>
    <div class="container">
      <header>
        <div class="fl m-l-30">
          <el-date-picker v-model="value6" type="daterange" range-separator="~" start-placeholder="开始时间"
                          end-placeholder="结束时间" value-format="yyyy-MM-dd" @change="pickChange">
          </el-date-picker>
          <el-input placeholder="设备ID" v-model="postData.device_id" class="w-150 m-r-10"></el-input>
          <el-input placeholder="设备名称" v-model="postData.device_name" class="w-150 m-r-10"></el-input>
          <DeviceType v-model="postData.device_type_id">
            <el-option label="设备类型" value=""></el-option>
          </DeviceType>
          <el-input placeholder="场馆名称" v-model="postData.bus_name" class="w-150 m-r-10"></el-input>
          <el-button type="success" icon="search" @click="search">搜索</el-button>
        </div>
      </header>
      <el-table :data="tableData" stripe>
        <el-table-column align="center" type="index" label="编号" width="80"></el-table-column>
        <el-table-column align="center" prop="create_time" label="操作时间"></el-table-column>
        <el-table-column align="center" prop="bus_name" label="场馆名称"></el-table-column>
        <el-table-column align="center" prop="device_id" label="设备ID"></el-table-column>
        <el-table-column align="center" prop="device_name" label="设备名称"></el-table-column>
        <el-table-column align="center" prop="device_type_name" label="设备类型"></el-table-column>
        <el-table-column align="center" prop="score" label="验证分"></el-table-column>
        <el-table-column align="center" label="指静脉图">
          <template scope="scope">
            <img @click="showTablePic(scope.row.verify_img)" :src="scope.row.verify_img" style="width: 100px; cursor: pointer" alt="">
          </template>
        </el-table-column>
        <el-table-column align="center" label="操作">
          <template scope="scope">
            <el-button type="primary" size="small" @click="handleCreate(scope.row.log_id)">生成特征</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pos-rel p-t-20 ovf-hd flexend">
        <div class="block">
          <el-pagination @current-change="handleCurrentChange" layout="prev, pager, next"
                         :page-size="postData.page_size" :current-page="postData.page_no" :total="dataCount">
          </el-pagination>
        </div>
      </div>
    </div>

    <el-dialog :visible.sync="showPic" :title="pictitle" :close="handleClose">
      <img style="width: 100%; height: 100%" :src="picsrc"/>
    </el-dialog>
    <el-dialog :visible.sync="showTablePicModal" title="指静脉图">
      <img style="width: 100%; height: 100%" :src="tablePic"/>
    </el-dialog>
  </div>
</template>

<script>
  import http from 'assets/js/http';
  import DeviceType from 'src/components/form/deviceType';

  export default {
    name: 'userManagementDetail',
    data() {
      return {
        tablePic: '',
        showTablePicModal: false,
        tableData: [
          // {
          //   bus_name: "惟度健身",
          //   create_time: "2018-12-25 16:31:53",
          //   device_id: "20100130c104b14d0070",
          //   device_name: "一体机新换",
          //   device_type_name: "signIn",
          //   log_id: "El595GcBSPdgjaZp4CUs",
          //   phone: "13072970324",
          //   score: "0.914",
          // }
        ],
        value6: '',
        userInfo: {
          encrypt_src_img: [],
        },
        userType: ['会员', '会籍', '教练', '运营'],
        postData: {
          vein_uid: this.$route.params.veinUid,
          start_time: '',
          end_time: '',
          device_id: '',
          device_name: '',
          device_type_id: '',
          bus_name: '',
          page_size: 10,
          page_no: 1
        },
        dataCount: 0,
        picsrc: '',
        showPic: false,
        pictitle: '原始特征图一'
      };
    },
    components: { DeviceType },
    methods: {
      showTablePic(img) {
        this.tablePic = img;
        this.showTablePicModal = true;
      },
      handleCreate(log_id) {
        const { vein_uid } = this.userInfo
        const url = 'device/UserManagement/updateVeinfeature'
        this.$service.post(url, { log_id, vein_uid }).then(res => {
          if (res.data.errorcode === 0) {
            this.$message.success(res.data.errormsg);
            this.getList();
          } else {
            this.$message.error(res.data.errormsg);
          }
        }).catch(e => {
          throw new Error(e)
        })
      },
      openSrc(val, index) {
        const titles = [
          '原始特征图一',
          '原始特征图二',
          '原始特征图三',
          '7.0学习特征图一',
          '8.5学习特征图一',
          '自由学习特征'
        ];
        this.pictitle = titles[index];

        this.picsrc = val;
        this.showPic = true;
      },
      handleClose() {
        this.picsrc = '';
        this.showPic = false;
        this.pictitle = '原始特征图一';
      },
      pickChange(val) {
        this.postData.start_time = val ? val[0] : '';
        this.postData.end_time = val ? val[1] : '';
      },
      getList() {
        this.apiGet('device/userManagement/opListData', this.postData).then(res => {
          if (res.errorcode == 0) {
            this.dataCount = res.data.count;
            this.tableData = res.data.list;
            this.userInfo = res.data.userInfo;
          } else {
            _g.toastMsg('success', res.errormsg);
          }
        });
      },
      handleCurrentChange(curPage) {
        this.postData.page_no = curPage;
        this.getList();
      },
      search() {
        this.postData.page_no = 1;
        this.getList();
      }
    },
    created() {
      this.getList();
    },
    mixins: [http]
  };
</script>
<style lang="less">
  .con-wrap {
    overflow: hidden;
    margin-bottom: 30px;
    padding-bottom: 15px;

    .item {
      width: 50%;
      float: left;
      margin-bottom: 15px;
      display: flex;
      flex-direction: row;
      justify-content: flex-start;
      align-items: center;
    }
  }

  .bluefont {
    color: #409eff;
    cursor: pointer;
    display: inline-block;
  }
</style>
