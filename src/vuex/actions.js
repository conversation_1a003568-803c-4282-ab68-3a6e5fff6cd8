const actions = {
  showLeftMenu({ commit }, status) {
    commit('showLeftMenu', status);
  },
  showLoading({ commit }, status) {
    commit('showLoading', status);
  },
  setMenus({ commit }, menus) {
    commit('setMenus', menus);
  },
  setRules({ commit }, rules) {
    commit('setRules', rules);
  },
  setUsers({ commit }, users) {
    commit('setUsers', users);
  },
  setUserGroups({ commit }, userGroups) {
    commit('setUserGroups', userGroups);
  },
  setOrganizes({ commit }, organizes) {
    commit('setOrganizes', organizes);
  },
  getDeviceTypeList({ commit }, info) {
     return axios
       .get('/device/deviceManagement/deviceTypeList')
       .then(res => {
         if (res.data.errorcode === 0) {
           const data = res.data.data;
           commit('SET_DEVICE_TYPE_LIST', data.list);
           return data.list;
         } else {
           console.error(res.data.errormsg);
         }
       });
  }
};

export default actions;
