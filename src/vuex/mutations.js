import * as Types from './mutationTypes';

const mutations = {
  showLeftMenu(state, status) {
    state.showLeftMenu = status;
  },
  showLoading(state, status) {
    state.globalLoading = status;
  },
  setMenus(state, menus) {
    state.menus = menus;
  },
  setRules(state, rules) {
    state.rules = rules;
  },
  setUsers(state, users) {
    state.users = users;
  },
  setUserGroups(state, userGroups) {
    state.userGroups = userGroups;
  },
  setOrganizes(state, organizes) {
    state.organizes = organizes;
  },
  SET_DEVICE_TYPE_LIST(state, data) {
    state.deviceTypeList = data;
  },
  [Types.SET_USER_INFO](state, userInfo) {
    state.userInfo = Object.assign({}, state.userInfo, userInfo);
  },
  [Types.SET_CHANNEL_LIST](state, list) {
    state.channelList = [{ id: 0, name: '全部区域' }].concat(list);
  }
};

export default mutations;
