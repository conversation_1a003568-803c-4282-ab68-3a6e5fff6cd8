const apiMethods = {
  methods: {
    setWxCompAppid(data) {
      if (router.currentRoute.meta.module === 'wxPlatform') {
        const wxCompAppid = localStorage.getItem('wxCompAppid') || ''
        if(typeof data === 'undefined') {
          data = { comp_appid : wxCompAppid }
        } else if(typeof data === 'object'){
          data.comp_appid = wxCompAppid
        }
      }
      if (router.currentRoute.meta.module === 'dyPlatform') {
        const dyCompAppid = localStorage.getItem('dyCompAppid') || ''
        if(typeof data === 'undefined') {
          data = { comp_appid : dyCompAppid }
        } else if(typeof data === 'object'){
          data.comp_appid = dyCompAppid
        }
      }
      return data
    },
    apiGet(url, data) {
      return new Promise((resolve, reject) => {
        data = this.setWxCompAppid(data)
        axios.get(url, { params: data }).then(
          (response) => {
            _g.closeGlobalLoading()
            resolve(response.data)
          },
          (response) => {
            reject(response)
            _g.closeGlobalLoading()
            bus.$message({
              message: '请求超时，请检查网络',
              type: 'warning',
            })
          }
        )
      })
    },
    apiPost(url, data) {
      return new Promise((resolve, reject) => {
        data = this.setWxCompAppid(data)
        axios
          .post(url, data)
          .then((response) => {
            _g.closeGlobalLoading()
            resolve(response.data)
          })
          .catch((response) => {
            console.log('f', response)
            resolve(response)
            bus.$message({
              message: '请求超时，请检查网络',
              type: 'warning',
            })
          })
      })
    },
    apiDelete(url, id) {
      return new Promise((resolve, reject) => {
        axios.delete(url + id).then(
          (response) => {
            resolve(response.data)
          },
          (response) => {
            reject(response)
            _g.closeGlobalLoading()
            bus.$message({
              message: '请求超时，请检查网络',
              type: 'warning',
            })
          }
        )
      })
    },
    apiPut(url, id, obj) {
      return new Promise((resolve, reject) => {
        axios.put(url + id, obj).then(
          (response) => {
            resolve(response.data)
          },
          (response) => {
            _g.closeGlobalLoading()
            bus.$message({
              message: '请求超时，请检查网络',
              type: 'warning',
            })
            reject(response)
          }
        )
      })
    },
    handelResponse(res, cb, errCb) {
      _g.closeGlobalLoading()
      if (
        res.errorcode == 200 ||
        res.errorcode == 0 ||
        res.errorcode == 'SUCCESS' ||
        res.code == 0
      ) {
        cb(res.data)
      } else {
        if (typeof errCb == 'function') {
          errCb()
        }
        this.handleError(res)
      }
    },
    handleError(res) {
      if (res.errorcode) {
        console.log(res.errorcode)
        switch (res.errorcode) {
          case 101:
            console.log('cookie = ', Cookies.get('rememberPwd'))
            if (Cookies.get('rememberPwd')) {
              let data = {
                rememberKey: Lockr.get('rememberKey'),
              }
              this.reAjax('admin/base/relogin', data).then((res) => {
                this.handelResponse(res, (data) => {
                  this.resetCommonData(data)
                })
              })
            } else {
              _g.toastMsg('error', res.errormsg)
              setTimeout(() => {
                router.replace('/')
              }, 1500)
            }
            break
          case 103:
            _g.toastMsg('error', res.errormsg)
            setTimeout(() => {
              router.replace('/')
            }, 1500)
            break
          // case 400:
          //   this.goback()
          //   break
          default:
            _g.toastMsg('error', res.errormsg)
        }
      } else {
        _g.toastMsg('error', res.errormsg)
        // console.log('default error')
      }
    },
    resetCommonData(data) {
      _(data.menusList).forEach((res, key) => {
        if (key == 0) {
          res.selected = true
        } else {
          res.selected = false
        }
      })
      Lockr.set('menus', data.menusList) // 菜单数据
      Cookies.set('authKey', data.authKey, { expires: 1 }) // 权限认证
      Lockr.set('authKey', data.authKey) // 权限认证
      Lockr.set('rememberKey', data.rememberKey) // 记住密码的加密字符串
      Lockr.set('authList', data.authList) // 权限节点列表
      Lockr.set('userInfo', data.userInfo) // 用户信息
      Cookies.set('sessionId', data.sessionId, { expires: 1 }) // 用户sessionid
      Lockr.set('sessionId', data.sessionId) // 用户sessionid
      window.axios.defaults.headers.authKey = Lockr.get('authKey')
      let routerUrl = ''
      if (data.menusList[0].child[0].url) {
        routerUrl = data.menusList[0].child[0].url
      } else {
        routerUrl = data.menusList[0].child[0].child[0].url
      }
      setTimeout(() => {
        let path = this.$route.path
        if (routerUrl != path) {
          router.replace(routerUrl)
        } else {
          _g.shallowRefresh(this.$route.name)
        }
      }, 1000)
    },
    reAjax(url, data) {
      return new Promise((resolve, reject) => {
        axios.post(url, data).then(
          (response) => {
            resolve(response.data)
          },
          (response) => {
            reject(response)
            bus.$message({
              message: '请求超时，请检查网络',
              type: 'warning',
            })
          }
        )
      })
    },
  },
  computed: {
    showLoading() {
      return store.state.globalLoading
    },
  },
}

export default apiMethods
