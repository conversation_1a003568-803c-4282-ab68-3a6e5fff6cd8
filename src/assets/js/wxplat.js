import apiGet from './http.js'
const getAppId = {
  methods: {
    getappIdList() {
      this.apiGet('/web/coding/auth_app_list').then(res => {
        if (res.errorcode == 0) {
          this.appIdList = res.data
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    },
    getversionList() {
      this.apiGet('/Web/Template/temidversionlist').then(res => {
        if (res.errorcode == 0) {
          this.versionList = res.data.list
        } else {
          _g.toastMsg('warning', res.errormsg)
        }
      })
    }
  }
}

export default getAppId