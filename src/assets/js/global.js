const commonFn = {
  j2s(obj) {
    return JSON.stringify(obj);
  },
  shallowRefresh(name) {
    router.replace({ path: '/refresh', query: { name: name } });
  },
  closeGlobalLoading() {
    setTimeout(() => {
      store.dispatch('showLoading', false);
    }, 0);
  },
  openGlobalLoading() {
    setTimeout(() => {
      store.dispatch('showLoading', true);
    }, 0);
  },
  clone<PERSON>son(obj) {
    return JSON.parse(JSON.stringify(obj));
  },
  toastMsg(type, msg, duration = 1500) {
    switch (type) {
      case 'normal':
        bus.$message(msg);
        break;
      case 'success':
        bus.$message({
          message: msg,
          type: 'success',
          duration
        });
        break;
      case 'warning':
        bus.$message({
          message: msg,
          type: 'warning',
          duration
        });
        break;
      case 'error':
        bus.$message.error(msg);
        break;
      default:
        bus.$message.error(msg);
    }
  },
  clearVuex(cate) {
    store.dispatch(cate, []);
  },
  getHasRule(val) {
    const moduleRule = 'admin';
    let userInfo = Lockr.get('userInfo');
    if (userInfo.id == 1) {
      return true;
    } else {
      let authList = moduleRule + Lockr.get('authList');
      return _.includes(authList, val);
    }
  },
  getBaseUrl() {
    let baseUrl;
    let host = window.location.host;
    let subDomain = host.split('.')[0];
    if (subDomain === 'bo-test') {
      baseUrl = 'https://bo-beta.rocketbird.cn';
    } else if (subDomain === 'bo') {
      baseUrl = 'https://bo-wx.rocketbird.cn';
    } else if (subDomain === 'bo-fe-sim') {
      baseUrl = 'https://bo-sim.rocketbird.cn';
    } else if (subDomain === 'op') {
      baseUrl = 'https://op-wx.rocketbird.cn';
    } else {
      baseUrl = 'https://bo-beta.rocketbird.cn';
    }
    return baseUrl;
  },
  getLoginAppId() {
    let appId;
    let host = window.location.host;
    let subDomain = host.split('.')[0];
    if (subDomain === 'bo') {
      appId = 'dingoaj4dak8zujuepiqzm';
    } else if (subDomain === 'bo-fe-sim') {
      appId = 'dingoarkyctaeissfpyitv';
    } else {
      appId = 'dingoas3ykd8ps1kiuuhcq';
    }
    return appId;
  },
  // 勤鸟运动平台的对应接口地址
  getRbBaseUrl() {
    let baseUrl;
    let host = window.location.host;
    let subDomain = host.split('.')[0];
    if (subDomain === 'bo-sim') {
      baseUrl = 'https://sim.rocketbird.cn';
    } else if (subDomain === 'bo') {
      baseUrl = 'https://wx.rocketbird.cn';
    } else {
      baseUrl = 'https://beta.rocketbird.cn';
    }
    return baseUrl;
  },
  formatDate(source, format) {
    const o = {
      'M+': source.getMonth() + 1, // 月份
      'd+': source.getDate(), // 日
      'H+': source.getHours(), // 小时
      'm+': source.getMinutes(), // 分
      's+': source.getSeconds(), // 秒
      'q+': Math.floor((source.getMonth() + 3) / 3), // 季度
      'f+': source.getMilliseconds() // 毫秒
    };
    if (/(y+)/.test(format)) {
      format = format.replace(RegExp.$1, (source.getFullYear() + '').substr(4 - RegExp.$1.length));
    }
    for (let k in o) {
      if (new RegExp('(' + k + ')').test(format)) {
        format = format.replace(RegExp.$1, RegExp.$1.length === 1 ? o[k] : ('00' + o[k]).substr(('' + o[k]).length));
      }
    }
    return format;
  }
};

export default commonFn;
