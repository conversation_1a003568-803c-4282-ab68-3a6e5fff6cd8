::-webkit-scrollbar-track {
  border-radius: 3px;
  background-color: #fff;
}

::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background-color: #ccc;
}
.top-menu-wrap {
	display: flex;
	align-items: center;
	white-space: nowrap;
	overflow-x: auto; 
}

.top-menu:hover {
	cursor: pointer;
	background: #324057;
	/*color: #20a0ff;*/
}
.top-menu:active, .left-menu:active {
	color: #20a0ff;
}
.top-active {
	color: #44B5DF
}
.top-menu, .left-menu {
	user-select:none;
	-webkit-user-select:none;
	-moz-user-select:none;
	-o-user-select:none;
	-ms-user-select:none;
}
.left-menu:hover {
	background: #42566C;
}
.pages {
	position: absolute;
	top: 20px;
	right: -5px;
}
.btn-link,.btn-link-large {
	display: inline-block;
	line-height: 1;
	color: #fff;
	white-space: nowrap;
	cursor: pointer;
	text-align: center;
	box-sizing: border-box;
	margin-right: 10px;
	padding: 7px 9px;
	font-size: 12px;
	border-radius: 4px;
}
.btn-link-large {
	margin: 0;
	padding: 10px 15px;
	font-size: 14px;
}
.add-btn {
	background: #4caf50
}
.add-btn:hover {
	background: #66bb6a
}
.add-btn:active {
	background: #43a047
}
.edit-btn {
	background: #339df7
}
.edit-btn:hover {
	background: #5bb1fa
}
.edit-btn:active {
	background: #047ce2
}
.user-menu {
	position: absolute;
	top: 0px;
	right: 100px;
}
.add-btn-right {
	position: absolute;
	top: 0px;
	right: 0px;
}
.table-head {
  position: relative;
  display: block;
  width: 100%;
  height: 30px;
  line-height: 30px;
  color: #fff;
  background: #515151
}
.table-head > .title {
  color: #fff;
  padding-left: 10px;
}
.table-head > .icon {
  position: absolute;
  font-size: 20px;
  top: 6px;
  right: 10px;
  cursor: pointer;
}
.table-head > .table-head-btn {
	position: absolute;
  top: 4px;
  right: 10px;
}
.table-head > .table-head-btn:last-child {
	right: 55px;
}
.form-frame {
  /*width: 100%;*/
  min-height: 470px;
  background: #fff;
  padding: 22px 22px 22px 0;
}
.form-title {
  padding-bottom: 10px;
  font-weight: bold;
  font-size: 20px;
  border-bottom: 1px solid #ccc;
}
.dynamadd .el-form-item__content {
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  align-items: flex-start;
}
/* wxplat里的穿梭弹窗搜索框和按钮 */
.dialogin .el-input__inner {
  height: 30px;
}
.dialogin .el-input {
  width: 30%;
}
.dialogin .el-button {
  height: 30px;
  padding: 0 15px;
}