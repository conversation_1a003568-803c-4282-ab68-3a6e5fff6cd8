.container {
  background-color: #fff;

  header {
    background-color: #fff;
    min-height: 80px;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    border-bottom: 1px solid #ececec;
    flex-wrap: wrap;
    > .el-input, .el-date-editor, .el-button, .el-select {
      margin-left: 15px;
      max-width: 200px;
    }
    > .el-date-editor {
      max-width: 260px;
    }
    .el-range-editor  {
      max-width: 260px;
    }
  }

  footer {
    background-color: #fff;
    min-height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 0 20px;
    .el-pagination {
      margin-left: auto;
    }
  }
  .modal-form footer {
    justify-content: center;
  }

  .el-tabs--border-card>.el-tabs__content {
    padding: 0;
  }
}
// 切换tab
.twoTabs {
  .el-tabs__nav {
    width: 100%;
  }
  .el-tabs--border-card>.el-tabs__header .el-tabs__item {
    width: 50%;
    text-align: center;
  }
}

 .el-tooltip__popper{
    max-width:20%;
  }
  .el-tooltip__popper,.el-tooltip__popper.is-dark{
    background:rgb(48, 65, 86) !important;
    color: #fff !important;
    line-height: 24px;
  }
.form {
  max-width: 600px;
}
