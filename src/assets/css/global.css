ul li {
  list-style: none
}
a {
  text-decoration: none;
  color: #66b1ff;
}
.pos-rel {
  position: relative;
}
.ovf-hd {
  overflow: hidden
}
.ovf-auto {
  overflow: auto
}
.ovf-y-auto {
  overflow-y: auto
}
.fl {
  float: left
}
.fr {
  float: right
}

.m-w-1280 {
  min-width: 1280px !important;
}
.w-80 {
  width: 80px !important;
}
.w-100 {
  width: 100px !important;
}
.w-115 {
  width: 115px !important;
}
.w-150 {
  width: 150px !important;
}
.w-180 {
  width: 180px !important;
}
.w-200 {
  width: 200px !important;
}
.w-230 {
  width: 230px !important;
}
.w-295 {
  width: 295px !important;
}
.w-300 {
  width: 300px !important;
}
.w-310 {
  width: 310px !important;
}
.w-400 {
  width: 400px !important;
}
.w-450 {
  width: 450px !important;
}
.w-500 {
  width: 500px !important;
}
.w-600 {
  width: 600px !important;
}
.w-650 {
  width: 650px !important;
}
.w-700 {
  width: 700px !important;
}
.w-800 {
  width: 800px !important;
}
.w-900 {
  width: 900px !important;
}
.w-950 {
  width: 950px !important;
}
.w-1000 {
  width: 1000px !important;
}
.w-100p {
  width: 100% !important;
}
.w-320 {
  width: 320px !important;
}
.h-28 {
  height: 28px !important;
  line-height: 28px !important;
}
.h-30 {
  height: 30px !important;
  line-height: 30px !important;
}
.h-36 {
  height: 36px !important;
  line-height: 36px !important;
}
.h-40 {
  height: 40px !important;
  line-height: 40px !important;
}
.h-50 {
  height: 50px !important;
  line-height: 50px !important;
}
.h-60 {
  height: 60px !important;
  line-height: 60px !important;
}
.h-100 {
  height: 100px !important;
}
.h-150 {
  height: 150px !important;
}
.h-190 {
  height: 190px !important;
}
.h-200 {
  height: 200px !important;
}
.h-300 {
  height: 300px !important;
}
.h-400 {
  height: 400px !important;
}
.h-480 {
  height: 480px !important;
}
.h-500 {
  height: 500px !important;
}
.h-600 {
  height: 600px !important;
}
.h-100p {
  height: 100% !important;
}
.mh-270 {
  max-height: 270px
}
.mh-400 {
  max-height: 400px
}


.m-t-5 {
  margin-top: 5px !important;
}
.m-t-10 {
  margin-top: 10px !important;
}
.m-t-15 {
  margin-top: 15px !important;
}
.m-t-20 {
  margin-top: 20px !important;
}
.m-t-30 {
  margin-top: 30px !important;
}
.m-l-5 {
  margin-left: 5px !important;
}
.m-l-10 {
  margin-left: 10px !important;
}
.m-l-20 {
  margin-left: 20px !important;
}
.m-l-15 {
  margin-left: 15px !important;
}
.m-l-30 {
  margin-left: 30px !important;
}
.m-l-50 {
  margin-left: 50px !important;
}
.m-l-150 {
  margin-left: 150px !important;
}
.m-r-5 {
  margin-right: 5px !important;
}
.m-r-10 {
  margin-right: 10px !important;
}
.m-r-15 {
  margin-right: 15px !important;
}
.m-r-30 {
  margin-right: 30px !important;
}
.m-b-5 {
  margin-bottom: 5px !important;
}
.m-b-10 {
  margin-bottom: 10px !important;
}
.m-b-15 {
  margin-bottom: 15px !important;
}
.m-b-20 {
  margin-bottom: 20px !important;
}
.m-b-30 {
  margin-bottom: 30px !important;
}

.p-t-0 {
  padding-top: 0px !important;
}
.p-t-3 {
  padding-top: 3px !important;
}
.p-t-5 {
  padding-top: 5px !important;
}
.p-t-6 {
  padding-top: 6px !important;
}
.p-t-10 {
  padding-top: 10px !important;
}
.p-t-15 {
  padding-top: 15px !important;
}
.p-t-20 {
  padding-top: 20px !important;
}
.p-t-30 {
  padding-top: 30px !important;
}
.p-t-40 {
  padding-top: 40px !important;
}
.p-t-50 {
  padding-top: 50px !important;
}
.p-l-0 {
  padding-left: 0px !important;
}
.p-l-5 {
  padding-left: 5px !important;
}
.p-l-6 {
  padding-left: 6px !important;
}
.p-l-10 {
  padding-left: 10px !important;
}
.p-l-13 {
  padding-left: 13px !important;
}
.p-l-15 {
  padding-left: 15px !important;
}
.p-l-20 {
  padding-left: 20px !important;
}
.p-l-30 {
  padding-left: 30px !important;
}
.p-l-40 {
  padding-left: 40px !important;
}
.p-l-50 {
  padding-left: 50px !important;
}
.p-r-0 {
  padding-right: 0px !important;
}
.p-r-5 {
  padding-right: 5px !important;
}
.p-r-6 {
  padding-right: 6px !important;
}
.p-r-10 {
  padding-right: 10px !important;
}
.p-r-15 {
  padding-right: 15px !important;
}
.p-r-20 {
  padding-right: 20px !important;
}
.p-r-30 {
  padding-right: 30px !important;
}
.p-r-40 {
  padding-right: 40px !important;
}
.p-r-50 {
  padding-right: 50px !important;
}
.p-b-0 {
  padding-bottom: 0px !important;
}
.p-b-3 {
  padding-bottom: 3px !important;
}
.p-b-5 {
  padding-bottom: 5px !important;
}
.p-b-6 {
  padding-bottom: 6px !important;
}
.p-b-10 {
  padding-bottom: 10px !important;
}
.p-b-15 {
  padding-bottom: 15px !important;
}
.p-b-20 {
  padding-bottom: 20px !important;
}
.p-b-30 {
  padding-bottom: 30px !important;
}
.p-b-40 {
  padding-bottom: 40px !important;
}
.p-b-50 {
  padding-bottom: 50px !important;
}

.bor-gray {
  border: 1px solid #e5e5e5;
}
.bor-gra {
  border: 1px solid #C0CCDA;
}
.bor-b-gray {
  border-bottom: 1px solid #e5e5e5;
}
.bor-b-ccc {
  border-bottom: 1px solid #ccc;
}
.bor-none {
  border: none;
}


.bor-ra-3 {
  border-radius: 3px;
  -webkit-border-radius: 3px;
  -moz-border-radius: 3px;
  -o-border-radius: 3px;
  -ms-border-radius: 3px;
}
.bor-ra-5 {
  border-radius: 5px;
  -webkit-border-radius: 5px;
  -moz-border-radius: 5px;
  -o-border-radius: 5px;
  -ms-border-radius: 5px;
}


.tx-c {
  text-align: center;
}

.bg-wh {
  background: #fff;
}
.bg-gray {
  background: #e5e5e5;
}
.bg-gra {
  background: #F9F9F9;
}
.bg-transparency {
  background: rgba(0,0,0,0);
}
.bg-blue {
  background: #20a0ff !important;
}

.c-gray {
  color: #ccc;
}
.c-light-gray {
  color: #99a9bf;
}
.c-gra {
  color: #C0CCDA;
}
.c-deep-blue {
  color: #1F2D3D;
}
.c-blue {
  color: #44B5DF;
}
.c-black {
  color: #333;
}
.c-warning {
  color: #e6a23c;
}
.c-success {
  color: #67c23a;
}
.c-danger {
  color: #f56c6c;
}
.c-primary {
  color: #409eff;
}

.fz-12 {
  font-size: 12px
}
.fz-14 {
  font-size: 14px
}
.fz-18 {
  font-size: 18px
}
.fz-20 {
  font-size: 20px
}
.fz-22 {
  font-size: 22px
}
.fz-24 {
  font-size: 24px
}

.pointer {
  cursor: pointer
}
.client-editor{
  background: #fff;
  max-width: 900px;
}
.client-editor .ql-container {
  min-height: 150px;
}

.img-dialog{width:auto;max-width:800px;min-width: 300px;}
.img-dialog .el-dialog__body{text-align: center;}

.upload-list .el-upload-list--picture .el-upload-list__item-name{
  display: none;
}
.upload-list  .el-upload-list--picture .el-upload-list__item{
  float: left;
  width: 80px;
  margin-right: 5px;
}
.flexend {
  display: flex;
  flex-direction: row;
  justify-content: flex-end;
  align-items: center;
}

