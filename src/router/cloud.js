import Sidebar from 'components/Common/sidebar'

export default {
  path: '/cloud',
  component: Sidebar,
  children: [
    {
      path: 'deviceStat',
      component: (resolve) => require(['src/views/cloud/deviceStat'], resolve),
      name: '设备统计',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'deviceStat',
      },
    },
    {
      path: 'devUpdate',
      component: (resolve) => require(['src/views/cloud/devUpdate.vue'], resolve),
      name: 'devUpdate',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'devUpdate',
      },
    },
    {
      path: 'faceDev',
      component: (resolve) => require(['src/views/cloud/faceDev.vue'], resolve),
      name: 'faceDev',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'faceDev',
      },
    },
    {
      path: 'faceDevLog',
      component: (resolve) => require(['src/views/cloud/faceDevLog.vue'], resolve),
      name: 'faceDevLog',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'faceDev',
      },
    },
    {
      path: 'user',
      component: (resolve) => require(['src/views/cloud/userTabs.vue'], resolve),
      name: 'cloudUserList',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'cloudUserList',
      },
    },
    {
      path: 'user/:veinUid',
      component: (resolve) => require(['src/views/cloud/userDetail.vue'], resolve),
      name: 'cloudUserDetail',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'cloudUserDetail',
      },
    },
    {
      path: 'channel',
      component: (resolve) => require(['src/views/cloud/channel.vue'], resolve),
      name: 'cloudChannel',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'cloudChannel',
      },
    },
    {
      path: 'agreement',
      component: (resolve) => require(['src/views/cloud/agreement.vue'], resolve),
      name: 'cloudAgreement',
      meta: {
        module: 'cloud',
        menu: 'cloudAgreement',
      },
    },
    {
      path: 'agreementAdd',
      component: (resolve) => require(['src/views/cloud/agreementAdd.vue'], resolve),
      name: 'cloudAgreementAdd',
      meta: {
        module: 'cloud',
        menu: 'cloudAgreementAdd',
      },
    },
    {
      path: 'channelAdd',
      component: (resolve) => require(['src/views/cloud/channelAdd.vue'], resolve),
      name: 'cloudChannelAdd',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'cloudChannelAdd',
      },
    },
    {
      path: 'version',
      component: (resolve) => require(['src/views/cloud/version.vue'], resolve),
      name: 'cloudVersion',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'cloudVersion',
      },
    },
    {
      path: 'versionAdd',
      component: (resolve) => require(['src/views/cloud/versionAdd.vue'], resolve),
      name: 'cloudVersionAdd',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'cloudVersionAdd',
      },
    },
    {
      path: 'devControl',
      component: (resolve) => require(['src/views/cloud/devControl.vue'], resolve),
      name: 'devControl',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'devControl',
      },
    },
    {
      path: 'cameraList',
      component: (resolve) => require(['src/views/cloud/cameraList.vue'], resolve),
      name: 'cameraList',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'cameraList',
      },
    },
    {
      path: 'cameraLog',
      component: (resolve) => require(['src/views/cloud/cameraLog.vue'], resolve),
      name: 'cameraLog',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'cameraLog',
      },
    },
    {
      path: 'cameraRecord',
      component: (resolve) => require(['src/views/cloud/cameraRecord.vue'], resolve),
      name: 'cameraRecord',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'cameraRecord',
      },
    },
    {
      path: 'cameraAdd',
      component: (resolve) => require(['src/views/cloud/cameraAdd.vue'], resolve),
      name: 'cameraAdd',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'cameraAdd',
      },
    },
    {
      path: 'devOpenRecord',
      component: (resolve) => require(['src/views/cloud/devOpenRecord.vue'], resolve),
      name: 'devOpenRecord',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'devOpenRecord',
      },
    },
    {
      path: 'devChange',
      component: (resolve) => require(['src/views/cloud/devChange.vue'], resolve),
      name: 'devChange',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'devChange',
      },
    },
    {
      path: 'devBug',
      component: (resolve) => require(['src/views/cloud/devBug.vue'], resolve),
      name: 'devBug',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'devBug',
      },
    },
    {
      path: 'devBugDetail',
      component: (resolve) => require(['src/views/cloud/devBugDetail.vue'], resolve),
      name: 'devBugDetail',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'devBugDetail',
      },
    },
    {
      path: 'devBugAdd',
      component: (resolve) => require(['src/views/cloud/devBugAdd.vue'], resolve),
      name: 'devBugAdd',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'devBugAdd',
      },
    },
    {
      path: 'devEdit',
      component: (resolve) => require(['src/views/cloud/devEdit.vue'], resolve),
      name: 'devEdit',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'devEdit',
      },
    },
    {
      path: 'lockerArr/:device_id',
      component: (resolve) => require(['src/views/cloud/lockerArr.vue'], resolve),
      name: 'lockerArr',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'lockerArr',
      },
    },
    {
      path: 'logs',
      component: (resolve) => require(['src/views/cloud/logs.vue'], resolve),
      // component: resolve => require(['src/views/cloud/logsReplace.vue'], resolve),
      name: 'logs',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'logs',
      },
    },
    {
      path: 'busStat',
      component: (resolve) => require(['src/views/cloud/busStat.vue'], resolve),
      name: 'busStat',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'busStat',
      },
    },
    {
      path: 'userPassStat',
      component: (resolve) => require(['src/views/cloud/userPassStat.vue'], resolve),
      name: 'userPassStat',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'userPassStat',
      },
    },
    {
      path: 'devStat',
      component: (resolve) => require(['src/views/cloud/devStat.vue'], resolve),
      name: 'devStat',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'devStat',
      },
    },
    {
      path: 'devActiveStat',
      component: (resolve) => require(['src/views/cloud/devActiveStat.vue'], resolve),
      name: 'devActiveStat',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'devActiveStat',
      },
    },
    {
      path: 'faceDevActiveStat',
      component: (resolve) => require(['src/views/cloud/faceDevActiveStat.vue'], resolve),
      name: 'faceDevActiveStat',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'faceDevActiveStat',
      },
    },
    {
      path: 'faceSync',
      component: (resolve) => require(['src/views/cloud/faceSync.vue'], resolve),
      name: 'faceSync',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'faceSync',
      },
    },
    {
      path: 'faceSyncAdd',
      component: (resolve) => require(['src/views/cloud/faceSyncAdd.vue'], resolve),
      name: 'faceSyncAdd',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'faceSyncAdd',
      },
    },
    {
      path: 'faceSyncLog',
      component: (resolve) => require(['src/views/cloud/faceSyncLog.vue'], resolve),
      name: 'faceSyncLog',
      meta: {
        hideLeft: false,
        module: 'cloud',
        menu: 'faceSyncLog',
      },
    },
  ],
}
