import Sidebar from 'src/components/Common/sidebar';
export default {
  path: '/wxPlatform',
  component: Sidebar,
  children: [
    {
      path: 'modelControl',
      component: resolve =>
        require(['src/views/wxPlatform/modelControl.vue'], resolve),
      name: 'modelControl',
      meta: { hideLeft: false, module: 'wxPlatform', menu: 'modelControl' }
    },
    {
      path: 'codeControl',
      component: resolve =>
        require(['src/views/wxPlatform/codeControl.vue'], resolve),
      name: 'codeControl',
      meta: { hideLeft: false, module: 'wxPlatform', menu: 'codeControl' }
    },
    {
      path: 'massApproval',
      component: resolve =>
        require(['src/views/wxPlatform/massApproval.vue'], resolve),
      name: 'massApproval',
      props: route => ({ query: route.query.appId }),
      meta: { hideLeft: false, module: 'wxPlatform', menu: 'massApproval' }
    },
    {
      path: 'codePublish',
      component: resolve =>
        require(['src/views/wxPlatform/codePublish.vue'], resolve),
      name: 'codePublish',
      meta: { hideLeft: false, module: 'wxPlatform', menu: 'codePublish' }
    },
    {
      path: 'wxmemberControl',
      component: resolve =>
        require(['src/views/wxPlatform/wxmemberControl.vue'], resolve),
      name: 'wxmemberControl',
      props: route => ({ query: route.query.appId }),
      meta: { hideLeft: false, module: 'wxPlatform', menu: 'wxmemberControl' }
    },
    {
      path: 'wxConfig',
      component: resolve =>
        require(['src/views/wxPlatform/wxConfig.vue'], resolve),
      name: 'wxConfig',
      meta: { hideLeft: false, module: 'wxPlatform', menu: 'wxConfig' }
    },
    {
      path: 'publicConfig',
      component: resolve =>
        require(['src/views/wxPlatform/publicConfig.vue'], resolve),
      name: 'publicConfig',
      meta: { hideLeft: false, module: 'wxPlatform', menu: 'publicConfig' }
    },
    {
      path: 'operRecord',
      component: resolve =>
        require(['src/views/wxPlatform/operRecord.vue'], resolve),
      name: 'operRecord',
      meta: { hideLeft: false, module: 'wxPlatform', menu: 'operRecord' }
    },
    {
      path: 'wxPrivacy',
      component: resolve =>
        require(['src/views/wxPlatform/wxPrivacy.vue'], resolve),
      name: 'wxPrivacy',
      meta: { hideLeft: false, module: 'wxPlatform', menu: 'wxPrivacy' }
    }
  ]
}

