import Sidebar from 'src/components/Common/sidebar';

export default {
  path: '/ad',
  component: Sidebar,
  children: [
    {
      path: 'sourceList',
      component: (resolve) => require(['src/views/ad/sourceList'], resolve),
      name: '素材管理',
      meta: {
        hideLeft: false,
        module: 'ad',
        menu: 'sourceList',
      },
    },
    {
      path: 'sourceEdit',
      component: (resolve) => require(['src/views/ad/sourceEdit.vue'], resolve),
      name: '素材编辑',
      meta: {
        module: 'ad',
        menu: 'sourceEdit',
      },
    },
    {
      path: 'adList',
      component: (resolve) => require(['src/views/ad/adList'], resolve),
      name: '广告管理',
      meta: {
        hideLeft: false,
        module: 'ad',
        menu: 'adList',
      },
    },
    {
      path: 'adEdit',
      component: (resolve) => require(['src/views/ad/adEdit.vue'], resolve),
      name: '广告编辑',
      meta: {
        module: 'ad',
        menu: 'adEdit',
      },
    },
    {
      path: 'channelList',
      component: (resolve) => require(['src/views/ad/channelList'], resolve),
      name: '渠道管理',
      meta: {
        hideLeft: false,
        module: 'ad',
        menu: 'channelList',
      },
    },
    {
      path: 'channelEdit',
      component: (resolve) => require(['src/views/ad/channelEdit.vue'], resolve),
      name: '渠道编辑',
      meta: {
        module: 'ad',
        menu: 'channelEdit',
      },
    },
    {
      path: 'adStatistics',
      component: (resolve) => require(['src/views/ad/adStatistics'], resolve),
      name: '广告统计',
      meta: {
        hideLeft: false,
        module: 'ad',
        menu: 'adStatistics',
      },
    },
    {
      path: 'channelStatistics',
      component: (resolve) => require(['src/views/ad/channelStatistics'], resolve),
      name: '渠道统计',
      meta: {
        hideLeft: false,
        module: 'ad',
        menu: 'channelStatistics',
      },
    },
  ],
}

