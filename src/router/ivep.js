import Sidebar from 'src/components/Common/sidebar';

export default {
  path: '/ivep',
  component: Sidebar,
  children: [
    {
      path: 'serviceList',
      component: resolve =>
        require(['src/views/ivep/serviceList'], resolve),
      name: '服务管理',
      meta: {
        hideLeft: false,
        module: 'IVEP',
        menu: 'serviceList'
      }
    },
    {
      path: 'serviceManagement',
      component: resolve =>
        require(['src/views/ivep/serviceManagement'], resolve),
      name: '服务管理',
      meta: {
        hideLeft: false,
        module: 'IVEP',
        menu: 'serviceManagement'
      }
    },
    {
      path: 'serviceDetail',
      component: resolve => require(['src/views/ivep/serviceDetail'], resolve),
      name: '服务详情',
      meta: {
        hideLeft: false,
        module: 'IVEP',
        menu: 'serviceDetail'
      }
    },
    {
      path: 'logs',
      component: resolve => require(['src/views/ivep/logs.vue'], resolve),
      name: 'logs',
      meta: {
        hideLeft: false,
        module: 'IVEP',
        menu: 'logs'
      }
    },
    {
      path: 'schoolList',
      component: resolve => require(['src/views/ivep/schoolList.vue'], resolve),
      name: '学院管理',
      meta: {
        module: 'IVEP',
        menu: 'schoolList'
      }
    },
    {
      path: 'schoolEdit',
      component: resolve => require(['src/views/ivep/schoolEdit.vue'], resolve),
      name: '学院编辑',
      meta: {
        module: 'IVEP',
        menu: 'schoolEdit'
      }
    },
    {
      path: 'bannerList',
      component: resolve => require(['src/views/ivep/bannerList.vue'], resolve),
      name: 'banner管理',
      meta: {
        module: 'IVEP',
        menu: 'bannerList'
      }
    },
    {
      path: 'bannerEdit',
      component: resolve => require(['src/views/ivep/bannerEdit.vue'], resolve),
      name: 'banner编辑',
      meta: {
        module: 'IVEP',
        menu: 'bannerEdit'
      }
    },
    {
      path: 'courseList',
      component: resolve => require(['src/views/ivep/courseList.vue'], resolve),
      name: '课程管理',
      meta: {
        module: 'IVEP',
        menu: 'courseList'
      }
    },
    {
      path: 'courseEdit',
      component: resolve => require(['src/views/ivep/courseEdit.vue'], resolve),
      name: '课程编辑',
      meta: {
        module: 'IVEP',
        menu: 'courseEdit'
      }
    },
    {
      path: 'courseOffline',
      component: resolve =>
        require(['src/views/ivep/courseOffline.vue'], resolve),
      name: '线下培训',
      meta: {
        module: 'IVEP',
        menu: 'courseOffline'
      }
    },
    {
      path: 'courseOfflineEdit',
      component: resolve =>
        require(['src/views/ivep/courseOfflineEdit.vue'], resolve),
      name: '编辑线下培训',
      meta: {
        module: 'IVEP',
        menu: 'courseOfflineEdit'
      }
    },
    {
      path: 'applyList',
      component: resolve => require(['src/views/ivep/applyList.vue'], resolve),
      name: '报名信息',
      meta: {
        module: 'IVEP',
        menu: 'applyList'
      }
    },
    {
      path: 'message',
      component: resolve => require(['src/views/ivep/message.vue'], resolve),
      name: '通知',
      meta: {
        module: 'IVEP',
        menu: 'message'
      }
    },
    {
      path: 'editMessage',
      component: resolve =>
        require(['src/views/ivep/editMessage.vue'], resolve),
      name: '通知详情',
      meta: {
        module: 'IVEP',
        menu: 'editMessage'
      }
    }
  ]
}

