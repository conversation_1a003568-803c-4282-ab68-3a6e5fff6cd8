import Sidebar from 'components/Common/sidebar';

export default {
  path: '/birdnets',
  component: Sidebar,
  children: [
    {
      path: '',
      component: (resolve) => require(['src/views/birdnets/index'], resolve),
      name: '鸟巢数据看板',
      meta: {
        hideLeft: false,
        module: 'Birdnets',
        menu: 'birdnets',
      },
    },
    {
      path: 'export',
      component: (resolve) => require(['src/views/birdnets/dataExport'], resolve),
      name: '场馆数据导出',
      meta: {
        hideLeft: false,
        module: 'Birdnets',
        menu: 'dataExport',
      },
    },
    {
      path: 'merchantExport',
      component: (resolve) => require(['src/views/birdnets/merchantExport'], resolve),
      name: '商家数据导出',
      meta: {
        hideLeft: false,
        module: 'Birdnets',
        menu: 'merchantExport',
      },
    },
    {
      path: 'detail',
      component: (resolve) => require(['src/views/birdnets/busDetail'], resolve),
      name: '场馆详情',
      meta: {
        hideLeft: false,
        module: 'Birdnets',
        menu: 'busDetail',
      },
    },
    {
      path: 'merchantDetail',
      component: (resolve) => require(['src/views/birdnets/merchantDetail'], resolve),
      name: '商家详情详情',
      meta: {
        hideLeft: false,
        module: 'Birdnets',
        menu: 'merchantDetail',
      },
    },
    {
      path: 'setting',
      component: (resolve) => require(['src/views/birdnets/setting'], resolve),
      name: '参数设置',
      meta: {
        hideLeft: false,
        module: 'Birdnets',
        menu: 'setting',
      },
    },
    {
      path: 'faq',
      component: (resolve) => require(['src/views/birdnets/faqPublish'], resolve),
      name: '帮助中心',
      meta: {
        hideLeft: false,
        module: 'Birdnets',
        menu: 'faqPublish',
      },
    },
    {
      path: 'faqContent',
      component: (resolve) => require(['src/views/birdnets/faqEdit'], resolve),
      name: '编辑/添加帮助',
      meta: {
        hideLeft: false,
        module: 'Birdnets',
        menu: 'faqEdit',
      },
    },
    {
      path: 'ivepAll',
      component: () => import('src/views/birdnets/ivepAll'),
      name: 'ivep看板',
      meta: {
        module: 'Birdnets',
        menu: 'ivepAll',
      },
    },
    {
      path: 'ivepAdminList',
      component: () => import('src/views/birdnets/ivepAdminList'),
      name: '账号数据详情',
      meta: {
        module: 'Birdnets',
        menu: 'ivepAdminList',
      },
    },
    {
      path: 'ivepUseList',
      component: () => import('src/views/birdnets/ivepUseList'),
      name: '数据使用详情',
      meta: {
        module: 'Birdnets',
        menu: 'ivepUseList',
      },
    },
    {
      path: 'alipayRecord',
      component: () => import('src/views/birdnets/alipayRecord'),
      name: '支付宝订单',
      meta: {
        module: 'Birdnets',
        menu: 'alipayRecord',
      },
    },
    {
      path: 'accessReportSettings',
      component: () => import('src/views/birdnets/accessReportSettings'),
      name: '监控节点设置',
      meta: {
        module: 'Birdnets',
        menu: 'accessReportSettings',
      },
    },
    {
      path: 'accessGateReport',
      component: () => import('src/views/birdnets/accessGateReport'),
      name: '出入闸机数据分析',
      meta: {
        module: 'Birdnets',
        menu: 'accessGateReport',
      },
    },
    {
      path: 'accessCabinetReport',
      component: () => import('src/views/birdnets/accessCabinetReport'),
      name: '存取退柜数据分析',
      meta: {
        module: 'Birdnets',
        menu: 'accessCabinetReport',
      },
    },
    {
      path: 'palmReport',
      component: () => import('src/views/birdnets/palmReport'),
      name: '刷掌用户开通分析',
      meta: {
        module: 'Birdnets',
        menu: 'palmReport',
      },
    },
    {
      path: 'skyPalmReport',
      component: () => import('src/views/birdnets/skyPalmReport'),
      name: '刷掌用户开通分析',
      meta: {
        module: 'Birdnets',
        menu: 'skyPalmReport',
      },
    },
    {
      path: 'accessHandScanReport',
      component: () => import('src/views/birdnets/accessHandScanReport'),
      name: '刷掌开通及购票分析',
      meta: {
        module: 'Birdnets',
        menu: 'accessHandScanReport',
      },
    },
    {
      path: 'accessOperationLog',
      component: () => import('src/views/birdnets/accessOperationLog'),
      name: '操作日志',
      meta: {
        module: 'Birdnets',
        menu: 'accessOperationLog',
      },
    },
  ],
}
