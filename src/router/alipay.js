import Sidebar from 'src/components/Common/sidebar';

export default {
  path: '/alipay',
  component: Sidebar,
  children: [
    {
      path: 'minConfig',
      component: (resolve) => require(['src/views/alipay/minConfig.vue'], resolve),
      name: 'alipayMinConfig',
      meta: { hideLeft: false, module: 'alipay', menu: 'minConfig' },
    },
    {
      path: 'record',
      component: (resolve) => require(['src/views/alipay/record.vue'], resolve),
      name: 'alipayRecord',
      meta: { hideLeft: false, module: 'alipay', menu: 'record' },
    },
    {
      path: 'version',
      component: (resolve) => require(['src/views/alipay/version.vue'], resolve),
      name: 'alipayVersion',
      meta: { hideLeft: false, module: 'alipay', menu: 'version' },
    },
    {
      path: 'service',
      component: (resolve) => require(['src/views/alipay/service.vue'], resolve),
      name: 'alipayService',
      meta: { hideLeft: false, module: 'alipay', menu: 'service' },
    },
    {
      path: 'serviceRecord',
      component: (resolve) => require(['src/views/alipay/serviceRecord.vue'], resolve),
      name: 'serviceRecord',
      meta: { hideLeft: false, module: 'alipay', menu: 'serviceRecord' },
    },
    {
      path: 'promoRecord',
      component: (resolve) => require(['src/views/alipay/promoRecord.vue'], resolve),
      name: 'promoRecord',
      meta: { hideLeft: false, module: 'alipay', menu: 'promoRecord' },
    },
    {
      path: 'zhimaMerchants',
      component: (resolve) => require(['src/views/alipay/zhimaMerchants.vue'], resolve),
      name: 'zhimaMerchants',
      meta: { hideLeft: false, module: 'alipay', menu: 'zhimaMerchants' },
    },
    {
      path: 'merchantUpdateLogs',
      component: (resolve) => require(['src/views/alipay/merchantUpdateLogs.vue'], resolve),
      name: 'merchantUpdateLogs',
      meta: { hideLeft: false, module: 'alipay', menu: 'merchantUpdateLogs' },
    },
    {
      path: 'zhimaStore',
      component: (resolve) => require(['src/views/alipay/zhimaStore.vue'], resolve),
      name: 'zhimaStore',
      meta: { hideLeft: false, module: 'alipay', menu: 'zhimaStore' },
    },
    {
      path: 'zhimaContract',
      component: (resolve) => require(['src/views/alipay/zhimaContract.vue'], resolve),
      name: 'zhimaContract',
      meta: { hideLeft: false, module: 'alipay', menu: 'zhimaContract' },
    },
    {
      path: 'zhimaProducts',
      component: (resolve) => require(['src/views/alipay/zhimaProducts.vue'], resolve),
      name: 'zhimaProducts',
      meta: { hideLeft: false, module: 'alipay', menu: 'zhimaProducts' },
    },
    {
      path: 'zhimaOrder',
      component: (resolve) => require(['src/views/alipay/zhimaOrder.vue'], resolve),
      name: 'zhimaOrder',
      meta: { hideLeft: false, module: 'alipay', menu: 'zhimaOrder' },
    },
    {
      path: 'zhimaStatistics',
      component: (resolve) => require(['src/views/alipay/zhimaDashboard.vue'], resolve),
      name: 'zhimaStatistics',
      meta: { hideLeft: false, module: 'alipay', menu: 'zhimaStatistics' },
    },
    {
      path: 'zhimaSignStatistics',
      component: (resolve) => require(['src/views/alipay/zhimaSignStatistics.vue'], resolve),
      name: 'zhimaSignStatistics',
      meta: { hideLeft: false, module: 'alipay', menu: 'zhimaSignStatistics' },
    },
    {
      path: 'zhimaRates',
      component: (resolve) => require(['src/views/alipay/zhimaRates.vue'], resolve),
      name: 'zhimaRates',
      meta: { hideLeft: false, module: 'alipay', menu: 'zhimaRates' },
    },
    {
      path: 'zhimaSales',
      component: (resolve) => require(['src/views/alipay/zhimaSales.vue'], resolve),
      name: 'zhimaSales',
      meta: { hideLeft: false, module: 'alipay', menu: 'zhimaSales' },
    },
    {
      path: 'zhimaSaleStatistics',
      component: (resolve) => require(['src/views/alipay/zhimaSaleStatistics.vue'], resolve),
      name: 'zhimaSaleStatistics',
      meta: { hideLeft: false, module: 'alipay', menu: 'zhimaSaleStatistics' },
    },
    {
      path: 'zhimaGMVSaleStatistics',
      component: (resolve) => require(['src/views/alipay/zhimaGMVSaleStatistics.vue'], resolve),
      name: 'zhimaGMVSaleStatistics',
      meta: { hideLeft: false, module: 'alipay', menu: 'zhimaGMVSaleStatistics' },
    },
    {
      path: 'zhimaGMVPerformanceStatistics',
      component: (resolve) => require(['src/views/alipay/zhimaGMVPerformanceStatistics.vue'], resolve),
      name: 'zhimaGMVPerformanceStatistics',
      meta: { hideLeft: false, module: 'alipay', menu: 'zhimaGMVPerformanceStatistics' },
    },
  ],
};
