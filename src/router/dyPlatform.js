import Sidebar from 'src/components/Common/sidebar';
export default {
  path: '/dyPlatform',
  component: Sidebar,
  children: [
    {
      path: 'modelControl',
      component: resolve =>
        require(['src/views/dyPlatform/modelControl.vue'], resolve),
      name: 'modelControldy',
      meta: { hideLeft: false, module: 'dyPlatform', menu: 'modelControl' }
    },
    {
      path: 'codeControl',
      component: resolve =>
        require(['src/views/dyPlatform/codeControl.vue'], resolve),
      name: 'codeControldy',
      meta: { hideLeft: false, module: 'dyPlatform', menu: 'codeControl' }
    },
    {
      path: 'massApproval',
      component: resolve =>
        require(['src/views/dyPlatform/massApproval.vue'], resolve),
      name: 'massApprovaldy',
      props: route => ({ query: route.query.appId }),
      meta: { hideLeft: false, module: 'dyPlatform', menu: 'massApproval' }
    },
    {
      path: 'codePublish',
      component: resolve =>
        require(['src/views/dyPlatform/codePublish.vue'], resolve),
      name: 'codePublishdy',
      meta: { hideLeft: false, module: 'dyPlatform', menu: 'codePublish' }
    },
    {
      path: 'dymemberControl',
      component: resolve =>
        require(['src/views/dyPlatform/dymemberControl.vue'], resolve),
      name: 'dymemberControldy',
      props: route => ({ query: route.query.appId }),
      meta: { hideLeft: false, module: 'dyPlatform', menu: 'dymemberControl' }
    },
    {
      path: 'dyConfig',
      component: resolve =>
        require(['src/views/dyPlatform/dyConfig.vue'], resolve),
      name: 'dyConfigdy',
      meta: { hideLeft: false, module: 'dyPlatform', menu: 'dyConfig' }
    },
    {
      path: 'publicConfig',
      component: resolve =>
        require(['src/views/dyPlatform/publicConfig.vue'], resolve),
      name: 'publicConfigdy',
      meta: { hideLeft: false, module: 'dyPlatform', menu: 'publicConfig' }
    },
    {
      path: 'operRecord',
      component: resolve =>
        require(['src/views/dyPlatform/operRecord.vue'], resolve),
      name: 'operRecorddy',
      meta: { hideLeft: false, module: 'dyPlatform', menu: 'operRecord' }
    },
    {
      path: 'dyPrivacy',
      component: resolve =>
        require(['src/views/dyPlatform/dyPrivacy.vue'], resolve),
      name: 'dyPrivacydy',
      meta: { hideLeft: false, module: 'dyPlatform', menu: 'dyPrivacy' }
    }
  ]
}

