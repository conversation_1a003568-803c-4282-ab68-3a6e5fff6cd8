import Login from './components/Account/Login.vue'
import refresh from './components/refresh.vue'
import Sidebar from 'components/Common/sidebar'
import menuList from './views/Administrative/system/menu/list.vue'
import menuAdd from './views/Administrative/system/menu/add.vue'
import menuEdit from './views/Administrative/system/menu/edit.vue'
import systemConfig from './views/Administrative/system/config/add.vue'
import ruleList from './views/Administrative/system/rule/list.vue'
import ruleAdd from './views/Administrative/system/rule/add.vue'
import ruleEdit from './views/Administrative/system/rule/edit.vue'
import positionList from './views/Administrative/structures/position/list.vue'
import positionAdd from './views/Administrative/structures/position/add.vue'
import positionEdit from './views/Administrative/structures/position/edit.vue'
import structuresList from './views/Administrative/structures/structures/list.vue'
import structuresAdd from './views/Administrative/structures/structures/add.vue'
import structuresEdit from './views/Administrative/structures/structures/edit.vue'
import groupsList from './views/Administrative/structures/groups/list.vue'
import groupsAdd from './views/Administrative/structures/groups/add.vue'
import groupsEdit from './views/Administrative/structures/groups/edit.vue'
import usersList from './views/Administrative/personnel/users/list.vue'
import usersAdd from './views/Administrative/personnel/users/add.vue'
import usersEdit from './views/Administrative/personnel/users/edit.vue'
import webindex from './views/Web/index.vue'
import settings from './views/Web/settings.vue'
import bustype from './views/Web/bus_type.vue'
import busbrand from './views/Web/bus_brand.vue'
import busregion from './views/Web/bus_region.vue'
import buswx from './views/Web/bus_wx.vue'
import basics from './views/Web/bus_basics.vue'
import users from './views/Web/users.vue'
import bus_chart from './views/Web/bus_chart.vue'
import bus_user_statistic from './views/Web/bus_users_statistics.vue'
import templateInfo from './views/Web/templateInfo.vue'
import bi from './views/Web/bi.vue'
import maintain from './views/Web/sign_bus.vue'
import busadd from './views/Web/bus_add.vue'
import busEdit from './views/Web/bus_edit.vue'
import biReports from './views/Web/biReports.vue'
import download from './views/Web/download.vue'
import payment from './views/client/payment.vue'
import posSettings from './views/client/posSettings.vue'
import shouqianba from './views/client/shouqianba.vue'
import tools from './views/Web/tools.vue'
import merchantList from './views/Web/payment/merchant/list.vue'
import merchantSave from './views/Web/payment/merchant/save.vue'
import providerList from './views/Web/payment/provider/list.vue'
import providerSave from './views/Web/payment/provider/save.vue'
import storeList from './views/Web/payment/store/list.vue'
import storeSave from './views/Web/payment/store/save.vue'
import approveRecord from './views/Web/approve/record.vue'

import BirdNets from './router/birdnets'
import Cloud from './router/cloud'
import IVEP from './router/ivep'
import WXPLATFORM from './router/wxPlatform'
import AliPAY from './router/alipay'
import AD from './router/ad'
import DYPLATFORM from './router/dyPlatform'

import logs from './views/Administrative/logs.vue'

/**
 * meta参数解析
 * hideLeft: 是否隐藏左侧菜单，单页菜单为true
 * module: 菜单所属模块
 * menu: 所属菜单，用于判断三级菜单是否显示高亮，如菜单列表、添加菜单、编辑菜单都是'menu'，用户列表、添加用户、编辑用户都是'user'，如此类推
 */

const routes = [
  BirdNets,
  Cloud,
  IVEP,
  WXPLATFORM,
  AliPAY,
  AD,
  DYPLATFORM,
  { path: '/', component: Login, name: 'Login' },
  {
    path: '/home',
    component: Sidebar,
    children: [{ path: '/refresh', component: refresh, name: 'refresh' }],
  },
  {
    path: '/home',
    component: Sidebar,
    children: [
      {
        path: 'menu/list',
        component: menuList,
        name: 'menuList',
        meta: { hideLeft: false, module: 'Administrative', menu: 'menu' },
      },
      {
        path: 'menu/add',
        component: menuAdd,
        name: 'menuAdd',
        meta: { hideLeft: false, module: 'Administrative', menu: 'menu' },
      },
      {
        path: 'menu/edit/:id',
        component: menuEdit,
        name: 'menuEdit',
        meta: { hideLeft: false, module: 'Administrative', menu: 'menu' },
      },
    ],
  },
  {
    path: '/home',
    component: Sidebar,
    children: [
      {
        path: 'config/add',
        component: systemConfig,
        name: 'systemConfig',
        meta: {
          hideLeft: false,
          module: 'Administrative',
          menu: 'systemConfig',
        },
      },
    ],
  },

  {
    path: '/home',
    component: Sidebar,
    children: [
      {
        path: 'rule/list',
        component: ruleList,
        name: 'ruleList',
        meta: { hideLeft: false, module: 'Administrative', menu: 'rule' },
      },
      {
        path: 'rule/add',
        component: ruleAdd,
        name: 'ruleAdd',
        meta: { hideLeft: false, module: 'Administrative', menu: 'rule' },
      },
      {
        path: 'rule/edit/:id',
        component: ruleEdit,
        name: 'ruleEdit',
        meta: { hideLeft: false, module: 'Administrative', menu: 'rule' },
      },
    ],
  },
  {
    path: '/home',
    component: Sidebar,
    children: [
      {
        path: 'position/list',
        component: positionList,
        name: 'positionList',
        meta: { hideLeft: false, module: 'Administrative', menu: 'position' },
      },
      {
        path: 'position/add',
        component: positionAdd,
        name: 'positionAdd',
        meta: { hideLeft: false, module: 'Administrative', menu: 'position' },
      },
      {
        path: 'position/edit/:id',
        component: positionEdit,
        name: 'positionEdit',
        meta: { hideLeft: false, module: 'Administrative', menu: 'position' },
      },
    ],
  },
  {
    path: '/home',
    component: Sidebar,
    children: [
      {
        path: 'structures/list',
        component: structuresList,
        name: 'structuresList',
        meta: { hideLeft: false, module: 'Administrative', menu: 'structures' },
      },
      {
        path: 'structures/add',
        component: structuresAdd,
        name: 'structuresAdd',
        meta: { hideLeft: false, module: 'Administrative', menu: 'structures' },
      },
      {
        path: 'structures/edit/:id',
        component: structuresEdit,
        name: 'structuresEdit',
        meta: { hideLeft: false, module: 'Administrative', menu: 'structures' },
      },
    ],
  },
  {
    path: '/home',
    component: Sidebar,
    children: [
      {
        path: 'groups/list',
        component: groupsList,
        name: 'groupsList',
        meta: { hideLeft: false, module: 'Administrative', menu: 'groups' },
      },
      {
        path: 'groups/add',
        component: groupsAdd,
        name: 'groupsAdd',
        meta: { hideLeft: false, module: 'Administrative', menu: 'groups' },
      },
      {
        path: 'groups/edit/:id',
        component: groupsEdit,
        name: 'groupsEdit',
        meta: { hideLeft: false, module: 'Administrative', menu: 'groups' },
      },
    ],
  },
  {
    path: '/home',
    component: Sidebar,
    children: [
      {
        path: 'users/list',
        component: usersList,
        name: 'usersList',
        meta: { hideLeft: false, module: 'Administrative', menu: 'users' },
      },
      {
        path: 'users/add',
        component: usersAdd,
        name: 'usersAdd',
        meta: { hideLeft: false, module: 'Administrative', menu: 'users' },
      },
      {
        path: 'users/edit/:id',
        component: usersEdit,
        name: 'usersEdit',
        meta: { hideLeft: false, module: 'Administrative', menu: 'users' },
      },
    ],
  },
  {
    path: '/home',
    component: Sidebar,
    children: [
      {
        path: 'logs/list',
        component: logs,
        name: 'logsList',
        meta: { hideLeft: false, module: 'Administrative', menu: 'logs' },
      },
    ],
  },
  {
    path: '/web',
    component: Sidebar,
    children: [
      {
        path: 'index/index',
        component: webindex,
        name: 'webindex',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'index' },
      },
    ],
  },
  {
    path: '/web',
    component: Sidebar,
    children: [
      {
        path: 'user_settings/settings',
        component: settings,
        name: 'settings',
        meta: {
          hideLeft: false,
          module: 'Webistrative',
          menu: 'user_settings',
        },
      },
    ],
  },
  {
    path: '/web',
    component: Sidebar,
    children: [
      {
        path: 'posSettings',
        component: posSettings,
        name: 'posSettings',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'posSettings' },
      },
      {
        path: 'shouqianba',
        component: shouqianba,
        name: 'shouqianba',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'shouqianba' },
      },
      {
        path: 'payment',
        component: payment,
        name: 'payment',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'payment' },
      },
      {
        path: 'business/bus_type',
        component: bustype,
        name: 'bus_type',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'bus_type' },
      },
      {
        path: 'business/bus_brand',
        component: busbrand,
        name: 'bus_brand',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'bus_type' },
      },
      {
        path: 'business/bus_region',
        component: busregion,
        name: 'bus_region',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'bus_type' },
      },
      {
        path: 'business/bus_wx',
        component: buswx,
        name: 'bus_wx',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'bus_type' },
      },
      {
        path: 'merchant/list',
        component: merchantList,
        name: 'merchantList',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'payment' },
      },
      {
        path: 'merchant/save/:id?',
        props: true,
        component: merchantSave,
        name: 'merchantSave',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'payment' },
      },
      {
        path: 'provider/list',
        component: providerList,
        name: 'providerList',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'payment' },
      },
      {
        path: 'provider/save/:id?',
        props: true,
        component: providerSave,
        name: 'providerSave',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'payment' },
      },
      {
        path: 'store/list',
        component: storeList,
        name: 'storeList',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'payment' },
      },
      {
        path: 'store/save/:id?',
        props: true,
        component: storeSave,
        name: 'storeSave',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'payment' },
      },
      {
        path: 'approve/record',
        component: approveRecord,
        name: 'approveRecord',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'approve' },
      },
    ],
  },
  {
    path: '/web',
    component: Sidebar,
    children: [
      {
        path: 'bus_basic_statistic/list',
        component: basics,
        name: 'basic_statistic',
        meta: {
          hideLeft: false,
          module: 'Webistrative',
          menu: 'bus_basic_statistic',
        },
      },
      {
        path: 'bus_chart/list',
        component: bus_chart,
        name: 'bus_chart',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'bus_chart' },
      },
      {
        path: 'bus_users_statistics/list',
        component: bus_user_statistic,
        name: 'bus_user_statistic',
        meta: {
          hideLeft: false,
          module: 'Webistrative',
          menu: 'bus_users_statistics',
        },
      },
    ],
  },
  {
    path: '/web',
    component: Sidebar,
    children: [
      {
        path: 'templateInfo/list',
        component: templateInfo,
        name: 'templateInfo',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'templateInfo' },
      },
      {
        path: 'noticeSend',
        component: (resolve) => require(['./views/Web/noticeSend.vue'], resolve),
        name: 'noticeSend',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'noticeSend' },
      },
      {
        path: 'noticeList',
        component: (resolve) => require(['./views/Web/noticeList.vue'], resolve),
        name: 'noticeList',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'noticeList' },
      },
      {
        path: 'modalNotice',
        component: (resolve) => require(['./views/Web/modalNotice.vue'], resolve),
        name: 'modalNotice',
        meta: { module: 'Webistrative', menu: 'modalNotice' },
      },
      {
        path: 'msgSign',
        component: (resolve) => require(['./views/Web/msgSign.vue'], resolve),
        name: 'msgSign',
        meta: { module: 'Webistrative', menu: 'msgSign' },
      },
      {
        path: 'modalSend',
        component: (resolve) => require(['./views/Web/modalSend.vue'], resolve),
        name: 'modalSend',
        meta: { module: 'Webistrative', menu: 'modalSend' },
      },
      {
        path: 'electronic',
        component: (resolve) => require(['./views/Web/electronic.vue'], resolve),
        name: 'electronic',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'electronic' },
      },
      {
        path: 'dialogNoticeList',
        component: (resolve) => require(['./views/Web/dialogNoticeList.vue'], resolve),
        name: 'dialogNoticeList',
        meta: {
          hideLeft: false,
          module: 'Webistrative',
          menu: 'dialogNoticeList',
        },
      },
      {
        path: 'dialogNoticeDetail',
        component: (resolve) => require(['./views/Web/dialogNoticeDetail.vue'], resolve),
        name: 'dialogNoticeDetail',
        meta: {
          hideLeft: false,
          module: 'Webistrative',
          menu: 'dialogNoticeDetail',
        },
      },
      {
        path: 'tools',
        component: (resolve) => require(['./views/Web/tools.vue'], resolve),
        name: 'tools',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'tools' },
      },
      {
        path: 'syncMeituanMember',
        component: (resolve) => require(['./views/Web/syncMeituanMember.vue'], resolve),
        name: 'syncMeituanMember',
        meta: {
          hideLeft: false,
          module: 'Webistrative',
          menu: 'syncMeituanMember',
        },
      },
    ],
  },
  {
    path: '/web',
    component: Sidebar,
    children: [
      {
        path: 'users/users_statistical_list',
        component: users,
        name: 'users_statistical_list',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'users' },
      },
    ],
  },
  {
    path: '/web',
    component: Sidebar,
    children: [
      {
        path: 'bi/statistical',
        component: bi,
        name: 'statistical',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'bi' },
      },
    ],
  },
  {
    path: '/web',
    component: Sidebar,
    children: [
      {
        path: 'maintenance/sign_bus',
        component: maintain,
        name: 'bus_maintain',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'bus_maintain' },
      },
    ],
  },
  {
    path: '/web',
    component: Sidebar,
    children: [
      {
        path: 'maintenance/add',
        component: busadd,
        name: 'busAdd',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'bus_maintain' },
      },
    ],
  },
  {
    path: '/web',
    component: Sidebar,
    children: [
      {
        path: 'maintenance/edit/:id',
        component: busEdit,
        name: 'busEdit',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'bus_edit' },
      },
    ],
  },
  {
    path: '/web',
    component: Sidebar,
    children: [
      {
        path: 'bi/chart',
        component: biReports,
        name: 'biChart',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'biChart' },
      },
    ],
  },
  {
    path: '/web',
    component: Sidebar,
    children: [
      {
        path: 'business/download',
        component: download,
        name: 'download',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'download' },
      },
    ],
  },
  {
    path: '/web',
    component: Sidebar,
    children: [
      {
        path: 'businesses',
        component: (resolve) => require(['./views/Web/Business.vue'], resolve),
        name: 'businesses',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'businesses' },
      },
      {
        path: 'newsPublish',
        component: (resolve) => require(['./views/Web/newsPublish.vue'], resolve),
        name: 'newsPublish',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'newsPublish' },
      },
      {
        path: 'salesPhoneList',
        component: (resolve) => require(['./views/Web/salesPhoneList.vue'], resolve),
        name: 'salesPhoneList',
        meta: {
          hideLeft: false,
          module: 'Webistrative',
          menu: 'salesPhoneList',
        },
      },
      {
        path: 'newsEdit',
        component: (resolve) => require(['./views/Web/newsEdit.vue'], resolve),
        name: 'newsEdit',
        // meta: { hideLeft: false, module: 'Webistrative', menu: 'newsEdit' },
        meta: { hideLeft: false, module: 'Webistrative', menu: 'newsPublish' },
      },
      {
        path: 'businessApply',
        component: (resolve) => require(['./views/Web/BusinessApply.vue'], resolve),
        name: 'businessApply',
        meta: {
          hideLeft: false,
          module: 'Webistrative',
          menu: 'businessApply',
        },
      },
      {
        path: 'packageControl',
        component: (resolve) => require(['./views/Web/packageControl.vue'], resolve),
        name: 'packageControl',
        meta: {
          hideLeft: false,
          module: 'Webistrative',
          menu: 'packageControl',
        },
      },
      {
        path: 'cuspackageEdit/:bus_id',
        component: (resolve) => require(['./views/Web/cuspackageEdit.vue'], resolve),
        name: 'cuspackageEdit',
        meta: {
          hideLeft: false,
          module: 'Webistrative',
          menu: 'cuspackageEdit',
        },
      },
      {
        path: 'packagesetControl',
        component: (resolve) => require(['./views/Web/packagesetControl.vue'], resolve),
        name: 'packagesetControl',
        meta: {
          hideLeft: false,
          module: 'Webistrative',
          menu: 'packagesetControl',
        },
      },
      {
        path: 'packageAdd/:version_id',
        component: (resolve) => require(['./views/Web/packageAdd.vue'], resolve),
        name: 'packageAdd',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'packageAdd' },
      },
      {
        path: 'versionImport',
        component: (resolve) => require(['./views/Web/versionImport.vue'], resolve),
        name: 'versionImport',
        meta: {
          hideLeft: false,
          module: 'Webistrative',
          menu: 'versionImport',
        },
      },
    ],
  },
  {
    path: '/c',
    component: (resolve) => require(['./components/Common/sidebar.vue'], resolve),
    children: [
      {
        path: 'gameList',
        component: (resolve) => require(['./views/client/gameList.vue'], resolve),
        name: 'gameList',
        meta: { hideLeft: false, module: 'client', menu: 'gameList' },
      },
      {
        path: 'gameAdd',
        component: (resolve) => require(['./views/client/gameAdd.vue'], resolve),
        name: 'gameAdd',
        meta: { hideLeft: false, module: 'client', menu: 'gameAdd' },
      },
      {
        path: 'memberList/:match_id',
        component: (resolve) => require(['./views/client/memberList.vue'], resolve),
        name: 'memberList',
        meta: { hideLeft: false, module: 'client', menu: 'memberList' },
      },
      {
        path: 'cardList',
        component: (resolve) => require(['./views/client/cardList.vue'], resolve),
        name: 'cardList',
        meta: { hideLeft: false, module: 'client', menu: 'cardList' },
      },
      {
        path: 'cardAdd',
        component: (resolve) => require(['./views/client/cardAdd.vue'], resolve),
        name: 'cardAdd',
        meta: { hideLeft: false, module: 'client', menu: 'cardAdd' },
      },
      {
        path: 'cardEdit/:cardId',
        component: (resolve) => require(['./views/client/cardAdd.vue'], resolve),
        name: 'cardEdit',
        meta: { hideLeft: false, module: 'client', menu: 'cardEdit' },
      },
      {
        path: 'memberControl',
        component: (resolve) => require(['./views/client/memberControl.vue'], resolve),
        name: 'memberControl',
        meta: { hideLeft: false, module: 'client', menu: 'memberControl' },
      },
      {
        path: 'memberEdit/:c_user_id',
        component: (resolve) => require(['./views/client/memberEdit.vue'], resolve),
        name: 'memberEdit',
        meta: { hideLeft: false, module: 'client', menu: 'memberEdit' },
      },
      {
        path: 'cardDetail/:c_user_id/:card_user_id/:c_user_card_id',
        component: (resolve) => require(['./views/client/cardDetail.vue'], resolve),
        name: 'cardDetail',
        meta: { hideLeft: false, module: 'client', menu: 'cardDetail' },
      },
      {
        path: 'gymControl',
        component: (resolve) => require(['./views/client/gymControl.vue'], resolve),
        name: 'gymControl',
        meta: { hideLeft: false, module: 'client', menu: 'gymControl' },
      },
      {
        path: 'newsManage',
        component: (resolve) => require(['./views/client/newsManage.vue'], resolve),
        name: 'newsManage',
        meta: { hideLeft: false, module: 'client', menu: 'newsManage' },
      },
      {
        path: 'bannerManage',
        component: (resolve) => require(['./views/client/bannerManage.vue'], resolve),
        name: 'bannerManage',
        meta: { hideLeft: false, module: 'client', menu: 'bannerManage' },
      },
      {
        path: 'activityManage',
        component: (resolve) => require(['./views/client/activityManage.vue'], resolve),
        name: 'activityManage',
        meta: { hideLeft: false, module: 'client', menu: 'activityManage' },
      },
      {
        path: 'activityMember',
        component: (resolve) => require(['./views/client/activityMember.vue'], resolve),
        name: 'activityMember',
        meta: { hideLeft: false, module: 'client', menu: 'activityMember' },
      },
      {
        path: 'gymEdit/:city_id/:c_bus_id',
        component: (resolve) => require(['./views/client/gymEdit.vue'], resolve),
        name: 'gymEdit',
        meta: { hideLeft: false, module: 'client', menu: 'gymEdit' },
      },
      {
        path: 'feedBack',
        component: (resolve) => require(['./views/client/feedBack.vue'], resolve),
        name: 'feedBack',
        meta: { hideLeft: false, module: 'client', menu: 'feedBack' },
      },
      {
        path: 'statisticsMember',
        component: (resolve) => require(['./views/client/statisticsMember.vue'], resolve),
        name: 'statisticsMember',
        meta: { hideLeft: false, module: 'client', menu: 'statisticsMember' },
      },
      {
        path: 'statisticsCard',
        component: (resolve) => require(['./views/client/statisticsCard.vue'], resolve),
        name: 'statisticsCard',
        meta: { hideLeft: false, module: 'client', menu: 'statisticsCard' },
      },
      {
        path: 'statisticsStadium',
        component: (resolve) => require(['./views/client/statisticsStadium.vue'], resolve),
        name: 'statisticsStadium',
        meta: { hideLeft: false, module: 'client', menu: 'statisticsStadium' },
      },
      {
        path: 'statisticsOrder',
        component: (resolve) => require(['./views/client/statisticsOrder.vue'], resolve),
        name: 'statisticsOrder',
        meta: { hideLeft: false, module: 'client', menu: 'statisticsOrder' },
      },
      {
        path: 'applicationControl',
        component: (resolve) => require(['./views/client/applicationControl.vue'], resolve),
        name: 'applicationControl',
        meta: { hideLeft: false, module: 'client', menu: 'applicationControl' },
      },
      {
        path: 'tagControl',
        component: (resolve) => require(['./views/client/tagControl.vue'], resolve),
        name: 'tagControl',
        meta: { hideLeft: false, module: 'client', menu: 'tagControl' },
      },
      {
        path: 'tagAdd/:tag_id',
        component: (resolve) => require(['./views/client/tagAdd.vue'], resolve),
        name: 'tagAdd',
        meta: { hideLeft: false, module: 'client', menu: 'tagAdd' },
      },
    ],
  },
  {
    path: '/fcabin',
    component: (resolve) => require(['./components/Common/sidebar.vue'], resolve),
    children: [
      {
        path: 'cabinControl',
        component: (resolve) => require(['./views/fcabin/cabinControl.vue'], resolve),
        name: 'cabinControl',
        meta: { hideLeft: false, module: 'fcabin', menu: 'cabinControl' },
      },
      {
        path: 'cabinAdd/:cabin_id',
        component: (resolve) => require(['./views/fcabin/cabinAdd.vue'], resolve),
        name: 'cabinAdd',
        meta: { hideLeft: false, module: 'fcabin', menu: 'cabinAdd' },
      },
      {
        path: 'cabinPara',
        component: (resolve) => require(['./views/fcabin/cabinPara.vue'], resolve),
        name: 'cabinPara',
        meta: { hideLeft: false, module: 'fcabin', menu: 'cabinPara' },
      },
      {
        path: 'fmemberControl',
        component: (resolve) => require(['./views/fcabin/fmemberControl.vue'], resolve),
        name: 'fmemberControl',
        meta: { hideLeft: false, module: 'fcabin', menu: 'fmemberControl' },
      },
      {
        path: 'fmemberAdd/:member_id',
        component: (resolve) => require(['./views/fcabin/fmemberAdd.vue'], resolve),
        name: 'fmemberAdd',
        meta: { hideLeft: false, module: 'fcabin', menu: 'fmemberAdd' },
      },
      {
        path: 'orderControl',
        component: (resolve) => require(['./views/fcabin/orderControl.vue'], resolve),
        name: 'orderControl',
        meta: { hideLeft: false, module: 'fcabin', menu: 'orderControl' },
      },
    ],
  },
  {
    path: '/web',
    component: Sidebar,
    children: [
      {
        path: 'meituan/list',
        component: (resolve) => require(['./views/Web/meituan/list.vue'], resolve),
        name: 'meituan_list',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'meituan_list' },
      },
      {
        path: 'meituan/save/:id?',
        props: true,
        component: (resolve) => require(['./views/Web/meituan/save.vue'], resolve),
        name: 'meituan_save',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'meituan_save' },
      },
      {
        path: 'meituan/renew_list',
        component: (resolve) => require(['./views/Web/meituan/renewList.vue'], resolve),
        name: 'meituan_renew_list',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'meituan_renew_list' },
      },
      {
        path: 'meituan/contract_list',
        component: (resolve) => require(['./views/Web/meituan/contractList.vue'], resolve),
        name: 'meituan_contract_list',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'meituan_contract_list' },
      },
      {
        path: 'meituan/record_list',
        component: (resolve) => require(['./views/Web/meituan/recordList.vue'], resolve),
        name: 'meituan_record_list',
        meta: { hideLeft: false, module: 'Webistrative', menu: 'meituan_record_list' },
      },
    ],
  },
]
export default routes
