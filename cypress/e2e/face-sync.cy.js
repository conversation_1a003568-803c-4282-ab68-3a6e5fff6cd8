/// <reference types="cypress" />

describe('Face Synchronization Module', () => {
  beforeEach(() => {
    cy.login()
    cy.wait(2000) // Wait for login to complete
    cy.visit('/cloud/faceSync')
  })

  it('should navigate to add page when clicking the add button', () => {
    cy.get('button[data-cy="add-button"]').click()
    cy.url().should('include', '/faceSyncAdd')
    cy.wait(2000) // Wait for API response

    // Add a new one
    cy.get('div[data-cy="merchant-select"]').click()
    cy.get('li[data-cy="merchant-option"]').first().click()
    cy.get('button[data-cy="save-button"]').click()
  })

  it('should filter the table when searching by merchant', () => {
    // If there are merchants in the dropdown
    cy.get('div[data-cy="merchant-select"]').click()
    cy.get('li[data-cy="merchant-option"]')
      .first()
      .then(($option) => {
        if ($option.length > 0) {
          // Get the merchant name for verification later
          const merchantName = $option.text()
          cy.wrap($option).click()

          // Click search button
          cy.get('button[data-cy="search-button"]').click()
          cy.wait(1000) // Wait for API response

          // Verify filtered results contain the selected merchant
          cy.get('div.cell').should('contain', merchantName)
        }
      })
  })

  it('should navigate to operation logs', () => {
    // Click logs button on the first row if available
    cy.get('button[data-cy="log-button"]').first().click()
  })

  it('should delete a face sync setting', () => {
    // Click delete button on the first row if available
    cy.get('button[data-cy="delete-button"]').first().click()
    cy.wait(2000) // Wait for API response
    cy.get('div.el-message-box').should('be.visible')
    cy.get('div.el-message-box button.el-button--primary').click()
  })
})
