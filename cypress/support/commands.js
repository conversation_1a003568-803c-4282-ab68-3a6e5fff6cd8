// ***********************************************
// This example commands.js shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************
//
//
// -- This is a parent command --
// Cypress.Commands.add('login', (email, password) => { ... })
//
//
// -- This is a child command --
// Cypress.Commands.add('drag', { prevSubject: 'element'}, (subject, options) => { ... })
//
//
// -- This is a dual command --
// Cypress.Commands.add('dismiss', { prevSubject: 'optional'}, (subject, options) => { ... })
//
//
// -- This will overwrite an existing command --
// Cypress.Commands.overwrite('visit', (originalFn, url, options) => { ... })

const login = () => {
  cy.visit('/')
  cy.get('div.tip img').first().click()
  cy.get('input[placeholder="账号"]').type(Cypress.env('username'))
  cy.get('input[placeholder="密码"]').type(Cypress.env('password'))
  cy.get('button').click()
}

// Cypress.Commands.add('url', url)
Cypress.Commands.add('login', login)
