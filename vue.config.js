const path = require('path')
function resolve(dir) {
  return path.join(__dirname, '..', dir)
}

module.exports = {
  lintOnSave: false,
  css: {
    loaderOptions: {
      less: {
        javascriptEnabled: true,
      }
    }
  },
  devServer: {
    port: 8890,
    disableHostCheck: true,
    open: true
  },
  configureWebpack: config => {
    const myConfig = {
      resolve: {
        extensions: ['*', '.js', '.vue', '.json'],
        alias: {
          'vue$': 'vue/dist/vue.esm.js',
          'src': '@',
          'static': resolve('static'),
          'assets': '@/assets',
          'components': '@/components'
        }
      }
    }
    return myConfig
  },
  chainWebpack: config => {
    /**
     * 删除懒加载模块的prefetch，降低带宽压力
     */
    config.plugins.delete('prefetch')
    
  }
}
